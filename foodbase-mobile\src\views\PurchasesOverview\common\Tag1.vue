<template>
  <span class="tag" :style="style">
    <slot></slot>
  </span>
</template>

<script setup>
import { computed } from 'vue';

const props = defineProps({
  color: {
    type: String,
    default: '#ffffff',
  }, // 1,2,3,4,5
  type: [String, Number],
});

const colorList = ['#32AE57', '#FFA50B', '#1989FA', '#0EC087'];

const colorOfType = (type) => {
  return colorList[type % 3];
};

const style = computed(() => {
  return {
    color: props.color,
    backgroundColor: props.type ? colorOfType(props.type) : '#8799B0',
  };
});
</script>

<style scoped>
.tag {
  font-size: 14px;
  display: inline-block;
  /* line-height: 18px; */
  border-radius: 2px;
  padding: 2px 8px;
  font-weight: 400;
  margin-left: 5px;
}
</style>
