<!DOCTYPE html>
<html lang="">

<head>
  <meta charset="utf-8">
  <meta http-equiv="X-UA-Compatible" content="IE=edge">
  <meta name="referrer" content="no-referrer">
  <meta name="viewport"
    content="width=device-width, user-scalable=no, initial-scale=1.0, maximum-scale=1.0, minimum-scale=1.0">
  <link rel="icon" href="" id="myLink">
  <!-- <link rel="stylesheet" href="<%= BASE_URL %>/videojs/css/video-js.min.css">
  <script src="<%= BASE_URL %>/videojs/js/video.min.js"></script>
  <script src="<%= BASE_URL %>/videojs/js/flv.min.js"></script>
  <script src="<%= BASE_URL %>/videojs/js/videojs-flvjs.min.js"></script> -->
  <script type="text/javascript" charset="utf-8" src="appUpdate.js"></script>
  <script src="https://prod-pass4.ehbapp.hubei.gov.cn:30443/static/jssdkv2/jssdk.js"></script>
  <script type="text/javascript" charset="utf-8" src="cordova.js"></script>
  <title>储好粮食一件事</title>
</head>

<body>
  <noscript>
    <strong>We're sorry but <%= htmlWebpackPlugin.options.title %> doesn't work properly without JavaScript enabled.
        Please enable it to continue.</strong>
  </noscript>
  <div id="app"></div>

  <!-- H265Player -->
  <script type='text/javascript' src="h265Player/common.js"></script>
  <script type='text/javascript' src="h265Player/webgl.js"></script>
  <script type='text/javascript' src="h265Player/player.js"></script>
  <!-- 添加密评SDK -->
  <script src="sw.webjs.sdk/autotrack.js"></script>

  <!-- built files will be auto injected -->
  <script>
    // 获取.env变量
    var myVariable = "<%= VUE_APP_LOGO %>"
    // 动态更新<link>标签的href值
    var linkElement = document.getElementById('myLink');
    linkElement.href = '/' + myVariable;

    document.addEventListener("deviceready", checkAppVersion, false);
    window.__remoteAppInfo = {}
    function checkAppVersion (isProd = true) {
      const envUrl = <%= VUE_APP_RESERVER_API_BASE_URL %>
          const updateUrl = `${envUrl[isProd ? '0' : '1']}/reserver/api/fileManager/getLastFileByAnonymous?checkCode=q6zny3,${isProd ? '' : 'dev-'}<%= VUE_APP_MODE %>-version.xml`;
      console.log('updateUrl: ', updateUrl);
      fetch(updateUrl).then(res => res.text())
        .then((data) => {
          console.log('远程服务器APP版本信息：', data)
          window.__remoteAppInfo = data
        });
      window.AppUpdate?.checkAppUpdate(onSuccess, onFail, updateUrl);

      function onFail () { console.log('fail', JSON.stringify(arguments), arguments); }
      function onSuccess () {
        console.log('success', JSON.stringify(arguments), arguments);
      }
    }
    window.__appInfo = {}
    console.log('当前APP版本信息：', JSON.stringify(__appInfo))



  </script>
  <script type='text/javascript' src="pro/jessibuca-pro.js"></script>

</body>

</html>