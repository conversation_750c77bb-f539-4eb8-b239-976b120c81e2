<template>
  <HCard title="产地分布（单位：吨）">
    <template #header-extra>
      <HPicker
        class="bg-gray"
        :options="foodCategoryOptions"
        v-model:value="selectedFoodCategory"
      ></HPicker>
    </template>
    <div class="stock-origin-distribution">
      <div class="chart">
        <HEchart ref="barChart" :options="structureOptions" v-if="barData.length > 0" />
        <EmptyHolder v-else />
      </div>
    </div>
  </HCard>
</template>

<script setup>
import { computed, ref, watch } from 'vue';
import { HCard, HEchart, HPicker } from '@/components';
// import { dataVFoodCategoryMap } from '@/views/StockOverview/constant';
import { useStore } from 'vuex';
import EmptyHolder from '@/views/common/EmptyHolder';

const props = defineProps({
  categoryType: Number, // 1 原粮，2 成品粮， 3 食用油
});

const store = useStore();

// console.log(store.state['food-category'].list.filter(i => i.categoryType === props.categoryType));

const foodCategoryOptions = computed(() => {
  const foodCategories =
    store.state['food-category'].list.filter((i) => i.categoryType === props.categoryType) || [];
  return foodCategories.map((it) => {
    return {
      text: it.name,
      value: it.id,
    };
  });
});

const selectedFoodCategory = ref(foodCategoryOptions.value[0]?.value);

watch(
  selectedFoodCategory,
  (newValue, oldValue) => {
    if (newValue != oldValue) {
      store.dispatch('stock-overview/fetchStockDistributionData', {
        categoryType: props.categoryType,
        originCategory: newValue,
      });
    }
  },
  {
    deep: true,
    immediate: true,
  },
);

const stockDistribution = computed(() => {
  const stockDistributionDataMap = store.state['stock-overview'].stockDistributionDataMap;
  const stockDistributionData = stockDistributionDataMap.find(
    (it) => it.category == props.categoryType,
  )?.data;
  return stockDistributionData;
});

const barChart = ref(null);

const barData = computed(() => {
  return stockDistribution.value.map((item) => {
    return {
      产地: item.name,
      库存: parseFloat((item?.value || 0).toFixed(2)),
    };
  });
});

const structureOptions = computed(() => {
  return {
    tooltip: {
      trigger: 'axis',
      backgroundColor: 'rgba(0,10,48,0.7)',
      borderWidth: 0,
      textStyle: {
        color: '#fff',
      },
    },
    legend: {
      show: false,
    },
    grid: {
      top: 30,
      right: 10,
      bottom: 60,
      left: 80,
    },
    dataset: {
      source: barData.value,
    },
    xAxis: {
      type: 'category',
      axisLabel: {
        interval: 0,
        fontSize: 16,
        rotate: 45,
        verticalAlign: 'left',
      },
    },
    yAxis: {
      minInterval: 1,
      boundaryGap: ['0%', '20%'],
      max: function (value) {
        if (value.max === 0) {
          return 5;
        }
      },
      axisLine: {
        show: false,
      },
      splitLine: {
        lineStyle: {
          width: 0.5,
          color: 'rgba(191,191,191,0.5)',
          type: 'solid',
        },
      },
      axisLabel: {
        fontSize: 14,
        lineHeight: 18,
      },
    },
    series: [
      {
        type: 'bar',
        barWidth: 10,
        textStyle: {
          color: '#323233',
        },
        itemStyle: {
          color: {
            type: 'linear',
            x: 0,
            y: 0,
            x2: 0,
            y2: 1,
            colorStops: [
              {
                offset: 0,
                color: '#2DB9FF', // 0% 处的颜色
              },
              {
                offset: 1,
                color: '#1A6CFF', // 100% 处的颜色
              },
            ],
          },
        },
      },
    ],
  };
});
</script>

<style scoped lang="scss">
.stock-origin-distribution {
  .chart {
    height: 230px;
  }
}

.h-picker {
  line-height: 34px;
  color: var(--van-gray-7);
}

.h-echarts {
  width: 100%;
  height: 100%;
}
</style>
