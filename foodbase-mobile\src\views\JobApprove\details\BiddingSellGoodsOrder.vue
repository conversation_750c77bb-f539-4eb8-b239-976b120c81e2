<template>
  <div class="auctionsales">
    <div>
      <van-tabs v-model:active="approval">
        <van-tab title="申请信息"></van-tab>
        <van-tab title="审批流程"></van-tab>
      </van-tabs>
    </div>
    <div class="contentsof" v-if="approval == 0">
      <div class="numbering">
        <div class="numbering-B">编号：</div>
        <div class="numbering-S">{{ form.serialNumber }}</div>
      </div>
      <div class="deliveryPoint">
        <div class="deliveryhead">提货单</div>
        <div class="deliverytext">
          <div>{{ form.header }}:</div>
          <div>&emsp;&emsp;{{ form.descriper }}</div>
        </div>
        <div v-for="item in detailList" :key="item.id">
          <div class="detailStart">
            <div class="detailStart_one">中标日期</div>
            <div class="detailStart_two">{{ item.winBidDate || '-' }}</div>
          </div>
          <div class="detailStart">
            <div class="detailStart_one">仓号：</div>
            <div class="detailStart_two">{{ item.storeHouse }}</div>
          </div>
          <div class="detailStart">
            <div class="detailStart_one">品种：</div>
            <div class="detailStart_two">{{ item.foodCategory }}</div>
          </div>
          <div class="detailStart">
            <div class="detailStart_one">合同数量(吨)：</div>
            <div class="detailStart_two">
              {{ Number(item.totalNumber).toLocaleString('zh', { minimumFractionDigits: 3 }) }}
            </div>
          </div>
          <div class="detailStart">
            <div class="detailStart_one">本次发货(吨)：</div>
            <div class="detailStart_two">
              {{ Number(item.pickedCounter).toLocaleString('zh', { minimumFractionDigits: 3 }) }}
            </div>
          </div>
        </div>
      </div>
      <div class="card3"></div>
      <div class="contact">
        <div class="contact_for">承储单位联系方式：</div>
        <div class="contact_act">{{ form.reserverPhone }}</div>
      </div>
      <div class="contact">
        <div class="contact_for">提货单位联系方式：</div>
        <div class="contact_act">{{ form.customerPhone }}</div>
      </div>
      <div class="indicates">
        {{ form.remark }}
      </div>
      <approvalButton :id="params?.id" coder="billLading"></approvalButton>
    </div>
    <div class="approvals_required" v-if="approval == 1">
      <approvalProcess :id="params?.id" coder="billLading"></approvalProcess>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue';
import approvalProcess from './common/approval-process';
import approvalButton from './common/approval-button';
import { useRoute } from 'vue-router';
import { getBillLadingById } from '@/api/job-approve';
// import { BizDictName } from '@/views/InoutManage/common';

const route = useRoute();

const params = ref(route.params);

// const active = ref(1);

const form = ref({});

const detailList = ref([]);
const approval = ref(0);

const getDetail = async () => {
  const res = await getBillLadingById({
    id: params.value.id,
  });
  detailList.value = res.detailList;
  form.value = res;
  form.value.descriper =
    '请发给' +
    form.value.biddinger +
    form.value.straightPipe +
    form.value.foodCategory +
    '，' +
    form.value.foodNum +
    '吨（' +
    form.value.store +
    form.value.storeHouse +
    '号仓），' +
    form.value.descriper;
};

// const isNumber = (val) => {
//   return !isNaN(parseFloat(val));
// };

onMounted(() => {
  getDetail();
});
</script>

<style scoped lang="scss">
.auctionsales {
  height: 36px;
  background-color: #fff;
}
.contentsof {
  margin-top: 10px;
  background-color: #fff;
}
.numbering {
  display: flex;
  flex-direction: row;
  padding-left: 16px;
  padding-top: 15px;
  .numbering-B {
    color: rgba(31, 31, 31, 0.65);
  }
  .numbering-S {
    color: #121212;
    font-weight: bold;
    font-size: 14px;
  }
}
.deliveryPoint {
  margin-top: 2px;
  .deliveryhead {
    text-align: center;
    font-size: 18px;
    font-weight: bold;
  }
  .deliverytext {
    padding-top: 15px;
    padding-left: 16px;
    margin-bottom: 15px;
  }
}
.detailStart {
  padding-left: 16px;
  display: flex;
  flex-direction: row;
  margin-bottom: 8px;
  .detailStart_one {
    width: 94px;
    font-size: 14px;
    color: rgba(31, 31, 31, 0.65);
  }
  .detailStart_two {
    margin-left: 2px;
  }
}
.contact {
  padding-left: 16px;
  display: flex;
  flex-direction: row;
  margin-top: 8px;
  .contact_for {
    width: 126px;
    font-size: 14px;
    color: rgba(31, 31, 31, 0.65);
  }
  .contact_act {
  }
}
.card3 {
  border: 0;
  border-top: 2px dotted #a2a9b6;
  margin-left: 16px;
  margin-right: 15px;
}
.indicates {
  padding-left: 16px;
  padding-right: 24px;
  margin-top: 12px;
  background-color: #fff;
  font-size: 14px;
}
.border-bottom {
  height: 99px;
  margin-top: 10px;
  background-color: #fff;
  display: flex;
  flex-direction: row;
  justify-content: space-between;
}
.border-left {
  padding-left: 16px;
  padding-top: 14px;
}
.border-right {
  padding-right: 16px;
  padding-top: 14px;
}
.approvals_required {
  background-color: #fff;
}
</style>
