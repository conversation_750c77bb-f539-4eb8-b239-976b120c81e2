<template>
  <h-echart :options="options" autoresize />
</template>

<script>
import HEchart from '@/components/HEchart/HEchart';

export default {
  name: 'HFloatLine',
  components: { HEchart },
  props: {
    data: {
      type: Array,
      default: () => [],
    },
  },
  computed: {
    options() {
      return {
        legend: {
          show: false,
        },
        grid: { top: 20, bottom: 20 },
        dataset: {
          source: this.data,
        },
        xAxis: {
          type: 'category',
          boundaryGap: false,
          axisLabel: {
            fontSize: 12,
          },
          axisLine: {
            show: false,
          },
        },
        yAxis: {
          boundaryGap: false,
          axisLine: {
            show: false,
          },
          axisLabel: {
            show: false,
          },
          splitLine: {
            show: false,
          },
        },
        series: [
          {
            type: 'line',
            symbolSize: 7,
            animationDuration: 300,
            label: {
              show: true,
              lineHeight: 17,
              fontSize: 12,
              fontWeight: 'bold',
              color: '#4BBCFF',
              position: 'top',
              distance: 0,
              formatter: (params) => params.value[1].toLocaleString(),
            },
            labelLine: {
              show: false,
            },
            lineStyle: {
              color: '#4BBCFF',
              width: 2,
              type: 'dashed',
            },
            areaStyle: {
              color: {
                tpe: 'linear',
                x: 0,
                y: 1,
                x2: 0,
                y2: 0,
                colorStops: [
                  {
                    offset: 0,
                    color: 'rgba(130, 223, 255, 0)',
                  },
                  {
                    offset: 1,
                    color: 'rgba(75, 188, 255, 1)',
                  },
                ],
              },
            },
          },
        ],
      };
    },
  },
};
</script>

<style scoped></style>
