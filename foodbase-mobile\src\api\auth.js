import { request, reserveRequest, hxdiframeworkRequest } from '@/utils/request';

export async function getAuthCode() {
  return reserveRequest().get('/auth/code');
}

export async function login(data, config) {
  return request().post('/auth/login', data, config);
}

export async function phoneLogin(data, config) {
  return request().post('/auth/office/login', data, config);
}

export async function loginWithPhone(data, config) {
  return reserveRequest().post('/auth/office/cbMatrixLogin', data, config);
}

export async function getUserInfo() {
  return request().get('/auth/info');
}

export async function gerUserInfoByReserve() {
  return reserveRequest().get('/auth/info');
}

export async function getExtraUserInfo() {
  return request().get('/auth/userInfo');
}

export async function checkLogin() {
  return request().get('/auth/check');
}

export async function logout() {
  return reserveRequest().delete('/auth/logout');
}

export async function zzdLogin(data, config) {
  return request().post('/auth/zzd/login', data, config);
}

export async function phoneLoginGetToken(data, config) {
  return request().post('/auth/zlb/token', data, config);
}

export async function getHxdiframeworkToken() {
  return hxdiframeworkRequest().get('/api/token/getToken', { params: { systemCode: 'APP_CLJG' } });
}
