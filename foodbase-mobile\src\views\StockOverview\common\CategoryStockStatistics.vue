<template>
  <div class="mt-4 category-stock-statistics">
    <!-- <UnitName></UnitName> -->

    <HCard title="储备库存（单位：吨）" class="small-card">
      <ReserveStock :category-type="categoryType" />
    </HCard>

    <GranaryScale
      v-if="props.categoryType === 1"
      :max="scaleData[1]?.reserve"
      :current="scaleData[1]?.stock"
      :red-line-ratio="0.7"
    />
    <ProductGrainScale
      v-else-if="props.categoryType === 2"
      :max="scaleData[2]?.reserve"
      :current="scaleData[2]?.stock"
      :red-line-ratio="isNeimeng ? 0.7 : 1"
    />
    <OilScale
      v-else-if="props.categoryType === 3"
      :max="scaleData[3]?.reserve"
      :current="scaleData[3]?.stock"
      :red-line-ratio="isNeimeng ? 0.7 : 1"
    />

    <HCard title="库存品种结构（单位：吨）">
      <StockVarietyStructure :category-type="categoryType"></StockVarietyStructure>
    </HCard>

    <HCard title="库存等级分布">
      <StockLeverStructure :category-type="categoryType"></StockLeverStructure>
    </HCard>

    <InventoryPropertyDistribution :category-type="categoryType"></InventoryPropertyDistribution>

    <StockOriginDistribution :category-type="categoryType"></StockOriginDistribution>
  </div>
</template>

<script setup>
import { computed } from 'vue';
import { useStore } from 'vuex';
import { keyBy } from 'lodash-es';
// import UnitName from '@/views/common/UnitName';
import StockVarietyStructure from '@/views/StockOverview/common/StockVarietyStructure';
import StockLeverStructure from '@/views/StockOverview/common/StockLeverStructure';
import GranaryScale from '@/views/StockOverview/common/GranaryScale';
import ProductGrainScale from '@/views/StockOverview/common/ProductGrainScale';
import OilScale from '@/views/StockOverview/common/OilScale';
import InventoryPropertyDistribution from '@/views/StockOverview/common/InventoryPropertyDistribution';
import StockOriginDistribution from '@/views/StockOverview/common/StockOriginDistribution';
import ReserveStock from '@/views/StockOverview/common/ReserveStock';

const props = defineProps({
  categoryType: Number, // 1 原粮，2 成品粮， 3 食用油
});

const store = useStore();

const scaleData = computed(() => {
  const { reserveData } = store.state['stock-overview'];
  return keyBy(reserveData, 'category');
});
const isNeimeng = computed(() => {
  return process.env.VUE_APP_MODE === 'neimenggu';
});
</script>

<style scoped lang="scss">
.category-stock-statistics {
  .h-card {
    margin-bottom: 16px;
  }
  .small-card {
    ::v-deep(.h-card-body) {
      height: 92px;
    }
  }
}
</style>
