<template>
  <div class="settlement-manage">
    <Search v-model="search" placeholder="请输入调度号" show-action @search="onSearch">
      <template #action>
        <div class="scan-action" @click="onScan">
          <SvgIcon name="scan" />
        </div>
      </template>
    </Search>
    <div class="total-contain">
      <!-- <SvgIcon class="icon-cls" name="notice" />  -->
      当前待结算任务{{ pagination.total }}条
      <SvgIcon name="refresh" class="icon-cls" style="margin-left: 10px" @click="onSearch" />
    </div>
    <PullRefresh @refresh="onRefresh" v-model="refreshLoading">
      <List
        ref="listRef"
        v-model:loading="isLoading"
        :finished="finished"
        finished-text="没有更多了"
        @load="getList"
      >
        <HCard class="detail-card" v-for="item in list" :key="item">
          <div class="card-head">
            <!-- <img :src="IconHouse" /> -->
            <div class="schedule-num">{{ item.schedulingNo?.split('_')[0] }}</div>
            <!-- 粮油品种名称 -->
            <Tag>{{ item.foodCategoryName }}</Tag>
            <!-- 结算类型 -->
            <Tag :type="item.buzTyper">{{ item.buzTyperName }}</Tag>
          </div>
          <div class="card-content">
            <div class="row">
              <span class="label">到库车船号：</span>
              <span class="value">{{ item.parentLicensePlateNumber }}</span>
            </div>
            <div class="row">
              <span class="label">作业车牌：</span>
              <span class="value">{{ item.transportVehicleNo }}</span>
            </div>
            <div class="row">
              <span class="label">值仓仓房：</span>
              <span class="value">
                {{
                  hasCargoSpace && item.cargoSpaceName
                    ? `${item.storeHouseName}_${item.cargoSpaceName}`
                    : item.storeHouseName
                }}
              </span>
            </div>
            <div class="row">
              <span class="label">客户名称：</span>
              <span class="value">{{ item.customerName }}</span>
            </div>
            <div class="row">
              <span class="label">过磅重量：</span>
              <span class="value">
                <HFixedNumber :ratio="1000" :fractionDigits="0">
                  {{ item.weighingQuantity }}
                </HFixedNumber>
                公斤
              </span>
            </div>
            <div class="row">
              <span class="label">结算数量：</span>
              <span class="value">
                <HFixedNumber :ratio="1000" :fractionDigits="0">
                  {{ item.count }}
                </HFixedNumber>
                公斤
              </span>
            </div>
          </div>
          <Divider :hairline="false" />
          <div class="card-action">
            <Button
              type="primary"
              class="next-button"
              style="height: 28px; width: 70px"
              @click="goToDetail(item.schedulingNo)"
              >结算</Button
            >
          </div>
        </HCard>
      </List>
    </PullRefresh>
  </div>
</template>

<script setup>
import { Search, List, PullRefresh, Divider, Button } from 'vant';
import { ref, reactive, watch, computed } from 'vue';
import { useRouter, useRoute } from 'vue-router';
import SvgIcon from '@/components/SvgIcon/SvgIcon';
import { HCard } from '@/components';
// import IconHouse from '@/assets/icon-house.png';
import { getSettlementList } from '@/api/in-out-manage';
import { HFixedNumber } from '@/components';
import Tag from '@/views/PurchasesOverview/common/Tag1';
import { checkPermission } from '@/utils/permission';

const router = useRouter();
const route = useRoute();
const search = ref('');
const listRef = ref();

const list = ref([]);
const pagination = reactive({
  page: 0,
  size: 10,
  total: 0,
});

const isLoading = ref(false);
const finished = ref(false);
const refreshLoading = ref(false);
const hasCargoSpace = computed(() => checkPermission(['cargo_space']));

const onScan = () => {
  window.cordova?.plugins.barcodeScanner.scan(
    (result) => {
      if (result.text) {
        goToDetail(result.text);
      }
    },
    (error) => {
      alert('扫码失败 ' + error);
    },
    {
      prompt: '请将二维码放在扫码框中',
      resultDisplayDuration: 300,
    },
  );
};
const onRefresh = () => {
  if (refreshLoading.value) {
    pagination.page = 0;
    pagination.total = 0;
    list.value = [];
    refreshLoading.value = false;
  }
  finished.value = false;
  isLoading.value = true;
  getList();
};
const onSearch = () => {
  finished.value = false;
  list.value = [];
  pagination.page = 0;
  pagination.total = 0;
  listRef.value?.check();
};
const getList = async () => {
  const { accountsResPageVO } = await getSettlementList({
    schedulingNo: search.value ? search.value : undefined,
    page: pagination.page + 1,
    size: pagination.size,
  });
  const { items, total } = accountsResPageVO;
  list.value.push(...items);
  pagination.total = total;
  // 加载状态结束
  isLoading.value = false;

  // 数据全部加载完成
  if (list.value.length >= pagination.total) {
    finished.value = true;
  }
};

const goToDetail = (schedulingNo) => {
  router.push({
    name: 'SettlementManageDetail',
    params: {
      schedulingNo: schedulingNo,
    },
  });
};
watch(
  () => route.name,
  () => {
    search.value = '';
    onSearch();
  },
);
</script>

<style lang="scss" scoped>
.scan-action {
  font-size: 25px;
  height: 34px;
  display: flex;
  align-items: center;
  padding: 0 10px;
}
.total-contain {
  display: flex;
  align-items: center;
  padding: 9px 14px;
}
.detail-card {
  padding: 16px;
  margin-bottom: 10px;
  ::v-deep {
    .van-divider {
      margin: 10px 0;
    }
  }
}
.card-head {
  display: flex;
  align-items: center;
  font-size: 18px;
  font-weight: bold;
  color: #1f3359;
  margin-bottom: 10px;
  .tag {
    margin-left: 10px;
  }
  .next {
    color: rgba(109, 116, 141, 0.6);
  }
}
.card-content {
  margin-top: 8px;
  line-height: 30px;
  .row {
    display: flex;
    .label {
      display: inline-block;
      width: 160px;
      font-size: 14px;
      font-weight: 400;
      color: #6d748d;
      flex-basis: 6em;
    }
    .value {
      font-size: 14px;
      font-weight: 400;
      color: #0f0f0f;
      flex-grow: 1;
    }
    .carNum {
      padding: 3px 6px;
      background: #f4f4f4;
      border-radius: 2px;
      border: 1px solid #ebedf0;
      margin-right: 5px;
    }
  }
}
.card-action {
  text-align: right;

  // height: 30px;
}
.next-contain {
  width: 80px;
  text-align: right;
}
.icon-cls {
  vertical-align: text-top;
}
</style>
