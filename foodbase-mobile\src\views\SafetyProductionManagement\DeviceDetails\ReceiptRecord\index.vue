<template>
  <HPicker
    class="w-1/3 mt-3 mb-3 ml-4"
    :options="[
      { text: '全部', value: '' },
      ...Object.keys(status).map((i) => ({ text: status[i], value: i })),
    ]"
    v-model:value="type"
    placeholder="领用状态"
  ></HPicker>

  <div class="mt-4" v-for="item in list.items" :key="item.instrumentSn">
    <van-cell-group inset>
      <van-cell title="领用库点：" :value="item.useStoreName" />
      <van-cell title="领用仓房：" :value="item.useStoreHouseName" />
      <van-cell title="领用人：" :value="item.useName" />
      <van-cell title="领用状态：" :value="status[item.computeStatus]" />
      <van-cell
        title="归还时间："
        :value="
          String(item.returnTime).length > 9 ? dayjs(item.returnTime).format('YYYY-MM-DD') : '-'
        "
      />
    </van-cell-group>
  </div>

  <van-empty v-if="!(list.items && list.items.length)" description="暂无数据" />
</template>

<script setup>
import { ref, onMounted, watch } from 'vue';
import { reserverPurchase } from '@/utils/request';
import { useRoute } from 'vue-router';
import dayjs from 'dayjs';

const route = useRoute();
const list = ref([]);
const type = ref('');

const status = {
  1: '领用中',
  2: '领用审批不通过',
  3: '使用中',
  4: '归还中',
  5: '归还审批不通过',
  6: '已完成',
};

watch(
  () => type.value,
  async (val) => {
    getList(val);
  },
);

onMounted(async () => {
  getList();
});

const getList = async (val) => {
  list.value = await reserverPurchase().get(
    `/api/hrps/instrument/use/list?page=1&size=999&instrumentId=${route.query.id}&status=${
      val ?? ''
    }`,
  );
};
</script>

<style lang="scss" scoped></style>
