<template>
  <div class="unit-picker">
    <div class="unit-picker-trigger" @click="showPicker = true">
      <span class="unit-picker-label" :class="{ 'unit-picker-placeholder': placeholder }">
        {{ currentLabel }}
      </span>
      <span class="unit-picker-icons">
        <Icon name="close" @click="handleClear" />
        <Icon name="arrow-down" />
      </span>
    </div>
    <Popup v-model:show="showPicker" round position="bottom" teleport="body">
      <Search v-model="unitSearch" placeholder="请输入单位名称"></Search>
      <Picker
        :title="props.title || '请选择单位'"
        :columns="unitFilter(unitSearch)"
        :columns-field-names="fieldNames"
        @cancel="onCancel"
        @confirm="onConfirm"
      />
    </Popup>
  </div>
</template>

<script setup>
import { Popup, Icon, Picker, Search } from 'vant';
import { ref, onMounted, computed, watch } from 'vue';
import { getUnitTreeByUserId, getCompany, getCompanyByUser } from '@/api/order-public';

const emits = defineEmits(['confirm']);

const props = defineProps({
  title: String,
  value: [String, Number],
  isCompanyUser: Boolean,
  userId: [String, Number],
  placeholder: String,
  isGetCompanyByUser: Boolean,
  areaCode: String,
});

const fieldNames = {
  text: 'name',
  value: 'id',
  children: 'child',
};

const showPicker = ref(false);
const unitList = ref([]);
const unitSearch = ref('');

onMounted(() => {
  getList();
});

watch(
  () => props.areaCode,
  () => {
    getList();
  },
);

const getList = async () => {
  const { isCompanyUser, userId, isGetCompanyByUser, areaCode } = props;
  let data;
  if (isCompanyUser) {
    data = await getCompany();
  } else if (isGetCompanyByUser) {
    data = await getCompanyByUser(areaCode && { areaCode });
  } else {
    data = await getUnitTreeByUserId({ userId });
    data = dealUnit(data);
  }
  unitList.value = data;
};

const unitFilter = (value) => {
  if (!value) {
    return unitList.value;
  }
  const filterUnitList = unitList.value.filter((item) => item.name.indexOf(value) > -1);
  return filterUnitList;
};

const dealUnit = (list) => {
  const newList = [];
  function childrenEach(list) {
    for (let i = 0; i < list.length; i++) {
      if (list[i].level === '4') {
        newList.push(list[i]);
      }
      if (list[i].children && list[i].children.length) {
        childrenEach(list[i].children);
      }
    }
    return newList;
  }
  return childrenEach(list);
};

const currentLabel = computed(() => {
  const { value, placeholder = '请选择' } = props;
  const currentNode = unitList.value.find((item) => item.id === value);
  return currentNode?.name || placeholder;
});

const placeholder = computed(() => {
  const node = unitList.value.find((item) => item.id === props.value);
  return !node;
});

const onCancel = () => {
  showPicker.value = false;
};

const onConfirm = (value) => {
  emits('confirm', value?.id);
  showPicker.value = false;
};
const handleClear = (e) => {
  e.stopPropagation();
  emits('confirm', undefined);
};
</script>

<style lang="scss">
.unit-picker {
  background-color: #ffffff;
  line-height: 32px;
  min-width: 140px;

  &-trigger {
    border: 1px solid var(--van-gray-5);
    padding: 0 10px;
    border-radius: 4px;
    display: flex;
    align-items: center;
    justify-content: space-between;
  }

  &-label {
    margin-right: 4px;
    overflow: hidden;
    white-space: nowrap;
  }

  &-placeholder {
    color: var(--van-gray-5);
  }
  &-icons {
    display: flex;
  }
}
</style>
