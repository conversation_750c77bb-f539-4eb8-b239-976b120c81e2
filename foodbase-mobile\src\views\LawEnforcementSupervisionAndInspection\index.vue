<template>
  <div class="px-2 py-2">
    <HPicker class="w-1/3 mb-3" :options="statusList" v-model:value="status"></HPicker>
    <div class="py-3 mb-3 bg-white rounded-md" v-for="item in listData?.items" :key="item.id">
      <h1 class="flex justify-between px-3 mb-4">
        <p class="font-[20px] font-bold text-blue-400">{{ item.planName }}</p>
        <p>{{ Number(item.status) > 1 ? '已完成' : '未完成' }}</p>
      </h1>
      <van-cell-group class="cells">
        <van-cell class="" title="任务编号：" :value="item.sn" />
        <!-- <van-cell class="" title="计划类型：" value="item." /> -->
        <van-cell class="" title="检查对象：" :value="item.targetName" />
        <van-cell
          class=""
          title="检查完成期限："
          :value="dayjs(item.endTime).format('YYYY-MM-DD')"
        />
      </van-cell-group>

      <div class="px-4 mt-4 overflow-hidden text-right" v-if="item.status == 1">
        <van-button
          @click="(showPopup = true), (detailData = item)"
          class=""
          type="primary"
          size="small"
          >详情</van-button
        >
      </div>
    </div>
    <EmptyHolder v-if="listData?.items?.length === 0" />
  </div>

  <van-popup
    v-model:show="showPopup"
    closeable
    position="bottom"
    class="!bg-gray-50 pop-content h-screen overflow-hidden"
  >
    <div class="h-[50px]">
      <h2 class="p-4 font-bold">{{ detailData.planName }}</h2>
    </div>
    <div class="pb-10 overflow-y-auto" style="height: calc(100vh - 50px)">
      <h2 class="px-4 py-2 text-sm text-gray-400">任务基础信息</h2>
      <van-cell-group inset>
        <van-cell title="任务编号：" :value="detailData.sn" />
        <van-cell title="计划名称：" :value="detailData.planName" />
        <!-- <van-cell title="计划类型：" value="内容" /> -->
        <van-cell title="检查对象：" :value="detailData.targetName" />
        <van-cell
          title="检查完成截止期限："
          :value="dayjs(detailData.endTime).format('YYYY-MM-DD')"
        />
        <van-cell
          title="人员分配："
          :value="(detailData.dispatchMemberNameList || [])?.join('、')"
        />
      </van-cell-group>

      <div v-for="item in detailData.checkResultBOList" :key="item.id">
        <h2 class="px-4 py-2 mt-3 text-sm text-gray-400">检查内容</h2>
        <van-cell-group inset>
          <van-cell title="检查内容：" :value="item?.checkItemBO?.content" />
          <!-- <van-cell
            title="检查事项："
            :value="item?.checkItemBO?.name"
            @click="showCheckInfo = true"
          /> -->
          <van-cell title="检查人：" :value="(item.dispatchMemberNameList || [])?.join('、')" />
          <van-field
            v-model="item.checkResult"
            rows="3"
            autosize
            label="检查结果："
            type="textarea"
            placeholder="请输入检查结果"
          />
          <van-cell center title="是否通过检查：">
            <template #right-icon>
              <van-radio-group v-model="item.pass" direction="horizontal">
                <van-radio :name="1">通过</van-radio>
                <van-radio :name="0">不通过</van-radio>
              </van-radio-group>
            </template>
          </van-cell>
        </van-cell-group>
      </div>
      <HCard class="opinion-card" style="margin-bottom: 70px">
        <div>检查证明材料<span class="min-size">支持最多3张</span></div>
        <Uploader
          :model-value="fileList"
          multiple
          :before-read="beforeRead"
          :max-count="3"
          class="upload-cls"
          @click-upload="onClickUpload"
          @delete="onDeleteFile"
        />
      </HCard>
      <ActionSheet
        v-model:show="showActionSheet"
        cancel-text="取消"
        description="请选择上传方式"
        :actions="actions"
        @select="onUploadActionSelect"
        close-on-click-action
      />
      <van-sticky :offset-bottom="10" class="mt-5" position="bottom">
        <van-row gutter="20" class="px-4">
          <van-col span="12"
            ><van-button
              v-p="[`app-law-enforcement-supervision-and-inspection:save`]"
              type="primary"
              plain
              block
              @click="save"
              >保存</van-button
            ></van-col
          >
          <van-col span="12"
            ><van-button
              v-p="[`app-law-enforcement-supervision-and-inspection:finish`]"
              type="primary"
              block
              @click="saveAndSubmit"
              >完成检查</van-button
            ></van-col
          >
        </van-row>
      </van-sticky>
    </div>
  </van-popup>

  <van-popup
    v-model:show="showCheckInfo"
    closeable
    position="bottom"
    class="!bg-gray-50 pop-content h-5/6 overflow-hidden"
  >
    <div class="h-[50px]">
      <h2 class="p-4 font-bold">检查事项</h2>
    </div>
    <div class="overflow-y-auto" style="height: calc(100vh - 50px)">
      <h2 class="px-4 py-2 text-sm text-gray-400">事项信息</h2>
      <van-cell-group inset>
        <van-cell title="检查事项：" value="内容" />
        <van-cell title="检查内容：" value="内容" />
      </van-cell-group>

      <h2 class="px-4 py-2 mt-3 text-sm text-gray-400">合格标准</h2>
      <van-cell-group inset>
        <van-cell title="" label="描述信息" />
      </van-cell-group>

      <h2 class="px-4 py-2 mt-3 text-sm text-gray-400">检查依据</h2>
      <van-cell-group inset>
        <van-cell title="" label="描述信息" />
      </van-cell-group>
    </div>
  </van-popup>
</template>

<script setup>
import { HPicker, HCard } from '@/components';
import EmptyHolder from '@/views/common/EmptyHolder';
import { ref, onMounted, watch, reactive } from 'vue';
import { getSupervisedcheckList, postSupervisedcheck, submitSupervisedcheck } from './apis';
import { Toast, ActionSheet, Uploader } from 'vant';
const actions = [{ name: '拍照' }, { name: '相册选择' }];
import dayjs from 'dayjs';
import { getReserverToken } from '@/utils/auth';
const statusList = ref([
  { text: '日常检查', value: 1 },
  { text: '定期巡查', value: 2 },
  { text: '专项检查', value: 3 },
]);
const detailData = ref({});
const listData = ref([]);
const fileList = ref([]);
const showActionSheet = ref(false);

// const taskStatusList = { 1: '执行中', 2: '已完成', 3: '存在问题待整改', 4: '已完成整改' };
const getList = async () => {
  listData.value = await getSupervisedcheckList(status.value);
};
const status = ref(2);

onMounted(async () => {
  getList();
});
const showPopup = ref(false);
const showCheckInfo = ref(false);

const save = async () => {
  await postSupervisedcheck({
    checkResultBOList: detailData.value.checkResultBOList,
    id: detailData.value.id,
    attachmentList: fileList.value.map((i) => ({
      fileUrl: i.url,
      fileName: i.name,
    })),
  });
  Toast('保存成功');
  await getList();
};

const onUploadActionSelect = ({ name }) => {
  const Camera = window.Camera;
  let sourceType = Camera.PictureSourceType.PHOTOLIBRARY;
  if (name === '拍照') {
    sourceType = Camera.PictureSourceType.CAMERA;
  } else if (name === '相册选择') {
    sourceType = Camera.PictureSourceType.SAVEDPHOTOALBUM;
  }
  navigator.camera.getPicture(
    (imageUri) => {
      console.log(imageUri, 'imageUri');
      // 添加图片到图片列表
      const fileItem = reactive({
        url: imageUri,
        isImage: true,
        status: 'done',
        deletable: true,
      });
      fileList.value.push(fileItem);
      const name = imageUri.substr(imageUri.lastIndexOf('/') + 1);
      // 上传参数
      const options = new window.FileUploadOptions();
      options.fileKey = 'file';
      options.fileName = String(name).split('?')[0];
      options.headers = {
        Authorization: getReserverToken(),
      };
      console.log(options);
      const params = {};
      params.coder = 'sgcb/storehouse_document';
      options.params = params;

      // 上传地址
      const reserverBaseUrl = () =>
        JSON.parse(process.env.VUE_APP_RESERVER_API_BASE_URL)[window.__isProd] || location.origin;
      const uploadUri = new URL(
        '/purchase_supervision/api/fast_dfs/upload',
        reserverBaseUrl(),
      ).toString();
      // 上传文件
      const fileTransfer = new window.FileTransfer();
      fileTransfer.upload(
        imageUri,
        uploadUri,
        (res) => {
          console.log(res, 'res,阿是大师大师的');
          // 上传成功
          const resp = res.response;
          if (resp) {
            const respJson = JSON.parse(resp);
            const { data } = respJson;
            console.log(data, 'data-上传文件');
            fileItem.name = data.fileName;
            fileItem.url = data.filePath;
            fileItem.status = 'done';
            return false;
          }
        },
        (error) => {
          // 上传失败
          fileItem.value.status = 'failed';
          Toast('上传失败');
          console.error(error);
        },
        options,
      );
    },
    (err) => {
      Toast('选择图片失败');
      console.error(err);
    },
    {
      quality: 85,
      destinationType: Camera.DestinationType.DATA_URI,
      sourceType: sourceType,
    },
  );
};
const saveAndSubmit = async () => {
  await save();
  await submitSupervisedcheck(detailData.value.id);
  Toast('已处理');
  showPopup.value = false;
  getList();
};

const beforeRead = (file) => {
  if (
    file.type.includes('jpg') ||
    file.type.includes('jpeg') ||
    file.type.includes('png') ||
    file.type.includes('gif')
  ) {
    return true;
  } else {
    Toast('请上传 jpg, png, gif 格式图片');
    return false;
  }
};
const onDeleteFile = (file, { index }) => {
  fileList.value.splice(index, 1);
};
const onClickUpload = (e) => {
  e.preventDefault();
  if (navigator.camera) {
    showActionSheet.value = true;
  } else {
    Toast('不支持选择照片');
  }
};
watch(
  () => status.value,
  () => {
    getList();
  },
);
watch(
  () => showPopup.value,
  () => {
    if (showPopup.value) {
      fileList.value = detailData.value?.attachmentList
        ? detailData.value?.attachmentList.map((i) => ({ url: i.fileUrl, name: i.fileName }))
        : [];
    }
  },
);
</script>

<style lang="scss" scoped>
// ::v-deep(.van-cell) {
//   // padding: 6px 16px;
// }
.opinion-card {
  padding: 16px;
  margin-bottom: 10px;
}
.min-size {
  font-size: 12px;
  color: #6d748d;
  vertical-align: text-top;
  margin-left: 12px;
}
</style>
