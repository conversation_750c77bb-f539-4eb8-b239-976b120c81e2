<template>
  <div class="risk-list">
    <div class="query-form">
      <!--      <button @click="showDetail({ id: 9999 })">按钮</button>-->
      <Row>
        <!--        <Col span="8">-->
        <!--          <AreaPicker v-model:value="queryForm.code" all-area-select placeholder="地市" />-->
        <!--        </Col>-->
        <Col span="24">
          <Search v-model="date" placeholder="选择日期区间" readonly @click="datePicker = true" />
        </Col>
        <Col span="8">
          <van-dropdown-menu>
            <van-dropdown-item
              @change="getList"
              v-model="queryForm.storeCode"
              :options="storeOptions"
            />
          </van-dropdown-menu>
        </Col>
        <Col span="8">
          <van-dropdown-menu>
            <van-dropdown-item
              @change="getList"
              v-model="queryForm.type"
              :options="warnTypeOptions"
            />
          </van-dropdown-menu>
        </Col>
        <Col span="8">
          <van-dropdown-menu>
            <van-dropdown-item
              @change="getList"
              v-model="queryForm.isDisposed"
              :options="statusOptions"
            />
          </van-dropdown-menu>
        </Col>
        <Col span="16">
          <div class="total">
            共有 <span>{{ pagination.total }}</span> 条数据
          </div>
        </Col>
      </Row>
    </div>
    <Calendar
      allow-same-day
      v-model:show="datePicker"
      type="range"
      :min-date="minDate"
      :default-date="[new Date(queryForm.startDate), new Date(queryForm.endDate)]"
      @confirm="onConfirm"
    />
    <PullRefresh v-model="refreshing" @refresh="onRefresh">
      <List
        ref="listRef"
        v-model:loading="loading"
        :finished="finished"
        :finished-text="list.length === 0 ? '' : '没有更多了'"
        @load="onLoad"
      >
        <EmptyHolder v-if="list.length === 0 && finished" />
        <div class="list" v-else>
          <HCard v-for="item in list" :key="item" @click="showDetail(item)">
            <template #header-title>
              {{ item.storeName }}
            </template>
            <template #header-extra>
              <van-icon name="arrow" />
            </template>
            <div class="detail">
              <div class="item">
                <div class="name">仓房：</div>
                <div class="value house-name">{{ item?.storeHouseName }}</div>
              </div>
              <div class="item">
                <div class="name">异常时间：</div>
                <div class="value">
                  <HDateTime :value="item.alarmTime" />
                </div>
              </div>
              <div class="item">
                <div class="name">风险类别：</div>
                <div class="value">{{ item.warningType }}</div>
              </div>
              <div class="item">
                <div class="name">异常详情：</div>
                <div class="value">{{ item.warningDetail }}</div>
              </div>
              <div class="detail-handle-status">
                <div v-if="item.isDisposed === '0'" class="not-handle"></div>
                <div v-else-if="item.isDisposed === '1'" class="already-handle"></div>
              </div>
            </div>
          </HCard>
        </div>
      </List>
    </PullRefresh>
  </div>
</template>

<script>
export default {
  name: 'RiskList',
};
</script>

<script setup>
import { onActivated, onMounted, reactive, ref, watch } from 'vue';
import { Row, Col, List, PullRefresh, Calendar, Search } from 'vant';
import { HCard } from '@/components';
// eslint-disable-next-line no-unused-vars
import AreaPicker from '@/views/common/AreaPicker';
import EmptyHolder from '@/views/common/EmptyHolder';
// import ProcessTag from '@/views/SupervisionOverview/common/ProcessTag';
import { useRouter } from 'vue-router';
// import { getRiskList } from '@/api/supervision';
import { useStore } from 'vuex';
import HDateTime from '@/components/HDateTime/HDateTime';
import { getStoreByCityCompany } from '@/api/store';
import { getRiskwarning } from './api';
import dayjs from 'dayjs';

const router = useRouter();
const store = useStore();
const datePicker = ref(false);
const warnTypeOptions = ref([
  // { value: null, text: '全部风险' },
  { value: 'person', text: '人员入仓' },
  { value: 'helmet', text: '安全帽提示' },
  { value: 'falldown', text: '人员跌倒' },
  { value: 'smoking', text: '吸烟提示' },
  { value: 'io', text: '出入库异常' },
  { value: 'temp', text: '粮温异常' },
  { value: 'door', text: '仓门开启' },
  { value: 'water', text: '水位异常' },
  { value: 'fire', text: '明火异常' },
  { value: 'tran', text: '粮面异动' },
]);
const statusOptions = ref([
  { value: null, text: '所有状态' },
  { value: 0, text: '未处理' },
  { value: 1, text: '已处理' },
]);
const storeOptions = ref([]);
// const isShowList = computed(() => {
//   return process.env.VUE_APP_MODE === 'zhejiang';
// });
// const beginDate = ref(dayjs().subtract(29, 'day').format('YYYY-MM-DD'));
// const endDate = ref(dayjs().format('YYYY-MM-DD'));
const minDate = new Date(dayjs().subtract(10, 'year').format('YYYY-MM-DD'));
const userInfo = store.getters['user/userInfo'];
const queryForm = reactive({
  username: userInfo.username,
  type: 'person',
  startDate: dayjs().subtract(29, 'day').format('YYYY-MM-DD'), //最早日期
  endDate: dayjs().format('YYYY-MM-DD'), //最晚日期
  isDisposed: null,
  storeCode: null, //库点
  // warnType: null,
  // storeId: null,
  // status: null,
});
const date = ref(`${queryForm.startDate}-${queryForm.endDate}`);
const listRef = ref();
const list = ref([]);
const loading = ref(false);
const finished = ref(false);
const refreshing = ref(false);

const pagination = reactive({
  page: 0,
  size: 10,
  total: 0,
});

const onLoad = async () => {
  if (refreshing.value) {
    pagination.page = 0;
    pagination.total = 0;
    list.value = [];
    refreshing.value = false;
  }

  // const { items, page, total } = await getRiskList({
  //   page: pagination.page + 1,
  //   ...queryForm,
  // });
  // list.value = [
  // ];
  // list.value.push(...items);
  // pagination.page = page;
  // pagination.page = 1;
  // pagination.total = total;
  // pagination.total = 2;
  //
  // if (list.value.length >= total) {
  //   finished.value = true;
  // }
  loading.value = false;
};
const getList = async () => {
  await getRiskwarning({ ...queryForm }).then((res) => {
    list.value = res.record;
  });
  pagination.total = list.value.length;
};
watch(
  queryForm,
  () => {
    pagination.page = 0;
    pagination.total = 0;
    list.value = [];
    finished.value = false;
    listRef.value?.check();
  },
  { deep: true },
);

const onRefresh = () => {
  finished.value = false;
  loading.value = true;
  getList();
  onLoad();
};

const showDetail = (item) => {
  router.push({ name: 'RiskDetail', query: { data: JSON.stringify(item) } });
};

const loadStores = async () => {
  storeOptions.value = [];
  // const code = queryForm.code;
  const code = store.getters['user/userAreaCode'];
  const { dept } = store.state.user.info;
  const list = (await getStoreByCityCompany(code, dept.id, dept.level)) || [];
  storeOptions.value = list.map((it, index) => {
    return {
      value: it.id ? it.id : index,
      text: it.name,
    };
  });
  storeOptions.value.unshift({ value: null, text: '所有库点' });
};

watch(
  () => queryForm.code,
  () => {
    queryForm.storeId = null;
    loadStores();
  },
);

onMounted(() => {
  loadStores();
});

onActivated(() => {
  refreshing.value = true;
  onRefresh();
});
const onConfirm = (values) => {
  const [start, end] = values;
  queryForm.startDate = dayjs(start).format('YYYY-MM-DD');
  queryForm.endDate = dayjs(end).format('YYYY-MM-DD');
  date.value = `${queryForm.startDate}-${queryForm.endDate}`;
  datePicker.value = false;
  // console.log( beginDate.value,  endDate.value );
  // console.log(queryForm);
  getList();
};
</script>

<style lang="scss">
// .query-form {
// }
//padding: 8px 16px;
// .van-col {
//   .van-dropdown-menu {
//     .van-dropdown-menu__bar {
//       box-shadow: none;
//     }
//   }
// }
</style>
<style scoped lang="scss">
.query-form {
  // background-color: #fff;
  ::v-deep(.van-search__content) {
    padding: 0px;
    // background-color: #fff;
  }

  ::v-deep(.van-field) {
    padding: 4px;
    font-size: 14px !important;
  }

  //padding: 8px 16px;
  .van-col {
    ::v-deep(.van-dropdown-menu) {
      ::v-deep(.van-dropdown-menu__bar) {
        box-shadow: none;
      }
    }

    //margin: 8px 0;
  }

  .total {
    line-height: 34px;
    margin-left: 20px;
    text-align: left;

    span {
      color: #1973f1;
    }
  }
}

.h-card {
  margin-bottom: 15px;

  ::v-deep(.h-card-header) {
    border-bottom: none;
    line-height: 40px;

    .h-card-header-title {
      font-weight: 600;
      font-size: 16px;
    }
  }
}

.detail {
  font-size: 16px;
  padding: 0 20px;
  color: var(--van-gray-7);
  position: relative;

  .item {
    display: flex;
    line-height: 38px;

    .name {
      white-space: nowrap;
    }

    .house-name {
      margin-left: 32px;
    }

    .value {
      color: #232323;
    }
  }

  .detail-handle-status {
    position: absolute;
    right: 20px;
    top: 32px;

    div {
      width: 52px;
      height: 52px;
      background-size: contain;
    }

    .not-handle {
      background-image: url(@/assets/notHandle.png);
    }

    .already-handle {
      background-image: url(@/assets/alreadyHandle.png);
    }
  }
}
</style>
