import store from '@/store';

/**
 * @param {Array|string} requiredPermissions
 * @returns {Boolean}
 */
export function checkPermission(requiredPermissions) {
  const userPermissions = store.state.user.roles;
  if (userPermissions.has('admin')) {
    return true;
  }
  if (typeof requiredPermissions === 'string') {
    requiredPermissions = [requiredPermissions];
  }
  if (Array.isArray(requiredPermissions)) {
    const hasPermission = requiredPermissions.some((permission) => userPermissions.has(permission));

    return hasPermission;
  } else {
    console.error(`need roles! array or string`);
    return false;
  }
}

const PermissionDirective = {
  mounted(el, binding) {
    const { value, arg } = binding;
    const hasPermission = checkPermission(value);

    if ((!hasPermission && arg !== 'not') || (hasPermission && arg === 'not')) {
      el.parentNode && el.parentNode.removeChild(el);
    }
  },
};

export default {
  install(app) {
    app.directive('p', PermissionDirective);
  },
};
