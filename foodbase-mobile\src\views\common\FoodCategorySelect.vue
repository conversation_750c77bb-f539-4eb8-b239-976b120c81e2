<template>
  <div class="category-picker">
    <div class="category-picker-trigger" @click="showPicker = true">
      <span class="category-picker-label">
        {{ currentLabel }}
      </span>

      <span class="category-picker-icons">
        <Icon name="close" @click="handleClear" />
        <Icon name="arrow-down" />
      </span>
    </div>
    <Popup v-model:show="showPicker" round position="bottom" teleport="body">
      <Picker
        ref="foodRef"
        :title="props.title || '请选择品种'"
        :columns="categoryTree"
        :columns-field-names="fieldNames"
        @cancel="onCancel"
        @confirm="onConfirm"
      />
    </Popup>
  </div>
</template>

<script setup>
import { Popup, Icon, Picker } from 'vant';
import { ref, onMounted, watch } from 'vue';
// import { getNode } from '@/utils/collection';
import { getFoodCategoryByPid } from '@/api/food-category';
import { keyBy, forEach } from 'lodash-es';

const emits = defineEmits(['confirm']);

const props = defineProps({
  title: String,
  foodCategory: [Array, Number],
});
const showPicker = ref(false);
const categoryTree = ref([]);
const fieldNames = {
  text: 'name',
  value: 'id',
  children: 'children',
};

let currentLabel = ref('粮油品种');
const onCancel = () => {
  showPicker.value = false;
};
const foodRef = ref();
const onConfirm = (value) => {
  const data = value[value.length - 1].id ? value[value.length - 1] : value[value.length - 2];
  currentLabel.value = data.name;
  emits('confirm', data.id);
  showPicker.value = false;
};
const handleClear = (e) => {
  e.stopPropagation();
  currentLabel.value = '粮油品种';
  emits('confirm', undefined);
};
const getFoodTree = async () => {
  const data = await getFoodCategoryByPid({
    categoryIds: props.foodCategory,
  });
  categoryTree.value = generateFoodCategoryTree(data);
};
const generateFoodCategoryTree = (list) => {
  const neatList = list.map((it) => {
    return {
      id: it.id,
      pid: it.pid,
      name: it.name,
      key: it.id,
      value: it.id,
      label: it.name,
      title: it.name,
      categoryType: it.categoryType,
    };
  });
  const foodMap = keyBy(neatList, 'id');
  const tree = [];
  forEach(foodMap, (food) => {
    const { pid } = food;
    delete food.pid;
    food.children = [
      {
        name: '请选择',
      },
    ];
    if (pid !== 0) {
      const parent = foodMap[pid];
      if (parent) {
        if (!parent.children) {
          parent.children = [];
        }

        parent.children.push(food);
        return;
      }
    }
    tree.push(food);
  });
  return tree;
};
onMounted(() => {
  getFoodTree();
});
watch(
  () => props.foodCategory,
  (value, preValue) => {
    if (value != preValue) {
      currentLabel.value = '粮油品种';
      emits('confirm', undefined);
      getFoodTree();
    }
  },
);
</script>

<style lang="scss">
.category-picker {
  background-color: #ffffff;
  line-height: 32px;

  &-trigger {
    border: 1px solid var(--van-gray-5);
    padding: 0 10px;
    border-radius: 4px;
    display: flex;
    align-items: center;
    justify-content: space-between;
  }

  &-label {
    margin-right: 4px;
    overflow: hidden;
    white-space: nowrap;
  }
  &-icons {
    display: flex;
  }
}
</style>
