<template>
  <div class="pb-6">
    <van-cell-group
      v-for="item in listData?.items"
      :key="item.id"
      inset
      class="!mt-4"
      @click="(showPopup = true), (detailData = item)"
    >
      <van-cell title="系统公告">
        <template #title>
          <div><van-icon name="bullhorn-o" color="#1989fa" /> 系统公告</div>
        </template>
      </van-cell>
      <van-cell :title="item.title" :label="dayjs(item.sendTime).format('YYYY-MM-DD HH:mm:ss')">
        <template #title>
          <h2 class="font-bold text-[18px]">{{ item.title }}</h2>
        </template>
      </van-cell>
    </van-cell-group>
  </div>

  <van-popup
    v-model:show="showPopup"
    position="bottom"
    class="!bg-gray-50 pop-content h-screen overflow-hidden"
  >
    <div
      class="h-[50px] page-header-layout"
      style="background: var(--van-nav-bar-background-color)"
    >
      <h2
        class="p-4 text-center text-white van-nav-bar"
        style="font-size: var(--van-nav-bar-title-font-size)"
      >
        <van-icon name="arrow-left" class="float-left text-white" @click="showPopup = false" />
        公告详情
      </h2>
    </div>
    <div class="px-6 pb-10 overflow-y-auto" style="height: calc(100vh - 50px)">
      <h2 class="items-center my-3 mt-5 text-lg font-bold">
        <van-tag
          plain
          type="primary"
          class="flex-shrink-0 mr-1"
          style="vertical-align: text-bottom; line-height: 20px"
          >公告</van-tag
        >
        {{ detailData.title }}
      </h2>
      <p class="text-gray-400">{{ dayjs(detailData.sendTime).format('YYYY-MM-DD HH:mm:ss') }}</p>
      <p class="mt-3" v-html="detailData.content"></p>
    </div>
  </van-popup>
</template>

<script setup>
import { reserveRequest } from '@/utils/request';
import { ref, onMounted } from 'vue';
import dayjs from 'dayjs';

const showPopup = ref(false);
const detailData = ref({});

const listData = ref({});
onMounted(async () => {
  listData.value = await reserveRequest().get('/api/systemMessageConfig/viewList');
});
</script>

<style lang="scss" scoped></style>
