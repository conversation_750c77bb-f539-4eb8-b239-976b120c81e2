<template>
  <span>{{ text }}</span>
</template>

<script>
export default {
  name: 'BizDictName',
  inheritAttrs: false,
  props: {
    dict: String,
    key: String,
    value: [String, Number],
  },
  computed: {
    text() {
      const dictDetails = this.$store.getters['dict/reserveDictOf'](this.dict);
      if (!dictDetails) {
        console.warn(`不存在的业务字典 ${this.dict}`);
        return '-';
      }
      const matched = dictDetails.find((it) => it.value == this.value);
      if (matched) {
        return matched.label;
      }
      console.warn(`未匹配的字典值 ${this.dict} ${this.value}`);
      return '-';
    },
  },
};
</script>
