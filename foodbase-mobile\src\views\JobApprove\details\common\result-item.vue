<template>
  <div class="module">
    <p class="title">{{ props.item.storageUnit }}</p>
    <div class="tags">
      <span class="tag category-tag">{{ props.item.foodCategory }}</span>
      <span class="tag level-tag" :style="{ 'background-color': levelColor[props.item.grade] }">{{
        props.item.grade
      }}</span>
    </div>
    <div class="detail">
      <img v-if="props.item.isAbandoned === 1" src="@/assets/miss-deal.png" />
      <img v-if="props.item.isAbandoned === 0" src="@/assets/all-deal.png" />
      <p><span class="label">标段：</span>{{ formatterNo(props.item.signNo) }}</p>
      <p><span class="label">库点：</span>{{ props.item.store }}</p>
      <p><span class="label">仓廒_货位：</span>{{ props.item.storeHouse }}</p>
      <div v-if="props.item.isAbandoned === 0">
        <p><span class="label">成交数量：</span>{{ props.item.numberOfTransactions }}吨</p>
        <p><span class="label">成交价：</span>{{ props.item.competitivePrice }}元/吨</p>
        <p><span class="label">标的物总价：</span>{{ props.item.totalPrice }}元</p>
        <p><span class="label">成交单位：</span>{{ props.item.winningUnitsName }}</p>
        <p>
          <span class="label">提货日期：</span>{{ props.item.startDate }}至{{ props.item.endDate }}
        </p>
      </div>
      <div v-if="props.item.isAbandoned === 1">
        <!-- <p><span class="label">等级：</span>{{ props.item.competitivePrice }}元/吨</p> -->
        <p>
          <span class="label">拍卖数量：</span
          >{{ Number(props.item.totalNumber || 0).toFixed(3) }}吨
        </p>
        <p><span class="label">是否流标：</span>{{ props.item.isAbandoned === 1 ? '是' : '否' }}</p>
      </div>
    </div>
  </div>
</template>

<script setup>
import { defineProps } from 'vue';
// import { BizDictName } from '@/views/InoutManage/common';
const props = defineProps({
  item: Object,
});
const levelColor = {
  一等: '#14C287',
  二等: '#1973F1',
  三等: '#FFA40D',
  四等: '#8658F0',
  五等: '#E95952',
  等外: '#A56126',
  一级: '#14C287',
  二级: '#1973F1',
  三级: '#FFA40D',
  四级: '#8658F0',
  原油: '#E95952',
};
const formatterNo = (no) => {
  let val = no?.replace(/[0-9]*_/, '');
  return (val.length === 1 ? '0' : '') + val;
};
</script>

<style scoped lang="scss">
.module {
  .title {
    font-size: 16px;
    font-weight: bold;
    color: #232323;
  }
  .tags {
    display: flex;
    margin-top: 12px;
    .tag {
      height: 22px;
      padding: 0 12px;
      line-height: 22px;
      border-radius: 2px;
      font-size: 14px;
      margin-right: 6px;
    }
    .category-tag {
      background: #8797af;
      color: #ffffff;
    }
    .level-tag {
      // background: #14c287;
      color: #ffffff;
    }
    .type-tag {
      background: rgba(25, 115, 241, 0.15);
      color: #1973f1;
    }
  }
  .detail {
    position: relative;
    img {
      position: absolute;
      height: 52px;
      width: 52px;
      right: 14px;
      top: -54px;
    }
    p {
      font-size: 16px;
      color: #232323;
      margin-top: 12px;

      .label {
        width: 120px;
        display: inline-block;
      }
    }
  }
}
</style>
