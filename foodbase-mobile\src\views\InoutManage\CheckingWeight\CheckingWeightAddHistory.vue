<template>
  <div class="checking-weight-other">
    <van-form ref="form" label-width="120px">
      <van-cell-group inset>
        <van-field
          v-model="weightTime"
          label="称重时间"
          is-link
          readonly
          name="timePicker"
          required
          :rules="[{ required: true, message: '请选择称重时间' }]"
          placeholder="请选择"
          @click="showTimePicker = true"
        >
        </van-field>
        <van-popup v-model:show="showTimePicker" position="bottom">
          <van-datetime-picker
            type="datetime"
            v-model="form.weightTime"
            :formatter="timeFormatter"
            :min-date="minDate"
            @confirm="onTimeConfirm"
            @cancel="showTimePicker = false"
            :columns-order="['year', 'month', 'day', 'hour', 'minute', 'second']"
          ></van-datetime-picker>
        </van-popup>
        <van-field
          v-model="form.count"
          label="重量"
          name="signPicker"
          placeholder="请输入"
          required
          :rules="[
            { required: true, message: '请输入重量' },
            {
              validator: validateDigit,
              trigger: 'onBlur',
              message: '请输入正数',
            },
          ]"
          type="number"
          @input="handleInput"
        >
        </van-field>
        <Tabbar>
          <TabbarItem class="cancelTab">
            <Button class="com-btn end" @click="onCancel">取消</Button>
          </TabbarItem>
          <TabbarItem class="saveTab">
            <Button class="com-btn save" type="primary" @click="addWightData">确定</Button>
          </TabbarItem>
        </Tabbar>
      </van-cell-group>
    </van-form>
  </div>
</template>
<script>
import { Form, Field, Button, CellGroup, Tabbar, TabbarItem, DatetimePicker, Popup } from 'vant';
import dayjs from 'dayjs';
import { timeFormatter } from '@/utils/inout-manage';
import { addWight } from '@/api/in-out-manage';

export default {
  components: {
    'van-datetime-picker': DatetimePicker,
    'van-popup': Popup,
    'van-form': Form,
    'van-field': Field,
    'van-cell-group': CellGroup,
    Button,
    Tabbar,
    TabbarItem,
  },
  data() {
    return {
      timeFormatter,
      minDate: new Date(2020, 0, 1),
      form: {},
      record: {},
      weightTime: '',
      showTimePicker: false,
    };
  },
  mounted() {
    this.weighTyper = this.$route.params.weighTyper;
    this.record = JSON.parse(this.$route.params.record);
    // console.log(this.record)
    this.form.weightTime = new Date();
    this.weightTime = dayjs(new Date()).format('YYYY-MM-DD HH:mm:ss');
  },

  methods: {
    validateDigit(value) {
      return value > 0;
    },

    async addWightData() {
      const data = {
        ...this.record,
        weighTyper: this.weighTyper,
        count: this.form.count / 1000,
        createTime: dayjs(this.weightTime).format('YYYY-MM-DD HH:mm:ss'),
        dataSource: 2, // 数据来源为数据填报
      };
      await addWight({
        weighDetailDTOList: [data],
        weighingDataType: 1,
        isWeighRepeatedly: this.record.isWeighRepeatedly,
      });
      this.$router.back();
    },
    onTimeConfirm(val) {
      this.weightTime = dayjs(val).format('YYYY-MM-DD HH:mm:ss');
      this.showTimePicker = false;
    },
    onCancel() {
      this.$router.back();
    },
    handleInput(e) {
      // 固定两位小数
      e.target.value = e.target.value.replace(/[^\d.]/g, ''); //清除“数字”和“.”以外的字符
      e.target.value = e.target.value.replace(/\.{2,}/g, '.'); //只保留第一个. 清除多余的
      e.target.value = e.target.value.replace('.', '$#$').replace(/\./g, '').replace('$#$', '.');
      e.target.value = e.target.value.replace(/^(-)*(\d+)\.(\d\d\d).*$/, '$1$2.$3'); //只能输入两个小数
      e.target.value = e.target.value.replace(/^\./g, ''); //首位不能输入“.”
      if (e.target.value.indexOf('.') < 0 && e.target.value != '') {
        //如果没有小数点，首位不能为0，如01、02...
        e.target.value = parseFloat(e.target.value);
      }
      e.target.value = e.target.value.match(/^\d*(\.?\d{0,3})/g)[0] || '';
      this.$nextTick(() => {
        this.form.count = e.target.value;
      });
    },
  },
};
</script>
<style lang="scss" scoped>
.checking-weight-other {
  background-color: white;
  .cancelTab,
  .saveTab {
    width: 50%;
    padding: 10px 10px;
  }
  .com-btn {
    padding: 0 60px;
    margin-bottom: 15px;
  }
  ::v-deep(.van-field__control) {
    text-align: right;
  }
}
</style>
