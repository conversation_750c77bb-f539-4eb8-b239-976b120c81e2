<template>
  <span class="short-schedule-no">{{ shortScheduleNo }}</span>
</template>

<script>
import { getShortSchedulingNo } from '@/utils/inout-manage';

export default {
  name: 'ShortScheduleNo',
  computed: {
    shortScheduleNo() {
      const defaultSlot = this.$slots.default();
      if (defaultSlot) {
        const [vNode] = defaultSlot;
        if (typeof vNode.children === 'string') {
          return getShortSchedulingNo(vNode.children);
        }
      }
      return '-';
    },
  },
};
</script>

<style scoped>
.short-schedule-no {
  font-weight: bold;
}
</style>
