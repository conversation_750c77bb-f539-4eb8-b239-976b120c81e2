<template>
  <div class="p-4 bg-white">
    <van-row gutter="8">
      <van-col span="24" class="mb-2">
        <AreaPicker v-model:value="areaCode" all-area-select filter-top-area placeholder="地市" />
      </van-col>
      <van-col span="24" class="mb-2" v-if="areaCode">
        <AllUnitSelect
          v-model:value="purchasingStation"
          isGetCompanyByUser
          :userId="userId"
          :areaCode="areaCode"
          @confirm="onConfirm"
          placeholder="请输入所属单位"
        />
      </van-col>
      <van-col span="24" v-if="purchasingStation">
        <HPicker
          :options="storeOptions"
          v-model:value="storeCode"
          showClear
          placeholder="选择库点"
        />
      </van-col>
    </van-row>
  </div>

  <div class="p-4 mt-4 bg-white" v-if="!storeCode">
    <van-grid>
      <van-grid-item>
        <p class="text-2xl">
          {{ counts.proStoreNum }}
        </p>
        <p><van-icon :color="colors.gree" class="mr-1" name="wap-home" />省库</p>
      </van-grid-item>
      <van-grid-item>
        <p class="text-2xl">
          {{ counts.cityStoreNum }}
        </p>
        <p><van-icon :color="colors.blue" class="mr-1" name="wap-home" />市库</p>
      </van-grid-item>
      <van-grid-item>
        <p class="text-2xl">
          {{ counts.areaStoreNum }}
        </p>
        <p><van-icon :color="colors.yellow" class="mr-1" name="wap-home" />县库</p>
      </van-grid-item>
      <van-grid-item>
        <p class="text-2xl">
          {{ counts.otherStoreNum }}
        </p>
        <p><van-icon :color="colors.purple" class="mr-1" name="wap-home" />其他库</p>
      </van-grid-item>
    </van-grid>

    <template v-if="!showDetail">
      <div class="mt-4" v-for="item in storeDistribution" :key="item.code">
        <p class="flex justify-between">
          <span>{{ item.name }}</span>
          <span>{{ item.areaSum + item.citySum + item.otherSum + item.provinceSum }}</span>
        </p>
        <div class="flex h-4 mt-2 bg-gray-100 border-gray-400">
          <div
            class="h-4 w-9"
            :style="`background-color: ${colors.gree};width: ${getPercentOfAccount(
              item.provinceSum,
            )}%`"
          ></div>
          <div
            class="h-4 w-9"
            :style="`background-color: ${colors.blue};width: ${getPercentOfAccount(item.citySum)}%`"
          ></div>
          <div
            class="h-4 w-9"
            :style="`background-color: ${colors.yellow};width: ${getPercentOfAccount(
              item.areaSum,
            )}%`"
          ></div>
          <div
            class="h-4 w-9"
            :style="`background-color: ${colors.purple};width: ${getPercentOfAccount(
              item.otherSum,
            )}%`"
          ></div>
        </div>
      </div>
    </template>
  </div>

  <template v-if="showDetail">
    <div
      class="p-4 mt-4 bg-white"
      v-for="item in filteredStoreDistributionCompany"
      :key="item.name"
    >
      <h3 class="flex items-center">
        <span class="text-lg font-bold">{{ item.name }}</span>
        <span
          v-if="item.category"
          class="inline-block px-1 ml-3"
          :style="`background-color: ${areaTypes[item.category].color}20;color: ${
            areaTypes[item.category].color
          };`"
          >{{ areaTypes[item.category].text }}</span
        >
      </h3>
      <p>
        <span class="inline-block w-2/5 text-gray-400">库点简称：</span
        ><span>{{ item.nickName }}</span>
      </p>
      <p>
        <span class="inline-block w-2/5 text-gray-400">所在行政区划：</span
        ><span>{{ item.area }}</span>
      </p>
      <p>
        <span class="inline-block w-2/5 text-gray-400">详细地址：</span><span>{{ item.addr }}</span>
      </p>
      <p class="mt-2">
        <span class="inline-block w-2/5"
          ><van-icon class="mr-3" name="manager-o" />{{ item.leader }}</span
        ><span><van-icon class="mr-3" name="phone-o" />{{ item.leaderPhone }}</span>
      </p>
    </div>
  </template>

  <div class="my-8"></div>
</template>

<script setup>
import { computed, ref, onMounted, watch } from 'vue';
import { useStore } from 'vuex';
import { getStoreFindByUser } from '@/api/store';
import AreaPicker from '@/views/common/AreaPicker';
import AllUnitSelect from '@/views/PurchasesOverview/common/AllUnitSelect';
import { getStoreDistributionCompany, getStoreDistribution } from '@/api/store-point-list';
const store = useStore();
// const { dept } = store.state.user.info;
const purchasingStation = ref('');
const areaCode = ref(store.getters['user/userAreaCode']);
const isBureauUser = ref(store.getters['user/isBureauUser']);
const storeCode = ref('');
const storeOptions = ref([]);
const counts = ref({});
const storeDistributionCompany = ref([]);
const storeDistribution = ref([]);
const filteredStoreDistributionCompany = computed(() => {
  return storeCode.value
    ? storeDistributionCompany.value.filter((i) => i.id === storeCode.value)
    : storeDistributionCompany.value;
});

const isDistrict = computed(() => {
  return String(areaCode.value.slice(-2)) !== '00';
});

const showDetail = computed(() => {
  return Boolean(purchasingStation.value) || isDistrict.value;
});

const getPercentOfAccount = (e) => {
  const accounts = storeDistribution.value.map(
    (item) => item.areaSum + item.citySum + item.otherSum + item.provinceSum,
  );
  const maxNum = Math.max(...accounts);
  return (e / maxNum) * 100;
};

onMounted(() => {
  isBureauUser.value ? getStoreDistributionData() : getStoreDistributionCompanyData();

  watch(
    () => isDistrict.value,
    (v) => {
      if (v) {
        getStoreDistributionCompanyData();
      }
    },
  );
});

const getStoreDistributionData = async () => {
  const { areasTotal, ...countsData } = await getStoreDistribution({
    areaCode: areaCode.value,
    unitId: purchasingStation.value,
    storeId: storeCode.value,
  });
  counts.value = countsData;
  storeDistribution.value = areasTotal;
};

const getStoreDistributionCompanyData = async (areaCodeValue) => {
  const { list, ...countsData } = await getStoreDistributionCompany({
    areaCode: areaCodeValue ?? areaCode.value,
    unitId: purchasingStation.value,
    storeId: storeCode.value,
  });
  counts.value = countsData;
  storeDistributionCompany.value = list;
};

watch(
  () => areaCode.value,
  () => {
    purchasingStation.value = '';
    isDistrict.value ? getStoreDistributionCompanyData() : getStoreDistributionData();
  },
);

watch(
  () => purchasingStation.value,
  (v) => {
    storeCode.value = '';
    loadStores();
    if (v) {
      getStoreDistributionCompanyData();
    } else {
      if (!isDistrict.value) {
        storeDistributionCompany.value = [];
        getStoreDistributionData();
      }
    }
  },
);
const loadStores = async () => {
  storeOptions.value = [];
  const list = (await getStoreFindByUser(purchasingStation.value)) || [];
  console.log(list);
  storeOptions.value = list.map((it) => {
    return {
      value: it.id,
      text: it.name,
    };
  });
  // const firstStoreCode = storeOptions.value[0]?.value;
};

// const  =  = computed(() => {
//   const userOrgLevel = store.state.user?.reserverIds?.userOrgLevel;
//   return [7, 8, 9].includes(userOrgLevel);
// });

const userId = computed(() => {
  return store.state.user?.reserverIds?.userId;
});

const onConfirm = (value) => {
  purchasingStation.value = value;
};

const colors = {
  gree: '#2bcab0',
  blue: '#2897fa',
  yellow: '#ffa50d',
  purple: '#a58cfd',
};

const areaTypes = {
  1: {
    color: colors.gree,
    text: '省级储备',
  },
  2: {
    color: colors.blue,
    text: '市级储备',
  },
  3: {
    color: colors.yellow,
    text: '县级储备',
  },
  4: {
    color: colors.purple,
    text: '其他储备',
  },
  5: {
    color: colors.purple,
    text: '其他储备',
  },
};
</script>

<style lang="scss" scoped></style>
