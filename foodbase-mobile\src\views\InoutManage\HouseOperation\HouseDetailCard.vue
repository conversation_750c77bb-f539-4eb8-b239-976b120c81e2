<template>
  <div>
    <HCard class="detail-card">
      <div class="card-head">
        <div>调度号：{{ props.detailInfo?.schedulingNo?.split('_')[0] }}</div>
      </div>
      <Divider />
      <div class="card-content">
        <div class="label">
          作业车牌：<span
            v-for="(it, index) in props.detailInfo?.transportVehicleNo"
            :key="index"
            >{{ it }}</span
          >
        </div>
        <div class="label">
          业务类型：
          <span class="value">{{ props.detailInfo.buzTyperName }}</span>
          <!-- {{ props.detailInfo.buzTyperName }} -->
        </div>
        <div class="label">
          粮油品种：<span class="value">{{ props.detailInfo.foodCategoryName }}</span>
        </div>
        <div class="label">
          预估重量：<span class="value"
            >{{
              (props.detailInfo?.counterOrg
                ? props.detailInfo?.counterOrg * 1000
                : 0
              )?.toLocaleString()
            }}公斤</span
          >
        </div>
        <div class="label">
          仓房：<span class="value">
            {{
              hasCargoSpace
                ? `${props.detailInfo.storeHouseName}_${props.detailInfo.cargoSpaceName}`
                : props.detailInfo.storeHouseName
            }}
          </span>
        </div>
        <div class="label">
          保管员：<span class="value">{{ props.detailInfo.custodian }}</span>
        </div>
        <div class="label">
          库存数量（吨）：<span class="value">
            {{ (Math.round(props.detailInfo?.sum * 1000) / 1000)?.toLocaleString() }}
          </span>
        </div>
        <div class="label">
          检查意见：<span class="value">{{ props.detailInfo.qualityResult }}</span>
        </div>
        <div class="label">
          客户名称：<span class="value">{{ props.detailInfo.customerName }}</span>
        </div>
        <div class="label" v-if="route.name === 'HouseDetail'" @click="showHousePicker = true">
          仓房货位：
          <span class="value">
            {{
              hasCargoSpace
                ? `${props.detailInfo.storeHouseName}_${props.detailInfo.cargoSpaceName}`
                : props.detailInfo.storeHouseName
            }}
          </span>
        </div>
      </div>

      <ActionSheet v-model:show="showHousePicker" title="值仓仓房">
        <CargoSelect
          v-if="hasCargoSpace"
          :food-category-id="props.detailInfo?.foodCategoryId"
          @cancel="showHousePicker = false"
          @confirm="onHouseConfirm"
        />
        <HouseSelect
          v-else
          :food-category-id="props.detailInfo?.foodCategoryId"
          :is-out-of-standard="props.detailInfo?.isOutOfStandard"
          @cancel="showHousePicker = false"
          @confirm="onHouseConfirm"
        ></HouseSelect>
      </ActionSheet>
    </HCard>
  </div>
</template>

<script setup>
import { HCard } from '@/components';
import { Divider, ActionSheet } from 'vant';
// import Tag from '@/views/InoutManage/common/InOutTypeTag.vue';
import { useRoute } from 'vue-router';

import { HouseSelect, CargoSelect } from '@/views/InoutManage/common';
import { ref, defineEmits, computed } from 'vue';
import { checkPermission } from '@/utils/permission';
const props = defineProps({
  detailInfo: Object,
});
// const router = useRouter();
const route = useRoute();
const emit = defineEmits(['on-change-house']);

const showHousePicker = ref(false);
const onHouseConfirm = (value) => {
  emit('on-change-house', value);
  showHousePicker.value = false;
};
const hasCargoSpace = computed(() => checkPermission(['cargo_space']));
</script>

<style scoped lang="scss">
.detail-card {
  padding: 16px;
  margin-bottom: 10px;
}
.card-head {
  display: flex;
  align-items: center;
  justify-content: space-between;
  font-size: 18px;
  font-weight: bold;
  color: #1f3359;
  .next {
    color: rgba(109, 116, 141, 0.6);
  }
}

.card-content {
  margin-top: 8px;
  line-height: 25px;
  .label {
    font-size: 14px;
    font-family: PingFangSC-Regular, PingFang SC;
    font-weight: 400;
    color: #6d748d;
    margin: 10px 0;
  }
  .value {
    font-size: 14px;
    font-family: PingFangSC-Regular, PingFang SC;
    font-weight: 400;
    color: #0f0f0f;
  }
  .carNum {
    padding: 3px 6px;
    background: #f4f4f4;
    border-radius: 2px;
    border: 1px solid #ebedf0;
    margin-right: 5px;
  }
}
.type-tag {
  padding: 6px 4px 5px 6px;
  background: #fdeae8;
  border-radius: 2px;
  color: #f25e4a;
  font-size: 12px;
}
</style>
