<template>
  <div class="settlement-manage-detail page-warpper">
    <Form ref="infoForm" class="info-form">
      <HCard class="detail-card" bgColor="#F7F8FA">
        <HCardHead :title="form.transportVehicleNo">
          <template #icon>
            <img src="@/assets/in-out-manage/title-car.png" alt="" />
          </template>
          <template #tail>
            <Tag>{{ form.foodCategoryName }}</Tag>
            <!-- 结算类型 -->
            <Tag :type="weightList[0]?.foodLevel" v-if="weightList[0]?.foodLevel">{{
              weightList[0]?.foodLevelName || '-'
            }}</Tag>
          </template>
        </HCardHead>
        <HDetail title="值仓仓房:" :value="form.storeHouseName" title-class="bold-text"></HDetail>
        <HDetail title="客户名称:" :value="form.customerName" title-class="bold-text"></HDetail>
      </HCard>
      <tabs v-model:active="active" style="margin-top: 15px">
        <tab title="检斤数据">
          <HCard class="detail-card" bgColor="#F7F8FA" v-for="item in weightList" :key="item">
            <HCardHead :title="item.transportVehicleNo">
              <template #icon>
                <img src="@/assets/in-out-manage/title-car.png" alt="" />
              </template>
            </HCardHead>
            <HDetail title="毛重/皮重:">
              <template #value>
                <HFixedNumber :ratio="1000" :fraction-digits="0">
                  {{ item?.countMao || 0 }}
                </HFixedNumber>
                公斤 /
                <HFixedNumber :ratio="1000" :fraction-digits="0">
                  {{ item?.countSkin || 0 }}
                </HFixedNumber>
                公斤
              </template>
            </HDetail>
            <HDetail title="过磅重量:">
              <template #value>
                <HFixedNumber :ratio="1000" :fraction-digits="0">
                  {{ item?.weighingQuantity || 0 }}
                </HFixedNumber>
                公斤
              </template>
            </HDetail>
            <HDetail title="增量:">
              <template #value>
                <HFixedNumber :ratio="1000" :fraction-digits="4">
                  {{ item?.incremental || 0 }}
                </HFixedNumber>
                公斤
              </template>
            </HDetail>
            <HDetail title="销售出库增量:">
              <template #value>
                <HFixedNumber :ratio="1000" :fraction-digits="0">
                  {{ item?.salesOutIncrement || 0 }}
                </HFixedNumber>
                公斤
              </template>
            </HDetail>
            <HDetail title="扣量:">
              <template #value>
                <HFixedNumber :ratio="1000" :fraction-digits="0">
                  {{ item?.deduction || 0 }}
                </HFixedNumber>
                公斤
              </template>
            </HDetail>
            <HDetail title="其他扣量:">
              <template #value>
                <HFixedNumber :ratio="1000" :fraction-digits="0">
                  {{ item?.otherDeduction || 0 }}
                </HFixedNumber>
                公斤
              </template>
            </HDetail>
            <HDetail title="结算数量:">
              <template #value>
                <HFixedNumber :ratio="1000" :fraction-digits="0">
                  {{ item?.count || 0 }}
                </HFixedNumber>
                公斤
              </template>
            </HDetail>
          </HCard>
        </tab>
        <tab title="质检结果">
          <div v-if="inspectionList.length">
            <HCard class="detail-card" bgColor="#F7F8FA" v-for="item in inspectionList" :key="item">
              <HCardHead :title="item?.schedulingNo?.split('_')[0]">
                <template #icon>
                  <img src="@/assets/in-out-manage/title-doc.png" alt="" />
                </template>
              </HCardHead>
              <HDetail
                v-for="(it, index) in item?.testingItemList"
                :key="index"
                :title="it.testItemName"
                :value="it.testItemAval"
              ></HDetail>
            </HCard>
          </div>
          <HCard v-else>
            <Empty description="暂无数据" />
          </HCard>
        </tab>
      </tabs>
    </Form>
    <div class="bottom-warpper">
      <div class="buttons">
        <Button class="next-button" @click="onSettlement" :loading="saveLoading">
          <Loading v-if="saveLoading" size="18px">完成结算</Loading>
          完成结算
        </Button>
      </div>
      <div class="bar"></div>
    </div>
  </div>
</template>

<script>
import { Form, Toast, Tab, Tabs, Empty, Loading, Dialog } from 'vant';
import { HCard, HFixedNumber } from '@/components';
// import { ShortScheduleNo } from '@/views/InoutManage/common';
import { ref, reactive, toRefs, onMounted, computed } from 'vue';
import {
  getSettlementDetail,
  saveSettlementTicket,
  getStoreHouseStatus,
} from '@/api/in-out-manage';
import { useRouter, useRoute } from 'vue-router';
import HDetail from '@/views/InoutManage/common/HDetail';
import HCardHead from '@/views/InoutManage/common/HCardHead';
import Tag from '@/views/PurchasesOverview/common/Tag1';
import { checkPermission } from '@/utils/permission';
export default {
  components: { Form, HCard, HFixedNumber, HDetail, HCardHead, Tag, Tabs, Tab, Empty, Loading },
  setup() {
    const router = useRouter();
    const route = useRoute();
    // const store = useStore();
    const infoForm = ref(null);
    const active = ref('检斤数据');
    const state = reactive({
      form: {},
      weightList: [],
      inspectionList: [],
      accountsVoucherList: [],
      saveLoading: false,
      weightSelected: 0,
      inspectionSelected: 0,
    });
    const hasCargoSpace = computed(() => checkPermission(['cargo_space']));

    const schedulingNoList = computed(() => {
      const list = route.params.schedulingNo.split(',');
      console.log(list, 'list');
      return list;
    });
    const ifMerge = computed(() => {
      if (schedulingNoList.value.length != 1) {
        return true;
      } else {
        return false;
      }
    });

    onMounted(async () => {
      initData();
    });

    const initData = async () => {
      try {
        const data = await getSettlementDetail({ schedulingNos: schedulingNoList.value });
        const {
          accountTemporaryDetailResVOList,
          accountTemporaryTestingResultList,
          accountTemporaryVoucherDetailResVOList,
          ...rest
        } = data;
        const transportVehicleNos = accountTemporaryDetailResVOList.map(
          (item) => item.transportVehicleNo,
        );
        const foodCategoryName = accountTemporaryDetailResVOList[0]?.foodCategoryName;
        const storeHouseName =
          hasCargoSpace.value && accountTemporaryDetailResVOList[0]?.cargoSpaceName
            ? `${accountTemporaryDetailResVOList[0]?.storeHouseName}_${accountTemporaryDetailResVOList[0]?.cargoSpaceName}`
            : accountTemporaryDetailResVOList[0]?.storeHouseName;
        const foodLevelName = accountTemporaryDetailResVOList[0]?.foodLevelName;
        const customerName = rest.customerName;
        const accountDetailNo = rest.accountDetailNo;
        state.form = {
          transportVehicleNo: transportVehicleNos.join(','),
          foodCategoryName,
          storeHouseName,
          foodLevelName,
          customerName,
          accountDetailNo,
        };
        state.weightSelected = 0;
        state.inspectionSelected = 0;
        state.accountsVoucherList = accountTemporaryVoucherDetailResVOList;
        state.weightList = accountTemporaryDetailResVOList;
        state.inspectionList = accountTemporaryTestingResultList
          ? accountTemporaryTestingResultList[0]
            ? accountTemporaryTestingResultList
            : []
          : [];
      } catch {
        router.back();
      }
    };

    const onClose = () => {
      router.back();
    };

    const onSettlement = async () => {
      const { buzTyper, storeHouseId, count } = state.weightList[0];

      // 判断是否已结算或已终止
      // 判断当前任务是否已完成质检
      // 判断结算数量是否为正数
      if (Number(count) <= 0) {
        Toast.fail('结算数量非正数，数据异常无法结算，请确认后终止作业重新录入');
        return;
      }

      // 判断业务类型选择是否可以结算
      let params = { storeHouseId, type: '1', buzTyper };
      let data = await getStoreHouseStatus(params);
      if (data && data.length != 0) {
        Dialog.confirm({
          title: data,
        }).then(async () => {
          onTempSave();
        });
      } else {
        onTempSave();
      }
    };

    const onTempSave = async () => {
      Dialog.confirm({
        title: '是否确认结算？',
      }).then(async () => {
        if (state.saveLoading) {
          return;
        }
        state.saveLoading = true;
        const data = {
          accountDetailNo: state.form.accountDetailNo,
          accountsVoucherList: state.accountsVoucherList,
          schedulingNoList: state.weightList.map((it) => ({
            schedulingNo: it.schedulingNo,
            poundYardNo: it.poundYardNo,
          })),
        };
        try {
          await saveSettlementTicket(data);
          Toast.success('保存成功');
          onClose();
        } catch (e) {
          // Toast.fail('保存失败');
        } finally {
          state.saveLoading = false;
        }
      });
    };
    return {
      ...toRefs(state),
      infoForm,
      schedulingNoList,
      ifMerge,
      onClose,
      onTempSave,
      active,
      onSettlement,
    };
  },
};
</script>

<style lang="scss" scoped>
@import '@/views/InoutManage/style/index';
.info-form {
  margin-bottom: 120px;
  ::v-deep {
    .van-tab {
      font-size: 16px;
    }
    .van-tabs__line {
      background: var(--van-primary-color);
    }
  }
  .detail-card {
    padding: 16px;
    margin-top: 2px;
    ::v-deep {
      .van-divider {
        margin: 10px 0;
      }
    }
  }
}
</style>
