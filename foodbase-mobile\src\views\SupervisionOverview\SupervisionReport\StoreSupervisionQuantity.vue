<template>
  <div class="supervision-quantity">
    <div class="item">
      <SvgIcon name="reserve" />
      <span class="name">储备库存</span>
      <HFixedNumber class="value" :fraction-digits="0">
        {{ props.data.stockSum }}
      </HFixedNumber>
      <span class="unit">吨</span>
    </div>
    <div class="item">
      <SvgIcon name="stock" />
      <span class="name">今年已轮入</span>
      <HFixedNumber class="value" :fraction-digits="0">
        {{ props.data.rollInSum }}
      </HFixedNumber>
      <span class="unit">吨</span>
    </div>
    <div class="item">
      <SvgIcon name="stock" />
      <span class="name">今年已轮出</span>
      <HFixedNumber class="value" :fraction-digits="0">
        {{ props.data.rollOutSum }}
      </HFixedNumber>
      <span class="unit">吨</span>
    </div>
    <div class="item">
      <SvgIcon name="check-result" />
      <span class="name">清查结果</span>
      <HFixedNumber class="value" :fraction-digits="0">
        {{ props.data.actualStockSum }}
      </HFixedNumber>
      <span class="unit">吨</span>
    </div>
    <div class="result-desc">
      <Icon name="info" />
      <span>
        清查结果/储备规模 {{ props.data.stockReach ? '&gt;' : '&lt;' }} 70%，
        {{ props.data.resultName }}
      </span>
    </div>
    <div class="detail-values" v-if="isAbnormal">
      <div class="value-item">
        <span class="name">异常粮食数量：</span>
        <span class="value">{{ props.data.abnormalSum }}</span>
        吨
      </div>
      <div class="value-item">
        <span class="name">异常仓房数：</span>
        <span class="value">{{ props.data.abnormalStorehouseSum }}</span>
        个
      </div>
      <div class="value-item">
        <span class="name">异常仓房：</span>
        <span class="store-name">{{ props.data.storeName }}</span>
      </div>
      <div class="storehouse-name-item">{{ props.data.abnormalStorehouse?.join('、') }}</div>
      <div class="value-item">
        <span class="name">风险提示：</span>
        <span class="value">{{ props.data.warnSum }}</span>
        个
      </div>
    </div>
  </div>
</template>

<script setup>
import SvgIcon from '@/components/SvgIcon/SvgIcon';
import { Icon } from 'vant';
import HFixedNumber from '@/components/HFixedNumber/HFixedNumber';
import { computed } from 'vue';

const props = defineProps({
  data: {
    type: Object,
    default: () => ({}),
  },
});

const isAbnormal = computed(() => {
  const { data } = props;
  return data.abnormalSum || data.abnormalStorehouseSum || data.warnSum;
});
</script>

<style scoped lang="scss">
.supervision-quantity {
  padding: 10px 16px;
  .item {
    display: flex;
    line-height: 32px;
    align-items: center;
    margin: 8px 0;

    .svg-icon {
      font-size: 18px;
    }

    .name {
      font-size: 18px;
      font-weight: bold;
      color: var(--van-gray-7);
      margin-left: 4px;
    }

    .value {
      font-size: 24px;
      font-weight: bold;
      color: #1492ff;
      margin-left: auto;
    }

    .unit {
      font-size: 16px;
      font-weight: 500;
      color: var(--van-gray-8);
      margin-left: 6px;
    }
  }

  .result-desc {
    font-size: 16px;
    line-height: 22px;
    display: flex;
    align-items: center;
    .van-icon {
      font-size: 18px;
      color: #ffb532;
      margin-right: 4px;
    }
  }

  .value-item {
    font-size: 16px;
    color: var(--van-gray-8);
    line-height: 22px;
    display: flex;
  }

  .value-item + .value-item {
    margin-top: 10px;
  }

  .name {
    width: 8em;
  }

  .store-name {
    font-size: 20px;
    font-weight: 500;
    color: #ff5814;
    line-height: 28px;
    text-align: right;
  }

  .storehouse-name-item {
    font-weight: normal;
    text-align: right;
  }

  .value {
    font-size: 20px;
    font-weight: 500;
    color: #ff5814;
    margin-left: auto;
    margin-right: 2px;
  }
}
</style>
