<template>
  <HCard title="库存性质分布（单位：吨）">
    <template #header-extra>
      <HPicker
        class="bg-gray"
        :options="foodCategoryOptions"
        v-model:value="selectedFoodCategory"
        v-if="barData.length > 0"
      ></HPicker>
    </template>
    <div class="stock-origin-distribution">
      <div class="chart" v-if="loaded">
        <HEchart ref="barChart" :options="structureOptions" v-if="barData.length > 0" />
        <EmptyHolder v-else />
      </div>
    </div>
  </HCard>
</template>

<script setup>
import { computed, ref, onMounted } from 'vue';
import { HCard, HEchart, HPicker } from '@/components';
import EmptyHolder from '@/views/common/EmptyHolder';
import { getStoreQuantityBarDataBaseDeploy } from '@/api/stock-overview';

const props = defineProps({
  categoryType: Number, // 1 原粮，2 成品粮， 3 食用油
});
const allData = ref([]);
const foodCategoryOptions = computed(() => {
  return (allData.value || []).map((it) => {
    return {
      text: it.categoryName,
      value: it.categoryName,
    };
  });
});

const selectedFoodCategory = ref(null);

const barChart = ref(null);
const loaded = ref(false);

onMounted(async () => {
  await fetchStoreQuantityBarDataBaseDeploy();
  loaded.value = true;
});

const fetchStoreQuantityBarDataBaseDeploy = async () => {
  allData.value = await getStoreQuantityBarDataBaseDeploy({ categoryType: props.categoryType });
  selectedFoodCategory.value = foodCategoryOptions.value[0]?.value;
  return Promise.resolve();
};

const barData = computed(() => {
  const findData = (allData.value || []).find((i) => i.categoryName === selectedFoodCategory.value);

  let soreProperty = ['省级储备', '市级储备', '县级储备'];
  if (process.env.VUE_APP_MODE === 'neimenggu') {
    soreProperty = ['自治区级储备', '盟/市级储备', '旗/县级储备'];
  }
  return [
    {
      库存性质: soreProperty[0],
      库存: findData?.provinceSum,
    },
    {
      库存性质: soreProperty[1],
      库存: findData?.citySum,
    },
    {
      库存性质: soreProperty[2],
      库存: findData?.countySum,
    },
    {
      库存性质: '其他储备',
      库存: findData?.otherSum,
    },
  ];
});

const structureOptions = computed(() => {
  return {
    tooltip: {
      trigger: 'axis',
      backgroundColor: 'rgba(0,10,48,0.7)',
      borderWidth: 0,
      textStyle: {
        color: '#fff',
      },
    },
    legend: {
      show: false,
    },
    grid: {
      top: 30,
      right: 10,
      bottom: 60,
      left: 80,
    },
    dataset: {
      source: barData.value,
    },
    xAxis: {
      type: 'category',
      axisLabel: {
        interval: 0,
        fontSize: 16,
        rotate: 20,
        verticalAlign: 'left',
      },
    },
    yAxis: {
      minInterval: 1,
      boundaryGap: ['0%', '20%'],
      max: function (value) {
        if (value.max === 0) {
          return 5;
        }
      },
      axisLine: {
        show: false,
      },
      splitLine: {
        lineStyle: {
          width: 0.5,
          color: 'rgba(191,191,191,0.5)',
          type: 'solid',
        },
      },
      axisLabel: {
        fontSize: 14,
        lineHeight: 18,
      },
    },
    series: [
      {
        type: 'bar',
        barWidth: 10,
        textStyle: {
          color: '#323233',
        },
        itemStyle: {
          color: {
            type: 'linear',
            x: 0,
            y: 0,
            x2: 0,
            y2: 1,
            colorStops: [
              {
                offset: 0,
                color: '#00de83', // 0% 处的颜色
              },
              {
                offset: 1,
                color: '#23b49c', // 100% 处的颜色
              },
            ],
          },
        },
      },
    ],
  };
});
</script>

<style scoped lang="scss">
.stock-origin-distribution {
  .chart {
    height: 230px;
  }
}

.h-picker {
  line-height: 34px;
  color: var(--van-gray-7);
}

.h-echarts {
  width: 100%;
  height: 100%;
}
</style>
