import request, { reserveRequest } from '@/utils/request';

// 根据单个地区代码还原出省市县三级代码
function getAreaParams(anyAreaCode) {
  const provinceCode = anyAreaCode.substring(0, 2).padEnd(6, '0');
  let cityCode = null;
  let areaCode = null;
  if (anyAreaCode.endsWith('0000')) {
    // 省
  } else if (anyAreaCode.endsWith('0001') || anyAreaCode.endsWith('00')) {
    // 省本级、市
    cityCode = anyAreaCode;
  } else {
    // 区县，县本级
    cityCode = anyAreaCode.substring(0, 4).padEnd(6, '0');
    areaCode = anyAreaCode;
  }

  return {
    areaCode,
    cityCode,
    provinceCode,
  };
}

export async function getSupervisionResult(areaCode) {
  return request().post('/flow-check-area-daily/getTitleDailyList', { ...getAreaParams(areaCode) });
}

export async function getRiskList(params) {
  return request().get('/flow-check-warn-detail/getWarnDetail', { params });
}

export async function getRiskDetail(id) {
  return request().get('/flow-check-warn-detail/getWarnDetailOne', { params: { id } });
}
//获取粮情信息
export async function temperatureDetail(params) {
  return reserveRequest().get('/api/cloudAtlas/store/temperatureDetail', { params });
}

export async function dealRisk(data) {
  return request().post('/flow-check-warn-detail/dealWithWarn', data);
}

export async function getAreaReport(areaCode) {
  return request().post('/flow-check-area-daily/getAreaReport', { ...getAreaParams(areaCode) });
}

export async function getStoreReport(storeId) {
  return request().get('/flow-check-store-daily/getStoreReport', { params: { storeId } });
}

export async function getCameraData(params) {
  // return reRequest().get('/api/rmCamera/getCameraTotal.do', { params });
  return reserveRequest().get('/api/cloudAtlas/getCameraTotal', { params });
}
// 视频推流接口
export async function pushStreamByCode(params) {
  // return reRequest().get('/api/rmCamera/pushStreamByCode.do', { params });
  return reserveRequest().get('/api/cloudAtlas/pushStream', { params });
}

//粮情信息
//根据用户获取存储单位
export function getStorageUnit() {
  return reserveRequest().get('/api/storage/result/getStorageInfoUnit');
}

/**
 * 根据单位获取库点
 * @param {number} handleUnitId 单位id
 * @returns {Promise<any>}
 */
export async function getStoreOfCompany(params) {
  return reserveRequest().post('/api/storeHandle/getStoreOfCompany', null, { params });
}

// 根据库点id查询仓房油罐列表
export function getStoreHouseAndOil(params) {
  return reserveRequest().get('/api/storeHouseAndOilTank/findByStoreId', {
    params,
  });
}

//获取粮情信息
export async function getTempList(params) {
  return reserveRequest().get('/api/cloudAtlas/store/temp', { params });
}
