<template>
  <div class="risk-list">
    <div class="query-form">
      <Row>
        <Col span="8">
          <van-dropdown-menu>
            <van-dropdown-item
              v-model="queryForm.houseId"
              :options="warehouseList"
              @change="getList"
            />
          </van-dropdown-menu>
        </Col>
        <Col span="8">
          <van-dropdown-menu>
            <van-dropdown-item
              v-model="queryForm.warnType"
              :options="typesList"
              @change="getList"
            />
          </van-dropdown-menu>
        </Col>
        <Col span="8">
          <van-dropdown-menu>
            <van-dropdown-item v-model="queryForm.status" :options="statusList" @change="getList" />
          </van-dropdown-menu>
        </Col>
      </Row>
    </div>
    <PullRefresh v-model="refreshing" @refresh="onRefresh">
      <List
        ref="listRef"
        :finished="finished"
        :finished-text="list.length === 0 ? '' : '没有更多了'"
      >
        <HCard class="detail-card" v-for="item in list" :key="item" @click="showDetail(item)">
          <template #header-title>
            <div class="titlehead">
              <img class="abarnimg" src="@/assets/abarnicon.png" alt="" />
              {{ item.houseName }}
            </div>
          </template>
          <template #header-extra>
            <van-icon name="arrow" />
          </template>
          <div class="detail">
            <div class="item">
              <div class="name">类型：</div>
              <div class="value house-name">
                {{ item?.warnType === '201' ? '业务预警信息' : '业务告警信息' }}
              </div>
            </div>
            <div class="item">
              <div class="name">告警内容：</div>
              <div class="value">{{ item?.warnContent }}</div>
            </div>
            <div class="item">
              <div class="name">告警时间：</div>
              <div class="value">{{ item?.warnTime }}</div>
            </div>
            <div class="item"></div>
            <div class="detail-handle-status">
              <div v-if="item.status === '0'" class="not-handle"></div>
              <div v-else-if="item.status === '1'" class="already-handle"></div>
            </div>
          </div>
        </HCard>
      </List>
    </PullRefresh>
  </div>
</template>

<script setup>
import { Row, Col, PullRefresh, List } from 'vant';
import { reactive, ref, onMounted } from 'vue';
import { HCard } from '@/components';
import { useRouter } from 'vue-router';
import { useStore } from 'vuex';
import { getWarninglog, getObtainWarehouse } from './api';

const store = useStore();
const router = useRouter();
const userInfo = ref(store.getters['user/userInfo']);
const warehouseList = ref([]);
const typesList = ref([
  { text: '全部', value: null },
  { text: '业务预警信息', value: 201 },
  { text: '业务告警信息', value: 202 },
]);
const statusList = ref([
  { text: '所有状态', value: null },
  { text: '已处理', value: 1 },
  { text: '未处理', value: 0 },
]);
const queryForm = reactive({
  houseId: null,
  warnType: null,
  status: null,
});
const list = ref([]);
const refreshing = ref(false);
// const loading = ref(false);
const finished = ref(false);

const showDetail = (item) => {
  router.push({ name: 'WarningDetail', query: { data: JSON.stringify(item) } });
};
const onRefresh = () => {
  if (refreshing.value) {
    refreshing.value = false;
  }
  getList();
};
const getWarehouse = () => {
  getObtainWarehouse().then((res) => {
    warehouseList.value = [];
    res.forEach((item) => {
      warehouseList.value.push({
        value: item.id,
        text: item.name,
      });
    });
    warehouseList.value.unshift({ value: null, text: '所有仓房' });
  });
};
// onActivated(() => {
//   console.log(123);
//   refreshing.value = true;
//   onRefresh();
// });
onMounted(() => {
  onRefresh();
  getWarehouse();
});
const getList = async () => {
  const params = {
    username: userInfo.value.username,
    houseId: queryForm.houseId,
    warnType: queryForm.warnType,
    status: queryForm.status,
  };
  await getWarninglog(params).then((res) => {
    list.value = res.data.records;
  });

  // loading.value = false;
};
</script>

<style scoped lang="scss">
.h-card {
  margin-bottom: 15px;

  ::v-deep(.h-card-header) {
    border-bottom: none;
    line-height: 40px;

    .titlehead {
      display: flex;
      flex-direction: row;
    }

    .abarnimg {
      width: 15px;
      height: 18px;
      margin-right: 10px;
      margin-top: 11px;
    }

    .h-card-header-title {
      font-weight: 600;
      font-size: 16px;
    }
  }
}

.detail {
  font-size: 16px;
  padding: 0 20px;
  color: var(--van-gray-7);
  position: relative;

  .item {
    display: flex;
    line-height: 38px;

    .name {
      white-space: nowrap;
    }

    .house-name {
      margin-left: 32px;
    }

    .value {
      color: #232323;
    }
  }

  .detail-handle-status {
    position: absolute;
    right: 20px;
    top: 32px;

    div {
      width: 52px;
      height: 52px;
      background-size: contain;
    }

    .not-handle {
      background-image: url(@/assets/untreated.png);
    }

    .already-handle {
      background-image: url(@/assets/processed.png);
    }
  }
}
</style>
