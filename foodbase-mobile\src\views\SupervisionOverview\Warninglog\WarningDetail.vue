<template>
  <div class="risk-detail">
    <HCard>
      <div class="titleHead">
        <img class="abarnimg" src="@/assets/Userprofile.png" alt="" />
        <div>预告警信息</div>
      </div>
      <div class="detail">
        <div class="item">
          <div class="name">仓房：</div>
          <div class="value house-name">
            {{ detail.houseName }}
          </div>
        </div>
        <div class="item">
          <div class="name">类型：</div>
          <div class="value house-name">
            {{ detail.warnType === '201' ? '业务预警信息' : '业务告警信息' }}
          </div>
        </div>
        <div class="item">
          <div class="name">告警内容：</div>
          <div class="value house-name">{{ detail.warnContent }}</div>
        </div>
        <div class="item">
          <div class="name">告警时间：</div>
          <div class="value house-name">
            <HDateTime :value="detail.warnTime" />
          </div>
        </div>
      </div>
    </HCard>
    <HCard class="handle-idea">
      <span class="handle-idea-title">处理结果</span>
      <van-cell-group inset class="handle-idea-field">
        <van-field
          :disabled="detail.status === '1'"
          v-model="form.message"
          rows="5"
          autosize
          label=""
          type="textarea"
          maxlength="200"
          placeholder="请输入处理结果"
          show-word-limit
        />
      </van-cell-group>
    </HCard>
    <HCard class="processed" v-if="detail.status == '1'">
      <div class="detail">
        <div class="item">
          <div class="name">处理人：</div>
          <div class="value house-name">{{ detail.warningDetail }}</div>
        </div>
        <div class="item">
          <div class="name">处理时间：</div>
          <div class="value house-name"><HDateTime :value="detail.alarmTime" /></div>
        </div>
      </div>
    </HCard>
    <HCard class="onbottom" v-if="detail.status == '0'">
      <div class="Bottombutton">
        <Button style="width: 165px" @click="goBack" type="default">取 消</Button>
        <Button
          style="width: 166px; margin-left: 13px"
          type="primary"
          @click="onSubmit"
          :loading="dealLoading"
          >确 定</Button
        >
      </div>
    </HCard>
  </div>
</template>
<script setup>
import { onMounted, ref, reactive } from 'vue';
import { Button, Toast } from 'vant';
import { HCard } from '@/components';
import { useRoute, useRouter } from 'vue-router';
import HDateTime from '@/components/HDateTime/HDateTime';
import { addWarninglog } from './api';
import { useStore } from 'vuex';

const router = useRouter();
const route = useRoute();
const store = useStore();
const userInfo = store.getters['user/userInfo'];

const detail = ref({});
const dealLoading = ref(false);
const form = reactive({
  message: '',
});
// const typesList=reactive({

// })
//获取详情
const getDetail = async () => {
  const data = JSON.parse(route.query.data);
  detail.value = data;
  form.message = data.handleResult || '';
  detail.value.warningDetail = data.handlerName || '';
};

onMounted(() => {
  getDetail();
});
const onSubmit = () => {
  const params = {
    username: userInfo?.username || '',
    grainJobWarnLogId: detail.value.grainJobWarnLogId,
    handleResult: form.message,
  };
  addWarninglog([params]).then(() => {
    Toast('保存成功');
    detail.value.status = '1';
    goBack();
  });
};
// 当点击已处理的时候,按钮是返回
const goBack = () => {
  router.back();
};
</script>

<style scoped lang="scss">
.risk-detail {
  position: relative;
  .handle-idea {
    margin-top: 10px;
    padding: 16px 0 0 16px;

    .handle-idea-title {
      font-size: 16px;
      font-weight: 600;
      color: #232323;
    }
  }
}
.titleHead {
  padding: 16px 20px;
  display: flex;
  flex-direction: row;
  font-size: 16px;
  font-weight: 600;
  color: #232323;
  .abarnimg {
    width: 16px;
    height: 16px;
    margin-top: 4px;
    margin-right: 8px;
  }
}
.detail {
  font-size: 16px;
  padding: 0 20px;
  color: var(--van-gray-7);
  position: relative;

  .item {
    display: flex;
    flex-direction: row;
    line-height: 49px;
    width: 100%;
    border-bottom: 1px solid #e8e9ec;
    .name {
      white-space: nowrap;
    }
    .house-name {
      width: 100%;
      text-align: right;
    }
    .value {
      color: #232323;
    }
  }
  .item:last-child {
    border-bottom: none;
  }
}
.processed {
  margin-top: 10px;
}
.onbottom {
  height: 70px;
  position: fixed;
  left: 0px;
  bottom: 0px;
  width: 100%;
  .Bottombutton {
    display: flex;
    flex-direction: row;
    justify-content: center;
    padding-top: 10px;
  }
}
</style>
