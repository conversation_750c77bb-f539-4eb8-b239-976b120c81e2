<template>
  <div class="risk-detail">
    <Swipe :autoplay="3000" v-if="images.length">
      <SwipeItem v-for="image in images" :key="image">
        <Image fit="cover" class="risk-image" :src="image" />
      </SwipeItem>
      <template #indicator="{ active }">
        <div class="custom-indicator">{{ active + 1 }}/{{ images.length }}</div>
      </template>
    </Swipe>
    <HCard>
      <template #header-title>
        {{ detail?.storeName }}
        <van-tag type="success">
          <!-- {{ detail.storeHouseStatus === '1' ? '满仓' : '空仓' }} -->
          {{ typesList[detail.storeHouseStatus] }}
        </van-tag>
      </template>
      <div class="detail">
        <div class="item">
          <div class="name">仓房：</div>
          <div class="value house-name">
            {{ detail?.storeHouseName }}
          </div>
        </div>
        <div class="item">
          <div class="name">异常时间：</div>
          <div class="value">
            <HDateTime :value="detail.alarmTime" />
          </div>
        </div>
        <div class="item">
          <div class="name">异常详情：</div>
          <div class="value">{{ detail.warningDetail }}</div>
        </div>
      </div>
    </HCard>
    <HCard class="handle-idea">
      <span class="handle-idea-title">处理意见</span>
      <van-cell-group inset class="handle-idea-field">
        <van-field
          :disabled="detail.isDisposed === '1'"
          v-model="detail.disposeResult"
          rows="5"
          autosize
          label=""
          type="textarea"
          maxlength="255"
          placeholder="请输入处理意见"
          show-word-limit
        />
      </van-cell-group>
    </HCard>
    <HCard class="handle-idea">
      <span class="handle-idea-title">识别结果</span>
      <van-radio-group v-model="discernResult" direction="horizontal" @change="handleResultChange">
        <van-radio name="1">正确</van-radio>
        <van-radio name="0">错误</van-radio>
        <van-radio name="3">测试</van-radio>
      </van-radio-group>
      <van-field
        v-if="discernResult === '0'"
        label="错误原因"
        is-link
        readonly
        @click="showErrorResultPicker = true"
      />
      <van-popup v-model:show="showErrorResultPicker" round position="bottom">
        <van-picker
          :columns="errorList"
          :columns-field-names="{ text: 'msg', value: 'id' }"
          @cancel="showErrorResultPicker = false"
          @confirm="onErrorResultConfirm"
        />
      </van-popup>
    </HCard>

    <HCard class="onSubmit-btn">
      <Button
        block
        type="primary"
        v-if="detail.isDisposed === '0'"
        @click="onSubmit"
        :loading="dealLoading"
        >确 定</Button
      >
      <Button block type="primary" @click="goBack" v-if="false">返 回</Button>
    </HCard>
  </div>
</template>

<script setup>
import { nextTick, onMounted, reactive, ref } from 'vue';
import { Button, Toast, Swipe, SwipeItem, Image } from 'vant';
import { useRoute, useRouter } from 'vue-router';
import { HCard } from '@/components';
import HDateTime from '@/components/HDateTime/HDateTime';
import { useStore } from 'vuex';
// eslint-disable-next-line no-unused-vars
// import { dealRisk, getRiskDetail } from '@/api/supervision';
import { addHandling, getErrorList, postErrorResult } from './api';

const route = useRoute();
const router = useRouter();
const store = useStore();
const userInfo = store.getters['user/userInfo'];
const detail = ref({});
const images = ref([]);
const dealLoading = ref(false);
const discernResult = ref('');
const showErrorResultPicker = ref(false);
const errorList = ref([]);
const typesList = reactive({
  1: '满仓',
  2: '空仓',
  3: '入库中',
  4: '出库中',
  5: '转作其他用途',
});
const typeList = ref({
  人员入仓: 1,
  未佩戴安全帽: 2,
  人员跌倒: 3,
  吸烟预警: 4,
  明火异常: 5,
  粮面异动: 6,
});
// 获取风险提示详情
const getDetail = async () => {
  // eslint-disable-next-line no-unused-vars
  const data = JSON.parse(route.query.data);
  console.log(data);
  detail.value = data;

  // console.log(data,'123123');
  // detail.value = {
  //   houseName: '衢州中心粮库',
  //   storeName: '0P28',
  //   warnDetail: '未戴安全帽',
  // };
  // detail.value = await getRiskDetail(id);
  // images.value = detail.value.imageList || [];
  // images.value = [
  //   'https://p9.itc.cn/q_70/images03/20200716/df549912266b4e879187c32521d8837d.jpeg',
  //   'https://n.sinaimg.cn/sinacn/w1200h800/20171229/6c62-fyqefvw1247940.jpg',
  // ];
  images.value = [data.imgUrl];
  await nextTick(() => {
    window.scrollTo({ top: 0, behavior: 'smooth' });
  });
};

onMounted(() => {
  getDetail();
});

const handleResultChange = async () => {
  if (discernResult.value === '0') {
    let params = { sceneCategoryType: typeList.value[detail.value.warningType] };
    const res = await getErrorList(params);
    errorList.value = res.record
  } else {
    await postErrorResult({
      rmWarningInfoId: JSON.parse(route.query.data).rmWarningInfoId,
      discernResult: discernResult.value,
    });
    Toast('设置成功');
  }
};

const onErrorResultConfirm = async (value) => {
  await postErrorResult({
    rmWarningInfoId: JSON.parse(route.query.data).rmWarningInfoId,
    discernResult: discernResult.value,
    errorSummaryId: value.id,
  });
  Toast('设置成功');
  showErrorResultPicker.value = false;
};
const onSubmit = async () => {
  // const disposeDetail = form.message;
  // const id = detail.value.id;
  const params = {
    rmWarningInfoId: detail.value.rmWarningInfoId,
    disposePerson: userInfo.nickName,
    disposeResult: detail.value.disposeResult,
  };
  try {
    dealLoading.value = true;
    // await dealRisk({ params});
    await addHandling({ ...params }).then((res) => {
      Toast('保存成功');
      if (res.result == 1) {
        detail.value.isDisposed = '1';
      }
    });
    // await getDetail();
  } catch (e) {
    Toast({ message: '保存失败' });
  } finally {
    dealLoading.value = false;
  }
};

// 当点击已处理的时候,按钮是返回
const goBack = () => {
  router.back();
};
</script>
<style lang="scss">
.handle-idea-field {
  margin: 0;

  .van-cell {
    padding: 8px 8px 8px 0;
  }
}
</style>
<style scoped lang="scss">
.risk-detail {
  //margin-bottom: 64px;
  .handle-idea {
    margin-top: 10px;
    padding: 16px 0 0 16px;

    .handle-idea-title {
      font-size: 16px;
      font-weight: 600;
      color: #232323;
    }
  }

  .onSubmit-btn {
    height: 113px;
    padding: 14px 16px;
    margin-top: 77px;
  }

  .risk-image {
    width: 100vw;
    height: 56vw;
  }

  .custom-indicator {
    position: absolute;
    right: 5px;
    bottom: 5px;
    padding: 2px 5px;
    font-size: 12px;
    color: #ffffff;
    background: rgba(255, 255, 255, 0.1);
  }

  ::v-deep(.store-name) {
    flex: 4;
  }

  ::v-deep(.dispose-time) {
    flex: 2;
  }

  ::v-deep(.dispose-detail) {
    flex: 2;
  }

  --van-cell-font-size: 18px;
  --van-cell-group-title-font-size: 17px;
  --van-button-normal-font-size: 20px;
}

.detail {
  font-size: 16px;
  padding: 0 20px;
  color: var(--van-gray-7);
  position: relative;

  .item {
    display: flex;
    line-height: 38px;

    .name {
      white-space: nowrap;
    }

    .house-name {
      margin-left: 32px;
    }

    .value {
      color: #232323;
    }
  }
}
</style>
