<template>
  <HCard title="收购数量总览">
    <template #header-extra>
      <HPicker
        class="bg-gray"
        :options="purchaseCategory"
        v-model:value="selectedFoodCategory"
      ></HPicker>
    </template>
    <div class="stock-origin-distribution">
      <div class="flex p-4">
        <div class="flex items-center w-1/3">
          <img src="@/views/Datav/GrainQuality/images/home.svg" alt="" srcset="" />
        </div>
        <div class="w-2/3">
          <p class="mt-0 text-right">收购数量(吨)</p>
          <p class="text-right text-[34px] font-bold text-yellow-500 leading-none">
            {{ acquisitionQualityNum?.totalCount?.toLocaleString() }}
          </p>
        </div>
      </div>
      <div class="p-4 pt-0">
        <div class="flex p-4 rounded-md bg-gradient-to-t from-blue-50 to-blue-200">
          <div class="flex-1">
            <p class="">合格粮食数量(吨)</p>
            <p class="text-[24px] text-blue-400 font-bold">
              {{ acquisitionQualityNum?.qualified?.toLocaleString() }}
            </p>
          </div>
          <div class="flex-1">
            <p class="">超标粮食数量(吨)</p>
            <p class="text-[24px] text-blue-400 font-bold">
              {{ acquisitionQualityNum?.overStandard?.toLocaleString() }}
            </p>
          </div>
        </div>
      </div>
    </div>
  </HCard>
</template>

<script setup>
import { ref, inject, onMounted, watch } from 'vue';
import { HCard, HPicker } from '@/components';
import { reserveRequest } from '@/utils/request';
import dayjs from 'dayjs';

const purchaseCategory = inject('purchaseCategory');

const acquisitionQualityNum = ref({});

const selectedFoodCategory = ref(purchaseCategory.value[0].value);
const getData = async () => {
  acquisitionQualityNum.value = await reserveRequest().get('/api/acquisitionQuality/overview', {
    params: {
      year: dayjs().format('YYYY'),
      foodBigCategory: selectedFoodCategory.value,
    },
  });
};
watch(
  () => selectedFoodCategory.value,
  () => getData(),
);
onMounted(async () => {
  getData();
});
</script>

<style lang="scss" scoped></style>
