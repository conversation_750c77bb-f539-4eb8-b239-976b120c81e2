const TOKEN_NAME = process.env.VUE_APP_TOKEN_NAME;
const RESERVER_TOKEN_NAME = process.env.VUE_APP_RESERVER_TOKEN_NAME;
const FRAMEWORK_TOKEN_NAME = process.env.VUE_APP_FRAMEWORK_TOKEN_NAME;

export function getToken() {
  return localStorage.getItem(TOKEN_NAME);
}

export function setToken(token) {
  return localStorage.setItem(TOKEN_NAME, token);
}

export function removeToken() {
  return localStorage.removeItem(TOKEN_NAME);
}

export function getReserverToken() {
  return localStorage.getItem(RESERVER_TOKEN_NAME);
}
export function setReserverToken(token) {
  return localStorage.setItem(RESERVER_TOKEN_NAME, token);
}
export function removeReserverToken() {
  return localStorage.removeItem(RESERVER_TOKEN_NAME);
}

export function getHxdiframeworkToken() {
  return localStorage.getItem(FRAMEWORK_TOKEN_NAME);
}
export function setHxdiframeworkToken(token) {
  return localStorage.setItem(FRAMEWORK_TOKEN_NAME, token);
}
export function removeHxdiframeworkToken() {
  return localStorage.removeItem(FRAMEWORK_TOKEN_NAME);
}
