<?xml version="1.0" encoding="UTF-8"?>
<!--
  Licensed to the Apache Software Foundation (ASF) under one
  or more contributor license agreements.  See the NOTICE file
  distributed with this work for additional information
  regarding copyright ownership.  The ASF licenses this file
  to you under the Apache License, Version 2.0 (the
  "License"); you may not use this file except in compliance
  with the License.  You may obtain a copy of the License at

    http://www.apache.org/licenses/LICENSE-2.0

  Unless required by applicable law or agreed to in writing,
  software distributed under the License is distributed on an
  "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY
  KIND, either express or implied.  See the License for the
  specific language governing permissions and limitations
  under the License.
-->

<plugin xmlns="http://apache.org/cordova/ns/plugins/1.0"
    xmlns:android="http://schemas.android.com/apk/res/android"
    id="cordova-plugin-file-transfer"
    version="2.0.0-dev">
    <name>File Transfer</name>
    <description>Cordova File Transfer Plugin</description>
    <license>Apache 2.0</license>
    <keywords>cordova,file,transfer</keywords>
    <repo>https://github.com/apache/cordova-plugin-file-transfer</repo>
    <issue>https://github.com/apache/cordova-plugin-file-transfer/issues</issue>

    <dependency id="cordova-plugin-file" version=">=5.0.0" />

    <js-module src="www/FileTransferError.js" name="FileTransferError">
        <clobbers target="window.FileTransferError" />
    </js-module>

    <js-module src="www/FileTransfer.js" name="FileTransfer">
        <clobbers target="window.FileTransfer" />
    </js-module>

    <!-- android -->
    <platform name="android">
        <config-file target="res/xml/config.xml" parent="/*">
            <feature name="FileTransfer" >
                <param name="android-package" value="org.apache.cordova.filetransfer.FileTransfer"/>
            </feature>
        </config-file>

        <config-file target="AndroidManifest.xml" parent="/*">
            <uses-permission android:name="android.permission.WRITE_EXTERNAL_STORAGE" />
        </config-file>

        <source-file src="src/android/FileTransfer.java" target-dir="src/org/apache/cordova/filetransfer" />
        <source-file src="src/android/FileProgressResult.java" target-dir="src/org/apache/cordova/filetransfer" />
        <source-file src="src/android/FileUploadResult.java" target-dir="src/org/apache/cordova/filetransfer" />
    </platform>

    <!-- ios -->
    <platform name="ios">
        <config-file target="config.xml" parent="/*">
            <feature name="FileTransfer">
                <param name="ios-package" value="CDVFileTransfer" />
            </feature>
        </config-file>
        <header-file src="src/ios/CDVFileTransfer.h" />
        <source-file src="src/ios/CDVFileTransfer.m" />

        <framework src="AssetsLibrary.framework" />
    </platform>

    <!-- windows -->
    <platform name="windows">
        <js-module src="src/windows/FileTransferProxy.js" name="FileTransferProxy">
            <runs />
        </js-module>
    </platform>

    <!-- browser -->
    <platform name="browser">
        <js-module src="www/browser/FileTransfer.js" name="BrowserFileTransfer">
            <clobbers target="window.FileTransfer" />
        </js-module>
    </platform>
</plugin>
