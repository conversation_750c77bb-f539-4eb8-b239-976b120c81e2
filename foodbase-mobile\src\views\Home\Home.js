const navItems = [
  {
    name: '库存查询',
    route: { name: 'StockStatistics' },
    id: '1',
    permission: ['app-stock-statistics'],
    bgPosition: 0,
  },
  {
    name: '库存概况',
    route: { name: 'StockStatisticsAnhui' },
    id: '1.1',
    permission: ['app-stock-statistics-anhui'],
    bgPosition: 0,
  },
  {
    name: '库点分布',
    route: { name: 'StorePointList' },
    id: '2',
    permission: ['app-store-point-list'],
    bgPosition: -1 * 48,
  },
  {
    name: '仓容分布',
    route: { name: 'HouseCapacityDistribution' },
    id: '3',
    permission: ['app-storehouse-capacity-list'],
    bgPosition: -2 * 48,
  },
  {
    name: '视频监控',
    route: { name: 'VideoMonitor' },
    id: '4',
    permission: ['app-video-monitor'],
    bgPosition: -3 * 48,
  },
  {
    name: '储粮信息',
    route: { name: 'FoodInfomationAnhui' },
    id: '16.1',
    permission: ['app-food-infomation-anhui'],
    bgPosition: -21 * 48,
  },
  {
    name: '收购管理',
    route: { name: 'PurchasesManage' },
    id: '5',
    permission: ['app-purchases-manage'],
    bgPosition: -4 * 48,
    children: [
      {
        name: '订单清册',
        route: { name: 'OrderPublicList' },
        id: '5-1',
        permission: ['app-order-public-list'],
        bgPosition: -9 * 48,
      },
      {
        name: '收购进度',
        route: { name: 'PurchasesProgress' },
        id: '5-2',
        permission: ['app-purchases-progress'],
        bgPosition: -10 * 48,
      },
      {
        name: '订单查询',
        route: { name: 'OrderSearch' },
        id: '5-3',
        permission: ['app-order-search'],
        bgPosition: -8 * 48,
      },
    ],
  },
  {
    name: '计划审批',
    route: { name: 'JobApprovePlan' },
    id: '10.1',
    permission: ['app-job-approve-plan'],
    bgPosition: -14 * 48,
  },
  {
    name: '单证审批',
    route: { name: 'JobApproveTicket' },
    id: '10.2',
    permission: ['app-job-approve-ticket'],
    bgPosition: -12 * 48,
  },
  {
    name: '智能出入库',
    route: { name: 'InoutManage' },
    id: '6',
    permission: ['app-inout-manage'],
    bgPosition: -5 * 48,
    children: [
      {
        name: '登记管理',
        route: { name: 'CheckInOut' },
        id: '6-0',
        permission: ['app-check-in-out'],
        bgPosition: -14 * 48,
      },
      {
        name: '作业调度',
        route: { name: 'JobScheduling' },
        id: '6-4',
        permission: ['app-job-scheduling'],
        bgPosition: -15 * 48,
      },
      {
        name: '扦样管理',
        route: { name: 'TakingSample' },
        id: '6-4',
        permission: ['app-taking-sample'],
        bgPosition: -16 * 48,
      },
      // {
      //   name: '新扦样管理',
      //   route: { name: 'newTakingSample' },
      //   id: '6-4',
      //   permission: [''],
      //   bgPosition: -15 * 48,
      // },
      {
        name: '化验质检',
        route: { name: 'SampleInspection' },
        id: '6-4',
        permission: ['app-sample-inspection'],
        bgPosition: -17 * 48,
      },
      {
        name: '称重检斤',
        route: { name: 'CheckingWeight' },
        id: '6-2',
        permission: ['app-checking-weight'],
        bgPosition: -18 * 48,
      },
      {
        name: '值仓管理',
        route: { name: 'HouseOperation' },
        id: '6-3',
        permission: ['app-house-operation'],
        bgPosition: -19 * 48,
      },
      {
        name: '结算管理',
        route: { name: 'SettlementManage' },
        id: '6-4',
        permission: ['app-settlement-management'],
        bgPosition: -20 * 48,
      },
      {
        name: '预约查询',
        route: { name: 'AppointmentList' },
        id: '6-1',
        permission: ['app-appointment-list'],
        bgPosition: -12 * 48,
      },
    ],
  },
  {
    name: '风险提示',
    route: { name: 'RiskList' },
    id: '7',
    permission: ['app-risk-list'],
    bgPosition: -6 * 48,
  },
  {
    name: '智慧储粮监管',
    route: { name: 'FoodInfomation' },
    id: '8',
    permission: ['app-subfood-infomation'],
    bgPosition: -7 * 48,
    children: [
      {
        name: '粮情信息',
        route: { name: 'FoodInfomation' },
        id: '8-1',
        permission: ['app-subfood-infomation'],
        bgPosition: -14 * 48,
      },
      {
        name: '健康指数',
        route: { name: 'HealthIndex' },
        id: '8-2',
        permission: ['app-health-index'],
        bgPosition: -14 * 48,
      },
      {
        name: '预告警日志',
        route: { name: 'warninglog' },
        id: '8-1',
        permission: ['app-warning-log'],
        bgPosition: -14 * 48,
      },
    ],
  },
  {
    name: '粮情信息',
    route: { name: 'FoodInfomation' },
    id: '16',
    permission: ['app-food-infomation'],
    bgPosition: -21 * 48,
  },
  {
    name: '智能仓储作业',
    route: { name: 'IntelligentStorage' },
    id: '9',
    permission: ['app-intelligent-storage'],
    bgPosition: -7 * 48,
    children: [
      {
        name: '通风记录',
        route: { name: 'VentilationRecord' },
        id: '8-1',
        permission: ['app-ventilation-record'],
        bgPosition: -14 * 48,
      },
      {
        name: '控温记录',
        route: { name: 'TemperatureRecord' },
        id: '8-2',
        permission: ['app-temperature-record'],
        bgPosition: -14 * 48,
      },
      {
        name: '气调日志',
        route: { name: 'AirconditioningLog' },
        id: '8-3',
        permission: ['app-airconditioning-log'],
        bgPosition: -14 * 48,
      },
      {
        name: '作业申请',
        route: { name: 'HomeWorkApply' },
        id: '8-3',
        permission: ['app-home-work-apply'],
        bgPosition: -14 * 48,
      },
    ],
  },
  {
    name: '应急企业',
    route: { name: 'firmEnterprise' },
    id: '18',
    permission: ['app-firm-enterprise'],
    bgPosition: -25 * 48,
  },
  {
    name: '流通监测',
    route: { name: 'circulationMonitor' },
    id: '19',
    permission: ['app-circulation-monitor'],
    bgPosition: -28 * 48,
  },
  {
    name: '业务审批',
    route: { name: 'JobApprove' },
    id: '10',
    permission: ['app-job-approve'],
    bgPosition: -21 * 48,
  },
  {
    name: '预警处理',
    route: { name: 'EarlyWarningApproval' },
    id: '11',
    permission: ['app-early-warning-approval'],
    bgPosition: -24 * 48,
  },
  {
    name: '监管检查',
    route: { name: 'LawEnforcementSupervisionAndInspection' },
    id: '12',
    permission: ['app-law-enforcement-supervision-and-inspection'],
    bgPosition: -22 * 48,
  },
  {
    name: '执法督察',
    route: { name: 'LawEnforcementSupervision' },
    id: '12',
    permission: ['app-law-enforcement-supervision'],
    bgPosition: -22 * 48,
  },
  {
    name: '安全生产管理',
    route: { name: 'SafetyProductionManagement' },
    id: '13',
    permission: ['app-safety-production-management'],
    bgPosition: -23 * 48,
  },
  {
    name: '系统公告',
    route: { name: 'SystemNotices' },
    id: '14',
    permission: ['app-system-notices'],
    bgPosition: -26 * 48,
  },
  {
    name: '空仓验收',
    route: { name: 'EmptyStoreCheck' },
    id: '15',
    permission: ['empty-store-check'],
    bgPosition: -27 * 48,
  },
  {
    name: '投诉意见',
    route: { name: 'ComplaintOpinion' },
    id: '16',
    permission: ['app-complaint-opinion'],
    bgPosition: -28 * 48,
  },
];

export { navItems };
