<template>
  <van-picker
    :columns="originData"
    :columns-field-names="fieldNames"
    @cancel="onCancel"
    @confirm="onConfirm"
  ></van-picker>
</template>

<script>
import { Picker } from 'vant';
import { mapState } from 'vuex';
import { findAvailableStoreHouseV2 } from '@/api/in-out-manage';

export default {
  name: 'HouseSelect',
  inheritAttrs: false,
  emits: ['confirm', 'cancel'],
  props: {
    foodCategoryId: {
      type: [String, Number],
      default: undefined,
    },
    isOutOfStandard: {
      type: String,
      default: null,
    },
  },
  components: {
    'van-picker': Picker,
  },
  created() {
    this.getStoreHouseList();
  },
  data() {
    return {
      originData: [],
      fieldNames: {
        text: 'name',
        value: 'id',
      },
    };
  },
  computed: {
    ...mapState({
      user: (state) => state.user.info,
    }),
  },
  methods: {
    async getStoreHouseList() {
      const dept = this.user?.dept;
      this.originData = [];
      this.originData = await findAvailableStoreHouseV2(
        dept?.id,
        this.foodCategoryId ? this.foodCategoryId : null,
        this.isOutOfStandard,
      );
    },
    onCancel() {
      this.$emit('cancel');
    },
    onConfirm(value) {
      this.$emit('confirm', value);
    },
  },
  watch: {
    foodCategoryId(newValue, oldValue) {
      if (newValue != oldValue) {
        this.$nextTick(() => {
          this.getStoreHouseList();
        });
      }
    },
    isOutOfStandard(newValue, oldValue) {
      if (newValue != oldValue) {
        this.$nextTick(() => {
          this.getStoreHouseList();
        });
      }
    },
  },
};
</script>
