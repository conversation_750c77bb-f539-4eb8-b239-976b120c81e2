<template>
  <div>
    <div class="plan-detail">
      <p class="title">
        <span> {{ title }}</span
        ><strong style="color: #f09235">{{ totalNum }}吨</strong>
      </p>
      <div class="detail">
        <div v-for="item in categoryList" :key="item.category">
          {{ item.category }}：{{ item.value }}吨
        </div>
      </div>
      <div class="time">
        <p><span class="label">竞价时间：</span>{{ outDetail.biddingTime }}</p>
        <p><span class="label">轮换开始时间：</span>{{ outDetail.startTime }}</p>
        <p><span class="label">轮换结束时间：</span>{{ outDetail.endTime }}</p>
      </div>
    </div>
    <div class="plan-info">
      <Tabs
        v-model="active"
        animated
        title-active-color="#323233"
        title-inactive-color="#686B73"
        line-height="5px"
      >
        <Tab title="申请信息">
          <div class="type-title" style="border-top: 1px solid #dddddd">
            <img src="@/assets/icon-rotation-in.png" />
            <p>轮入方案</p>
          </div>
          <div v-for="item in outList" :key="item.level">
            <rotationItem :rotation-type="'bidInvitingPlan'" :item="item" class="rotation-item" />
          </div>
          <approvalButton :id="params?.id" coder="rotationScheme"></approvalButton>
        </Tab>
        <Tab title="审批流程">
          <approvalProcess :id="params?.id" coder="rotationScheme"></approvalProcess>
        </Tab>
      </Tabs>
    </div>
  </div>
</template>

<script setup>
import { Tab, Tabs } from 'vant';
import { ref, onMounted, computed } from 'vue';
import rotationItem from './common/rotation-item';
import approvalProcess from './common/approval-process';
import approvalButton from './common/approval-button';
import { useRoute } from 'vue-router';
import { getSchemeDetailById } from '@/api/job-approve';

const route = useRoute();

const title = ref(route.query.name);

const params = ref(route.params);

const active = ref(1);

const outDetail = ref({});

const outList = ref([]);

const categoryList = ref([]);

const getDetail = async () => {
  const { list, ...res } = await getSchemeDetailById({ id: params.value.id });

  outList.value = list.map((it) => {
    return {
      ...it,
      count: Number(it.allocated || 0)?.toFixed(3),
    };
  });

  outDetail.value = res;

  categoryList.value = outDetail.value.total
    .map((it) => {
      return {
        category: it.category,
        value: it.total.toFixed(3),
      };
    })
    .filter((it) => it.category !== '总计');
};

const totalNum = computed(() => {
  return (
    outDetail.value.total?.find((item) => item.category == '总计')?.total.toFixed(3) || '0.000'
  );
});

onMounted(() => {
  getDetail();
});
</script>

<style lang="scss" scoped>
.plan-detail {
  padding: 16px;
  background: #fff;
  color: #232323;
  .title {
    font-size: 16px;
    font-weight: bold;
    display: flex;
    align-items: center;
    justify-content: space-between;
  }
  .detail {
    border-radius: 4px;
    background: #f4f8ff;
    margin-top: 16px;
    padding: 10px 12px;
    display: flex;
    justify-content: space-between;
    flex-wrap: wrap;
    padding-top: 12px;
  }
  .time {
    margin-top: 5px;
    p {
      font-size: 16px;
      color: #232323;
      margin-top: 12px;

      .label {
        color: #686b73;
        width: 120px;
        display: inline-block;
      }
    }
  }
}
.plan-info {
  margin-top: 10px;
  .type-title {
    display: flex;
    align-items: center;
    border-bottom: 1px solid #dddddd;
    background: #fff;
    height: 54px;
    img {
      width: 20px;
      height: 20px;
      margin: 0 8px 0 16px;
    }
    p {
      font-weight: bold;
    }
  }
  .rotation-item {
    margin-bottom: 10px;
    background: #fff;
    padding: 16px;
  }
}
</style>
