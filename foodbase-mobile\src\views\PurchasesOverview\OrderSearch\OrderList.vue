<template>
  <div class="order-list">
    <div class="query-form">
      <div class="order-total">共{{ list.length }}个订单</div>
      <Row gutter="8">
        <Col span="8">
          <AreaPicker v-model:value="queryForm.areaCode" all-area-select />
        </Col>
        <Col span="8">
          <HPicker :options="orderTypeOptions" v-model:value="queryForm.orderType"></HPicker>
        </Col>
        <Col span="8">
          <HYearPicker v-model:value="queryForm.year" />
        </Col>
      </Row>
    </div>
    <List
      ref="listRef"
      v-model:loading="loading"
      :finished="finished"
      finished-text="没有更多了"
      @load="onLoad"
    >
      <EmptyHolder v-if="list.length === 0 && finished" />
      <div class="list" v-else>
        <HCard v-for="item in list" :key="item.id" :title="item.acquisitionUnitName">
          <template #header-extra>
            <Tag :color-type="item.tagColor">{{ item.orderType }}</Tag>
          </template>
          <div class="order-detail">
            <div class="data-item">
              <HFixedNumber :fraction-digits="0" class="value">
                {{ item.allocatedAcreage }}
              </HFixedNumber>
              <div class="name">订单面积(亩)</div>
            </div>
            <div class="data-item">
              <HFixedNumber :fraction-digits="0" class="value">
                {{ item.orderQuantityD }}
              </HFixedNumber>
              <div class="name">订单数量(吨)</div>
            </div>
            <div class="data-item">
              <HFixedNumber :fraction-digits="0" class="value">
                {{ item.ordersSoldD }}
              </HFixedNumber>
              <div class="name">订单收购数量(吨)</div>
            </div>
            <div class="data-item">
              <span class="value">
                <HFixedNumber :fraction-digits="item.orderProgressD >= 100 ? 0 : 1">
                  {{ item.orderProgressD }}
                </HFixedNumber>
                %
              </span>
              <div class="name">订单收购进度</div>
            </div>
          </div>
          <div class="actions">
            <Button round plain type="primary" @click="showDetail(item)">查看详情</Button>
          </div>
        </HCard>
      </div>
    </List>
  </div>
</template>

<script setup>
import { reactive, ref, watch, computed } from 'vue';
import { List, Button, Row, Col } from 'vant';
import { HCard, HFixedNumber } from '@/components';
import Tag from '@/views/PurchasesOverview/common/Tag';
import HPicker from '@/components/HPicker/HPicker';
import EmptyHolder from '@/views/common/EmptyHolder';
import HYearPicker from '@/components/HYearPicker/HYearPicker';
// import { getOrderList } from '@/api/order-search';
import { getInfoZZD } from '@/api/order-search';
import { useRouter } from 'vue-router';
import AreaPicker from '@/views/common/AreaPicker';
import { useStore } from 'vuex';

const props = defineProps({
  foodCategory: Array,
});

const router = useRouter();
const store = useStore();

const queryForm = reactive({
  areaCode: store.getters['user/userAreaCode'],
  orderType: '',
  year: String(new Date().getFullYear()),
});

const pagination = reactive({
  page: 0,
  size: 9999,
  total: 0,
});

const orderTypeOptions = [
  { value: '', text: '全部' },
  { value: '1', text: '省订单' },
  { value: '2', text: '市订单' },
  { value: '3', text: '县订单' },
];
const deptId = computed(() => {
  return store.state.user?.reserverIds?.deptId;
});
const level = computed(() => {
  return store.state.user?.reserverIds?.level;
});
const listRef = ref();
const list = ref([]);
const loading = ref(false);
const finished = ref(false);

const onLoad = async () => {
  const { items, page, total } = await getInfoZZD({
    page: pagination.page + 1,
    size: 9999,
    typeList: [1, 2, 3],
    deptId: deptId.value,
    level: level.value,
    // categoryIds: categoryId.value || props.foodCategory,
    categoryIds: props.foodCategory,
    ...queryForm,
  });

  list.value.push(...items);
  pagination.page = page;
  pagination.total = total;
  finished.value = list.value.length >= total;
  loading.value = false;
};

watch(
  queryForm,
  () => {
    pagination.page = 0;
    pagination.total = 0;
    list.value = [];
    finished.value = false;
    listRef.value?.check();
  },
  { deep: true },
);

const showDetail = (record) => {
  router.push({ name: 'DetailOrderList', query: { flowOrderInfoId: record.flowOrderInfoId } });
};
</script>

<style scoped lang="scss">
.h-card {
  margin-bottom: 16px;
}

.query-form {
  padding: 16px;
}

.order-total {
  font-size: 18px;
  font-weight: 500;
  line-height: 25px;
  margin-bottom: 8px;
}

.order-detail {
  display: flex;
  padding: 20px 0;

  .data-item {
    flex: 1;
    display: flex;
    flex-direction: column;
    align-items: center;

    .value {
      font-size: 22px;
      font-weight: 500;
      line-height: 30px;
      text-align: center;
    }

    .name {
      width: 4.5em;
      font-size: 18px;
      color: #686b73;
      line-height: 25px;
      text-align: center;
    }
  }
}
.actions {
  --van-button-normal-font-size: 18px;
  --van-button-default-height: 36px;
  display: flex;
  padding: 0 16px 16px 16px;

  .van-button {
    margin-left: auto;
  }
}
</style>
