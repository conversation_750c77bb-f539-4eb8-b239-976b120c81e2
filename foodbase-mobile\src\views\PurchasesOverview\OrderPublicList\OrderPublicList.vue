<template>
  <div class="order-public">
    <div class="query-form">
      <Row gutter="8">
        <Col span="8">
          <HYearPicker v-model:value="year" />
        </Col>
        <Col span="8">
          <AllUnitSelect
            v-model:value="purchasingStation"
            :isCompanyUser="isCompanyUser"
            :userId="userId"
            @confirm="onConfirm"
          />
        </Col>
      </Row>
    </div>
    <PullRefresh @refresh="onRefresh" v-model="refreshLoading">
      <List
        ref="listRef"
        v-model:loading="isLoading"
        :finished="finished"
        finished-text="没有更多了"
        @load="getList"
      >
        <EmptyHolder v-if="list.length === 0 && finished" />
        <div v-else>
          <HCard
            class="detail-card"
            v-for="(order, index) in list"
            :key="index"
            @click="goToDetail(order)"
          >
            <div class="header">
              <div>
                {{ order.harvestYear }}年
                {{ order.purchasingStationName }}
                {{ order.foodCategoryName }}订单清册
              </div>
            </div>
            <div class="content">
              <div class="summary">
                <div class="item">
                  <SvgIcon name="farmer" />
                  <span>农户数量：</span>
                  <span>{{ order.farmerCount }}</span>
                </div>
                <div class="item">
                  <SvgIcon name="fields" />
                  <span>农业核定面积（亩）：</span>
                  <HFixedNumber :fraction-digits="3">
                    {{ order.cultivatedAcreageVerification }}
                  </HFixedNumber>
                </div>
                <div class="item">
                  <SvgIcon name="fields" />
                  <span>订单面积（亩）：</span>
                  <HFixedNumber :fraction-digits="3">
                    {{ order.allocatedCultivatedAcreage }}
                  </HFixedNumber>
                </div>
                <div class="item">
                  <SvgIcon name="sum" />
                  <span>订单数量（吨）：</span>
                  <HFixedNumber :ratio="1" :fraction-digits="3">
                    {{ order.allocatedGrowingNumber }}
                  </HFixedNumber>
                </div>
              </div>
            </div>
          </HCard>
        </div>
      </List>
    </PullRefresh>
  </div>
</template>

<script setup>
import { ref, reactive, computed, watch } from 'vue';
import { useStore } from 'vuex';
import { Row, Col, List, PullRefresh } from 'vant';
import { HCard, HFixedNumber } from '@/components';
import SvgIcon from '@/components/SvgIcon/SvgIcon';
import EmptyHolder from '@/views/common/EmptyHolder';
import HYearPicker from '@/components/HYearPicker/HYearPicker';
import AllUnitSelect from '@/views/PurchasesOverview/common/AllUnitSelect';
import { useRouter } from 'vue-router';
import { getOrderInventoryList } from '@/api/order-public';

const categoryMap = {
  1: ['110103001001000', '110103002001000'],
  2: ['110103002000000', '110103001002000'],
  3: ['110101005000000'],
};
const router = useRouter();
const props = defineProps({
  foodCategory: Number,
});
const store = useStore();
const deptId = computed(() => {
  return store.state.user?.reserverIds?.deptId;
});
const level = computed(() => {
  return store.state.user?.reserverIds?.level;
});
const isCompanyUser = computed(() => {
  const userOrgLevel = store.state.user?.reserverIds?.userOrgLevel;
  return [7, 8, 9].includes(userOrgLevel);
});
const userId = computed(() => {
  return store.state.user?.reserverIds?.userId;
});

const year = ref(String(new Date().getFullYear()));
const purchasingStation = ref('');
const list = ref([]);
const pagination = reactive({
  page: 0,
  size: 10,
  total: 0,
});
const isLoading = ref(false);
const finished = ref(false);
const refreshLoading = ref(false);

const onRefresh = () => {
  if (refreshLoading.value) {
    pagination.page = 0;
    pagination.total = 0;
    list.value = [];
    refreshLoading.value = false;
  }
  finished.value = false;
  isLoading.value = true;
  getList();
};
// 列表
const getList = async () => {
  const categoryIds = categoryMap[props.foodCategory];
  const { items, page, total } = await getOrderInventoryList({
    categoryIds: categoryIds,
    deptIdZZD: deptId.value,
    levelZZD: level.value,
    year: year.value,
    purchasingStation: purchasingStation.value,
    page: pagination.page + 1,
    size: pagination.size,
  });
  list.value.push(...items);
  pagination.page = page;
  pagination.total = total;
  // 加载状态结束
  isLoading.value = false;

  // 数据全部加载完成
  if (list.value.length >= total) {
    finished.value = true;
  }
};
const onConfirm = (value) => {
  purchasingStation.value = value;
};
const goToDetail = (row) => {
  router.push({
    name: 'DetailOrderPublicList',
    query: {
      categoryId: row.foodCategoryId,
      year: row.harvestYear,
      purchasingStation: row.purchasingStation,
      orderType: row.orderType,
    },
  });
};
watch(year, (newValue, oldValue) => {
  if (newValue != oldValue) {
    pagination.page = 0;
    pagination.total = 0;
    list.value = [];
    getList();
  }
});
watch(purchasingStation, (newValue, oldValue) => {
  if (newValue != oldValue) {
    pagination.page = 0;
    pagination.total = 0;
    list.value = [];
    getList();
  }
});
</script>

<style scoped lang="scss">
.query-form {
  margin: 8px 16px;
}
.detail-card {
  margin-bottom: 10px;
}

.header {
  padding: 15px 0;
  border-bottom: 1px solid var(--van-gray-4);
  text-align: center;
  color: var(--van-text-color);
  background-color: #ffffff;

  .title {
    font-size: 20px;
    font-weight: 500;
    line-height: 28px;
    margin-bottom: 8px;
  }

  .sub-title {
    font-size: 16px;
    font-weight: 500;
    color: var(--van-gray-6);
    line-height: 25px;
  }
}

.content {
  padding: 16px;
  color: var(--van-gray-6);
  font-size: 18px;
  background-color: #ffffff;

  .summary {
    font-weight: bold;
    color: var(--van-gray-7);
    line-height: 32px;
    margin-bottom: 8px;

    .item {
      display: flex;
      align-items: center;

      .svg-icon {
        margin-right: 4px;
      }
    }
  }
}
</style>
