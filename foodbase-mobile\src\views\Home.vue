<template>
  <div class="home">
    <div class="banner"></div>
    <UnitName></UnitName>

    <HCard title="储备一本账" @header-click="onNavigate('StockOverview')">
      <template #header-extra>
        <Icon name="arrow" />
      </template>
      <StockVarietyStructure :category-type="1"></StockVarietyStructure>
    </HCard>

    <HCard title="收购服务一条龙" @header-click="onNavigate('PurchasesOverview')">
      <template #header-extra>
        <Icon name="arrow" />
      </template>
      <PurchasesProportion></PurchasesProportion>
    </HCard>

    <SupervisionResult></SupervisionResult>
  </div>
</template>

<script setup>
import { Icon } from 'vant';
import { HCard } from '@/components';
import { useRouter } from 'vue-router';
import StockVarietyStructure from '@/views/StockOverview/common/StockVarietyStructure';
import PurchasesProportion from '@/views/PurchasesOverview/common/PurchasesProportion';
import SupervisionResult from '@/views/SupervisionOverview/SupervisionResult';
import UnitName from '@/views/common/UnitName';
import { onMounted } from 'vue';
import { useStore } from 'vuex';

const router = useRouter();
const store = useStore();

onMounted(() => {
  store.dispatch('stock-overview/fetchData');
});

const onNavigate = (routeName, params = {}) => {
  router.push({ name: routeName, params });
};
</script>

<style scoped lang="scss">
.banner {
  height: 133px;
  background-image: url('../assets/bg-banner.png');
  background-size: cover;
  background-position: center;
  border-bottom: 4px solid #a95400;
}

.h-card {
  margin-bottom: 16px;
}
</style>
