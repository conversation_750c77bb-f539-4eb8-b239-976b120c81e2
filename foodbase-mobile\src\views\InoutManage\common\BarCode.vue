<template>
  <img id="barcode" />
</template>

<script setup>
import jsbarcode from 'jsbarcode';
import { Toast } from 'vant';
import { onMounted, watch } from 'vue';
// import axios from 'axios';
const props = defineProps({
  barcode: {
    type: String,
    default: () => '',
  },
  serialNo: {
    type: String,
    default: () => '',
  },
});
const generateBarCode = () => {
  if (props.barcode) {
    jsbarcode('#barcode', props.barcode, {
      displayValue: true,
      width: 2,
      text: props.serialNo + '   ' + props.barcode,
    });
  }
};
const downloadBarcode = () => {
  const element = document.getElementById('barcode');
  const src = element.getAttribute('src');
  window.cordova?.base64ToGallery(
    src,
    {
      prefix: 'img_',
      mediaScanner: true,
    },

    function (path) {
      Toast.success('保存成功');
      console.log(path, 'true');
    },

    function (err) {
      Toast.fail('保存失败');
      console.error(err, '2222222false2222222');
    },
  );
  console.log(src, 'src---------BarCode');
};
onMounted(() => {
  generateBarCode();
});
watch(
  () => props.barcode,
  (value, preValue) => {
    if (value != preValue) {
      generateBarCode();
    }
  },
);

defineExpose({ downloadBarcode });
</script>

<style lang="scss" scoped>
@media print {
  #barcode {
    zoom: 0.75;
  }
}
</style>
