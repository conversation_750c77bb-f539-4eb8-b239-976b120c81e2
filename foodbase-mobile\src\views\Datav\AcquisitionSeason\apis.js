import { hrpsRequest } from '@/utils/request';

// eslint-disable-next-line no-unused-vars
import { ref, onMounted } from 'vue';
import dayjs from 'dayjs';
import { getPurchaseCategory } from '@/api/common';

const time = dayjs();

const getPurchaseCategoryFn = async () => {
  return await getPurchaseCategory();
};

const getAcquisitionData = async (foodCategoryId, userInfo) => {
  const params = {
    todayStr: time.format('YYYY-MM-DD'),
    deptPath: userInfo?.dept?.path,
    foodCategoryId: foodCategoryId,
    areaCode: userInfo?.provinceCode,
  };
  return await hrpsRequest().get('/hrps/mapOverview/acquisition', { params });
};

const getAcquisitionPlan = async (foodCategoryId, userInfo, year) => {
  const params = {
    year: year,
    todayStr: time.format('YYYY-MM-DD'),
    deptPath: userInfo?.dept?.path,
    foodCategoryId: foodCategoryId,
    areaCode: userInfo?.provinceCode,
  };
  return await hrpsRequest().get('/hrps/acquisitionPlan/statisticData', { params });
};

const getOrderContract = async (foodCategoryId, userInfo, year) => {
  const params = {
    year: year,
    todayStr: time.format('YYYY-MM-DD'),
    deptPath: userInfo?.dept?.path,
    foodCategoryId: foodCategoryId,
    areaCode: userInfo?.provinceCode,
    deptLevel: userInfo?.dept?.level,
  };
  return await hrpsRequest().get('/hrps/orderContract/statisticData', { params });
};

const getGrainSalest = async (foodCategoryId, userInfo) => {
  const params = {
    todayStr: time.format('YYYY-MM-DD'),
    deptPath: userInfo?.dept?.path,
    foodCategoryId: foodCategoryId,
  };
  return await hrpsRequest().get('/hrps/grainSales/statisticData', { params });
};

const getGrainSettlement = async (foodCategoryId, userInfo) => {
  const params = {
    todayStr: time.format('YYYY-MM-DD'),
    deptPath: userInfo?.dept?.path,
    foodCategoryId: foodCategoryId,
  };
  return await hrpsRequest().get('/hrps/grainSettlement/statisticData', { params });
};

export {
  getPurchaseCategoryFn,
  getAcquisitionData,
  getAcquisitionPlan,
  getOrderContract,
  getGrainSalest,
  getGrainSettlement,
};
