<template>
  <div class="watch-reported-record">
    <Row>
      <Col :span="24" v-if="props.status === 2">
        <Search v-model="date" placeholder="选择日期区间" readonly @click="datePicker = true" />
      </Col>
      <Col :span="24">
        <Search
          v-model="watchPointName"
          placeholder="请选择监测点名称"
          readonly
          @click-input="showWatchPointPicker = true"
          @cancel="clearStoreHouse"
        >
          <template #right-icon>
            <div class="close-action" @click="clearWatchPoint" v-if="watchPointName">
              <Icon name="clear" style="color: #c8c9cc" />
            </div>
          </template>
        </Search>
      </Col>
      <Col :span="24">
        <Search
          v-model="areaName"
          placeholder="请选择监测地区"
          readonly
          @click-input="showWatchAreaPicker = true"
          @cancel="clearStoreHouse"
        >
          <template #right-icon>
            <div class="close-action" @click="clearArea" v-if="areaName">
              <Icon name="clear" style="color: #c8c9cc" />
            </div>
          </template>
        </Search>
      </Col>
    </Row>
    <Calendar
      allow-same-day
      v-model:show="datePicker"
      first-day-of-week="1"
      type="range"
      :min-date="minDate"
      :default-date="[new Date(beginDate), new Date(endDate)]"
      @confirm="onConfirmDate"
    />
    <PullRefresh @refresh="onRefresh" v-model="refreshLoading">
      <List
        ref="listRef"
        v-model:loading="isLoading"
        :finished="finished"
        finished-text="没有更多了"
        @load="getList"
      >
        <HCard class="detail-card" v-for="item in list" :key="item" @click="handleDetail(item)">
          <div class="card-content">
            <div class="row">
              <span class="label">上报地区：</span>
              <span class="value">{{ item.areaName }}</span>
            </div>
            <div class="row">
              <span class="label">上报监测点：</span>
              <span class="value">{{ item.monitorPointName }}</span>
            </div>
            <div class="row">
              <span class="label">上报人：</span>
              <span class="value">{{ item.reportedPerson }}</span>
            </div>
            <div class="row">
              <span class="label">上报时间：</span>
              <span class="value">{{ item.monitorTime }}</span>
            </div>
            <div class="row">
              <span class="label">状态：</span>
              <span class="value">{{ item.publishStatus === 1 ? '已发布' : '未发布' }}</span>
            </div>
          </div>
          <div class="card-action" v-if="props.status === 1">
            <Button
              type="primary"
              v-p="['app-watch-reported-record:check']"
              size="small"
              :disabled="item.publishStatus === 1"
              @click.stop="handleDetail(item)"
              >审核</Button
            >
            <Button
              type="primary"
              size="small"
              v-p="['app-watch-reported-record:publish']"
              style="margin-left: 10px"
              :disabled="item.publishStatus === 1"
              @click.stop="handlePublish(item)"
              >发布</Button
            >
          </div>
          <div class="card-action" v-if="props.status === 2">
            <Button
              type="primary"
              v-p="['app-watch-reported-record:check']"
              size="small"
              @click.stop="handleDetail(item)"
              >详情</Button
            >
          </div>
        </HCard>
      </List>
    </PullRefresh>
    <ActionSheet v-model:show="showWatchPointPicker" title="监测点">
      <Picker
        :columns="watchPointOptions"
        :columns-field-names="fieldNames"
        @cancel="showWatchPointPicker = false"
        @confirm="onConfirmWatchPoint"
      ></Picker>
    </ActionSheet>
    <ActionSheet v-model:show="showWatchAreaPicker" title="监测地区">
      <areaCascader
        :value="areaCode"
        title="请选择监测地区"
        active-color="#1989fa"
        :change-on-select="true"
        :is-edit-city="true"
        @close="showWatchAreaPicker = false"
        @change="onChange"
        @finish="onConfirmArea"
      />
    </ActionSheet>
  </div>
</template>

<script setup>
import {
  Search,
  List,
  PullRefresh,
  Row,
  Col,
  ActionSheet,
  Icon,
  Picker,
  Calendar,
  Button,
  Toast,
  Dialog,
} from 'vant';
import { ref, reactive, onMounted, watch } from 'vue';
import { HCard } from '@/components';
import areaCascader from './../common/area-cascader';
import dayjs from 'dayjs';
import {
  getWatchConfigSelect,
  getDataMaintainList,
  getDataMaintainRecord,
  publishDataMaintain,
} from '@/api/circulation-monitor';
import { useRouter } from 'vue-router';

const props = defineProps({
  status: {
    type: Number,
    default: null,
  },
});

const fieldNames = {
  text: 'name',
  value: 'id',
  children: 'children',
};

const router = useRouter();

const watchPointName = ref();
const watchPointId = ref();
const areaCode = ref();
const areaName = ref();

const minDate = new Date(2020, 0, 1);

const watchPointOptions = ref([]);

const weekday = dayjs(dayjs()).day();
const we = weekday === 0 ? 0 - 1 : 0;
const beginDate = ref(dayjs(dayjs()).add(we, 'week').startOf('week').add(1, 'day'));
const endDate = ref(dayjs(dayjs()).add(we, 'week').endOf('week').add(1, 'day'));
const date = ref(
  `${dayjs(beginDate.value).format('YYYY-MM-DD')} - ${dayjs(endDate.value).format('YYYY-MM-DD')}`,
);
const datePicker = ref(false);
const showWatchPointPicker = ref(false);
const showWatchAreaPicker = ref(false);

const pagination = reactive({
  page: 0,
  size: 10,
  total: 0,
});
const listRef = ref();
const isLoading = ref(false);
const finished = ref(false);
const refreshLoading = ref(false);

const list = ref([]);

const onConfirmWatchPoint = (value) => {
  watchPointName.value = value.name;
  watchPointId.value = value.id;
  showWatchPointPicker.value = false;
  refreshLoading.value = true;
  onRefresh();
};

const clearWatchPoint = () => {
  watchPointName.value = '';
  watchPointId.value = '';
  refreshLoading.value = true;
  onRefresh();
};

const getWatchPoitSelectList = async () => {
  watchPointOptions.value = await getWatchConfigSelect({
    isAll: 1,
  });
};

const onConfirmDate = (value) => {
  beginDate.value = value[0];
  endDate.value = value[1];
  date.value = `${dayjs(beginDate.value).format('YYYY-MM-DD')} - ${dayjs(endDate.value).format(
    'YYYY-MM-DD',
  )}`;
  datePicker.value = false;
  refreshLoading.value = true;
  onRefresh();
};

const onConfirmArea = (obj) => {
  areaCode.value = obj.value;
  areaName.value = obj.selectedOptions
    .map((i) => {
      return i.name;
    })
    ?.join('/');
  showWatchAreaPicker.value = false;
  refreshLoading.value = true;
  onRefresh();
};

const clearArea = () => {
  areaCode.value = '';
  areaName.value = '';
  refreshLoading.value = true;
  onRefresh();
};

const onRefresh = () => {
  if (refreshLoading.value) {
    pagination.page = 0;
    pagination.total = 0;
    list.value = [];
    refreshLoading.value = false;
  }
  finished.value = false;
  isLoading.value = true;
  getList();
};

const handleDetail = (item) => {
  router.push({
    name: 'WatchReportedRecordDetail',
    query: {
      status:props.status,
      monitorPointId: item.monitorPointId,
      monitorPointName: item.monitorPointName,
      monitorTime: item.monitorTime,
      areaName: item.areaName,
      fillStartTime: dayjs(beginDate.value).format('YYYY-MM-DD 00:00:00'),
      fillEndTime: dayjs(endDate.value).format('YYYY-MM-DD 23:59:59'),
      remark: item.remark
    },
  });
};

const handlePublish = async (item) => {
  Dialog.confirm({
    title: '是否确认发布？',
    cancelText: '取消',
    okText: '确定',
  })
    .then(async () => {
      await publishDataMaintain({
        monitorPointId: item.monitorPointId,
        fillStartTime: dayjs(beginDate.value).format('YYYY-MM-DD 00:00:00'),
        fillEndTime: dayjs(endDate.value).format('YYYY-MM-DD 23:59:59'),
      });
      Toast.success('发布成功');
      refreshLoading.value = true;
      onRefresh();
    })
    .catch(() => {});
};

const getList = async () => {
  try {
    let time = {};
    let api = '';
    if (props.status === 1) {
      time = {
        fillStartTime: dayjs(dayjs(dayjs()).add(we, 'week').startOf('week').add(1, 'day')).format(
          'YYYY-MM-DD 00:00:00',
        ),
        fillEndTime: dayjs(dayjs(dayjs()).add(we, 'week').endOf('week').add(1, 'day')).format(
          'YYYY-MM-DD 23:59:59',
        ),
      };
      api = getDataMaintainList;
    } else {
      time = {
        startTime: dayjs(beginDate.value).format('YYYY-MM-DD 00:00:00'),
        endTime: dayjs(endDate.value).format('YYYY-MM-DD 23:59:59'),
      };
      api = getDataMaintainRecord;
    }
    let obj = {
      ...time,
      monitorPointId: watchPointId.value,
      areaCode: areaCode.value,
      page: pagination.page + 1,
      size: pagination.size,
    };
    let { items, page, total } = await api(obj);
    items.forEach((item) => {
      item.areaName = (item.provinceName || '') + (item.cityName || '') + (item.countyName || '');
      return item;
    });

    list.value.push(...items);
    pagination.page = page;
    pagination.total = total;
    // 加载状态结束
    isLoading.value = false;

    // 数据全部加载完成
    if (list.value.length >= total) {
      finished.value = true;
    }
  } catch {
    isLoading.value = false;
    finished.value = true;
  }
};

watch(
  () => props.status,
  (newVal, val) => {
    if (val !== newVal) {
      refreshLoading.value = true;
      onRefresh();
    }
  },
);

onMounted(() => {
  getWatchPoitSelectList();
});
</script>

<style scoped lang="scss">
.detail-card {
  padding: 4px 16px 10px;
  margin-top: 10px;
}

.card-head {
  display: flex;
  align-items: center;
  justify-content: space-between;
  font-size: 18px;
  font-weight: bold;
  color: #1f3359;
}

.card-content {
  margin: 8px 0;
  line-height: 30px;

  .label {
    font-size: 14px;
    font-weight: 400;
    color: #6d748d;
  }

  .value {
    font-size: 14px;
    font-weight: 400;
    color: #0f0f0f;
  }
}

.card-action {
  display: flex;
  justify-content: flex-end;
}
</style>
