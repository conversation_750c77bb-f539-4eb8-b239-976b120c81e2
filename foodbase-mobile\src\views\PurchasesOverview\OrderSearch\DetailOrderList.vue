<template>
  <div class="detail-order-list">
    <div class="query-form">
      <div class="order-total">共{{ pagination.total }}个订单</div>
      <Row gutter="8">
        <Col span="24">
          <Field v-model="queryForm.farmer" placeholder="搜索农户" left-icon="search" />
        </Col>
        <!-- <Col span="12">
          <HPicker :options="farmerTypeOptions" v-model:value="queryForm.farmerType"></HPicker>
        </Col> -->
      </Row>
    </div>
    <List
      ref="listRef"
      v-model:loading="loading"
      :finished="finished"
      :finished-text="list.length > 0 ? '没有更多了' : null"
      @load="onLoad"
    >
      <EmptyHolder v-if="list.length === 0 && finished" />
      <div class="list" v-else>
        <HCard v-for="item in list" :key="item.farmerName" :title="item.farmerName">
          <template #header-extra>
            <Tag :color-type="Number(item.tagColor)">{{ item.typeName }}</Tag>
          </template>
          <div class="order-detail">
            <div class="data-item">
              <HFixedNumber :fraction-digits="0" class="value">{{
                item.allocatedCultivatedAcreage
              }}</HFixedNumber>
              <div class="name">订单面积(亩)</div>
            </div>
            <div class="data-item">
              <HFixedNumber :fraction-digits="0" class="value">{{
                item.allocatedGrowingNumber
              }}</HFixedNumber>
              <div class="name">订单数量(吨)</div>
            </div>
            <div class="data-item">
              <HFixedNumber :fraction-digits="0" class="value">{{
                item.completedNumber
              }}</HFixedNumber>
              <div class="name">投售数量(吨)</div>
            </div>
            <div class="data-item">
              <span class="value">
                <HFixedNumber :fraction-digits="item.acquisitionProgress >= 100 ? 0 : 3">
                  {{ item.acquisitionProgress }}
                </HFixedNumber>
                %
              </span>
              <div class="name">完成进度</div>
            </div>
          </div>
        </HCard>
      </div>
    </List>
  </div>
</template>

<script setup>
import { reactive, ref, watch } from 'vue';
import { Field, List, Row, Col } from 'vant';
import { HCard, HFixedNumber } from '@/components';
import Tag from '@/views/PurchasesOverview/common/Tag';
// import HPicker from '@/components/HPicker/HPicker';
import EmptyHolder from '@/views/common/EmptyHolder';
import { getDetailZZD } from '@/api/order-search';
import { useRoute } from 'vue-router';
// import { useStore } from 'vuex';

const tagColorMap = {
  种粮大户: 1,
  一般农户: 2,
  良种繁育户: 3,
  合作社: 4,
  家庭农场: 5,
};

const route = useRoute();
// const store = useStore();

// const farmerTypes = store.getters['dict/dictOf']('FARMER_TYPE');

// const farmerTypeOptions = [
//   { value: '', text: '全部' },
//   ...farmerTypes.map((it) => ({
//     value: it.value,
//     text: it.label,
//   })),
// ];

const queryForm = reactive({
  farmer: '',
});

const pagination = reactive({
  page: 0,
  size: 10,
  total: 0,
});

const listRef = ref();
const list = ref([]);
const loading = ref(false);
const finished = ref(false);

const onLoad = async () => {
  const { foodCategoryId, deptIdZZD, levelZZD, orderType, purchasingStation, year, id } =
    route.query;
  const { items, page, total } = await getDetailZZD({
    page: pagination.page + 1,
    categoryId: foodCategoryId,
    deptIdZZD,
    levelZZD,
    orderType,
    purchasingStation,
    year,
    planId: id,
    // flowOrderInfoId,
    ...queryForm,
  });
  if (items) {
    items.forEach((it) => {
      // it.percent = (it.tsNum / it.signNum) * 100 || 0;
      it.tagColor = tagColorMap[it.typeName];
      list.value.push(it);
    });
  } else {
    list.value = [];
  }

  pagination.page = page;
  pagination.total = total;
  finished.value = list.value.length >= total;
  loading.value = false;
};

watch(
  queryForm,
  () => {
    pagination.page = 0;
    pagination.total = 0;
    list.value = [];
    finished.value = false;
    listRef.value?.check();
  },
  { deep: true },
);
</script>

<style scoped lang="scss">
.h-card {
  margin-bottom: 16px;
}

.query-form {
  padding: 16px;
}

.order-total {
  font-size: 18px;
  font-weight: 500;
  line-height: 25px;
  margin-bottom: 8px;
}

.empty-holder {
  height: 200px;
}

.order-detail {
  display: flex;
  padding: 20px 0;

  .data-item {
    flex: 1;
    display: flex;
    flex-direction: column;
    align-items: center;

    .value {
      font-size: 18px;
      font-weight: 600;
      line-height: 30px;
      text-align: center;
    }

    .name {
      width: 4.5em;
      font-size: 18px;
      color: #686b73;
      line-height: 25px;
      text-align: center;
    }
  }
}
</style>
