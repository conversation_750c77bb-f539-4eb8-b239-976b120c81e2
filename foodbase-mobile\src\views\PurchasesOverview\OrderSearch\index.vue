<template>
  <div class="order-search">
    <Tabs v-model:active="active" swipeable @change="changeTab">
      <Tab :name="110101005000000" title="小麦">
        <component :is="listComponent" :food-category="foodCategory" />
      </Tab>
      <Tab :name="110103001001000" title="早稻">
        <component :is="listComponent" :food-category="foodCategory" />
      </Tab>
      <Tab :name="1" title="晚稻">
        <component :is="listComponent" :food-category="foodCategory" />
      </Tab>
    </Tabs>
  </div>
</template>

<script setup>
import { Tabs, Tab } from 'vant';
import { computed, ref } from 'vue';
import CompanyOrderList from '@/views/PurchasesOverview/OrderSearch/CompanyOrderList';
// import { useStore } from 'vuex';
// import OrderList from '@/views/PurchasesOverview/OrderSearch/OrderList';

const active = ref(110101005000000);
const foodCategory = ref([110101005000000]);
const changeTab = (val) => {
  if (val === 1) {
    foodCategory.value = [110103001002000, 110103002000000];
  } else {
    foodCategory.value = [val];
  }
};
// const store = useStore();

const listComponent = computed(() => {
  // const isCompanyUser = store.getters['user/isCompanyUser'];
  // return isCompanyUser ? CompanyOrderList : OrderList;
  return CompanyOrderList;
});
</script>

<style scoped></style>
