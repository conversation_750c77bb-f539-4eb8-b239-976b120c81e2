import { reserveRequest } from '@/utils/request';
import qs from 'qs';
import { pickBy } from 'lodash-es';

export async function getHouseOperationPaging(params) {
  return reserveRequest().get('/api/appv1/info4App', { params });
}

export async function reserverLogin(data) {
  return reserveRequest().post('/auth/login', data);
}
export async function getHouseOperationDetail(data) {
  return reserveRequest().post('/api/appv1/getDetail', data);
}

export async function getHouseOperationRecord(params) {
  return reserveRequest().get('/api/appv1/info/detail4App', { params });
}
export async function houseOperationSave(data) {
  return reserveRequest().post('/api/appv1/save', data);
}
// 保存值仓数据
export async function saveHouseOperation(data) {
  return reserveRequest().post('/api/dostorehouse/save', data);
}
// 完成值仓或终止值仓
export async function completeHouseOperation(data) {
  return reserveRequest().post('/api/dostorehouse/complete', data);
}
export async function houseOperationUpload(data) {
  return reserveRequest().post('/api/appv1/uploadStoreImg', data);
}

export async function getFileUrls(fileNames = []) {
  return reserveRequest().get('/api/appv1/getFileList', {
    params: {
      fileId: fileNames,
    },
  });
}

// 预约审核列表
export async function getAppointmentList(params) {
  return reserveRequest().get('/api/booking/audit/info', { params });
}

// 预约审核
export async function approvalAppointment(data) {
  return reserveRequest().post('/api/booking/audBatch', data);
}

// 入库登记根据预约号获取信息
export async function getBookingInfo(bookingNo) {
  return reserveRequest().post(
    '/api/store/registration/booking/infoNew',
    {},
    {
      params: { bookingNo },
    },
  );
}

// 生成登记序列号
export async function generateSerialNo(params) {
  return reserveRequest().get('/api/store/registration/generateSerialNo', { params });
}

// 生成调度号
export async function getSchedulingNo() {
  return reserveRequest().get('/api/jobScheduling/generateScheduleNO');
}

// 到库登记
export async function checkIn(data) {
  return reserveRequest().post('/api/store/registration', data);
}

// 获取离库车辆信息
export async function getCheckOutList(params) {
  return reserveRequest().get('/api/store/registration/getStoreSchedulingVehicleVOS', { params });
}

// 离库登记
export async function checkOut(data) {
  return reserveRequest().put('/api/store/registration', data);
}

// 登记记录
export async function getCheckInOutRecord(params) {
  let paramsData = qs.stringify(
    pickBy(params, (value) => value !== ''), // 排除空字符串
    { arrayFormat: 'repeat', skipNulls: true }, // 跳过 null 值
  );
  return reserveRequest().get(`/api/store/registration?${paramsData}`);
}

// 登记记录 调整记录
export async function UpdateCheckInOutDetail(data) {
  return reserveRequest().post('/api/store/registration/adjust', data);
}

// 登记记录 打印调度二维码
export async function getCheckInOutPrintingInfo(params) {
  return reserveRequest().get('/api/jobScheduling/printSchdulingInfo', { params });
}

// 调度列表
export async function getJobSchedulingList(params) {
  return reserveRequest().get('/api/jobScheduling', { params });
}

// 调度详情
export async function getJobSchedulingDetail(params) {
  return reserveRequest().get('/api/jobScheduling/getJobSchedulingDetail', { params });
}

// 获取新调度号
export async function getNewSchedulingNo(schedulingNo) {
  return reserveRequest().get('/api/jobScheduling/generateAddNo', {
    params: { schedulingNo },
  });
}

//油罐数据
export async function getListByCapacityCalculation(params) {
  return reserveRequest().get('/api/oil/getListByCapacityCalculation', {
    params,
  });
}

// 调度获取客户列表
export async function findCrkCustomerRecords(customerName, typer) {
  return reserveRequest().get('/api/customer/findCrkCustomerRecords', {
    params: { customerName, typer },
  });
}

// 调度获取作业凭证列表
export async function getVoucherList({ customerId, typer, voucherId }) {
  return reserveRequest().get('/api/donoticeDeportation/getNoticeDeportation', {
    params: { customerId, typer, voucherId },
  });
}

// 根据作业凭证返回获取信息
export function getFoodProperty(params) {
  return reserveRequest().get('/api/booking/getFoodProperty', { params });
}

// 获取品种
export async function getFoodCategory(categoryType) {
  return reserveRequest().get('/api/foodCategory/info', {
    params: {
      categoryType: categoryType,
    },
  });
}

// 获取烘干塔列表
export function getDryTowerList() {
  return reserveRequest().get('/api/info-dry-tower/infoEquipmentList', {
    params: {
      categoryType: 5,
    },
  });
}

// 获取库区可用车辆
export function getAllEnabledCar() {
  return reserveRequest().get('/api/carMaintenance/queryEnabled');
}

// 获取质检方案
export async function getInspectionSchemeList(foodCategoryId) {
  return reserveRequest().get('/api/info-testing-item-config/showItemConfigs', {
    params: { infoFoodCategoryId: foodCategoryId },
  });
}

// 查询可用的仓房包含虚拟仓（品种及空仓)
export function findAvailableStoreHouse(deptId, foodCategoryId) {
  return reserveRequest().get('/api/storeInfo/findAvailableStoreHouse', {
    params: { deptId, foodCategoryId },
  });
}
// 根据品种id查询库点下所有可用货位
export async function findAvailableStoreHouseCargoSpace(deptId, foodCategoryId) {
  return reserveRequest().get('/api/storeInfo/findAvailableStoreHouseCargoSpace', {
    params: { deptId, foodCategoryId },
  });
}
//查询可用的仓房包含虚拟仓（品种及空仓)
export function findAvailableStoreHouseV2(deptId, foodCategoryId, isOutOfStandard) {
  return reserveRequest().get('/api/storeInfo/findAvailableStoreHouseV2', {
    params: { deptId, foodCategoryId, isOutOfStandard },
  });
}
// 保存调度
export async function saveScheduling(data) {
  return reserveRequest().post('/api/jobScheduling/doJobSchedulings', data);
}

// 调度记录
export async function getJobSchedulingHistory(params) {
  return reserveRequest().get('/api/jobScheduling/jobSchedulingRecord', { params });
}

// 调度记录详情
export async function getSchedulingDetail(schedulingNo) {
  return reserveRequest().get('/api/jobScheduling/jobSchedulingDetail', {
    params: { schedulingNo },
  });
}

// 扦样列表
export async function getTakingSampleList(params) {
  return reserveRequest().get('/api/sampling', { params });
}

// 扦样记录
export async function getTakingSampleHistory(params) {
  return reserveRequest().get('/api/sampling/findSamplingsRecords', { params });
}

// 完成扦样
export async function savePrintedSampleCode(id) {
  return reserveRequest().put('/api/sampling', { id });
}

// 质检列表
export async function getInspectionJobs(params) {
  return reserveRequest().get('/api/qualityTesting', { params });
}

// 质检详情
export async function getQualityTestInfoForApp(params) {
  return reserveRequest().get('/api/qualityTesting/getQualityTestInfoForApp', { params });
}

// 获取地区
export async function getOriginPlace() {
  return reserveRequest().get('/api/foodFormAddr/info');
}

// 根据品种和质检方案id获取质检项
export async function getInspectionItem(foodCategoryId, schemeId) {
  return reserveRequest().get('/api/info-testing-item/getTestingItemList', {
    params: {
      configId: schemeId,
      foodCategoryId,
    },
  });
}

// 所有质检项
export async function getFoodInspectionItems(foodCategoryId) {
  return reserveRequest().get('/api/testingItemCategory/info', { params: { foodCategoryId } });
}

// 等级标准
export async function getGradingStandardOfFoodCategory(foodCategoryId) {
  return reserveRequest().get('/api/info-testing-item-grade/infoTestingItemGrade', {
    params: {
      foodCategoryId,
    },
  });
}

// 判断粮食品种是油还是粮食
export function judgeFoodCategory(params) {
  return reserveRequest().get('/api/storage/result/judgeFoodCategory', {
    params,
  });
}

// 扣量比例
export async function getCostValue(data) {
  return reserveRequest().post('/api/info-testing-item-cost/getCostComputeMultiValue', data);
}

// 增量比例
export async function getIncreaseValue(data) {
  return reserveRequest().post('/api/info-testing-item-cost/getIncreaseComputeMultiValue', data);
}

// 计算等级
export async function getFoodCategoryLevel(data) {
  return reserveRequest().post('/api/info-testing-item-grade/getTestingItemGrade', data);
}

// 质检保存或完成
export async function saveInspectionJob(data) {
  return reserveRequest().post('/api/qualityTesting/saveQualityMngResultTestItemDTO', data);
}

// 判断是否超标粮
export async function checkIsOverStandardFood(data) {
  return reserveRequest().post('/api/overProofItem/checkIsOverStandardFood', data);
}

// 质检记录列表
export async function getInspectionHistory(params) {
  return reserveRequest().get('/api/qualityTesting/findSampleFlowQualityTestingRecords', {
    params,
  });
}

// 检斤列表
export async function getWeighingList(params) {
  return reserveRequest().get('/api/weigh/info', { params });
}

// 检斤详情
export async function getWeighingDetail(params) {
  return reserveRequest().get('/api/weigh/info/app/detail', { params });
}

export async function addWeight(data) {
  return reserveRequest().post('/api/weigh/save/detail', data);
}

// 检斤记录列表
export async function getWeightHistory(params) {
  return reserveRequest().get('/api/weigh/info/effective', { params });
}

// 确认检斤单
export async function saveWeightTicket(data) {
  return reserveRequest().post('/api/weigh/info/print', data, {
    params: {
      save: 1,
    },
  });
}

//删除称重流水
export async function deleteWeighDetail(data) {
  return reserveRequest().post('/api/weigh/delete/detail', data);
}

export function addWight(params) {
  return reserveRequest().post('/api/weigh/save/detail', params);
}

// 获取所有地磅
export function getAllEnableWeighbridge() {
  return reserveRequest().get('/api/weighbridgeEquipment/queryEnabled');
}

// 获取结算列表
export async function getSettlementList(params) {
  return reserveRequest().get('/api/accounts/info', { params });
}

// 结算详情
export async function getSettlementDetail(data) {
  return reserveRequest().post('/api/accounts/info/settlement/detail', data);
}

// 完成结算
export async function saveSettlementTicket(data) {
  return reserveRequest().post('/api/accounts/voucher/temporary/save', data);
}

// 获取结算记录列表
export async function getSettlementHistory(params) {
  return reserveRequest().get('/api/accounts/info/record', { params });
}

// 结算审批
export function settlementApproval(data) {
  return reserveRequest().post('/api/accounts/approval', data);
}

// 获取指定类型功能开关
export function getTypeSwitch(params) {
  return reserveRequest().get('/api/storeJobCap/getStoreJobOnoff', { params });
}

// 获取网关配置
export function getAllEnableInstruction() {
  return reserveRequest().get('/api/instructions/queryEnabled');
}

// 判断是否值仓
export function isDoStoreHouse(params) {
  return reserveRequest().get('/api/dostorehouse/isDoStoreHouse', { params });
}

// 单车多次确认净重
export async function addTotalWeight(data) {
  return reserveRequest().post('/api/weigh/save/detailList', data);
}
// 单车单次确认净重
export async function confirmNetWeighSingle(data) {
  return reserveRequest().post('/api/weigh/confirmNetWeigh', data);
}

export async function confirmWeight(schedulingNo) {
  return reserveRequest().post('/api/weigh/confirm/weighing/result', {
    schedulingNo,
  });
}

export async function getStoreHouseOfUser(deptId) {
  return reserveRequest().get('/api/storeInfo/findStoreHouseList', {
    params: { deptId },
  });
}

//根据作业凭证号获取收购品种列表
export async function getPurchaseCategory(params) {
  return reserveRequest().get('/api/flowContractOrder/getPurchaseCategory', { params });
}

// 获取仓房信息
export function getStoreHouseStatus(params) {
  return reserveRequest().get('/api/accounts/info/storehouse/status', { params });
}
// 单项判定结果自动判断逻辑优化
export async function checkTestItemIsMatchGrade(data) {
  return reserveRequest().post('/api/info-testing-item-grade/checkTestItemIsMatchGrade', data);
}

// 保存或更新其他扣量
export function saveOtherDeduction(data) {
  return reserveRequest().post('/api/weigh/save/otherDeduction', data);
}

//退回
export function stepBack(params) {
  return reserveRequest().get('/api/dostorehouse/stepBack', { params });
}
