<template>
  <div class="appointment-search">
    <div class="query-form">
      <div class="status-tag-list">
        <Tag :class="{ active: status === 'all' }" round @click="changeStatus('all')">全 部</Tag>
        <Tag :class="{ active: status === '4' }" round @click="changeStatus('4')">待审核</Tag>
        <Tag :class="{ active: status === '1' }" round @click="changeStatus('1')">预约成功</Tag>
        <Tag :class="{ active: status === '2' }" round @click="changeStatus('2')">预约失败</Tag>
        <Tag :class="{ active: status === '3' }" round @click="changeStatus('3')">预约取消</Tag>
      </div>
    </div>
    <AppointmentList :appointment-status="status" />
  </div>
</template>

<script setup>
import { ref } from 'vue';
import { Tag } from 'vant';
import AppointmentList from './AppointmentList';

const status = ref('all');

const changeStatus = (value) => {
  status.value = value;
};
</script>

<style scoped lang="scss">
.status-tag-list {
  padding: 12px;
  overflow-y: auto;
  white-space: nowrap;

  --van-tag-line-height: 20px;
  --van-tag-font-size: 20px;
  --van-tag-padding: 8px 14px;
  --van-tag-default-color: #e6e9f2;
  --van-tag-text-color: #000;

  .van-tag + .van-tag {
    margin-left: 12px;
  }

  .van-tag.active {
    background-color: #d6e7fb;
    color: #1890ff;
  }
}
</style>
