<template>
  <div class="inout-manage">
    <div
      v-for="(item, index) in list"
      v-p="item.permission"
      :key="index"
      class="item"
      @click="gotoRoute(item)"
    >
      <div class="icon" :style="{ backgroundImage: `url(${item.icon})` }"></div>
      <div class="name">{{ item.name }}</div>
    </div>
  </div>
</template>

<script>
import IconJobAppointment from '@/assets/icon-job-appointment.png';
import IconCheckInOut from '@/assets/icon-check-in-out.png';
import IconJobScheduling from '@/assets/icon-job-scheduling.png';
import IconTakeSample from '@/assets/icon-take-sample.png';
import IconSampleInspection from '@/assets/icon-sample-inspection.png';
import IconCheckingWeight from '@/assets/icon-checking-weight.png';
import IconHouseOperation from '@/assets/icon-house-operation.png';
import IconSettlementManage from '@/assets/icon-settlement-manage.png';

const list = [
  {
    icon: IconJobAppointment,
    name: '作业预约',
    route: { name: 'JobAppointment' },
    permission: ['app-appointment-list'],
  },
  {
    icon: IconCheckInOut,
    name: '登记管理',
    route: { name: 'CheckInOut' },
    permission: ['app-check-in-out'],
  },
  {
    icon: IconJobScheduling,
    name: '作业调度',
    route: { name: 'JobScheduling' },
    permission: ['app-job-scheduling'],
  },
  {
    icon: IconTakeSample,
    name: '扦样管理',
    route: { name: 'TakingSample' },
    permission: ['app-taking-sample'],
  },

  {
    icon: IconSampleInspection,
    name: '化验质检',
    route: { name: 'SampleInspection' },
    permission: ['app-sample-inspection'],
  },
  {
    icon: IconCheckingWeight,
    name: '称重检斤',
    route: { name: 'CheckingWeight' },
    permission: ['app-sample-inspection'],
  },
  {
    icon: IconHouseOperation,
    name: '值仓管理',
    route: { name: 'HouseOperation' },
    permission: ['app-house-operation'],
  },
  {
    icon: IconSettlementManage,
    name: '结算管理',
    route: { name: 'SettlementManage' },
    permission: ['app-settlement-management'],
  },
];
export default {
  name: 'InoutManage',
  data() {
    return {
      list: list,
    };
  },
  methods: {
    gotoRoute(item) {
      this.$router.push({ name: item.route.name });
    },
  },
};
</script>

<style lang="scss" scoped>
.inout-manage {
  display: flex;
  flex-wrap: wrap;
  padding: 14px;
  justify-content: space-between;
  .item {
    flex-basis: calc((100% - 15px) / 2);
    height: 80px;
    background: #ffffff;
    border-radius: 4px;
    display: flex;
    justify-content: center;
    align-items: center;
    margin-bottom: 14px;
    .icon {
      height: 35px;
      width: 35px;
      background-position: center;
      background-size: 100%;
      background-repeat: no-repeat;
      margin-right: 12px;
    }
    .name {
      font-size: 16px;
      font-weight: 500;
      color: #272727;
      line-height: 16px;
    }
  }
}
</style>
