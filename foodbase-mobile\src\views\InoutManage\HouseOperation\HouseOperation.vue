<template>
  <div class="house-operation">
    <Row>
      <Col :span="24">
        <Search
          v-if="route.name === 'HouseOperationRecord'"
          v-model="recordDate"
          placeholder="选择日期区间"
          readonly
          @click="datePicker = true"
        />
        <Search
          v-else
          v-model="date"
          placeholder="选择日期区间"
          readonly
          @click="datePicker = true"
        />
      </Col>
      <Col :span="24">
        <Search v-model="search" placeholder="请输入调度号" show-action @search="onSearch">
          <template #action>
            <div class="scan-action" @click="onScan">
              <SvgIcon name="scan" />
            </div>
          </template>
        </Search>
      </Col>
      <Col v-if="route.name !== 'HouseOperationRecord'" :span="24">
        <Search
          v-model="storeHouseName"
          placeholder="选择仓房"
          readonly
          @click-input="showHousePicker = true"
          @cancel="clearStoreHouse"
        >
          <template #right-icon>
            <div class="close-action" @click="clearStoreHouse" v-if="storeHouseName">
              <Icon name="clear" style="color: #c8c9cc" />
            </div>
          </template>
        </Search>
      </Col>
      <Col v-if="route.name !== 'HouseOperationRecord'" :span="24">
        <Search
          v-model="buzTyperName"
          placeholder="选择业务类型"
          readonly
          @click-input="showTyperPicker = true"
          @cancel="clearBuzTyper"
        >
          <template #right-icon>
            <div class="close-action" @click="clearBuzTyper" v-if="buzTyperName">
              <Icon name="clear" style="color: #c8c9cc" />
            </div>
          </template>
        </Search>
      </Col>
    </Row>
    <Calendar
      v-if="route.name === 'HouseOperationRecord'"
      v-model:show="datePicker"
      allow-same-day
      type="range"
      :min-date="minDate"
      :default-date="[new Date(recordBeginDate), new Date(recordEndDate)]"
      @confirm="onConfirm"
    />
    <Calendar
      v-else
      v-model:show="datePicker"
      type="range"
      allow-same-day
      :min-date="minDate"
      :default-date="[new Date(beginDate), new Date(endDate)]"
      @confirm="onConfirm"
    />
    <ActionSheet v-model:show="showHousePicker" title="仓房">
      <AllHouseSelect @cancel="showHousePicker = false" @confirm="onHouseConfirm"></AllHouseSelect>
    </ActionSheet>
    <ActionSheet v-model:show="showTyperPicker" title="业务类型">
      <BizDictPicker
        dict="INBOUND_AND_OUTBOUND_BUSINESS_TYPE"
        @cancel="showTyperPicker = false"
        @confirm="onTyperConfirm"
      ></BizDictPicker>
    </ActionSheet>
    <div class="total-contain" v-if="route.name != 'HouseOperationRecord'">
      <!-- <SvgIcon class="inline-block icon-cls" name="notice" />  -->
      当前值仓任务{{ pagination.total }}条
      <SvgIcon
        name="refresh"
        class="inline-block icon-cls"
        style="margin-left: 10px"
        @click="onSearch"
      />
    </div>
    <PullRefresh @refresh="onRefresh" v-model="refreshLoading">
      <List
        ref="listRef"
        v-model:loading="isLoading"
        :finished="finished"
        finished-text="没有更多了"
        @load="getList"
      >
        <HCard class="detail-card" v-for="item in list" :key="item">
          <div class="card-head" @click="goToDetail(item.schedulingNo)">
            <img :src="IconHouse" />
            <div class="schedule-num">
              <p class="text-xs font-normal">调度号：</p>
              <p>{{ item.schedulingNo?.split('_')[0] }}</p>
            </div>
            <!-- <div class="next-contain">{{ item.storeHouseName }} <van-icon name="arrow" /></div> -->
          </div>
          <div class="card-content" @click="goToDetail(item.schedulingNo)">
            <div class="row">
              <span class="label">作业车牌：</span>
              <span v-for="(it, index) in item.transportVehicleNo" :key="index">
                {{ it }}
              </span>
            </div>
            <div class="row">
              <span class="label">业务类型：</span>
              <Tag class="tag-cls" :color-type="Number(item.buzTyper)">
                {{ item.buzTyperName }}
              </Tag>
            </div>
            <div class="row">
              <span class="label">粮油品种：</span>
              <span class="value">{{ item.foodCategoryName }}</span>
            </div>
            <!-- <div class="row" v-if="route.name === 'HouseOperation'">
              <span class="label">预估重量：</span>
              <span class="value">
                {{ (item.counterOrg * 1000)?.toFixed(3).toLocaleString() }}公斤
              </span>
            </div> -->

            <div class="row">
              <span class="label">值仓仓房：</span>
              <span class="value">
                {{
                  hasCargoSpace && item.cargoSpaceName
                    ? `${item.storeHouseName}_${item.cargoSpaceName}`
                    : item.storeHouseName
                }}
              </span>
            </div>
            <div class="row" v-if="route.name === 'HouseOperationRecord'">
              <span class="label">值仓结果：</span>
              <span class="value">{{ item.statusName }}</span>
            </div>
            <div class="row" v-if="route.name === 'HouseOperationRecord'">
              <span class="label">值仓时间：</span>
              <span class="value">{{ item.createTime }}</span>
            </div>
            <div class="row">
              <span class="label">仓房：</span>
              <span class="value">{{ item.storeHouseName }}</span>
            </div>
            <div v-if="hasCargoSpace && route.name === 'HouseOperation'" class="row">
              <span class="label">货位：</span>
              <span class="value">{{ item.cargoSpaceName }}</span>
            </div>
            <div class="row">
              <span class="label">客户名称：</span>
              <span class="value">{{ item.customerName }}</span>
            </div>
          </div>
          <div class="backButton" v-if="route.name === 'HouseOperationRecord'">
            <van-button type="primary" block @click="onBack(item)">回退</van-button>
          </div>
        </HCard>
      </List>
    </PullRefresh>
  </div>
</template>

<script setup>
import {
  Search,
  List,
  PullRefresh,
  Row,
  Col,
  Calendar,
  ActionSheet,
  Icon,
  Toast,
  Dialog,
} from 'vant';
import { ref, reactive, watch, computed, onMounted } from 'vue';
import { useRouter, useRoute } from 'vue-router';
import SvgIcon from '@/components/SvgIcon/SvgIcon';
import { HCard } from '@/components';
import IconHouse from '@/assets/icon-house.png';
import Tag from '@/views/InoutManage/common/InOutTypeTag.vue';
import { getHouseOperationPaging, getHouseOperationRecord, stepBack } from '@/api/in-out-manage';
import { AllHouseSelect, BizDictPicker } from '@/views/InoutManage/common';
import dayjs from 'dayjs';
import { checkPermission } from '@/utils/permission';

const router = useRouter();
const route = useRoute();
const search = ref('');
const listRef = ref();

const list = ref([]);
const pagination = reactive({
  page: 0,
  size: 10,
  total: 0,
});

const isLoading = ref(false);
const finished = ref(false);
const refreshLoading = ref(false);

const beginDate = ref(dayjs().subtract(29, 'day').format('YYYY-MM-DD'));
const endDate = ref(dayjs().format('YYYY-MM-DD'));
const recordBeginDate = ref(dayjs().format('YYYY-MM-DD'));
const recordEndDate = ref(dayjs().format('YYYY-MM-DD'));
const date = ref(`${beginDate.value} - ${endDate.value}`);
const recordDate = ref(`${recordBeginDate.value} - ${recordEndDate.value}`);
const datePicker = ref(false);
const minDate = new Date(dayjs().subtract(10, 'year').format('YYYY-MM-DD'));
const storeHouse = ref(null);
const storeHouseName = ref(null);
const showHousePicker = ref(false);
const buzTyper = ref(null);
const buzTyperName = ref(null);
const showTyperPicker = ref(false);

//回退
const onBack = (row) => {
  if (row.isConfirmNetWeigh == 1) {
    Toast.fail('当前数据已确定净重，无法回退值仓状态');
    return;
  }
  Dialog.confirm({
    title: '提示',
    message: '回退值仓将删除值仓记录，请确认是否继续操作？',
  })
    .then(async () => {
      await stepBack({ originCode: '1', schedulingNo: row.schedulingNo });
      Toast.success('回退成功');
      onSearch();
    })
    .catch(() => {});
};
const onScan = () => {
  if (process.env.VUE_APP_THEME_MODE === 'hubei-ehb') {
    window.ehbAppJssdk.device.scan({
      scanType: 'noLogin',
      returnResult: 'yes',
      success: (res) => {
        goToDetail(res);
      },
    });
  } else {
    window.cordova?.plugins.barcodeScanner.scan(
      (result) => {
        if (result.text) {
          goToDetail(result.text);
        }
      },
      (error) => {
        alert('扫码失败 ' + error);
      },
      {
        prompt: '请将二维码放在扫码框中',
        resultDisplayDuration: 300,
      },
    );
  }
};
const onRefresh = () => {
  if (refreshLoading.value) {
    pagination.page = 0;
    pagination.total = 0;
    list.value = [];
    refreshLoading.value = false;
  }
  finished.value = false;
  isLoading.value = true;
  getList();
};
const onSearch = () => {
  finished.value = false;
  list.value = [];
  pagination.page = 0;
  pagination.total = 0;
  listRef.value?.check();
};
const getList = async () => {
  try {
    if (route.name === 'HouseOperationRecord') {
      const { items, page, total } = await getHouseOperationRecord({
        search: search.value,
        page: pagination.page + 1,
        size: pagination.size,
        beginDate: recordBeginDate.value,
        endDate: recordEndDate.value,
      });
      list.value.push(...items);
      pagination.page = page;
      pagination.total = total;

      // 加载状态结束
      isLoading.value = false;

      // 数据全部加载完成
      if (list.value.length >= total) {
        finished.value = true;
      }
    } else {
      const { items, page, total } = await getHouseOperationPaging({
        schedulingNo: search.value,
        page: pagination.page + 1,
        size: pagination.size,
        beginDate: beginDate.value,
        endDate: endDate.value,
        storeHouseId: storeHouse.value ? storeHouse.value.id : undefined,
        buzTyper: buzTyper.value ? buzTyper.value : undefined,
      });
      console.log(items, page, total, ' items, page, total');
      list.value.push(...items);
      pagination.page = page;
      pagination.total = total;
      // 加载状态结束
      isLoading.value = false;

      // 数据全部加载完成
      if (list.value.length >= total) {
        finished.value = true;
      }
    }
  } catch {
    isLoading.value = false;
    finished.value = true;
  }
};

const onConfirm = (values) => {
  const [start, end] = values;
  if (route.name === 'HouseOperationRecord') {
    recordBeginDate.value = dayjs(start).format('YYYY-MM-DD');
    recordEndDate.value = dayjs(end).format('YYYY-MM-DD');
    recordDate.value = `${recordBeginDate.value} - ${recordEndDate.value}`;
  } else {
    beginDate.value = dayjs(start).format('YYYY-MM-DD');
    endDate.value = dayjs(end).format('YYYY-MM-DD');
    date.value = `${beginDate.value} - ${endDate.value}`;
  }
  datePicker.value = false;
  onSearch();
};
const onHouseConfirm = (value) => {
  storeHouse.value = value;
  storeHouseName.value = value.name;
  showHousePicker.value = false;
  onSearch();
};
const clearStoreHouse = () => {
  storeHouse.value = null;
  storeHouseName.value = null;
  onSearch();
};
const onTyperConfirm = (value) => {
  buzTyper.value = value.value;
  buzTyperName.value = value.label;
  showTyperPicker.value = false;
  onSearch();
};
const clearBuzTyper = () => {
  buzTyper.value = null;
  buzTyperName.value = null;
  onSearch();
};

const goToDetail = (schedulingNo) => {
  router.push({
    name: route.name === 'HouseOperationRecord' ? 'HouseDetailRecord' : 'HouseDetail',
    params: {
      schedulingNo: schedulingNo,
    },
  });
};
onMounted(() => {
  // finished.value = false;
  list.value = [];
  pagination.page = 0;
  pagination.total = 0;
  // getList();
});
const hasCargoSpace = computed(() => checkPermission(['cargo_space']));

watch(
  () => route.name,
  () => {
    search.value = '';
    onSearch();
    console.log('wacth');
  },
);
</script>

<style scoped lang="scss">
.scan-action {
  font-size: 25px;
  height: 34px;
  display: flex;
  align-items: center;
  padding: 0 10px;
}

.total-contain {
  padding: 9px 14px;
  display: flex;
  align-items: center;
}

.detail-card {
  padding: 16px;
  margin-bottom: 10px;
}

.card-head {
  display: flex;
  align-items: center;
  justify-content: space-between;
  font-size: 18px;
  font-weight: bold;
  color: #1f3359;

  .next {
    color: rgba(109, 116, 141, 0.6);
  }
}

.schedule-num {
  flex-grow: 1;
  margin-left: 12px;
}

.card-content {
  margin-top: 8px;
  line-height: 30px;

  .label {
    font-size: 14px;
    font-weight: 400;
    color: #6d748d;
  }

  .value {
    font-size: 14px;
    font-weight: 400;
    color: #0f0f0f;
  }

  .carNum {
    padding: 3px 6px;
    background: #f4f4f4;
    border-radius: 2px;
    border: 1px solid #ebedf0;
    margin-right: 5px;
  }
}

.tag-cls {
  line-height: 25px;
}

.next-contain {
  // width: 80px;
  text-align: right;
}

.icon-cls {
  vertical-align: text-top;
}

.backButton {
  width: 100%;
}
</style>
