<template>
  <div class="checking-weight">
    <Row>
      <Col :span="12">
        <Search v-model="date" placeholder="选择日期区间" readonly @click="datePicker = true" />
      </Col>
      <Col :span="12">
        <Search v-model="search" placeholder="请输入调度号" show-action @search="onSearch">
          <template #action>
            <div class="scan-action" @click="onScan">
              <SvgIcon name="scan" />
            </div>
          </template>
        </Search>
      </Col>
    </Row>
    <Calendar
      allow-same-day
      v-model:show="datePicker"
      type="range"
      :min-date="minDate"
      :default-date="[new Date(beginDate), new Date(endDate)]"
      @confirm="onConfirm"
    />

    <!-- <div class="used-weight-bridge"> -->
    <Field
      class="fie-item"
      v-model="useWeighbridge"
      label="地磅："
      is-link
      readonly
      placeholder="请选择"
      @click="showUseWeighbridgePicker = true"
    />
    <!-- <div
        class="weight-bridge-item"
        v-bind:style="{ width: 100 / weighbridgeList.length + '%' }"
        v-for="item in weighbridgeList"
        :key="item.id"
        v-bind:class="{ selected: item.id === useWeighbridge }"
        @click="changeBridge(item)"
      >
        {{ item.name }}
      </div> -->
    <!-- </div> -->

    <div class="total-contain">
      <SvgIcon class="icon-cls" name="notice" /> 当前待称重任务
      <span style="color: #3b82f6">{{ pagination.total }}</span> 条
      <SvgIcon name="refresh" class="icon-cls" style="margin-left: 10px" @click="onSearch" />
    </div>
    <PullRefresh @refresh="onRefresh" v-model="refreshLoading">
      <List
        ref="listRef"
        v-model:loading="isLoading"
        :finished="finished"
        finished-text="没有更多了"
        @load="getList"
      >
        <HCard class="detail-card" v-for="item in list" :key="item">
          <div class="card-head">
            <img :src="IconHouse" />
            <div class="schedule-num">{{ item.schedulingNo?.split('_')[0] }}</div>
          </div>
          <div class="card-content">
            <div class="row">
              <span class="label">到库车船号：</span>
              <span v-for="(it, index) in item.transportShipOrVehicleNo" :key="index">
                {{ it }}
              </span>
            </div>
            <div class="row">
              <span class="label">作业车牌：</span>
              <span v-for="(it, index) in item.transportVehicleNo" :key="index">
                {{ it }}
              </span>
            </div>
            <div class="row">
              <span class="label">粮食品种：</span>
              <span class="value">{{ item.foodCategoryName }}</span>
            </div>
            <div class="row">
              <span class="label">业务类型：</span>
              <Tag class="tag-cls" :color-type="Number(item.buzTyper)">
                {{ item.buzTyperName }}
              </Tag>
            </div>
            <div class="row">
              <span class="label">值仓仓房：</span>
              <span class="value">
                {{
                  hasCargoSpace && item.cargoSpaceName
                    ? `${item.storeHouse}_${item.cargoSpaceName}`
                    : item.storeHouse
                }}
              </span>
            </div>
            <div class="row">
              <span class="label">称重：</span>
              <span class="value">{{ item.weighingRecommendation }}</span>
            </div>
            <div class="row">
              <span class="label">客户名称：</span>
              <span class="value">{{ item.customerName }}</span>
            </div>
          </div>
          <div class="botton-warpper">
            <Button @click="goToDetail(item.schedulingNo)" type="primary">称重</Button>
          </div>
        </HCard>
      </List>
    </PullRefresh>
    <Popup v-model:show="showUseWeighbridgePicker" position="bottom">
      <Picker
        ref="foodRef"
        title="请选择地磅"
        :columns="weighbridgeList"
        :columns-field-names="fieldNames"
        @cancel="showUseWeighbridgePicker = false"
        @confirm="onConfirmUseWeighbridge"
      />
    </Popup>
  </div>
</template>

<script setup>
import { Search, List, PullRefresh, Row, Col, Calendar, Field, Popup, Picker, Button } from 'vant';
import { ref, reactive, watch, onMounted, computed } from 'vue';
import { useRouter, useRoute } from 'vue-router';
import SvgIcon from '@/components/SvgIcon/SvgIcon';
import { HCard } from '@/components';
import IconHouse from '@/assets/icon-house.png';
import { getWeighingList, getAllEnableWeighbridge } from '@/api/in-out-manage';
import dayjs from 'dayjs';
import { checkPermission } from '@/utils/permission';

const router = useRouter();
const route = useRoute();
const search = ref('');
const listRef = ref();
const fieldNames = {
  text: 'name',
  value: 'id',
};

const list = ref([]);
const pagination = reactive({
  page: 0,
  size: 10,
  total: 0,
});
const weighbridgeList = ref([]);
const useWeighbridge = ref('');

const showUseWeighbridgePicker = ref(false);
const isLoading = ref(false);
const finished = ref(false);
const refreshLoading = ref(false);

const beginDate = ref(dayjs().subtract(29, 'day').format('YYYY-MM-DD'));
const endDate = ref(dayjs().format('YYYY-MM-DD'));
const date = ref(`${beginDate.value} - ${endDate.value}`);
const datePicker = ref(false);
const minDate = new Date(dayjs().subtract(10, 'year').format('YYYY-MM-DD'));
const hasCargoSpace = computed(() => checkPermission(['cargo_space']));

const onScan = () => {
  window.cordova?.plugins.barcodeScanner.scan(
    (result) => {
      if (result.text) {
        goToDetail(result.text);
      }
    },
    (error) => {
      alert('扫码失败 ' + error);
    },
    {
      prompt: '请将二维码放在扫码框中',
      resultDisplayDuration: 300,
    },
  );
};
const onRefresh = () => {
  if (refreshLoading.value) {
    pagination.page = 0;
    pagination.total = 0;
    list.value = [];
    refreshLoading.value = false;
  }
  finished.value = false;
  isLoading.value = true;
  getList();
};
const onSearch = () => {
  finished.value = false;
  list.value = [];
  pagination.page = 0;
  pagination.total = 0;
  listRef.value?.check();
};
const getList = async () => {
  try {
    const data = await getWeighingList({
      schedulingNo: search.value ? search.value : undefined,
      page: pagination.page + 1,
      size: pagination.size,
      beginDate: beginDate.value,
      endDate: endDate.value,
    });
    list.value.push(...data);
    list.value.forEach((item) => {
      let isOut =
        item.buzTyperName.indexOf('出库') !== -1 ||
        item.buzTyperName === '卸船' ||
        item.buzTyperName === '卸车';
      if (item.pickUpDetailResVOList && item.pickUpDetailResVOList.length > 0) {
        item.pickUpDetailResVOList = item.pickUpDetailResVOList.sort(
          (a, b) => dayjs(b.createTime).unix() - dayjs(a.createTime).unix(),
        );
        let lastRecord = item.pickUpDetailResVOList[0];
        if (lastRecord.weighTyper === 2) {
          item.weighingRecommendation = '称皮'; // 称皮
        } else {
          item.weighingRecommendation = '称毛'; // 称毛
        }
      } else {
        if (isOut) {
          item.weighingRecommendation = '称皮'; // 称皮
          if (item.typeShipping == '2' && this.isShipAdjustment == 1 && item.buzTyper < 8) {
            item.weighingRecommendation = '称毛'; // 称毛
          }
        } else {
          item.weighingRecommendation = '称毛'; // 称毛
        }
      }
    });
    pagination.total = data.length;
    // 加载状态结束
    isLoading.value = false;

    // 数据全部加载完成
    if (list.value.length >= data.length) {
      finished.value = true;
    }
  } catch {
    isLoading.value = false;
    finished.value = true;
  }
};

const onConfirm = (values) => {
  const [start, end] = values;
  beginDate.value = dayjs(start).format('YYYY-MM-DD');
  endDate.value = dayjs(end).format('YYYY-MM-DD');
  date.value = `${beginDate.value} - ${endDate.value}`;
  datePicker.value = false;
  onSearch();
};
const onConfirmUseWeighbridge = (values) => {
  useWeighbridge.value = values.name;
  showUseWeighbridgePicker.value = false;
  // onSearch();
};

const goToDetail = (schedulingNo) => {
  router.push({
    name: 'CheckingWeightDetail',
    params: {
      schedulingNo: schedulingNo,
    },
  });
};
const initBridgeData = async () => {
  const data = await getAllEnableWeighbridge();
  weighbridgeList.value = data || [];
  // useWeighbridge.value = data[0]?.id;
  useWeighbridge.value = data[0]?.name;
  localStorage.setItem('useWeighbridge', JSON.stringify(data[0]));
};
// const changeBridge = (item) => {
//   useWeighbridge.value = item?.id;
//   localStorage.setItem('useWeighbridge', JSON.stringify(item));
// };
onMounted(async () => {
  await initBridgeData();
});
watch(
  () => route.name,
  () => {
    search.value = '';
    onSearch();
  },
);
</script>

<style lang="scss" scoped>
.van-field {
  ::v-deep(.van-field__control) {
    text-align: right;
  }
}
.scan-action {
  font-size: 25px;
  height: 34px;
  display: flex;
  align-items: center;
  padding: 0 10px;
}
.used-weight-bridge {
  width: 100%;
  height: 42px;
  background: rgba(255, 255, 255, 1);
  display: flex;
  justify-content: space-between;
  align-items: center;
  .weight-bridge-item {
    font-size: 16px;
    font-weight: 400;
    color: #1d2129;
    line-height: 42px;
    text-align: center;
  }
  .selected {
    font-size: 16px;
    font-weight: 500;
    color: #165dff;
    line-height: 22px;
    &::before {
      position: absolute;
      bottom: 0;
      left: 50%;
      transform: translateX(-50%);
      content: '';
      width: 24px;
      height: 2px;
      background: #165dff;
    }
  }
}
.total-contain {
  padding: 9px 14px;
  display: flex;
  align-items: center;
}
.detail-card {
  padding: 16px;
  margin-bottom: 10px;
}
.card-head {
  display: flex;
  align-items: center;
  justify-content: space-between;
  font-size: 18px;
  font-weight: bold;
  color: #1f3359;
  .next {
    color: rgba(109, 116, 141, 0.6);
  }
}
.schedule-num {
  flex-grow: 1;
  margin-left: 12px;
}
.card-content {
  margin-top: 8px;
  line-height: 30px;
  .row {
    display: flex;
    .label {
      font-size: 14px;
      font-weight: 400;
      color: #6d748d;
      flex-basis: 6em;
    }
    .value {
      font-size: 14px;
      font-weight: 400;
      color: #0f0f0f;
      flex-grow: 1;
    }
    .carNum {
      padding: 3px 6px;
      background: #f4f4f4;
      border-radius: 2px;
      border: 1px solid #ebedf0;
      margin-right: 5px;
    }
  }
}
.tag-cls {
  line-height: 25px;
}
.next-contain {
  width: 80px;
  text-align: right;
}
.icon-cls {
  vertical-align: text-top;
}
.botton-warpper {
  // width: 100%;
  .van-button {
    width: 100%;
  }
}
</style>
