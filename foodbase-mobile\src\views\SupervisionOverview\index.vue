<template>
  <div class="supervision-navigation">
    <div class="background"></div>
    <div class="links">
      <RouterLink :to="{ name: 'SupervisionAreaReport', params: { areaCode } }" class="link">
        <div class="icon icon-report"></div>
        <span class="name">清查报告</span>
        <Icon name="arrow" />
      </RouterLink>
      <RouterLink :to="{ name: 'RiskList' }" class="link">
        <div class="icon icon-listing"></div>
        <span class="name">风险清单</span>
        <Icon name="arrow" />
      </RouterLink>
      <RouterLink :to="{ name: 'VideoMonitor' }" class="link">
        <div class="icon icon-video"></div>
        <span class="name">视频监控</span>
        <Icon name="arrow" />
      </RouterLink>
      <RouterLink :to="{ name: 'ManualCheck' }" class="link">
        <div class="icon icon-manual"></div>
        <span class="name">人工清查</span>
        <Icon name="arrow" />
      </RouterLink>
    </div>
  </div>
</template>

<script setup>
import { Icon } from 'vant';
import { useStore } from 'vuex';
import { computed } from 'vue';

const store = useStore();

const areaCode = computed(() => {
  return store.getters['user/userAreaCode'];
});
</script>

<style scoped lang="scss">
.supervision-navigation {
  position: relative;
  .background {
    position: absolute;
    width: 100%;
    height: 165px;
    background-image: url('./assets/bg-supervison.png');
    background-size: cover;
    background-repeat: no-repeat;
  }

  .links {
    position: relative;
    padding: 120px 16px 0;

    .link {
      height: 100px;
      background-color: #ffffff;
      margin-bottom: 16px;
      box-shadow: 0 0 6px 0 rgba(194, 194, 194, 0.14);
      border-radius: 4px;
      display: flex;
      font-size: 24px;
      font-weight: 500;
      color: #363a44;
      align-items: center;
      padding: 0 16px;

      &.disabled {
        opacity: 0.5;
        pointer-events: none;
        touch-action: none;
      }
    }

    .icon {
      height: 60px;
      width: 60px;
      background-position: center;
      background-size: 100%;
      background-repeat: no-repeat;
      transform: translateY(2px);

      &.icon-report {
        background-image: url('./assets/icon-report.png');
      }
      &.icon-listing {
        background-image: url('./assets/icon-listing.png');
      }
      &.icon-video {
        background-image: url('./assets/icon-video.png');
      }
      &.icon-manual {
        background-image: url('./assets/icon-manual.png');
      }
    }

    .name {
      margin-left: 15px;
    }

    .van-icon {
      margin-left: auto;
    }
  }
}
</style>
