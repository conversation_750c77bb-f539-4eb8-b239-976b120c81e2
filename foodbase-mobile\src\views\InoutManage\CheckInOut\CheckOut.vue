<template>
  <div class="check-out in-out-manage-page">
    <div class="scan-warpper">
      <div class="scan" @click="handleScan"></div>
      <div class="text" @click="handleScan">扫描调度二维码</div>
    </div>
    <van-form ref="form">
      <div class="warpper">
        <van-field
          v-model="displaySchedulingNo"
          label="调度号"
          :rules="[{ required: true, message: '请输入调度号' }]"
          required
          placeholder="扫码或输入"
          @update:model-value="onSchedulingNoScanned"
        >
          <template #right-icon>
            <div class="scan-action" @click="handleScan">
              <svg-icon name="scan" />
            </div>
          </template>
        </van-field>
        <van-field
          v-model="form.cardNumber"
          label="卡号"
          placeholder="请输入"
          @update:model-value="onCardNumberScanned"
        ></van-field>
      </div>
      <div class="warpper">
        <van-field
          v-model="shippingTypeText"
          label="运输方式:"
          is-link
          readonly
          name="signPicker"
          placeholder="选择运输方式"
          @click="showShippingTypePicker = true"
        >
        </van-field>
        <van-popup v-model:show="showShippingTypePicker" position="bottom">
          <biz-dict-picker
            dict="SHIPPING_TYPE"
            title="运输方式:"
            @cancel="showShippingTypePicker = false"
            @confirm="onShippingTypeConfirm"
          ></biz-dict-picker>
        </van-popup>
        <van-field
          v-if="form.typeShipping === '1'"
          v-model="form.transportVehicleNo"
          label="车牌号:"
          :rules="rules.transportVehicleNo"
          required
          placeholder="请输入"
        ></van-field>
        <van-field name="uploader" center label="车辆照片：">
          <template #input>
            <Uploader
              :model-value="carFileList"
              multiple
              :max-count="1"
              :before-read="beforeRead"
              class="upload-cls"
              @click-upload="(e) => onClickUpload(e)"
              @delete="onDeleteFile"
            />
          </template>
        </van-field>
        <van-field
          v-if="form.typeShipping !== '1'"
          v-model="form.transportVehicleNo"
          :label="typeLabel"
          :rules="rules.transportVehicleNoOther"
          required
          placeholder="请输入"
        ></van-field>
        <van-field
          v-model="form.outTime"
          label="离库时间"
          is-link
          readonly
          name="timePicker"
          :rules="[
            {
              required: true,
              message: '请选择离库时间',
            },
          ]"
          required
          placeholder="请选择"
          @click="showTimePicker = true"
        >
        </van-field>
        <van-popup v-model:show="showTimePicker" position="bottom">
          <van-datetime-picker
            type="datetime"
            v-model="selectedTime"
            :formatter="timeFormatter"
            @confirm="onTimeConfirm"
            @cancel="showTimePicker = false"
          ></van-datetime-picker>
        </van-popup>
      </div>
      <div class="bottom-warpper">
        <van-button
          class="button"
          @click="onFinishCheckOut"
          :disabled="!canSubmit"
          :loading="loading"
        >
          完成离库登记
        </van-button>
        <div class="bar"></div>
      </div>
    </van-form>
    <ActionSheet
      v-model:show="showActionSheet"
      cancel-text="取消"
      description="请选择上传方式"
      :actions="actions"
      @select="onUploadActionSelect"
      close-on-click-action
    />
  </div>
</template>

<script>
import { Form, Field, Button, Toast, Popup, DatetimePicker, ActionSheet, Uploader } from 'vant';
import { getShortSchedulingNo, timeFormatter } from '@/utils/inout-manage';
import { checkOut, getCheckOutList, getFileUrls } from '@/api/in-out-manage';
import dayjs from 'dayjs';
import { BizDictPicker } from '@/views/InoutManage/common';
import { getReserverToken } from '@/utils/auth';
const transportVehicleNoReg =
  /^([京津晋冀蒙辽吉黑沪苏浙皖闽赣鲁豫鄂湘粤桂琼渝川贵云藏陕甘青宁新]){1}([A-Z]|[a-z]|[0-9]){6,7}$/;

export default {
  name: 'CheckOut',
  components: {
    'van-form': Form,
    'van-field': Field,
    'van-button': Button,
    'van-popup': Popup,
    'van-datetime-picker': DatetimePicker,
    BizDictPicker,
    ActionSheet,
    Uploader,
  },
  data() {
    return {
      form: {
        typeShipping: '1',
        schedulingNo: '',
        cardNumber: '',
        transportVehicleNo: '',
        outTime: dayjs().format('YYYY-MM-DD HH:mm'),
      },
      shippingTypeText: '汽运',
      focusVehicleNo: false,
      showTimePicker: false,
      showShippingTypePicker: false,
      selectedTime: new Date(),
      loading: false,
      canSubmit: false,
      rules: {
        transportVehicleNo: [
          { required: true, message: '请输入车牌号' },
          { pattern: transportVehicleNoReg, message: '请输入正确的车牌号' },
        ],
        transportVehicleNoOther: [{ required: true, message: '请输入牌号' }],
      },
      carFileList: [],
      showActionSheet: false,

      actions: [{ name: '拍照' }, { name: '相册选择' }],
      transportVehiclePhoto: '', //车辆照片id
      fileItem: {},
    };
  },
  computed: {
    displaySchedulingNo() {
      const { schedulingNo } = this.form;
      if (!schedulingNo) {
        return '';
      }
      return getShortSchedulingNo(schedulingNo);
    },
    typeLabel() {
      const obj = {
        2: '船号',
        3: '火车号',
        4: '其他',
      };
      return obj[this.form.typeShipping];
    },
  },
  methods: {
    timeFormatter,
    getReserverToken,
    handleScan() {
      window.cordova?.plugins.barcodeScanner.scan(
        (result) => {
          if (result.text) {
            this.onSchedulingNoScanned(result.text);
          }
        },
        (error) => {
          alert('扫码失败 ' + error);
        },
        {
          prompt: '请将二维码放在扫码框中',
          resultDisplayDuration: 300,
        },
      );
    },
    onSchedulingNoScanned(no) {
      this.canSubmit = false;
      if (no) {
        this.form.schedulingNo = no;
        this.getCheckoutInfo(no);
      }
    },
    onCardNumberScanned(no) {
      this.canSubmit = false;
      if (no) {
        this.form.cardNumber = no;
        this.getCheckoutInfo(no);
      }
    },
    onPlateNumberChange() {
      this.canSubmit = false;
      const { transportVehicleNo } = this.form;
      if (transportVehicleNo) {
        this.getCheckoutInfo(transportVehicleNo);
      }
    },
    onShippingTypeConfirm(value) {
      this.shippingTypeText = value.label;
      this.form.typeShipping = value.value;
      this.showShippingTypePicker = false;
    },
    onTimeConfirm(value) {
      this.form.outTime = dayjs(value).format('YYYY-MM-DD HH:mm');
      this.selectedTime = new Date(value);
      this.showTimePicker = false;
    },
    async getCheckoutInfo(blurry) {
      try {
        const list = await getCheckOutList({ blurry });
        if (list && list.length === 1) {
          const [item] = list;
          this.form.schedulingNo = item.schedulingNo;
          this.form.transportVehicleNo = item.transportVehicleNo;
          this.form.outTime = dayjs().format('YYYY-MM-DD HH:mm');
          this.selectedTime = new Date();
          this.form.cardNumber = item.cardNumber ?? '';
          this.canSubmit = true;
        }
      } catch (e) {
        //
      }
    },
    async onFinishCheckOut() {
      try {
        this.loading = true;
        await this.$refs.form.validate();
        await checkOut({
          ...this.form,
          cardNumber: this.form.cardNumber,
          outTime: dayjs(this.form.outTime).format('YYYY-MM-DD HH:mm'),
          transportVehiclePhoto: this.transportVehiclePhoto,
        });
        Toast.success('离库登记成功');

        this.resetFields();
        this.canSubmit = false;
      } catch (e) {
        // eslint-disable-next-line no-empty
      } finally {
        this.loading = false;
      }
    },
    resetFields() {
      this.form = {
        typeShipping: '1',
        schedulingNo: '',
        cardNumber: '',
        transportVehicleNo: '',
        outTime: dayjs().format('YYYY-MM-DD HH:mm'),
      };
      this.selectedTime = new Date();
    },
    beforeRead(file) {
      console.log('b-file', file);
      const maxSize = 5 * 1024 * 1024; // 5 MB
      if (!(file.type.includes('jpg') || file.type.includes('jpeg') || file.type.includes('png'))) {
        Toast('请上传 jpg, png, jpeg 格式图片');
        return false;
      }
      if (file.size > maxSize) {
        Toast('文件大小不能超过5MB');
        return false; // 阻止文件上传
      }
      return true;
    },
    onDeleteFile(file, { index }) {
      this.carFileList.splice(index, 1);
    },
    onUploadActionSelect({ name }) {
      const Camera = window.Camera;
      let sourceType = Camera.PictureSourceType.PHOTOLIBRARY;
      if (name === '拍照') {
        sourceType = Camera.PictureSourceType.CAMERA;
      } else if (name === '相册选择') {
        sourceType = Camera.PictureSourceType.SAVEDPHOTOALBUM;
      }
      navigator.camera.getPicture(
        (imageUri) => {
          // 添加图片到图片列表
          this.fileItem = {
            url: imageUri,
            isImage: true,
            status: 'uploading',
            deletable: true,
          };

          this.carFileList.push(this.fileItem);

          // 上传参数
          const options = new window.FileUploadOptions();
          options.fileKey = 'file';
          options.fileName = imageUri.substr(imageUri.lastIndexOf('/') + 1);
          options.headers = {
            Authorization: this.getReserverToken(),
          };
          const params = {};
          params.coder = 'sgcb/storehouse_document';
          options.params = params;

          // 上传地址
          const reserverBaseUrl = () =>
            JSON.parse(process.env.VUE_APP_RESERVER_API_BASE_URL)[window.__isProd] ||
            location.origin;
          const uploadUri = new URL(
            '/reserver/api/fileManager/uploadFileBatch',
            reserverBaseUrl(),
          ).toString();

          console.log(imageUri, 'imageUri');
          console.log(options, 'options');
          // 上传文件
          const fileTransfer = new window.FileTransfer();
          fileTransfer.upload(
            imageUri,
            uploadUri,
            (res) => {
              console.log(res, 'res');
              // 上传成功
              const resp = res.response;
              if (resp) {
                const respJson = JSON.parse(resp);
                const { data } = respJson;
                this.transportVehiclePhoto = data;
                this.fileItem.name = data.split(',')[0];
                this.fileItem.status = 'done';

                getFileUrls(data).then((urls) => {
                  this.fileItem.url = urls[0];
                });
              }
            },
            (error) => {
              // 上传失败
              this.fileItem.status = 'failed';
              Toast('上传失败');
              console.error(error);
            },
            options,
          );
        },
        (err) => {
          Toast('选择图片失败');
          console.error(err);
        },
        {
          quality: 85,
          destinationType: Camera.DestinationType.FILE_URI,
          sourceType: sourceType,
        },
      );
    },
    onClickUpload(e) {
      e.preventDefault();
      if (navigator.camera) {
        this.showActionSheet = true;
      } else {
        Toast('不支持选择照片');
      }
    },
  },
};
</script>

<style lang="scss" scoped>
@import '@/views/InoutManage/style/index';
.scan-action {
  font-size: 25px;
  height: 34px;
  display: flex;
  align-items: center;
  padding: 0 10px;
}
.check-out {
  .scan-warpper {
    width: 100%;
    height: 210px;
    background: #ffffff;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    .scan {
      width: 110px;
      height: 110px;
      background-image: url('../../../assets/in-out-manage/scan.png');
      background-size: 100% 100%;
      background-repeat: no-repeat;
      margin-bottom: 20px;
    }
    .text {
      font-size: 18px;
      font-weight: 500;
      color: #1677ff;
      line-height: 25px;
    }
  }
  .carNum {
    padding: 3px 6px;
    background: #f4f4f4;
    border-radius: 2px;
    border: 1px solid #ebedf0;
    margin-right: 5px;
  }
}
</style>
