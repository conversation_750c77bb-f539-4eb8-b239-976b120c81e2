import {
  getReserveScale,
  getStockSum,
  getStoreQuantityProducingAreaBarData,
  getStoreQuantityPieData,
  getQuantityGradeDistribute,
  getStoreQuantityYearGainPieData,
  getTopStockSum,
  getGovermentReserver,
} from '@/api/stock-overview';
import { sumBy } from 'lodash-es';

export default {
  namespaced: true,
  state: {
    category: 1,
    reserveData: [
      { category: 1, name: '粮食', reserve: 0, stock: 0 },
      { category: 2, name: '成品粮', reserve: 0, stock: 0 },
      { category: 3, name: '食用油', reserve: 0, stock: 0 },
    ],
    stockVarietyStructureData: [],
    stockLeaveStructureData: [],
    stockYearStructureData: [],
    stockDistributionData: [],
    stockDistributionDataMap: [
      { category: 1, name: '粮食', data: [] },
      { category: 2, name: '成品粮', data: [] },
      { category: 3, name: '食用油', data: [] },
    ],
    stockYearStructureDataMap: [
      { category: 1, name: '粮食', data: [] },
      { category: 2, name: '成品粮', data: [] },
      { category: 3, name: '食用油', data: [] },
    ],
    stockSum: 0, // 储备库存
    stockSumAllCategory: 0,
    topStockSumMap: {
      1: 0,
      2: 0,
      3: 0,
    },
  },
  mutations: {
    setCategory(state, value) {
      state.category = value;
    },
    setData(state, data) {
      Object.keys(data).forEach((key) => {
        state[key] = data[key];
      });
    },
  },
  actions: {
    fetchData({ dispatch, rootState }) {
      let areaCode;
      if (rootState.user.info.areaCode) {
        areaCode = rootState.user.info.areaCode;
      } else if (rootState.user.info.cityCode) {
        areaCode = rootState.user.info.cityCode;
      } else if (rootState.user.info.provinceCode) {
        areaCode = rootState.user.info.provinceCode;
      }
      dispatch('fetchReserveScaleData', { areaCode });
      dispatch('fetchReserveStockData');
      dispatch('fetchStockVarietyStructureData');
      dispatch('fetchStockLeaveStructureData');
      dispatch('getStockSum');
      dispatch('getStockSumAllCategory');
    },
    // 储备规模数据
    async fetchReserveScaleData({ commit, state }, { areaCode }) {
      const { reserveData } = state;
      const thisYear = new Date().getFullYear();
      const region = process.env.VUE_APP_MODE;
      if (['sichuan', 'neimenggu'].includes(region)) {
        const data = await getGovermentReserver({ year: thisYear });
        const lsCount = data.proGrain + data.cityGrain + data.areaGrain;
        const cplCount = data.proFinished + data.cityFinished + data.areaFinished;
        const syyCount = data.proOil + data.cityOil + data.areaOil;
        reserveData[0].reserve = lsCount || 0;
        reserveData[1].reserve = cplCount || 0;
        reserveData[2].reserve = syyCount || 0;
      } else {
        const data1 =
          (await getReserveScale({
            regionCode: areaCode,
            year: thisYear,
            categoryType: 1,
          })) || {};
        const data2 =
          (await getReserveScale({
            regionCode: areaCode,
            year: thisYear,
            categoryType: 2,
          })) || {};
        const data3 =
          (await getReserveScale({
            regionCode: areaCode,
            year: thisYear,
            categoryType: 3,
          })) || {};
        reserveData[0].reserve = data1['lsCount'] || 0;
        reserveData[1].reserve = data2['cplCount'] || 0;
        reserveData[2].reserve = data3['syyCount'] || 0;
      }
      commit('setData', { reserveData });
    },
    // 储备库存数据
    async fetchReserveStockData({ commit, state }) {
      const { reserveData } = state;
      const data1 = await getStockSum({
        areaCode: '100000',
        categoryType: 1,
      });
      const data2 = await getStockSum({
        areaCode: '100000',
        categoryType: 2,
      });
      const data3 = await getStockSum({
        areaCode: '100000',
        categoryType: 3,
      });
      const num1 = sumBy(data1, 'total') + sumBy(data2, 'total') / 0.7;
      const num2 = sumBy(data2, 'total');
      const num3 = sumBy(data3, 'total');
      reserveData[0].stock = num1;
      reserveData[1].stock = num2;
      reserveData[2].stock = num3;
      const num1Only = sumBy(data1, 'total');
      const topStockSumMap = {
        1: num1Only,
        2: num2,
        3: num3,
      };
      commit('setData', { reserveData });
      commit('setData', { topStockSumMap });
    },
    // 库存品种结构数据
    async fetchStockVarietyStructureData({ commit, state }) {
      const { category } = state;
      // const username = rootState.user.info.username;
      const data = await getStoreQuantityPieData({ categoryType: category });
      const stockVarietyStructureData =
        data?.map((item) => {
          return {
            name: item.categoryName,
            value: item.sum || 0,
            percent: item.percent || 0,
          };
        }) || [];
      commit('setData', { stockVarietyStructureData });
    },
    // 库存等级分布
    async fetchStockLeaveStructureData({ commit, state }) {
      const { category } = state;
      // const username = rootState.user.info.username;
      const data = await getQuantityGradeDistribute({ categoryType: category });
      const stockLeaveStructureData =
        data?.map((item) => {
          return {
            name: item.foodLevel,
            value: item.gradePercent || 0,
          };
        }) || [];
      commit('setData', { stockLeaveStructureData });
    },
    // 库存年份结构数据
    async fetchStockYearStructureData(
      { commit, state, rootState },
      { categoryType, yearCategory },
    ) {
      const { stockYearStructureDataMap } = state;
      const username = rootState.user.info.username;
      const data = await getStoreQuantityYearGainPieData({
        categoryId: yearCategory,
        categoryType: categoryType,
        username: username,
      });
      stockYearStructureDataMap.forEach((it) => {
        if (it.category === categoryType) {
          it.data =
            data?.map((item) => {
              return {
                year: item.yearGain,
                value: parseFloat(item.sum) || 0,
              };
            }) || [];
        }
      });
      commit('setData', { stockYearStructureDataMap });
    },
    // 库存产地分布数据
    async fetchStockDistributionData(
      { commit, state, rootState },
      { categoryType, originCategory },
    ) {
      const { stockDistributionDataMap } = state;
      const username = rootState.user.info.username;
      const data = await getStoreQuantityProducingAreaBarData({
        categoryId: originCategory,
        categoryType: categoryType,
        username: username,
      });
      stockDistributionDataMap.forEach((it) => {
        if (it.category === categoryType) {
          it.data =
            data?.map((item) => {
              return {
                name: item.producingArea,
                value: parseFloat(item.sum) || 0,
              };
            }) || [];
        }
      });
      commit('setData', { stockDistributionDataMap });
    },
    async getStockSum({ commit, state }) {
      const { category } = state;
      const data = await getTopStockSum({ categoryType: category });
      commit('setData', { stockSum: data });
    },
    async getStockSumAllCategory({ commit }) {
      const data1 = await getTopStockSum({ categoryType: 1 });
      const data2 = await getTopStockSum({ categoryType: 2 });
      const data3 = await getTopStockSum({ categoryType: 3 });
      commit('setData', { stockSumAllCategory: Number(data1) + Number(data2) + Number(data3) });
    },
  },
};
