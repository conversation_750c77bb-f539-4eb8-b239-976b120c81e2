<template>
  <div class="plan-info">
    <Tabs
      v-model="active"
      animated
      title-active-color="#323233"
      title-inactive-color="#686B73"
      line-height="5px"
    >
      <Tab title="申请信息">
        <div class="info">
          <div class="info-title">
            <img src="@/assets/icon-station.png" />
            <strong class="title">{{ form.storageUnit }}</strong>
            <div class="tags">
              <span class="tag category-tag">{{ form.category }}</span>
              <span class="tag level-tag" :style="{ 'background-color': levelColor[form.grade] }">
                <BizDictName
                  dict="GRAIN_AND_OIL_QUALITY_INSPECTION_GRADE"
                  :value="form.grade"
                ></BizDictName>
              </span>
            </div>
          </div>
          <p><span class="label">库点：</span>{{ form.store }}</p>
          <p><span class="label">仓廒_货位：</span>{{ form.storeHouse }}</p>
        </div>
        <div class="detail">
          <Field
            v-model="form.plannedNumber"
            label="配仓数量:"
            placeholder="输入框内容右对齐"
            input-align="right"
            readonly
          >
            <template #button><span style="color: #232323">吨</span></template>
          </Field>
          <Field
            v-model="form.yearGain"
            label="收获年份:"
            placeholder="输入框内容右对齐"
            input-align="right"
            readonly
          />
          <Field
            v-model="form.originPlace"
            label="产地:"
            placeholder="输入框内容右对齐"
            input-align="right"
            readonly
          />
        </div>
        <approvalButton :id="params.id" coder="rotationPlanAllocation"></approvalButton>
      </Tab>
      <Tab title="审批流程">
        <approvalProcess :id="params.id" coder="rotationPlanAllocation"></approvalProcess>
      </Tab>
    </Tabs>
  </div>
</template>

<script setup>
import { onMounted, ref } from 'vue';
import { Tab, Tabs, Field } from 'vant';
import approvalProcess from './common/approval-process';
import approvalButton from './common/approval-button';
import { useRoute } from 'vue-router';
import { getAllocationResultById } from '@/api/job-approve';
import { BizDictName } from '@/views/InoutManage/common';

const levelColor = {
  1: '#14C287',
  2: '#1973F1',
  3: '#FFA40D',
  4: '#8658F0',
  5: '#E95952',
  6: '#A56126',
  101: '#14C287',
  102: '#1973F1',
  103: '#FFA40D',
  104: '#8658F0',
  105: '#E95952',
};

const route = useRoute();

const params = ref(route.params);

const form = ref({
  count: '14.000吨',
  year: '2023年',
  area: '中国',
});

const getDetail = async () => {
  form.value = await getAllocationResultById({ id: params.value.id });
  form.value.plannedNumber = Number(form.value.plannedNumber).toFixed(3);
};

onMounted(() => {
  getDetail();
});
</script>

<style scoped lang="scss">
.info {
  padding: 16px;
  background: #fff;

  .info-title {
    display: flex;
    align-items: center;
    flex-wrap: wrap;
    .title {
      display: inline-block;
      max-width: 70%;
    }
    img {
      width: 20px;
      height: 20px;
      margin-right: 8px;
    }
    .tags {
      height: 24px;
      .tag {
        height: 24px;
        padding: 0 6px;
        line-height: 24px;
        border-radius: 2px;
        font-size: 14px;
        margin-left: 10px;
      }
      .category-tag {
        background: #8797af;
        color: #ffffff;
      }
      .level-tag {
        // background: #14c287;
        color: #ffffff;
      }
    }
  }
  p {
    font-size: 16px;
    color: #232323;
    margin-top: 16px;

    .label {
      color: #686b73;
      width: 120px;
    }
  }
}

.detail {
  margin-top: 10px;
}
</style>
