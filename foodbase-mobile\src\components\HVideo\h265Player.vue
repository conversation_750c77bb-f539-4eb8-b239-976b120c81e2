<template>
  <div class="container" id="videoPlayer">
    <div class="canvasDiv" ref="canvasWrap">
      <canvas
        id="playCanvas"
        ref="playCanvas"
        style="width: 100%; height: 100%; position: absolute; background-color: black; left: 0"
        width="1920"
        height="1080"
      ></canvas>
      <img v-if="loading" class="ddd" src="./images/ddd.gif" />
    </div>
    <div class="sideBar">
      <span class="no-padding" style="margin-left: 6px">
        <img
          src="./images/img/play.png"
          class="left"
          id="btnPlayVideo"
          ref="btnPlayVideo"
          @click="playVideo()"
          v-if="playVideoStatus"
        />
        <img
          src="./images/img/pause.png"
          class="left"
          id="btnPlayVideo"
          ref="btnPlayVideo"
          @click="playVideo()"
          v-else
        />
      </span>
      <span class="no-padding" style="padding-left: 5px">
        <img src="./images/img/stop.png" class="left" id="btnStopVideo" @click="stopVideo()" />
      </span>
      <span class="track-padding"> </span>
      <span class="no-padding" style="width: calc(100% - 300px)">
        <label id="timeLabel" v-if="stream" ref="timeLabel">LIVE</label>
        <input v-else style="width: 100%" id="timeTrack" ref="timeTrack" type="range" value="0" />
      </span>
      <span class="no-padding" style="padding-left: 10px; display: none">
        <label id="timeLabel" ref="timeLabel">00:00:00/00:00:00</label>
      </span>
      <!-- <span class="no-padding right">
        <img
          src="./images/img/fullscreen.png"
          class="right"
          id="btnFullscreen"
          @click="fullscreen()"
        />
      </span> -->
    </div>
  </div>
</template>

<script>
export default {
  name: 'H265Player',
  props: {
    url: {
      type: String,
      default: '',
    },
  },
  data() {
    return {
      player: null,
      playVideoStatus: false,
      loading: true,
    };
  },
  computed: {
    stream() {
      return this.url.indexOf('/live/') > -1; // 视频流都是live+设备id.flv
    },
  },
  mounted() {
    this.init();
  },
  methods: {
    init() {
      // eslint-disable-next-line no-undef
      this.player = new Player();
      this.playVideo();
    },
    playVideo() {
      // let logger = new logger('Page');
      let defaultProtos = {
        httpFlv: {
          url: this.url,
          waitLength: 512 * 1024,
          stream: this.stream,
        },
      };
      var protoObj = defaultProtos['httpFlv'];
      // var el = this.$refs.btnPlayVideo;
      var currentState = this.player.getState();

      // eslint-disable-next-line no-undef
      if (currentState == playerStatePlaying) {
        this.playVideoStatus = true;
      } else {
        this.playVideoStatus = false;
      }

      // eslint-disable-next-line no-undef
      if (currentState != playerStatePlaying) {
        const canvasId = 'playCanvas';
        var canvas = this.$refs[canvasId];
        if (!canvas) {
          // logger.logError('No Canvas with id ' + canvasId + '!');
          return false;
        }

        this.player.play(
          protoObj.url,
          canvas,
          function (e) {
            console.log('play error ' + e.error + ' status ' + e.status + '.');
            if (e.error == 1) {
              // logger.logInfo('Finished.');
            }
          },
          protoObj.waitLength,
          protoObj.stream,
          () => {
            this.loading = false;
          },
          this.$refs.canvasWrap,
        );

        var timeTrack = this.$refs.timeTrack;
        var timeLabel = this.$refs.timeLabel;
        this.player.setTrack(timeTrack, timeLabel);
      } else {
        this.player.pause();
      }

      return true;
    },
    stopVideo() {
      this.player.stop();
      this.playVideoStatus = true;
    },

    fullscreen() {
      this.$emit('fullscreen');
      setTimeout(() => {
        this.player.fullscreen();
      });
    },

    exitfullscreen() {
      this.player.exitfullscreen();
    },

    // onSelectProto() {
    //   stopVideo()
    //   var protoList = document.getElementById('protocol')
    //   var proto = protoList.options[protoList.selectedIndex].value
    //   var protoObj = defaultProtos[proto]
    //   var inputUrl = document.getElementById('inputUrl')
    //   inputUrl.value = protoObj['url']
    //   //getStreamCodeID();
    // },
  },
  beforeUnmount() {
    this.stopVideo();
  },
};
</script>

<style lang="scss" scoped>
.container {
  position: relative;
  &:hover {
    .sideBar {
      display: block;
    }
  }
}

.sideBar {
  width: 100%;
  height: 32px;
  padding: 2px 0;
  background-color: rgba(243, 244, 246, 0.7);
  position: absolute;
  bottom: 0;
  display: none;
  img {
    cursor: pointer;
  }
}

#btnHome:hover {
  transform: scale(1.2);
}

#btnPlayVideo:hover {
  transform: scale(1.2);
}

#btnStopVideo:hover {
  transform: scale(1.2);
}

#btnFullscreen:hover {
  transform: scale(1.2);
}

#timeTrack {
  margin: auto;
  // width: 540px;
  height: 28px;
  padding-top: 4px;
  line-height: 28px;
  &[type='range'] {
    -webkit-appearance: none;
    appearance: none;
    margin: 0;
    outline: 0;
    background-color: transparent;
    width: 500px;
  }
  &[type='range']::-webkit-slider-runnable-track {
    height: 4px;
    background: #eee;
  }
  &[type='range']::-webkit-slider-container {
    height: 20px;
    overflow: hidden;
  }
  &[type='range']::-webkit-slider-thumb {
    -webkit-appearance: none;
    appearance: none;
    width: 20px;
    height: 20px;
    border-radius: 50%;
    background-color: #20c2ff;
    border: 1px solid transparent;
    margin-top: -8px;
    border-image: linear-gradient(#20c2ff, #20c2ff) 0 fill / 8 20 8 0 / 0px 0px 0 2000px;
  }
}

#timeLabel {
  margin: auto;
  padding-top: 0;
  font-size: 14px;
  width: 200px;
  height: 28px;
  line-height: 28px;
  color: #000;
}

.loadEffect {
  width: 120px;
  height: 120px;
  position: absolute;
  margin: auto;
  top: 0;
  left: 0;
  bottom: 0;
  right: 0;
  z-index: 999;
}

.loadEffect span {
  display: inline-block;
  width: 20px;
  height: 20px;
  border-radius: 50%;
  background: lightgreen;
  position: absolute;
  -webkit-animation: load 1.04s ease infinite;
}
@-webkit-keyframes load {
  0% {
    opacity: 1;
  }
  100% {
    opacity: 0.2;
  }
}
.loadEffect span:nth-child(1) {
  left: 0;
  top: 50%;
  margin-top: -8px;
  -webkit-animation-delay: 0.13s;
}
.loadEffect span:nth-child(2) {
  left: 14px;
  top: 14px;
  -webkit-animation-delay: 0.26s;
}
.loadEffect span:nth-child(3) {
  left: 50%;
  top: 0;
  margin-left: -8px;
  -webkit-animation-delay: 0.39s;
}
.loadEffect span:nth-child(4) {
  top: 14px;
  right: 14px;
  -webkit-animation-delay: 0.52s;
}
.loadEffect span:nth-child(5) {
  right: 0;
  top: 50%;
  margin-top: -8px;
  -webkit-animation-delay: 0.65s;
}
.loadEffect span:nth-child(6) {
  right: 14px;
  bottom: 14px;
  -webkit-animation-delay: 0.78s;
}
.loadEffect span:nth-child(7) {
  bottom: 0;
  left: 50%;
  margin-left: -8px;
  -webkit-animation-delay: 0.91s;
}
.loadEffect span:nth-child(8) {
  bottom: 14px;
  left: 14px;
  -webkit-animation-delay: 1.04s;
}

.canvasDiv {
  width: 100%;
  height: 100%;
}

// #playCanvas {
//   width: 852px;
//   height: 480px;
// }

.no-padding {
  margin-top: 0px;
  padding-right: 0px;
  padding-left: 0px;
  padding-top: 0px;
  height: 28px;
  display: inline-block;
  float: left;
  vertical-align: middle;
}

.track-padding {
  padding-left: 0px;
  width: 30px;
  height: 28px;
  display: inline-block;
  float: left;
}

.left {
  width: 28px;
  float: left;
}

.right {
  width: 28px;
  float: right;
  margin-right: 5px;
}

#footer {
  height: 40px;
  line-height: 40px;
  position: fixed;
  bottom: 0;
  width: 100%;
  text-align: center;
  background: #f3f4f6;
  color: #000;
  font-family: Arial;
  font-size: 14px;
  letter-spacing: 1px;
}

#input {
  float: right;
  margin-right: 10px;
  margin-top: 3px;
  font-size: 14px;
}
</style>
