<template>
  <div>
    <Tabs v-model:active="status" @change="changeStatus(status)" swipeable>
      <Tab v-if="checkPermission('app-watch-reported-record:check')" :name="1" title="审核"> </Tab>
      <Tab :name="2" title="全部"> </Tab>
    </Tabs>
    <Content :status="status"></Content>
  </div>
</template>
<script setup>
import { Tabs, Tab } from 'vant';
import Content from './content.vue';
import { ref, onMounted } from 'vue';
import { checkPermission } from '@/utils/permission';

const status = ref(checkPermission('app-watch-reported-record:check') ? 1 : 2);

// const changeStatus = (val) => {
//   sessionStorage.setItem('tabStatus', val);
// };

onMounted(() => {
  // let s = sessionStorage.getItem('tabStatus');
  // status.value = s && s === '2' ? 2 : checkPermission('app-watch-reported-record:check') ? 1 : 2;
});
</script>
<style scoped></style>
