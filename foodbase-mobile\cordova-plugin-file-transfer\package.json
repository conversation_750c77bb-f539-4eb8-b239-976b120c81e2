{"name": "cordova-plugin-file-transfer", "version": "2.0.0-dev", "description": "Cordova File Transfer Plugin", "types": "./types/index.d.ts", "cordova": {"id": "cordova-plugin-file-transfer", "platforms": ["android", "ios", "windows", "browser"]}, "scripts": {"test": "npm run lint", "lint": "eslint ."}, "repository": "github:apache/cordova-plugin-file-transfer", "bugs": "https://github.com/apache/cordova-plugin-file-transfer/issues", "keywords": ["<PERSON><PERSON>", "file", "transfer", "ecosystem:cordova", "cordova-android", "cordova-ios", "cordova-windows", "cordova-browser"], "author": "Apache Software Foundation", "license": "Apache-2.0", "engines": {"cordovaDependencies": {"3.0.0": {"cordova": ">100"}}}, "devDependencies": {"@cordova/eslint-config": "^3.0.0"}}