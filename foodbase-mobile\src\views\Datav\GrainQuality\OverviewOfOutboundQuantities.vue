<template>
  <HCard title="出库数量总览">
    <template #header-extra>
      <span class="text-gray-400">单位：吨</span>
    </template>
    <div class="stock-origin-distribution">
      <div v-if="ready" class="p-4">
        <div class="flex items-center justify-center p-3 rounded-md bg-gray-50">
          <img src="@/views/Datav/GrainQuality/images/library-point.svg" alt="" srcset="" />
          <p class="ml-3">轮出计划数量</p>
          <p class="font-bold text-center text-[20px] ml-3">
            {{ (Number(outQualityAnalysisData?.outPlanTotal || 0) ?? 0)?.toLocaleString() ?? '-' }}
          </p>
        </div>
        <div class="flex" v-if="pieData.length > 0">
          <div class="relative flex-1">
            <div class="h-[180px] flex">
              <HEchart ref="pieChart" class="flex-1" :options="getOutInspectedPct()" />
            </div>
            <p class="font-bold text-center text-[30px] absolute top-[70px] w-full">
              {{ (Number(outQualityAnalysisData?.outInspectedPct || 0) * 100)?.toFixed(0) ?? 0 }}%
            </p>
            <p class="font-bold text-center text-[20px]">
              {{
                (
                  Number(outQualityAnalysisData?.outInspectedPct || 0) *
                  Number(outQualityAnalysisData?.outPlanTotal || 0)
                )?.toLocaleString() ?? 0
              }}
            </p>
            <p class="text-center">出库已质检粮食占比</p>
          </div>
          <div class="relative justify-center flex-1">
            <div class="h-[180px] flex">
              <HEchart ref="pieChart" class="flex-1" :options="getOutInspectedResultPct()" />
            </div>
            <p class="font-bold text-center text-[30px] absolute top-[70px] w-full">
              {{ (Number(outQualityAnalysisData?.outInspectedResultPct) * 100)?.toFixed(0) ?? 0 }}%
            </p>
            <p class="font-bold text-center text-[20px]">
              {{
                (
                  Number(outQualityAnalysisData?.outInspectedResultPct || 0) *
                  Number(outQualityAnalysisData?.outPlanTotal || 0)
                )?.toLocaleString() ?? 0
              }}
            </p>
            <p class="text-center">出库质检结果占比</p>
          </div>
        </div>
        <EmptyHolder v-else />
      </div>
    </div>
  </HCard>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue';
import { HCard, HEchart } from '@/components';
import EmptyHolder from '@/views/common/EmptyHolder';
import { useStore } from 'vuex';
import { reserveRequest } from '@/utils/request';
import dayjs from 'dayjs';
const store = useStore();
const outQualityAnalysisData = ref({});
const userAreaCode = store.getters['user/userAreaCode'];
const getData = async () => {
  outQualityAnalysisData.value = await reserveRequest().get(
    '/api/outQualityAnalysis/totalOutRotation',
    {
      params: {
        year: dayjs().format('YYYY'),
        area: userAreaCode,
      },
    },
  );
  return Promise.resolve();
};
const ready = ref(false);
onMounted(async () => {
  await getData();
  ready.value = true;
});

const pieChart = ref(null);

// const handleHighlightPieItem = (name) => {
//   if (!name) {
//     return;
//   }
//   pieChart.value.dispatchAction({ type: 'downplay' });
//   pieChart.value.dispatchAction({
//     type: 'highlight',
//     name: name,
//   });
// };

// let currentPieHighlight = false;
// const handlePieFinished = (force) => {
//   if (!currentPieHighlight || force) {
//     const firstValidItem = pieData.value.find((it) => !!it.value);
//     if (firstValidItem) {
//       handleHighlightPieItem(firstValidItem?.name);
//     } else {
//       handleHighlightPieItem(pieData.value[0]?.name);
//     }
//     currentPieHighlight = true;
//   }
// };

// const handlePieMouseDown = (params) => {
//   const { seriesType, data } = params;
//   if (seriesType === 'pie') {
//     const { name } = data;
//     handleHighlightPieItem(name);
//   }
// };

const colors = [
  ['#0ccc74', '#f5f6fa'],
  ['#f06744', '#f5f6fa'],
];
const pieData = (data, colorIndex) => {
  return data.map((i, index) => ({
    name: i.name,
    value: i.value || 0,
    itemStyle: { color: colors[colorIndex][index] },
  }));
};

const structureOptions = (pieData) =>
  computed(() => {
    return {
      legend: {
        show: false,
      },
      series: [
        {
          name: '库存品种结构',
          type: 'pie',
          radius: ['55%', '80%'],
          data: pieData,
          label: {
            show: false,
            position: 'center',
            fontSize: 18,
            fontWeight: 'bold',
            lineHeight: 25,
            color: '#323233',
            textBorderColor: 'transparent',
            formatter: () => {
              return ``;
            },
          },
          labelLine: {
            show: false,
          },
          emphasis: {
            // label: {
            //   show: false,
            // },
            // itemStyle: {
            //   shadowBlur: 10,
            //   shadowOffsetX: 0,
            //   shadowColor: 'rgba(0, 0, 0, 0.5)',
            // },
          },
          animationType: 'scale',
          animationEasing: 'elasticOut',
        },
      ],
    };
  });

const getOutInspectedPct = () => {
  return structureOptions(
    pieData(
      [
        { name: '', value: outQualityAnalysisData.value.outInspectedPct },
        { name: '', value: 1 - outQualityAnalysisData.value.outInspectedPct },
      ],
      0,
    ),
  ).value;
};

const getOutInspectedResultPct = () => {
  return structureOptions(
    pieData(
      [
        { name: '', value: outQualityAnalysisData.value.outInspectedResultPct },
        { name: '', value: 1 - outQualityAnalysisData.value.outInspectedResultPct },
      ],
      1,
    ),
  ).value;
};
</script>

<style lang="scss" scoped>
.chart {
  height: 230px;
}
.h-echarts {
  width: 100%;
  height: 100%;
}
</style>
