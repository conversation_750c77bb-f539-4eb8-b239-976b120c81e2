<template>
  <HCard title="库存收获年份分布（单位：吨）">
    <template #header-extra>
      <HPicker :options="foodCategoryOptions" v-model:value="selectedFoodCategory"></HPicker>
    </template>
    <div class="stock-year-structure">
      <div class="chart">
        <HEchart
          v-if="pieData.length"
          ref="pieChart"
          @e:mousedown="handlePieMouseDown"
          @e:finished="handlePieFinished"
          :options="structureOptions"
        />
        <EmptyHolder v-else />
      </div>
    </div>
  </HCard>
</template>

<script setup>
import { computed, ref, watch } from 'vue';
import { HCard, HEchart } from '@/components';
import { useStore } from 'vuex';
import HPicker from '@/components/HPicker/HPicker';
import { dataVFoodCategoryMap } from '@/views/StockOverview/constant';
import EmptyHolder from '@/views/common/EmptyHolder';
import { sumBy } from 'lodash-es';

const props = defineProps({
  categoryType: Number, // 1 原粮，2 成品粮， 3 食用油
});

const store = useStore();

const foodCategoryOptions = computed(() => {
  const foodCategories = dataVFoodCategoryMap[props.categoryType] || [];
  return foodCategories.map((it) => {
    return {
      text: it.name,
      value: it.id,
    };
  });
});

const selectedFoodCategory = ref(foodCategoryOptions.value[0]?.value);

watch(
  selectedFoodCategory,
  (newValue, oldValue) => {
    if (newValue != oldValue) {
      store.dispatch('stock-overview/fetchStockYearStructureData', {
        categoryType: props.categoryType,
        yearCategory: newValue,
      });
    }
  },
  {
    deep: true,
    immediate: true,
  },
);

const stockStructure = computed(() => {
  const stockYearStructureDataMap = store.state['stock-overview'].stockYearStructureDataMap;
  const stockYearStructureData = stockYearStructureDataMap.find(
    (it) => it.category == props.categoryType,
  )?.data;
  return stockYearStructureData;
});

const pieData = computed(() => {
  let items = stockStructure.value.map((it) => {
    return {
      name: Number(it.year),
      value: it?.value || 0,
    };
  });
  items.sort((a, b) => b.name - a.name);
  if (items.length > 5) {
    const splicedItems = items.splice(5); // 切下5个之后的
    const otherTotal = sumBy(splicedItems, 'value');
    items.push({
      name: '其他',
      value: otherTotal,
    });
  }
  return items.map((item) => {
    return {
      name: String(item.name),
      value: item?.value || 0,
    };
  });
});

const structureOptions = computed(() => {
  return {
    legend: {
      show: true,
      textStyle: {
        color: '#363A44',
        fontSize: 18,
        lineHeight: 24,
      },
      selectedMode: false,
      orient: 'vertical',
      top: 'middle',
      right: 30,
      itemWidth: 16,
      itemHeight: 16,
    },
    color: [
      '#fa8151',
      '#00de83',
      '#0092f9',
      '#1d4eff',
      '#f44765',
      '#1cd3f3',
      '#5470c6',
      '#3ba272',
      '#9a60b4',
      '#ea7ccc',
    ],
    series: [
      {
        type: 'pie',
        radius: ['55%', '80%'],
        data: pieData.value,
        left: -120,
        label: {
          show: false,
          position: 'center',
          fontSize: 18,
          fontWeight: 'bold',
          lineHeight: 25,
          color: '#323233',
          textBorderColor: 'transparent',
          formatter: ({ name, percent }) => {
            return `${name}\n${percent}%`;
          },
        },
        labelLine: {
          show: false,
        },
        emphasis: {
          label: {
            show: true,
          },
          itemStyle: {
            shadowBlur: 10,
            shadowOffsetX: 0,
            shadowColor: 'rgba(0, 0, 0, 0.5)',
          },
        },
        animationType: 'scale',
        animationEasing: 'elasticOut',
      },
    ],
  };
});

const pieChart = ref(null);

const handleHighlightPieItem = (name) => {
  pieChart.value.dispatchAction({ type: 'downplay' });
  pieChart.value.dispatchAction({
    type: 'highlight',
    name: name,
  });
};

let currentPieHighlight = false;

const handlePieFinished = () => {
  if (!currentPieHighlight) {
    handleHighlightPieItem(pieData.value[0]?.name);
    currentPieHighlight = true;
  }
};
const handlePieMouseDown = (params) => {
  const { seriesType, data } = params;
  if (seriesType === 'pie') {
    const { name } = data;
    handleHighlightPieItem(name);
  }
};

watch(pieData, () => {
  currentPieHighlight = false;
});
</script>

<style scoped lang="scss">
.stock-year-structure {
  .chart {
    height: 230px;
  }
}

.h-picker {
  line-height: 34px;
  color: var(--van-gray-7);
}

.h-echarts {
  width: 100%;
  height: 100%;
}
</style>
