<template>
  <div class="stock-variety-structure">
    <div class="chart">
      <HEchart
        ref="pieChart"
        @e:mousedown="handlePieMouseDown"
        @e:finished="handlePieFinished"
        :options="structureOptions"
      />
    </div>
    <div class="category-list">
      <div class="category" v-for="cate in pieData" :key="cate.id">
        <span class="icon" :style="{ backgroundColor: cate.itemStyle.color }"></span>
        <span class="name">{{ cate.name }}</span>
        <!-- <HFixedNumber :fraction-digits="0" class="value" :key="cate.value">
          {{ cate.value }}
        </HFixedNumber> -->
      </div>
    </div>
  </div>
</template>

<script setup>
import { computed, ref, inject, watch } from 'vue';
import { HEchart } from '@/components';
// import { dataVFoodCategories, dataVFoodCategoryMap } from '@/views/StockOverview/constant';
import { useStore } from 'vuex';

// const props = defineProps({
//   categoryType: Number, // 1 原粮，2 成品粮， 3 食用油
// });

const store = useStore();

const stockStructure = computed(() => {
  const sort = [2, 0, 1, 3];
  const stockLeaveStructureData = store.state['stock-overview'].stockLeaveStructureData;
  console.log(stockLeaveStructureData);
  return stockLeaveStructureData
    .map((i, index) => ({ ...i, sort: process.env.VUE_APP_MODE === 'hubei' ? sort[index] : 0 }))
    .sort((a, b) => a.sort - b.sort);
});

const colors = ['#00F8FA', '#FFB100', '#00DE83', '#FC663B', '#8C4EF0', '#0092FF'];
const pieData = computed(() => {
  return stockStructure.value.map((i, index) => ({
    name: i.name,
    value: i.value || 0,
    itemStyle: { color: colors[index] },
  }));
});

const structureOptions = computed(() => {
  return {
    legend: {
      show: false,
    },
    series: [
      {
        name: '库存品种结构',
        type: 'pie',
        radius: ['55%', '80%'],
        data: pieData.value,
        label: {
          show: false,
          position: 'center',
          fontSize: 18,
          fontWeight: 'bold',
          lineHeight: 25,
          color: '#323233',
          textBorderColor: 'transparent',
          formatter: ({ name, percent }) => {
            return `${name}\n${percent}%`;
          },
        },
        labelLine: {
          show: false,
        },
        emphasis: {
          label: {
            show: true,
          },
          itemStyle: {
            shadowBlur: 10,
            shadowOffsetX: 0,
            shadowColor: 'rgba(0, 0, 0, 0.5)',
          },
        },
        animationType: 'scale',
        animationEasing: 'elasticOut',
      },
    ],
  };
});

const pieChart = ref(null);

const handleHighlightPieItem = (name) => {
  if (!name) {
    return;
  }
  pieChart.value.dispatchAction({ type: 'downplay' });
  pieChart.value.dispatchAction({
    type: 'highlight',
    name: name,
  });
};

let currentPieHighlight = false;
const handlePieFinished = (force) => {
  if (!currentPieHighlight || force) {
    const firstValidItem = pieData.value.find((it) => !!it.value);
    if (firstValidItem) {
      handleHighlightPieItem(firstValidItem?.name);
    } else {
      handleHighlightPieItem(pieData.value[0]?.name);
    }
    currentPieHighlight = true;
  }
};

const category = inject('category');

watch(
  () => category.value,
  () => {
    setTimeout(() => {
      handlePieFinished(true);
    }, 1000);
  },
);

const handlePieMouseDown = (params) => {
  const { seriesType, data } = params;
  if (seriesType === 'pie') {
    const { name } = data;
    handleHighlightPieItem(name);
  }
};
</script>

<style scoped lang="scss">
.stock-variety-structure {
  .chart {
    height: 230px;
  }

  .category-list {
    display: flex;
    flex-wrap: wrap;
    padding: 0 16px;

    .category {
      min-width: 50%;
      font-size: 16px;
      line-height: 32px;
      user-select: none;
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;

      .icon {
        display: inline-block;
        width: 14px;
        height: 14px;
        border-radius: 50%;
      }

      .name {
        display: inline-block;
        margin-left: 10px;
        margin-right: 8px;
      }
      .value {
        font-size: 16px;
      }
    }
  }
}

.h-echarts {
  width: 100%;
  height: 100%;
}
</style>
