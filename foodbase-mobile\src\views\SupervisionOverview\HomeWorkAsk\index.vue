<template>
  <div class="homework-ask">
    <van-form required label-width="100px" @submit="onSubmit">
      <Field
        v-model="queryForm.storehouseText"
        label="仓房"
        readonly
        is-link
        required
        name="storehouseText"
        input-align="left"
        placeholder="请选择"
        @click="() => showStoreHousePicker = true"
        size="large"
        :rules="[{ required: true, message: '请选择' }]"
      />
      <Field
        v-model="queryForm.jobTypeName"
        label="作业类型"
        readonly
        is-link
        required
        name="jobTypeName"
        input-align="left"
        placeholder="请选择"
        @click="() => showJobType = true"
        size="large"
        :rules="[{ required: true, message: '请选择' }]"
      />
      <Field
        v-model="queryForm.plainOperateTime"
        readonly
        required
        name="plainOperateTime"
        label="计划操作时间"
        placeholder="请选择"
        @click="() => showPicker = true"
        right-icon="warning-o"
        :rules="[{ required: true, message: '请选择' }]"
      >
        <template #right-icon>
          <img class="abarnimg mr-[10px]" src="@/assets/date.png" alt="" />
        </template>
      </Field>
      <van-cell-group class="mt-4">
        <van-cell title="操作员" :value="userInfo?.nickName" />
        <van-cell title="申请时间" :value="dayjs(new Date()).format('YYYY-MM-DD')" />
      </van-cell-group>
      <div class="submit-btn">
        <van-button type="primary" block round :loading="loading" native-type="submit">
          发起申请
        </van-button>
      </div>
    </van-form>
    <HMultipleSelect
      v-model:visible="showStoreHousePicker"
      :options="storeHouseOptions"
      :fieldName="{ label: 'text', value: 'value'}"
      @change="onStoreHouseConfirm"
    />
    <Popup v-model:show="showJobType" position="bottom">
      <Picker
        :columns="jobTypeOptions"
        @confirm="onJobTypeConfirm"
        @cancel="() => showJobType = false"
      />
    </Popup>
    <popup v-model:show="showPicker" position="bottom">
      <van-datetime-picker
        ref="datetimePicker"
        v-model="currentDate"
        type="datetime"
        title="选择日期时间"
        :columns-order="['year', 'month', 'day', 'hour', 'minute', 'second']"
        @cancel="onDatetimeCancel"
        @confirm="onDatetimeConfirm"
      />
    </popup>
  </div>
</template>

<script setup>
import { ref, onMounted } from "vue";
import { getStoreHouseAndOil } from '@/api/supervision';
import { Field, Picker, Popup, Toast } from 'vant';
import dayjs from 'dayjs';
import { useStore } from 'vuex';
import { startHomeworkAsk, saveApproveTask, getStoreInfoByUser } from '@/api/home-work.js'
import { useRouter } from 'vue-router';

const router = useRouter();

const store = useStore();

const userInfo = store.getters['user/userInfo'];

const queryForm = ref({
  storeId: undefined,
  storeHouseIds: undefined,
  storehouseNames: undefined,
  storehouseText: undefined,
  jobTypeCode: undefined,
  jobTypeName: undefined,
  plainOperateTime: undefined,
})

const storeHouseOptions = ref([])

const jobTypeOptions = ref([
  {
    text: '通风作业',
    value: 'JOB_TYPE_02',
  },
  {
    text: '控温作业',
    value: 'JOB_TYPE_03',
  },
  {
    text: '气调作业',
    value: 'JOB_TYPE_04',
  },
  {
    text: '薰蒸作业',
    value: 'JOB_TYPE_05',
  },
])

const showStoreHousePicker = ref(false)

const showPicker = ref(false)

const showJobType = ref(false)

//默认赋值时间
const currentDate = ref(new Date());

const loading = ref(false);

const onStoreHouseConfirm = (value, text) => {
  if (value && value.length > 0) {
    queryForm.value.storehouseNames = text;
    queryForm.value.storeHouseIds = value;
    queryForm.value.storehouseText = text.join(',')
  } else {
    queryForm.value.storehouseNames = undefined
    queryForm.value.storeHouseIds = undefined
    queryForm.value.storehouseText = undefined
  }
}

const onDatetimeCancel = () => {
  currentDate.value = new Date();
  showPicker.value = false;
}

const onDatetimeConfirm = (val) => {
  let date = dayjs(val).format('YYYY-MM-DD HH:mm');
  queryForm.value.plainOperateTime = date;
  showPicker.value = false;
}

const onJobTypeConfirm = (val) => {
  queryForm.value.jobTypeCode = val.value
  queryForm.value.jobTypeName = val.text
  showJobType.value = false;
}

const onSubmit = () => {
  loading.value = true
  startHomeworkAsk({
    jobTypeCode: queryForm.value.jobTypeCode,
    jobTypeName: queryForm.value.jobTypeName,
    plainOperateTime: queryForm.value.plainOperateTime,
    storeId: queryForm.value.storeId,
    storeHouseIds: queryForm.value.storeHouseIds?.join(','),
    storehouseNames: queryForm.value.storehouseNames?.join(","),
  }).then(res => {
    return saveApproveTask({ businessId: res?.id, coder: 'mobile-jobApply' })
  })
  .then(() => {
    Toast.success('申请成功！');
    loading.value = false
    setTimeout(() => {
      router.back();
    }, 500)
  }).catch(() => {
    loading.value = false
  })
}

//获取仓房下拉框数据
const loadStoreHouses = async () => {
  storeHouseOptions.value = [];
  const res = await getStoreInfoByUser({ deptId: userInfo?.dept?.id })
  if (res) {
    queryForm.value.storeId = res?.id
    const list = await getStoreHouseAndOil({ storeId: res?.id });
    storeHouseOptions.value = list?.map((it) => {
      return {
        text: it.name,
        value: it.id,
      };
    });
  }
};

onMounted(() => {
  loadStoreHouses()
})
</script>

<style lang="scss" scoped>
.homework-ask {
  width: 100%;
  height: 100%;
  position: relative;
  .submit-btn {
    width: 100%;
    position: absolute;
    bottom: 0px;
    left: 0px;
    padding: 10px 20px;
  }
}
</style>
