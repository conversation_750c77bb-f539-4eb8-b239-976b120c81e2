import { getDryTowerList } from '@/api/in-out-manage';

export default {
  namespaced: true,
  state: {
    list: [],
  },
  mutations: {
    setList(state, list) {
      state.list = list;
    },
  },
  actions: {
    async load({ state, commit }, forceReload = false) {
      if (state.list.length > 0 && !forceReload) {
        return;
      }
      const list = await getDryTowerList();
      commit('setList', list);
    },
  },
};
