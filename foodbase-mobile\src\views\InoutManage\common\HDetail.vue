<template>
  <div class="h-field">
    <div class="row">
      <div class="title" :style="{ width: labelWidth }">{{ title }}</div>
      <div class="value">
        <slot name="value">{{ value }}</slot>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: '<PERSON><PERSON>ield',
  props: {
    title: {
      type: String,
      default: () => '',
    },
    value: {
      type: Boolean,
      default: () => false,
    },
    labelWidth: {
      type: String,
      default: '110px',
    },
  },
  data() {
    return {};
  },
};
</script>

<style lang="scss" scoped>
.h-field {
  margin-top: 6px;
  line-height: 30px;
  .row {
    display: flex;
    .title {
      // width: 160px;
      font-size: 14px;
      font-weight: 400;
      color: #6d748d;
      // flex-basis: 6em;
    }
    .value {
      font-size: 14px;
      font-weight: 400;
      color: #0f0f0f;
      flex-grow: 1;
    }
  }
}
</style>
