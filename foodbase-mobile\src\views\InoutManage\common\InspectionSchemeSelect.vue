<template>
  <van-picker
    :columns="filledList"
    :columns-field-names="fieldNames"
    @cancel="onCancel"
    @confirm="onConfirm"
  ></van-picker>
</template>

<script>
import { Picker } from 'vant';
import { getInspectionSchemeList } from '@/api/in-out-manage';
import { memoize } from 'lodash-es';

const getMemoizedSchemeList = memoize(async (foodCategory) => {
  return await getInspectionSchemeList(foodCategory);
});

export default {
  name: 'InspectionSchemeSelect',
  inheritAttrs: false,
  components: {
    'van-picker': Picker,
  },
  emits: ['cancel', 'confirm'],
  props: {
    foodCategory: [String, Number],
    onlyRealScheme: {
      type: Boolean,
      default: false,
    },
  },
  created() {
    this.getSchemeList(this.foodCategory);
  },
  data() {
    return {
      list: [],
      fieldNames: {
        text: 'name',
        value: 'id',
      },
    };
  },
  computed: {
    filledList() {
      if (this.onlyRealScheme) {
        return this.list;
      }
      return [
        { id: 0, name: '待定' },
        { id: -1, name: '(不做质检)' },
        { id: -2, name: '(复用质检结果)' },
        ...this.list,
      ];
    },
  },
  methods: {
    async getSchemeList(foodCategory) {
      this.list = await getMemoizedSchemeList(foodCategory);
    },
    onCancel() {
      this.$emit('cancel');
    },
    onConfirm(value) {
      this.$emit('confirm', value);
    },
  },
  watch: {
    foodCategory(value) {
      this.getSchemeList(value);
    },
  },
};
</script>
