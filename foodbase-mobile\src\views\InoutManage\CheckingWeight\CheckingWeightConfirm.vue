<template>
  <div class="checking-weight-confirm">
    <van-form ref="form" @submit="onSave">
      <van-cell-group inset>
        <!-- 称重 -->
        <van-field
          v-model="weighTyperText"
          label="称重"
          is-link
          readonly
          name="signPicker"
          placeholder="请选择"
          required
          :rules="[{ required: true, message: '请选择称重' }]"
          @click="showWeighTyperPicker = true"
        >
        </van-field>
        <van-popup v-model:show="showWeighTyperPicker" position="bottom">
          <van-picker
            :columns="weighTyperColumns"
            @confirm="onWeighTyperConfirm"
            @cancel="showWeighTyperPicker = false"
          />
        </van-popup>

        <!-- 称重时间 -->
        <van-field
          v-model="weightTime"
          label="称重时间"
          is-link
          readonly
          name="timePicker"
          required
          :rules="[{ required: true, message: '请选择称重时间' }]"
          placeholder="请选择"
          @click="showTimePicker = true"
        >
        </van-field>
        <van-popup v-model:show="showTimePicker" position="bottom">
          <van-datetime-picker
            type="datetime"
            v-model="form.weightTime"
            :formatter="timeFormatter"
            :min-date="minDate"
            @confirm="onTimeConfirm"
            @cancel="showTimePicker = false"
            :columns-order="['year', 'month', 'day', 'hour', 'minute', 'second']"
          ></van-datetime-picker>
        </van-popup>

        <!-- 重量(公斤) -->
        <van-field
          v-model="form.count"
          label="重量(公斤)"
          name="signPicker"
          placeholder="请输入"
          required
          :rules="[{ required: true, message: '请输入重量' }]"
          type="textarea"
          @input="handleInput"
        >
        </van-field>

        <!--称重类型  -->
        <van-field
          v-model="subDataSourceText"
          label="称重类型"
          is-link
          readonly
          name="signPicker"
          placeholder="请选择"
          required
          :rules="[{ required: true, message: '请选择称重类型' }]"
          @click="showSubDataSourcePicker = true"
        >
        </van-field>
        <van-popup v-model:show="showSubDataSourcePicker" position="bottom">
          <van-picker
            :columns="subDataSourceColumns"
            @confirm="onSubDataSourceConfirm"
            @cancel="showSubDataSourcePicker = false"
          />
        </van-popup>

        <!-- 称重照片 -->
        <van-field name="uploader" label="称重照片">
          <template #input>
            <div class="upload-item">
              <Uploader
                :model-value="fileListFront"
                :before-read="beforeRead"
                class="upload-cls"
                :max-count="1"
                @click-upload="onClickUploadFront"
                @delete="onDeleteFileFront"
              />
              <span style="text-align: center">{{ '前摄像头' }}</span>
            </div>
            <div class="upload-item">
              <Uploader
                :model-value="fileListBehind"
                :before-read="beforeRead"
                class="upload-cls"
                :max-count="1"
                @click-upload="onClickUploadBehind"
                @delete="onDeleteFileBehind"
              />
              <span style="text-align: center">{{ '后摄像头' }}</span>
            </div>
            <div class="upload-item">
              <Uploader
                :model-value="fileListUp"
                :before-read="beforeRead"
                class="upload-cls"
                :max-count="1"
                @click-upload="onClickUploadUp"
                @delete="onDeleteFileUp"
              />
              <span style="text-align: center">{{ '顶摄像头' }}</span>
            </div>
          </template>
        </van-field>
        <img :src="watermarkedDataUrl" alt="" v-if="watermarkedDataUrl" />

        <!-- 称重照片 -->
        <Tabbar>
          <TabbarItem class="cancelTab">
            <Button class="com-btn end" @click="onCancel">取消</Button>
          </TabbarItem>
          <TabbarItem class="saveTab">
            <Button class="com-btn save" type="primary" native-type="submit">确定</Button>
          </TabbarItem>
        </Tabbar>
      </van-cell-group>
    </van-form>

    <ActionSheet
      v-model:show="showActionSheet"
      cancel-text="取消"
      description="请选择上传方式"
      :actions="actions"
      @select="onUploadActionSelect"
      close-on-click-action
    />
  </div>
</template>
<script>
import { timeFormatter } from '@/utils/inout-manage';
import {
  Form,
  Field,
  Button,
  Popup,
  DatetimePicker,
  Picker,
  Uploader,
  Toast,
  Tabbar,
  TabbarItem,
  CellGroup,
  ActionSheet,
} from 'vant';
import dayjs from 'dayjs';
import { getReserverToken } from '@/utils/auth';
import { getFileUrls } from '@/api/in-out-manage';
import { mapState } from 'vuex';
import { addWeight } from '@/api/in-out-manage';
export default {
  components: {
    'van-form': Form,
    'van-field': Field,
    'van-popup': Popup,
    'van-datetime-picker': DatetimePicker,
    'van-picker': Picker,
    'van-cell-group': CellGroup,
    Button,
    Tabbar,
    Uploader,
    TabbarItem,
    ActionSheet,
  },
  data() {
    return {
      minDate: new Date(2020, 0, 1),
      outForm: [],
      fileItem: {},
      form: {
        weighTyper: 2,
        subDataSource: 21,
      },
      weighTyperText: '称毛',
      subDataSourceText: '地磅',
      weightTime: '',
      actions: [{ name: '拍照' }, { name: '相册选择' }],
      weighTyperColumns: [
        { text: '称毛', value: 2 },
        { text: '称皮', value: 3 },
      ],
      subDataSourceColumns: [
        { text: '地磅', value: 21 },
        { text: '散料称', value: 22 },
      ],
      showWeighTyperPicker: false,
      showTimePicker: false,
      showSubDataSourcePicker: false,
      showActionSheet: false,
      weighFinish: false, // 确认净重后不能再确认本次称重，数据填报，调整其他扣量和包装物扣量
      fileListFront: [],
      fileListBehind: [],
      fileListUp: [],
      files: [],
      watermarkedDataUrl: '',
      uploadPicType: null, // 判断前后顶摄像头
    };
  },
  computed: {
    ...mapState({
      nickName: (state) => state.user?.info?.nickName,
    }),
  },
  mounted() {
    console.log(this.$route.params);
    this.form.weighTyper = +this.$route.params.weighTyper;
    this.outForm = JSON.parse(this.$route.params.outForm);
    this.form.weightTime = new Date();
    this.weightTime = dayjs(new Date()).format('YYYY-MM-DD HH:mm:ss');
    if (this.form.weighTyper === 3) {
      this.weighTyperText = '称皮';
    }
  },
  methods: {
    timeFormatter,
    onClickUploadFront(e) {
      e.preventDefault();
      if (navigator.camera) {
        this.showActionSheet = true;
        this.uploadPicType = 1;
      } else {
        Toast('不支持选择照片');
      }
    },
    onClickUploadBehind(e) {
      e.preventDefault();
      if (navigator.camera) {
        this.showActionSheet = true;
        this.uploadPicType = 2;
      } else {
        Toast('不支持选择照片');
      }
    },
    onClickUploadUp(e) {
      e.preventDefault();
      if (navigator.camera) {
        this.showActionSheet = true;
        this.uploadPicType = 3;
      } else {
        Toast('不支持选择照片');
      }
    },
    handleInput(e) {
      // 固定两位小数
      e.target.value = e.target.value.replace(/[^\d.]/g, ''); //清除“数字”和“.”以外的字符
      e.target.value = e.target.value.replace(/\.{2,}/g, '.'); //只保留第一个. 清除多余的
      e.target.value = e.target.value.replace('.', '$#$').replace(/\./g, '').replace('$#$', '.');
      e.target.value = e.target.value.replace(/^(-)*(\d+)\.(\d\d\d).*$/, '$1$2.$3'); //只能输入两个小数
      e.target.value = e.target.value.replace(/^\./g, ''); //首位不能输入“.”
      if (e.target.value.indexOf('.') < 0 && e.target.value != '') {
        //如果没有小数点，首位不能为0，如01、02...
        e.target.value = parseFloat(e.target.value);
      }
      e.target.value = e.target.value.match(/^\d*(\.?\d{0,3})/g)[0] || '';
      this.$nextTick(() => {
        this.form.count = e.target.value;
      });
    },
    beforeRead(file) {
      if (file.type.includes('jpg') || file.type.includes('jpeg') || file.type.includes('png')) {
        return true;
      } else {
        Toast('请上传 jpg, png 格式图片');
        return false;
      }
    },
    onDeleteFileFront(file, { index }) {
      this.fileListFront.splice(index, 1);
    },
    onDeleteFileBehind(file, { index }) {
      this.fileListBehind.splice(index, 1);
    },
    onDeleteFileUp(file, { index }) {
      this.fileListUp.splice(index, 1);
    },
    onUploadActionSelect({ name }) {
      const Camera = window.Camera;
      let sourceType = Camera.PictureSourceType.PHOTOLIBRARY;
      if (name === '拍照') {
        sourceType = Camera.PictureSourceType.CAMERA;
      } else if (name === '相册选择') {
        sourceType = Camera.PictureSourceType.SAVEDPHOTOALBUM;
      }
      navigator.camera.getPicture(
        (imageUri) => {
          //图片添加水印
          // console.log(imageUri, 'imageUri');
          // this.setWatermark(imageUri);
          // this.addWatermark(imageUri);
          // 添加图片到图片列表
          // console.log(this.watermarkedDataUrl, 'this.watermarkedDataUrl');
          this.fileItem = {
            url: imageUri,
            isImage: true,
            status: 'uploading',
            deletable: true,
          };
          if (this.uploadPicType === 1) {
            this.fileListFront.push(this.fileItem);
          } else if (this.uploadPicType === 2) {
            this.fileListBehind.push(this.fileItem);
          } else if (this.uploadPicType === 3) {
            this.fileListUp.push(this.fileItem);
          }

          // 上传参数
          const options = new window.FileUploadOptions();
          options.fileKey = 'file';
          options.fileName = imageUri.substr(imageUri.lastIndexOf('/') + 1);
          options.headers = {
            Authorization: getReserverToken(),
          };
          const params = {};
          params.coder = 'sgcb/storehouse_document';
          options.params = params;

          // 上传地址
          const reserverBaseUrl = () =>
            JSON.parse(process.env.VUE_APP_RESERVER_API_BASE_URL)[window.__isProd] ||
            location.origin;
          const uploadUri = new URL(
            '/reserver/api/fileManager/uploadMarkFileBatch',
            reserverBaseUrl(),
          ).toString();
          // 上传文件
          const fileTransfer = new window.FileTransfer();
          fileTransfer.upload(
            imageUri,
            uploadUri,
            (res) => {
              // 上传成功
              const resp = res.response;
              if (resp) {
                const respJson = JSON.parse(resp);
                const { data } = respJson;
                this.fileItem.name = data.split(',')[0];
                this.fileItem.status = 'done';

                getFileUrls(data).then((urls) => {
                  console.log(urls, 'urls');
                  this.fileItem.url = urls[0];
                  this.fileItem.fileId = data;
                  // this.setWatermark(this.fileItem.url);
                });
              }
            },
            (error) => {
              // 上传失败
              this.fileItem.status = 'failed';
              Toast('上传失败');
              console.error(error);
            },
            options,
          );
        },
        (err) => {
          Toast('选择图片失败');
          console.error(err);
        },
        {
          quality: 85,
          destinationType: Camera.DestinationType.FILE_URI,
          sourceType: sourceType,
        },
      );
    },
    async setWatermark(imageUri) {
      // 创建一个新的 Image 元素
      var img = new Image();

      // 设置图像加载完成时的处理函数
      img.onload = async function () {
        console.log(1);
        // 创建一个 Canvas 元素
        var canvas = document.createElement('canvas');
        var ctx = canvas.getContext('2d');
        const watermarkText = this.nickName;
        const watermarkFontSize = 20;

        // 设置 Canvas 大小与图像相同
        canvas.width = img.width;
        canvas.height = img.height;

        // 在 Canvas 上绘制图像
        ctx.drawImage(img, 0, 0);
        console.log('99w99');
        // 计算水印文本的位置
        const textWidth = ctx.measureText(watermarkText)?.width + watermarkText?.length * 10;
        const x = canvas.width - textWidth - 50;
        const y = canvas.height - 50;

        // 添加水印文本
        ctx.font = `${watermarkFontSize}px Arial`;
        ctx.fillStyle = 'rgba(255, 255, 255, 0.5)';
        ctx.fillText(watermarkText, x, y, textWidth);
        console.log(9999);
        // 获取带有水印的图像数据
        const watermarkedDataUrl = canvas.toDataURL('image/jpeg');

        console.log(watermarkedDataUrl, 'watermarkedFile');
        this.watermarkedDataUrl = watermarkedDataUrl;
        // 示例：在页面上显示带有水印的图像
      };

      // 将照片 URI 设置为 Image 元素的 src
      img.src = imageUri;
    },
    async onSave() {
      if (dayjs(this.weightTime).valueOf() > dayjs().valueOf()) {
        Toast('称重时间不能够选择超过当前时间');
        return;
      }
      try {
        const data = {
          ...this.outForm,
          weighTyper: this.form.weighTyper,
          count: this.form.count / 1000,
          createTime: dayjs(this.weightTime).format('YYYY-MM-DD HH:mm:ss'),
          subDataSource: this.form.subDataSource,
          dataSource: 2, // 数据来源为数据填报
          imageInfoList: {
            vehiclePictureAfter: this.fileListFront[0]?.fileId,
            vehiclePictureBefore: this.fileListBehind[0]?.fileId,
            vehiclePictureTop: this.fileListUp[0]?.fileId,
          },
        };
        await addWeight({
          weighDetailDTOList: [data],
          weighingDataType: 1,
          isWeighRepeatedly: this.outForm.isWeighRepeatedly,
        });
        this.onCancel();
      } catch (error) {
        console.log(error);
      } finally {
        this.saveWeightLoading = false;
      }
    },
    onCancel() {
      this.$router.back();
    },
    dataURLtoFile(dataUrl, filename) {
      const arr = dataUrl.split(',');
      const mime = arr[0].match(/:(.*?);/)[1];
      const bstr = arr[1];
      let n = bstr.length;
      const u8arr = new Uint8Array(n);
      while (n--) {
        u8arr[n] = bstr.charCodeAt(n);
      }
      return new File([u8arr], filename, { type: mime });
    },
    onWeighTyperConfirm(val) {
      this.form.weighTyper = val.value;
      this.weighTyperText = val.text;
      this.showWeighTyperPicker = false;
    },
    onTimeConfirm(val) {
      this.weightTime = dayjs(val).format('YYYY-MM-DD HH:mm:ss');
      this.showTimePicker = false;
    },
    onSubDataSourceConfirm(val) {
      this.form.subDataSource = val.value;
      this.subDataSourceText = val.text;
      this.showSubDataSourcePicker = false;
    },
  },
};
</script>
<style lang="scss" scoped>
.checking-weight-confirm {
  background-color: white;
  .cancelTab,
  .saveTab {
    width: 50%;
    padding: 10px 10px;
  }
  .com-btn {
    padding: 0 60px;
    margin-bottom: 15px;
  }
  .upload-item {
    display: flex;
    flex-direction: column;
    justify-content: center;
  }
}
</style>
