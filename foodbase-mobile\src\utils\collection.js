import { forEach, keyBy, clone } from 'lodash-es';

// 列表生成树
export function generateTree(list, id = 'id', pid = 'pid') {
  const keyedMap = keyBy(clone(list), id);
  const tree = [];
  forEach(keyedMap, (it) => {
    const parentId = it[pid];
    const parent = keyedMap[parentId];
    if (parent) {
      if (!parent.children) {
        parent.children = [];
      }
      parent.children.push(it);
    } else {
      tree.push(it);
    }
  });

  return tree;
}

// 根据 callback 查找节点，返回节点组成的路径
export function getPath(tree, callback) {
  const stack = tree.map((node) => [node, [node]]);
  while (stack.length) {
    const [node, path] = stack.pop();
    if (callback(node)) {
      return path;
    }
    if (node.children) {
      stack.push(...node.children.map((node) => [node, [...path, node]]));
    }
  }
  return [];
}

// 根据 callback 查找节点，返回节点
export function getNode(tree, callback) {
  const stack = [...tree];
  while (stack.length) {
    const node = stack.shift();
    if (callback(node)) {
      return node;
    }
    if (node.children) {
      stack.push(...node.children);
    }
  }
  return null;
}
