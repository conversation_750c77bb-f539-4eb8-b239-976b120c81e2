<template>
  <div>
    <HouseDetailCard :detailInfo="detailInfo" />
    <HCard class="opinion-card">
      <img
        class="status-icon"
        v-if="detailInfo.status == 4"
        src="@/assets/icon-status-finish.png"
      />
      <img class="status-icon" v-if="detailInfo.status == 5" src="@/assets/icon-status-end.png" />

      <div class="header">值仓结果</div>
      <div class="card-content">
        <div class="label">
          值仓结果：<span class="value">{{ detailInfo.statusName }}</span>
        </div>
        <div class="label">
          值仓员：<span class="value">{{ detailInfo.valueHolder }}</span>
        </div>
        <div class="label">
          保管员：<span class="value">{{ detailInfo.custodian }}</span>
        </div>
        <div class="label">
          值仓时间：<span class="value">{{
            dayjs(detailInfo.createTime).format('YYYY-MM-DD HH:mm:ss')
          }}</span>
        </div>
        <div class="label">
          <span>值仓意见：</span><span class="value">{{ detailInfo.descriper }}</span>
        </div>
      </div>
    </HCard>
    <HCard class="opinion-card">
      <div class="img-head">值仓照片</div>
      <Grid :gutter="10" :column-num="4">
        <GridItem v-for="item in detailInfo.fileUrl" :key="item">
          <img class="house-img" :src="item" @click="handlerBigView(item)" />
        </GridItem>
      </Grid>
    </HCard>
  </div>
</template>

<script setup>
import { onMounted, ref } from 'vue';
import { useRoute } from 'vue-router';
import dayjs from 'dayjs';
import { Grid, GridItem, ImagePreview } from 'vant';
import { HCard } from '@/components';
import HouseDetailCard from './HouseDetailCard.vue';
import { getHouseOperationDetail } from '@/api/in-out-manage';

const route = useRoute();

const detailInfo = ref({});

const getDetail = async () => {
  const { schedulingNo } = route.params;
  if (schedulingNo) {
    detailInfo.value = await getHouseOperationDetail({
      schedulingNo: schedulingNo,
      houseStatusType: 1, //1是值仓值仓记录获取详情 2扫码获取详情
    });
  }
};
const handlerBigView = (url) => {
  ImagePreview([url]);
};
onMounted(() => {
  getDetail();
});
</script>

<style scoped lang="scss">
.opinion-card {
  padding: 16px;
  margin-bottom: 10px;
  position: relative;
}
.header {
  font-size: 16px;

  color: #323233;
  font-weight: bold;
}
.card-content {
  margin-top: 8px;
  line-height: 25px;
  .label {
    margin: 10px 0;
    font-size: 14px;
    font-family: PingFangSC-Regular, PingFang SC;
    font-weight: 400;
    color: #6d748d;
  }
  .value {
    font-size: 14px;
    font-family: PingFangSC-Regular, PingFang SC;
    font-weight: 400;
    color: #0f0f0f;
  }
}
.status-icon {
  position: absolute;
  top: 20px;
  right: 16px;
  width: 50px;
  height: 44px;
}
.house-img {
  width: 100%;
}
.img-head {
  font-weight: bold;
  margin-bottom: 16px;
}
</style>
