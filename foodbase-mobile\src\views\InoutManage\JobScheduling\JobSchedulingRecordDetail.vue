<template>
  <div class="job-scheduling-record-detail">
    <div class="receipt-num">
      <div>调度单据号：</div>
      <div v-if="list.length > 0">{{ list[0].schedulingOrderNo }}</div>
    </div>
    <div v-for="job in list" :key="job.id" class="detail-warpper">
      <div class="detail-header">
        <div class="scheduling-no">
          <div class="scheduling-no-text">
            <span>调度号</span>
            <ShortScheduleNo>{{ job.schedulingNo }}</ShortScheduleNo>
          </div>
          <div class="collapse-button" @click="job.isCollapse = !job.isCollapse">
            {{ job.isCollapse ? '展开' : '收起' }}
          </div>
        </div>
        <div class="vehicle-no">
          <div>作业车牌</div>
          <div>{{ job.transportVehicleNo }}</div>
        </div>
        <div class="vehicle-no">
          <div>承运人</div>
          <div>{{ job.transportVehicleDriver }}</div>
        </div>
        <div class="vehicle-no">
          <div>联系电话</div>
          <div>{{ job.transportVehicleTel }}</div>
        </div>
      </div>

      <div class="job-processes" v-if="!job.isCollapse">
        <div class="tag-status">
          <van-tag
            type="warning"
            v-if="
              job.processName === '离库' || job.processName === '终止' || job.processName === '作废'
            "
            >已{{ job.processName }}</van-tag
          >
          <van-tag type="danger" v-else>作业中</van-tag>
        </div>
        <div class="job-process" v-if="job.sampleVo">
          <div class="job-process-title">
            <div class="job-process-title-name">扦样</div>
            <div class="job-process-title-describe">{{ job.sampleVo.sampleTime }}</div>
          </div>
          <div class="job-process-info">
            <span>扦样单号：{{ job.sampleVo.sampleCode }}</span>
          </div>
        </div>
        <div class="job-process" v-if="job.qualityTestVo">
          <div class="job-process-title">
            <div class="job-process-title-name">质检</div>
            <Tag class="tag-cls" :color-type="qualityMap[job.qualityTestVo.status]">
              {{ job.qualityTestVo.status }}
            </Tag>
          </div>
          <div class="job-process-info">
            <div
              v-for="testItem in job.qualityTestVo.qualityTestDetailVoList"
              :key="testItem.testItemName"
            >
              {{ testItem.testItemName }}：{{ testItem.testItemAval }}
            </div>
          </div>
        </div>
        <div class="job-process" v-if="job.inOrOut === '1' && job.weightGrossVo">
          <div class="job-process-title">
            <div class="job-process-title-name">称毛</div>
            <div class="job-process-title-describe">
              {{ dayjs(job.weightGrossVo.grossWeightTime).format('YYYY-MM-DD HH:mm:ss') }}
            </div>
          </div>
          <div class="job-process-info">
            <span>
              毛重：
              <HFixedNumber :ratio="1000" :fraction-digits="0">
                {{ job.weightGrossVo.grossWeightCount }}
              </HFixedNumber>
              公斤
            </span>
          </div>
        </div>
        <div class="job-process" v-if="job.inOrOut === '1' && job.dostorehouseVo">
          <div class="job-process-title">
            <div class="job-process-title-name">值仓</div>
            <div class="job-process-title-describe">
              {{
                hasCargoSpace && job.dostorehouseVo.cargoSpaceName
                  ? `${job.dostorehouseVo.storeHouse}_${job.dostorehouseVo.cargoSpaceName}`
                  : job.dostorehouseVo.storeHouse
              }}
            </div>
          </div>
        </div>
        <div class="job-process" v-if="job.weightTareVo">
          <div class="job-process-title">
            <div class="job-process-title-name">称皮</div>
            <div class="job-process-title-describe">
              {{ dayjs(job.weightTareVo.tareWeightTime).format('YYYY-MM-DD HH:mm:ss') }}
            </div>
          </div>
          <div class="job-process-info">
            <span>
              皮重：
              <HFixedNumber :ratio="1000" :fraction-digits="0">
                {{ job.weightTareVo.tareWeightCount }}
              </HFixedNumber>
              公斤
            </span>
          </div>
        </div>
        <div class="job-process" v-if="job.inOrOut === '2' && job.dostorehouseVo">
          <div class="job-process-title">
            <div class="job-process-title-name">值仓</div>
            <div class="job-process-title-describe">
              {{
                hasCargoSpace && job.dostorehouseVo.cargoSpaceName
                  ? `${job.dostorehouseVo.storeHouse}_${job.dostorehouseVo.cargoSpaceName}`
                  : job.dostorehouseVo.storeHouse
              }}
            </div>
          </div>
        </div>
        <div class="job-process" v-if="job.inOrOut === '2' && job.weightGrossVo">
          <div class="job-process-title">
            <div class="job-process-title-name">称毛</div>
            <div class="job-process-title-describe">
              {{ dayjs(job.weightGrossVo.grossWeightTime).format('YYYY-MM-DD HH:mm:ss') }}
            </div>
          </div>
          <div class="job-process-info">
            <span>
              毛重：
              <HFixedNumber :ratio="1000" :fraction-digits="0">
                {{ job.weightGrossVo.grossWeightCount }}
              </HFixedNumber>
              公斤
            </span>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { ref, onMounted, computed } from 'vue';
import { useRoute } from 'vue-router';
import { HFixedNumber } from '@/components';
import { ShortScheduleNo } from '@/views/InoutManage/common';
import Tag from '@/views/InoutManage/common/InOutTypeTag.vue';
import { getSchedulingDetail } from '@/api/in-out-manage';
import dayjs from 'dayjs';
import { checkPermission } from '@/utils/permission';

export default {
  components: { HFixedNumber, ShortScheduleNo, Tag },
  setup() {
    const route = useRoute();
    const list = ref([]);
    const qualityMap = {
      合格: 2,
      不合格: 1,
      入库: 3,
      退货: 4,
    };
    const hasCargoSpace = computed(() => checkPermission(['cargo_space']));

    onMounted(() => {
      initData();
    });

    const initData = async () => {
      const schedulingNo = route.params.schedulingNo;
      const data = await getSchedulingDetail(schedulingNo);
      list.value = data.map((item, index) => ({ ...item, isCollapse: index === 0 ? false : true }));
    };

    return {
      list,
      qualityMap,
      dayjs,
      hasCargoSpace,
    };
  },
};
</script>

<style lang="scss" scoped>
.job-scheduling-record-detail {
  .receipt-num {
    display: flex;
    padding: 13px 16px;
    justify-content: space-between;
  }
  .detail-warpper {
    width: 100%;
    margin-bottom: 10px;
    background: #ffffff;
    .detail-header {
      background: linear-gradient(180deg, #e3ecf5 0%, #ffffff 100%);
      padding: 13px 16px;
      border-bottom: 1px solid #ebedf0;
      .scheduling-no {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 10px;
        &-text {
          font-size: 16px;
          font-weight: 500;
          color: #111111;
        }
        .collapse-button {
          font-size: 14px;
          font-weight: 400;
          color: #1677ff;
        }
      }
      .vehicle-no {
        display: flex;
        justify-content: space-between;
        align-items: center;
        height: 25px;
        font-size: 14px;
        font-weight: 500;
        color: #323233;
      }
    }
  }

  .job-processes {
    position: relative;
    padding: 13px 16px;
    .tag-status {
      position: absolute;
      right: 16px;
      top: 3px;
    }
    .job-process {
      position: relative;
      line-height: 2;
      font-size: 14px;
      font-weight: 400;
      margin-bottom: 20px;

      &:before {
        position: absolute;
        left: 7px;
        top: 6px;
        display: inline-block;
        width: 1px;
        height: calc(100% + 20px);
        background: #c5c6cc;
        content: '';
      }

      &:last-child:before {
        display: none;
      }

      &:after {
        position: absolute;
        top: 6px;
        display: inline-block;
        width: 11px;
        height: 11px;
        border: 2px solid #1890ff;
        border-radius: 50%;
        content: '';
        background-color: #ffffff;
      }

      &-title {
        margin-left: 20px;
        display: flex;
        align-items: center;
        .tag-cls {
          line-height: 25px;
          &.tag {
            margin-left: 0;
          }
        }
        &-name {
          margin-right: 20px;
          color: #1d2129;
        }
        &-describe {
          color: #4e5969;
        }
      }
      &-info {
        margin-left: 20px;
        color: #4e5969;
      }
    }
  }
}
</style>
