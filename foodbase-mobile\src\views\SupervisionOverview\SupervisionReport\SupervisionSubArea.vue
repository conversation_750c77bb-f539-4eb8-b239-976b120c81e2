<template>
  <div class="supervision-area">
    <div class="title" v-if="isSubStores">库点详情：</div>
    <div class="title" v-else>地区详情：</div>
    <table v-for="item in props.data" :key="item.areaName">
      <tbody>
        <tr>
          <td colspan="2">
            <div class="area-name" :class="{ link: item.access }" @click="showDetail(item)">
              {{ item.areaName }}
              <Icon name="arrow" v-if="item.access" />
              <span class="not-access" v-else>暂未开通</span>
            </div>
          </td>
        </tr>
        <tr>
          <td class="item-name">数量清查：</td>
          <td class="check-result">
            <template v-if="item.quantityCheck === 1">
              达标<Icon class="checked" name="checked" />
            </template>
            <template v-else-if="item.quantityCheck === 2">
              不达标<Icon class="warning" name="warning" />
            </template>
            <template v-else> - </template>
          </td>
        </tr>
        <tr>
          <td class="item-name">质量清查：</td>
          <td class="check-result">
            <template v-if="item.qualityCheck === 1">
              合格<Icon class="checked" name="checked" />
            </template>
            <template v-else-if="item.qualityCheck === 2">
              不合格<Icon class="warning" name="warning" />
            </template>
            <template v-else> - </template>
          </td>
        </tr>
        <tr>
          <td class="item-name">安全清查：</td>
          <td class="check-result">
            <template v-if="item.securityCheck === 1">
              正常<Icon class="checked" name="checked" />
            </template>
            <template v-else-if="item.securityCheck === 2">
              异常<Icon class="warning" name="warning" />
            </template>
            <template v-else> - </template>
          </td>
        </tr>
        <tr>
          <td class="item-name">设施清查：</td>
          <td class="check-result">
            <template v-if="item.infrastructureCheck === 1">
              完备<Icon class="checked" name="checked" />
            </template>
            <template v-else-if="item.infrastructureCheck === 2">
              不完备<Icon class="warning" name="warning" />
            </template>
            <template v-else> - </template>
          </td>
        </tr>
      </tbody>
    </table>
  </div>
</template>

<script setup>
import { Icon } from 'vant';
import { computed } from 'vue';
import { useRouter } from 'vue-router';

const props = defineProps({
  data: {
    type: Array,
    default: () => [],
  },
});

const router = useRouter();

const isSubStores = computed(() => {
  return props.data.some((it) => it.isStore);
});

const showDetail = (item) => {
  if (!item.access) {
    return;
  }
  if (item.isStore) {
    router.push({ name: 'SupervisionStoreReport', params: { storeId: item.storeId } });
  } else {
    router.push({ name: 'SupervisionAreaReport', params: { areaCode: item.areaCode } });
  }
  window.scrollTo({ top: 0, behavior: 'smooth' });
};
</script>

<style scoped lang="scss">
.supervision-area {
  padding: 16px;
  font-size: 18px;

  .title {
    font-weight: 500;
    color: var(--van-gray-8);
    line-height: 25px;
  }

  table {
    width: 100%;
    border-collapse: collapse;
    color: var(--van-gray-7);
    margin-top: 16px;

    tr {
      border: 1px solid var(--van-gray-5);
    }
    td {
      line-height: 22px;
      padding: 8px 16px;

      &.store-name {
        font-size: 16px;
        padding: 8px;
        white-space: nowrap;
        text-overflow: ellipsis;
        overflow: hidden;
      }
    }
    .item-name {
      padding-left: 2em;
    }
    .check-result {
      width: 5em;
      text-align: right;
    }
  }

  .area-name {
    display: flex;
  }

  .van-icon-arrow {
    margin-left: auto;
    line-height: 22px;
  }

  .link {
    color: #1492ff;
  }

  .not-access {
    margin-left: auto;
    color: #aaa;
  }

  .checked {
    margin-left: 10px;
    color: var(--van-green);
  }
  .warning {
    margin-left: 10px;
    color: var(--van-orange);
  }
}
</style>
