# This file was originally created by the Android Tools, but is now
# used by cordova-android to manage the state of the various third party
# libraries used in your application

# This is the Library Module that contains the Cordova Library, this is not
# required when using an AAR

# This is the application project.  This is only required for Android Studio Gradle projects

# Project target.
target=android-30
android.library.reference.1=CordovaLib
android.library.reference.2=app
cordova.system.library.1=androidx.core:core:1.6.+
cordova.system.library.2=androidx.webkit:webkit:1.4.0
cordova.gradle.include.1=phonegap-plugin-barcodescanner/foodreserves-barcodescanner.gradle
cordova.system.library.3=com.android.support:support-v4:27.+
cordova.system.library.4=com.android.support.constraint:constraint-layout:1.1.3
cordova.system.library.5=com.android.support:support-v4:24.1.1+
