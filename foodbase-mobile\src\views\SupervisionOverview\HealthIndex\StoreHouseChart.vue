<template>
  <h-echart :options="options" autoresize theme="light"></h-echart>
</template>

<script>
import HEchart from '@/components/HEchart/HEchart'
export default {
  name: 'TrendsChart',
  components: { HEchart },
  props: {
    color: {
      type: Array,
      default: () => ['#D8021C']
    },
    storeCount: {
      type: Number,
      default: 0
    },
    proportion: {
      type: Number,
      default: 0
    },
    lableColor: {
      type: String,
      default: '#000000'
    },
    circleColor: {
      type: Array,
      default: () => ['#00B286', '#E5E5E5']
    },
    title: {
      type: String,
      default: ''
    }
  },
  data () {
    return {
    }
  },
  computed: {
    options () {
      const series = [
        {
          silent: true,
          radius: '95%',
          type: 'liquidFill',
          data: [this.proportion / 100 - 0.05, this.proportion / 100, this.proportion / 100 - 0.05],
          color: this.color,
          outline: {
            show: false
          },
          backgroundStyle: {
            borderColor: '#ffffff',
            borderWidth: 0,
            color: '#ffffff' // 背景颜色
          },
          itemStyle: {
            shadowBlur: 0,
            shadowColor: '#ffffff'
          },
          label: {
            normal: {
              position: ['50%', '45%'],
              show: true,
              formatter: params => {
                if (params.dataIndex === 0) {
                  return '{c|' + this.proportion + '%}'
                } else {
                  return ''
                }
              },
              rich: {
                c: {
                  fontSize: 16,
                  color: this.lableColor,
                  align: 'center',
                  lineHeight: 36
                },
                a: {
                  color: '#2A2F3F',
                  fontWeight: 500,
                  fontSize: 18,
                  marginTop: 10
                }
              }
            }
          }
        },
        {
          silent: true,
          type: 'pie',
          radius: ['90%', '100%'],
          color: this.circleColor,
          label: {
            show: false,
            position: 'center'
          },
          data: [
           { value: this.proportion / 100, name: '' },
            { value: 1 - this.proportion / 100, name: '全部' }
          ]
        }
      ]

      return {
        series,
        title: {
          text: this.title,
          left: 'center',
          bottom: '0',
          textStyle: {
            color: this.lableColor,
            fontSize: 12,
            fontWeight: 400
          }
        }
      }
    }
  }
}
</script>

<style scoped>
.h-echarts {
  width: 100%;
  height: 100%;
}
</style>
