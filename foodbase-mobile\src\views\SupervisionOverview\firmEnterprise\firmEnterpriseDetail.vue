<template>
  <div class="firmEnterpriseDetail-style">
    <!-- <van-loading v-if="loading" vertical>加载中...</van-loading> -->
    <div class="contentstyle">
      <div class="header-content">
        <HCard class="header-hcard">
          <div class="header-style">
            <div class="header">
              <div class="header-title">{{ formValue.name }}</div>
              <van-icon
                name="logistics"
                :color="colorType(formValue.typer)"
                class="header-icon"
                size="2rem"
              />
            </div>
            <ul>
              <li>
                <div class="span-title">统一社会信用代码:</div>
                <div class="title-content">{{ formValue.coder }}</div>
              </li>
              <li>
                <div class="span-title">应急级别</div>
                <div class="title-content">{{ getLevel(formValue.emergencyLevel) }}</div>
              </li>
              <li>
                <div class="span-title">所在地区:</div>
                <div class="title-content">{{ formValue.location }}</div>
              </li>
              <li>
                <div class="span-title">实际经营地址:</div>
                <div class="title-content">{{ formValue.addrDetail }}</div>
              </li>
              <li>
                <div class="span-title">联系人:</div>
                <div class="title-content">{{ formValue.contactMan }}</div>
              </li>
              <li>
                <div class="span-title">联系人电话:</div>
                <div class="title-content">{{ formValue.contactPhone }}</div>
              </li>
            </ul>
            <!--  -->
          </div>
        </HCard>
      </div>
      <div class="fooder">
        <HCard class="fooder-hcard">
          <!-- <van-list v-model:loading="loading" @load="onLoad" :finished="finished"> -->
          <van-cell v-for="item in list" :key="item.key" :title="item.name" is-link />
          <!-- </van-list> -->
        </HCard>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'firmEnterpriseDetail',
};
</script>

<script setup>
import { ref, onMounted } from 'vue';
import { useRoute } from 'vue-router';
import { HCard } from '@/components';
import { getHouseListId } from '@/api/firmEnterprise';

const route = useRoute();
const loading = ref(false);
// const finished = ref(false);
const typeOptions = ref([
  { key: 1, value: 1, name: '省级' },
  { key: 2, value: 2, name: '市级' },
  { key: 3, value: 3, name: '县级' },
]);
const list = ref([
  { key: 1, value: 1, name: '配送能力' },
  { key: 2, value: 2, name: '仓储能力' },
  { key: 3, value: 3, name: '基础库存品种' },
]);
const formValue = ref({});

const getStorageUnit = async () => {
  loading.value = true;
  try {
    let result = await getHouseListId(route.params.id);
    formValue.value = result;
    loading.value = false;
  } catch (error) {
    // this.finished = true;
    loading.value = false;
  }
};
const getLevel = (level) => {
  let levelObjet = typeOptions.value.find((item) => item.value == level);
  return levelObjet?.name || '';
};
const colorType = (typer) => {
  let colorValues = '';
  switch (typer) {
    case 1:
      colorValues = '#70b603';
      break;
    case 2:
      colorValues = '#02a7f0';
      break;
    case 3:
      colorValues = '#d9001b';
      break;
    case 4:
      colorValues = '#8080ff';
      break;
    default:
      colorValues = '#7f7f7f';
      break;
  }
  return colorValues;
};
onMounted(() => {
  getStorageUnit();
});
</script>

<style scoped lang="scss">
.firmEnterpriseDetail-style {
  min-height: 100%;
  .contentstyle {
    height: 100%;
  }
  margin: 5px;
  // padding: 5px;
  // background-color: aqua;
  .fooder {
    height: 62%;
    .fooder-hcard {
      height: 100%;
      // margin: 5px;
      padding: 12px;
      height: 100%;
    }
  }
  // .header-content {
  // }
}
.header-hcard {
  margin-bottom: 15px;
  padding: 12px;
}

.header-style {
  width: 100%;
  // text-align: center;
  .header {
    // display: flex;
    width: 100%;
    font-size: 22px;
    text-align: center;
    position: relative;
    display: flex;
    .header-title {
      width: 100%;
      padding: 0 55px;
      word-wrap: break-word;
      word-break: break-all;
    }
    .header-icon {
      // margin-left: 25px;
      position: absolute;
      right: 4%;
    }
  }
  .span-title {
    width: 45%;
    // display: inline-block;
    color: #c3c4c4;
  }
  ul {
    li {
      margin: 12px;
      display: flex;
      .title-content {
        width: 50%;
        word-break: break-all;
      }
    }
  }
}
</style>
