<template>
  <div class="h-card">
    <div
      class="h-card-header"
      @click="onHeaderClick"
      v-if="showHeader"
      v-bind:style="{ 'background-color': bgColor ? bgColor : 'inherit' }"
    >
      <div class="h-card_headaches">
        <div class="h-card-header-title">
          <slot name="header-title">{{ title }}</slot>
        </div>
        <div class="h-card-header-year">
          <slot name="header-year">{{ year }}</slot>
        </div>
      </div>
      <div class="h-card-header-extra">
        <slot name="header-extra">{{ extra }}</slot>
      </div>
    </div>
    <div class="h-card-body">
      <slot></slot>
    </div>
  </div>
</template>

<script setup>
import { computed, useSlots } from 'vue';

const props = defineProps({
  title: String,
  bgColor: String,
  extra: String,
  year: String,
});

const emits = defineEmits(['header-click']);

const showHeader = computed(() => {
  const { title } = props;
  const slots = useSlots();
  return !!(title || slots['header-title'] || slots['header-extra']);
});

const onHeaderClick = () => {
  emits('header-click');
};
</script>

<style lang="scss">
.h-card {
  background-color: #fff;

  &-header {
    font-size: 20px;
    line-height: 55px;
    font-weight: 500;
    padding: 0 20px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    border-bottom: 1px solid var(--van-gray-3);

    &-extra {
      color: var(--van-gray-5);
    }
  }

  &-body {
    min-height: 100px;
  }
}

.h-card_headaches {
  display: flex;
  flex-direction: row;
  justify-content: flex-start;
  .h-card-header-year {
    display: flex;
    align-items: center;
    margin-left: 15px;
  }
}
</style>
