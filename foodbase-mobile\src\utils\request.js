import axios from 'axios';
// import qs from 'qs';
import { Toast } from 'vant';
import { getToken, getReserverToken } from '@/utils/auth';
// import { getToken, getReserverToken, getHxdiframeworkToken } from '@/utils/auth';
import { getCodeMessages } from '@/utils/http-code-messages';
import router from '@/router';
// import qs from 'qs';
function createRequest(baseUrl) {
  const request = axios.create({
    baseURL: baseUrl,
    timeout: 0,
    withCredentials: false,
    //   baseUrl.includes('/remote/') || baseUrl.includes('/purchase_supervision/') ? false : true,
    // paramsSerializer: (params) => qs.stringify(params, { arrayFormat: 'repeat', skipNulls: true }),
  });

  // 异常拦截
  const errorHandler = (error) => {
    const { config, response } = error;

    if (config.noErrorHandler) {
      return Promise.resolve(response);
    }

    if (response) {
      if (response.status === 401) {
        router.replace('/login');
      }

      const {
        status,
        statusText,
        data: { message: info },
      } = response;
      const message = info || getCodeMessages(status) || `未知错误 ${statusText}`;
      Toast({ message });
    }
    return Promise.reject(error);
  };

  request.interceptors.request.use((config) => {
    let token = getToken();
    if (
      config.baseURL.includes('/api/') ||
      config.baseURL.includes('/reserver/') ||
      config.baseURL.includes('/purchase_supervision/')
    ) {
      token = getReserverToken();
    }
    if (token && !config.baseURL.includes('/remote/')) {
      config.headers.Authorization = token;
    }
    // if (config.baseURL.includes('/hxdiframework/') && config.url !== '/api/token/getToken') {
    //   token = getHxdiframeworkToken();
    //   config.params = {
    //     ...config.params,
    //     systemCode: 'APP_CLJG',
    //     token,
    //   };
    // }
    return config;
  });

  // 处理响应
  request.interceptors.response.use((response) => {
    const { data: respData, status, config } = response;
    if (config.noErrorHandler) {
      return response;
    }
    if (status >= 200 && status < 400) {
      if (config.responseType === 'blob') {
        return respData;
      }
      if (typeof respData === 'string') {
        return respData;
      }
      if ('success' in respData) {
        const { success, data, code, msg } = respData;

        if (success) {
          return data;
        }

        Toast.fail({
          message: msg,
        });
        throw new Error(
          `\n[Api Response Error]\nurl ${response.request.responseURL} \ncode ${code} \nmsg ${msg}`,
        );
      } else {
        // 原系统格式直接返回
        return respData;
      }
    }

    throw new Error(
      `Server Response Error\nurl ${response.request.responseURL} \nstatus ${status}`,
    );
  }, errorHandler);

  return request;
}

window.__isProd = window.__isProd || 0;

if (process.env.VUE_APP_API_ENV) {
  window.__isProd = process.env.VUE_APP_API_ENV === 'test' ? 1 : 0;
}

// const hrpsBaseUrl = () =>
//   JSON.parse(process.env.VUE_APP_HRPS_BASE_URL)[window.__isProd] || location.origin;
const apiBaseUrl = () =>
  JSON.parse(process.env.VUE_APP_API_BASE_URL)[window.__isProd] || location.origin;
const remoteBaseUrl = () =>
  JSON.parse(process.env.VUE_APP_REMOTE_API_BASE_URL)[window.__isProd] || location.origin;
const reserverBaseUrl = () =>
  JSON.parse(process.env.VUE_APP_RESERVER_API_BASE_URL)[window.__isProd] || location.origin;
const ingestBaseUrl = () =>
  JSON.parse(process.env.VUE_APP_INGEST_API_BASE_URL)[window.__isProd] || location.origin;
const hxdiframeworkBaseUrl = () =>
  JSON.parse(process.env.VUE_APP_HXDIFRAMEWORK_API_BASE_URL)[window.__isProd] || location.origin;
const hxdiframeworkBaseUrlMobeil = () =>
  JSON.parse(process.env.VUE_APP_MOBILE_API_BASE_URL)[window.__isProd] || location.origin;

const manualBaseUrl = () =>
  JSON.parse(process.env.VUE_APP_MANUAL_API_BASE_URL)[window.__isProd] || location.origin;

const hrpsRequest = () => createRequest(reserverBaseUrl() + '/api/');
const request = () => createRequest(apiBaseUrl() + '/security/');
const atlasRequest = () => createRequest(apiBaseUrl() + '/atas/');
const reRequest = () => createRequest(remoteBaseUrl() + '/remote/'); // 远程监管接口
const reserveRequest = () => createRequest(reserverBaseUrl() + '/reserver/'); //浙江储备
const reserverPurchase = () => createRequest(reserverBaseUrl() + '/purchase_supervision/'); //浙江储备
const ingestRequest = () => createRequest(apiBaseUrl() + '/ingest/'); //视频推流接口
const ingestRequestControl = () => createRequest(ingestBaseUrl() + '/ingest/'); //视频推流接口-视频监管-云台控制

const hxdiframeworkRequest = () => createRequest(hxdiframeworkBaseUrl() + '/hxdiframework/');

const manualRequest = () => createRequest(manualBaseUrl());

const hxdiframeworkMobeilRequest = () =>
  createRequest(hxdiframeworkBaseUrlMobeil() + '/hxdiframework/');

export default request;

export {
  hrpsRequest,
  request,
  atlasRequest,
  reRequest,
  reserveRequest,
  ingestRequest,
  ingestRequestControl,
  reserverPurchase,
  hxdiframeworkRequest,
  hxdiframeworkMobeilRequest,
  manualRequest,
};
