<template>
  <div class="px-2 py-2">
    <HPicker
      class="w-1/3 mb-3"
      :options="statusList"
      v-model:value="status"
      @update:value="update_value"
    ></HPicker>
    <div
      class="py-3 mb-3 bg-white rounded-md"
      v-for="item in listData?.items"
      :key="item.id"
      @click="(showPopup = true), (currentRow = item), openPopup()"
    >
      <h1 class="flex justify-between px-3 mb-4">
        <p class="font-[20px] font-bold text-blue-400">{{ item.name }}</p>
        <p>{{ Number(item.status) > 0 ? '已完成' : '未完成' }}</p>
      </h1>
      <van-cell-group class="cells">
        <van-cell class="" title="所属季度：" :value="quarterType(item.orderQuarter)" />
        <van-cell class="" title="任务编号：" :value="item.coder" />
        <van-cell class="" title="检查场所：" :value="item.storeName" />
      </van-cell-group>
    </div>
    <EmptyHolder v-if="listData?.items?.length === 0" />
  </div>

  <van-popup
    v-model:show="showPopup"
    closeable
    position="bottom"
    @close="removeFile(newFileIds)"
    class="!bg-gray-50 pop-content h-screen overflow-hidden law-enforcement-supervision-popup"
  >
    <div class="h-[50px]">
      <h2 class="p-4 font-bold">{{ detailData.planName }}</h2>
    </div>
    <van-form
      @submit="onSubmit"
      input-align="right"
      error-message-align="right"
      label-width="150px"
      ref="formRef"
    >
      <div class="pb-10 overflow-y-auto" style="height: calc(100vh - 50px)">
        <h2 class="px-4 py-2 text-sm text-gray-400">任务基础信息</h2>
        <van-cell-group inset>
          <!-- <van-field label="任务编号：" v-model="detailData.coder" readonly /> -->
          <van-cell title="任务编号：" :value="detailData.coder" />
          <van-field
            name="name"
            v-model="detailData.name"
            autosize
            label="计划名称："
            placeholder="请输入计划名称"
            :readonly="detailData.status == 1"
            :rules="[{ required: true, message: '请输入计划名称' }]"
          />
          <van-cell title="所属季度：" value="第一季度" v-if="detailData.orderQuarter == 1" />
          <van-cell title="所属季度：" value="第二季度" v-if="detailData.orderQuarter == 2" />
          <van-cell title="所属季度：" value="第三季度" v-if="detailData.orderQuarter == 3" />
          <van-cell title="所属季度：" value="第四季度" v-if="detailData.orderQuarter == 4" />
        </van-cell-group>

        <div>
          <h2 class="px-4 py-2 mt-3 text-sm text-gray-400">检查对象信息</h2>
          <van-cell-group inset>
            <van-cell class="" title="被检查对象(人)：" :value="detailData.checkedPerson" />
            <van-cell class="" title="检查场所：" :value="detailData.storeName" />

            <van-field
              v-model="detailData.unitCoder"
              autosize
              label="社会统一信用代码："
              placeholder="请输入社会统一信用代码"
              :readonly="detailData.status == 1"
              :rules="[{ required: true, message: '请输入社会统一信用代码' }]"
            />
            <van-field
              v-model="detailData.registrationNumber"
              autosize
              label="粮食收购备案登记号："
              placeholder="请输入粮食收购备案登记号"
              :readonly="detailData.status == 1"
              :rules="[{ required: true, message: '请输入粮食收购备案登记号' }]"
            />
            <van-field
              v-model="detailData.lawMan"
              autosize
              label="法定代表人："
              placeholder="请输入法定代表人"
              :readonly="detailData.status == 1"
              :rules="[{ required: true, message: '请输入法定代表人' }]"
            />
            <van-field
              v-model="detailData.lawPhone"
              autosize
              label="联系电话："
              placeholder="请输入联系电话"
              :readonly="detailData.status == 1"
            />
          </van-cell-group>
        </div>
        <h2 class="px-4 py-2 mt-3 text-sm text-gray-400">检查人员</h2>
        <div v-for="(item, index) in detailData.checkerList" :key="item.id">
          <van-cell-group inset>
            <van-field
              v-model="item.userName"
              label="姓名："
              placeholder="请输入姓名"
              :readonly="detailData.status == 1"
              :rules="[{ required: true, message: '请输入姓名' }]"
              :maxlength="20"
            />
            <van-field
              v-model="item.enforceNo"
              label="执法证号："
              placeholder="请输入执法证号"
              :readonly="detailData.status == 1"
              :maxlength="30"
            />
            <div class="delete-button" @click="deleteRow(index)">
              <Icon name="delete-o" />
              删除该人员
            </div>
          </van-cell-group>
        </div>
        <div @click="addRow" class="add-button">
          <Icon name="add-o" />
          新增人员
        </div>
        <div v-for="item in detailData.detailList" :key="item.id">
          <h2 class="px-4 py-2 mt-3 text-sm text-gray-400">检查内容</h2>
          <van-cell-group inset>
            <van-cell title="检查事项：" :value="item?.name" />
            <!-- <van-field
              v-model="item.userName"
              autosize
              label="检查人："
              placeholder="请输入检查人"
              :readonly="detailData.status == 1"
              :rules="[{ required: true, message: '请输入检查人' }]"
              :maxlength="15"
            /> -->
            <van-field
              v-model="item.situation"
              rows="3"
              autosize
              label="检查结果："
              type="textarea"
              placeholder="请输入检查结果"
              :readonly="detailData.status == 1"
              :rules="[{ required: true, message: '请输入检查结果' }]"
              :maxlength="125"
            />
            <van-field
              name="radio"
              label="是否通过检查："
              :rules="[{ required: true, message: '请选择是否通过检查' }]"
            >
              <template #input>
                <van-radio-group
                  v-model="item.status"
                  direction="horizontal"
                  :disabled="detailData.status == 1"
                >
                  <van-radio :name="1">通过</van-radio>
                  <van-radio :name="0">不通过</van-radio>
                </van-radio-group>
              </template>
            </van-field>
          </van-cell-group>
        </div>
        <h2 class="px-4 py-2 text-sm text-gray-400">检查证明材料</h2>
        <van-cell-group inset>
          <Uploader
            :model-value="fileList"
            multiple
            :before-read="beforeRead"
            class="m-2 upload-cls"
            @click-upload="onClickUpload"
            @delete="onDeleteFile"
            :max-count="3"
          />
        </van-cell-group>
        <van-sticky
          :offset-bottom="10"
          class="mt-5"
          position="bottom"
          v-if="detailData.status != 1"
        >
          <van-row gutter="20" class="px-4">
            <van-col span="12"
              ><van-button
                v-p="[`app-law-enforcement-supervision:save`]"
                type="primary"
                plain
                block
                @click="save"
                native-type="submit"
                >保存</van-button
              ></van-col
            >
            <van-col span="12"
              ><van-button
                v-p="[`app-law-enforcement-supervision:finish`]"
                type="primary"
                block
                @click="saveAndSubmit"
                native-type="submit"
                >完成检查</van-button
              ></van-col
            >
          </van-row>
        </van-sticky>
      </div>
    </van-form>
    <ActionSheet
      v-model:show="showActionSheet"
      cancel-text="取消"
      description="请选择上传方式"
      :actions="actions"
      @select="onUploadActionSelect"
      close-on-click-action
    />
  </van-popup>
</template>

<script setup>
import { HPicker } from '@/components';
import EmptyHolder from '@/views/common/EmptyHolder';
import { ref, onMounted, watch, reactive } from 'vue';
import { getData, getDetail, saveData, submitSupervisedcheck } from './apis';
import { Toast, Dialog, Uploader, ActionSheet, Icon } from 'vant';
import { getReserverToken } from '@/utils/auth';
import { getFileUrls } from '@/api/in-out-manage';
import { getFiles, deleteFile } from '@/api/file';
// import dayjs from 'dayjs';

const statusList = ref([
  { text: '第一季度', value: 1 },
  { text: '第二季度', value: 2 },
  { text: '第三季度', value: 3 },
  { text: '第四季度', value: 4 },
]);

const fileList = ref([]);
const showActionSheet = ref(false);

const currentRow = ref({});
const detailData = ref({});
const listData = ref([]);
const formRef = ref();
const delFileIds = ref([]);
const newFileIds = ref([]);

// const fileType = ref('jpg');
// const taskStatusList = { 1: '执行中', 2: '已完成', 3: '存在问题待整改', 4: '已完成整改' };
const getList = async () => {
  listData.value = await getData(status.value);
};

const openPopup = async () => {
  detailData.value = await getDetail({ id: currentRow.value.id });
  if (!detailData.value.checkerList?.length) {
    detailData.value.checkerList = [{ userName: '', enforceNo: '' }];
  }
  // console.log('detailData',detailData.value);
  newFileIds.value = [];
  fechFiles();
};

const status = ref(1);

const update_value = () => {
  getList();
};
watch(() => {
  getList();
});

onMounted(async () => {
  getList();
});
const showPopup = ref(false);

const save = async () => {
  formRef.value.validate().then(async () => {
    detailData.value.list = detailData.value.detailList;
    detailData.value.checkedPersonDtoList = detailData.value.checkerList;
    await saveData(detailData.value);
    removeFile(delFileIds.value);
    Toast('保存成功');
  });
};

const saveAndSubmit = async () => {
  formRef.value.validate().then(async () => {
    Dialog.confirm({
      title: '完成检查后，内容将不可修改，是否确认完成检查？',
    }).then(async () => {
      await save();
      await submitSupervisedcheck(detailData.value.id);
      removeFile(delFileIds.value);
      Toast('已处理');
      showPopup.value = false;
      getList();
    });
  });
};
const quarterType = (type) => {
  switch (type) {
    case 1:
      return '第一季度';
    case 2:
      return '第二季度';
    case 3:
      return '第三季度';
    case 4:
      return '第四季度';
  }
};
const fechFiles = async () => {
  const res = await getFiles({ businessId: currentRow.value.id, coder: 'inputTestResult' });
  fileList.value = [];
  res.forEach((item) => {
    // const isImage = item.fileUrl;
    fileList.value.push({
      url: item.fileUrl,
      name: item.fileName,
      status: 'done',
      isImage: true,
      deletable: true,
      id: item?.id,
    });
  });
};

const actions = [{ name: '拍照' }, { name: '相册选择' }];

// const uploadFile = async () => {};

const onUploadActionSelect = ({ name }) => {
  const Camera = window.Camera;
  let sourceType = Camera.PictureSourceType.PHOTOLIBRARY;
  if (name === '拍照') {
    sourceType = Camera.PictureSourceType.CAMERA;
  } else if (name === '相册选择') {
    sourceType = Camera.PictureSourceType.SAVEDPHOTOALBUM;
  }
  navigator.camera.getPicture(
    (imageUri) => {
      // 添加图片到图片列表
      const fileItem = reactive({
        url: imageUri,
        isImage: true,
        status: 'uploading',
        deletable: true,
      });
      fileList.value.push(fileItem);
      const name = imageUri.substr(imageUri.lastIndexOf('/') + 1);

      // 上传参数
      const options = new window.FileUploadOptions();
      options.fileKey = 'file';
      options.fileName = String(name).split('?')[0];
      options.headers = {
        Authorization: getReserverToken(),
      };
      const params = {};
      params.coder = 'inputTestResult';
      console.log(currentRow.value.id, 'currentRow.value.id');
      params.businessId = currentRow.value.id;
      options.params = params;

      // 上传地址
      const reserverBaseUrl = () =>
        JSON.parse(process.env.VUE_APP_RESERVER_API_BASE_URL)[window.__isProd] || location.origin;
      const uploadUri = new URL(
        '/reserver/api/fileManager/uploadFileBatch',
        reserverBaseUrl(),
      ).toString();

      // 上传文件
      const fileTransfer = new window.FileTransfer();
      console.log(imageUri, 'imageUri');
      console.log(options, 'options');
      fileTransfer.upload(
        imageUri,
        uploadUri,
        (res) => {
          console.log(res, 'res');
          // 上传成功
          const resp = res.response;
          if (resp) {
            const respJson = JSON.parse(resp);
            const { data } = respJson;
            fileItem.name = (data || '').split(',')[0];
            fileItem.status = 'done';
            fileItem.id = (data || '').split(',')[0];
            newFileIds.value.push(...(data || '').split(','));
            console.log(newFileIds.value, 'newFileIds.value');
            getFileUrls(data).then((urls) => {
              fileItem.url = urls[0];
            });
          }
        },
        (error) => {
          // 上传失败
          fileItem.status = 'failed';
          Toast('上传失败');
          console.error(error);
        },
        options,
      );
    },
    (err) => {
      Toast('选择图片失败');
      console.error(err);
    },
    {
      quality: 85,
      destinationType: Camera.DestinationType.FILE_URI,
      sourceType: sourceType,
    },
  );
};

const onClickUpload = (e) => {
  e.preventDefault();
  if (navigator.camera) {
    showActionSheet.value = true;
  } else {
    Toast('不支持选择照片');
  }
};

const onDeleteFile = (file, { index }) => {
  if (file?.id) {
    delFileIds.value.push(file.id);
  }
  fileList.value.splice(index, 1);
};
const removeFile = async (delList) => {
  console.log(delList, 'delList');
  if (delList.length) {
    await deleteFile(delList);
    delFileIds.value = [];
  }
};
const beforeRead = (file) => {
  if (file.type.includes('jpg') || file.type.includes('jpeg') || file.type.includes('png')) {
    return true;
  } else {
    Toast('请上传jpg,png格式图片');
    return false;
  }
};
const addRow = () => {
  detailData.value.checkerList.push({
    userName: '',
    enforceNo: '',
  });
};
const deleteRow = (index) => {
  detailData.value.checkerList.splice(index, 1);
};
</script>

<style lang="scss">
.law-enforcement-supervision-popup {
  .van-field__control:read-only {
    color: #969799 !important;
  }
}

.add-button {
  color: #0073ff;
  padding-left: 30px;
  margin-top: 5px;
}
.delete-button {
  color: #f56c6c;
  padding-left: 10px;
}
</style>
