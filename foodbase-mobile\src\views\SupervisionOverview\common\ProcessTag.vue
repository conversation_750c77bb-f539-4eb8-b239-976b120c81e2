<template>
  <Tag v-bind="colors">
    <slot></slot>
  </Tag>
</template>

<script setup>
import { Tag } from 'vant';
import { computed } from 'vue';

const textColorMap = {
  warn: '#F48419',
  finished: '#cddc39',
};

const props = defineProps({
  type: String,
});

const colors = computed(() => {
  return { color: 'transparent', textColor: textColorMap[props.type] };
});
</script>

<style scoped>
.van-tag {
  font-size: 18px;
  line-height: 28px;
}
</style>
