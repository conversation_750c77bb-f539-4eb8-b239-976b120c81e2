<template>
  <div class="appointment-list">
    <List
      ref="listRef"
      v-model:loading="loading"
      :finished="finished"
      finished-text="没有更多了"
      @load="onLoad"
    >
      <EmptyHolder v-if="list.length === 0 && !loading" />
      <HCard v-for="item in list" :key="item.contractNum" :title="`预约客户：${item.farmer}`">
        <template #header-extra>
          <Tag :type="tagTypeMap[item.status]">{{ item.statusName }}</Tag>
        </template>
        <div class="appointment-item">
          <div class="appointment-info">
            <span class="appointment-name">预约库点：</span>
            <span class="appointment-value">{{ item.store }}</span>
          </div>
          <div class="appointment-info">
            <span class="appointment-name">预约业务：</span>
            <span class="appointment-value">
              {{ item.reserveType === '1' ? '收购入库' : '其他' }}
            </span>
          </div>
          <div class="appointment-info">
            <span class="appointment-name">预约时间：</span>
            <span class="appointment-value">{{ item.saleDate }}</span>
          </div>
          <div class="appointment-info">
            <span class="appointment-name">粮油品种：</span>
            <span class="appointment-value">{{ item.category }}</span>
          </div>
          <div class="appointment-info">
            <span class="appointment-name">预约数量（公斤）：</span>
            <HFixedNumber :fraction-digits="0" :ratio="1000" class="appointment-value">
              {{ item.reservationNum }}
            </HFixedNumber>
          </div>
        </div>
      </HCard>
    </List>
  </div>
</template>

<script setup>
import { HCard } from '@/components';
import { Tag, List } from 'vant';
import { reactive, ref, watch } from 'vue';
import EmptyHolder from '@/views/common/EmptyHolder';
import { getAppointmentList } from '@/api/appointment';
import HFixedNumber from '@/components/HFixedNumber/HFixedNumber';

const props = defineProps({
  appointmentStatus: String,
});

const tagTypeMap = {
  1: 'success', // 成功
  2: 'warning', // 失败
  3: '', // 取消
  4: 'primary', // 待审核
};

const listRef = ref();
const list = ref([]);
const loading = ref(false);
const finished = ref(false);

const pagination = reactive({
  page: 0,
  size: 10,
  total: 0,
});

const onLoad = async () => {
  const status = props.appointmentStatus === 'all' ? '' : props.appointmentStatus;
  const { items, page, total } = await getAppointmentList({
    status,
    page: pagination.page + 1,
  });
  list.value.push(...items);
  pagination.page = page;
  pagination.total = total;

  if (list.value.length >= total) {
    finished.value = true;
  }
  loading.value = false;
};

watch(
  () => props.appointmentStatus,
  () => {
    pagination.page = 0;
    pagination.total = 0;
    list.value = [];
    finished.value = false;
    listRef.value?.check();
  },
);
</script>

<style scoped lang="scss">
.appointment-list {
  --van-tag-line-height: 18px;
  --van-tag-font-size: 18px;
  --van-tag-padding: 6px 8px;
}

.h-card {
  margin-bottom: 16px;
}
.appointment-item {
  padding: 20px;
  font-size: 18px;
}
</style>
