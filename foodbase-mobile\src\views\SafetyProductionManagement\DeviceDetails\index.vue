<template>
  <div>
    <div class="p-5">
      <img :src="detailData.pictureUrl" class="w-full h-auto" alt="" />
    </div>
    <van-cell-group inset>
      <van-cell title="设备编号：" :value="detailData.sn" />
      <van-cell title="设备类型：" :value="types[detailData.type]" />
      <van-cell title="设备名称：" :value="detailData.name" />
      <van-cell title="所属库点：" :value="detailData.storeName" />
      <van-cell
        title="下一次维护时间："
        :value="
          String(detailData.nextMaintenanceTime).length > 5
            ? dayjs(detailData.nextMaintenanceTime).format('YYYY-MM-DD HH:mm:ss')
            : '-'
        "
      />
      <van-cell
        title="维护记录："
        value=""
        is-link
        :to="`/safety-production-management/maintain-records?id=${route.query.id ?? 1}`"
      />
      <van-cell
        title="领用记录："
        value=""
        is-link
        :to="`/safety-production-management/receipt-record?id=${route.query.id ?? 1}`"
      />
    </van-cell-group>
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue';
import { reserverPurchase } from '@/utils/request';
import { useRoute } from 'vue-router';
import dayjs from 'dayjs';

const route = useRoute();
const detailData = ref({});

const types = {
  1: '固定式设备',
  2: '移动式工艺设备',
  3: '安全防护设备',
  4: '质检设备',
  5: '其他',
};

onMounted(async () => {
  detailData.value = await reserverPurchase().get(
    `/api/hrps/instrument/basic/detail/${route.query.id ?? 1}`,
  );
});
</script>

<style lang="scss" scoped></style>
