<template>
  <span class="h-date-time">
    <span v-if="!hideDate" class="h-date-time--date">{{ date }}</span>
    {{ delimiter }}
    <span v-if="!hideTime" class="h-date-time--time">{{ time }}</span>
  </span>
</template>

<script>
import { computed } from 'vue';
import dayjs from 'dayjs';

export default {
  name: 'HDateTime',
  props: {
    value: {
      type: [String, Number, Date],
    },
    dateFormat: {
      type: String,
      default: 'YYYY-MM-DD',
    },
    timeFormat: {
      type: String,
      default: 'HH:mm:ss',
    },
    hideDate: {
      type: Boolean,
      default: false,
    },
    hideTime: {
      type: Boolean,
      default: false,
    },
    delimiter: {
      type: String,
      default: ' ',
    },
  },
  setup(props) {
    const datetime = computed(() => dayjs(props.value));

    const date = props.hideDate || computed(() => datetime.value.format(props.dateFormat));
    const time = props.hideTime || computed(() => datetime.value.format(props.timeFormat));
    return {
      date,
      time,
    };
  },
};
</script>

<style lang="scss">
.h-date-time {
  &--date,
  &--time {
    white-space: nowrap;
  }
}
</style>
