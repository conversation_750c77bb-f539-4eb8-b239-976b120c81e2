<template>
  <div class="job-scheduling-detail page-warpper">
    <SchedulingSteps :active="active"></SchedulingSteps>
    <Form ref="infoForm">
      <HCard class="info-card" v-if="active === 0">
        <Cell title="调度号" title-class="bold-text">
          <template #value>
            <span>{{ form.schedulingNo?.split('_')[0] }}</span>
          </template>
        </Cell>
        <Cell title="到库车船号" title-class="bold-text">
          <template #value>
            <span class="carNum" v-for="(it, index) in form.transportVehicleNo" :key="index">
              {{ it }}
            </span>
          </template>
        </Cell>
        <Cell title="运输方式" title-class="bold-text">
          <template #value>
            <BizDictName dict="SHIPPING_TYPE" :value="form.typeShipping"></BizDictName>
          </template>
        </Cell>
        <Field
          v-model="form.transportVehicleTel"
          label="联系电话"
          required
          placeholder="请输入"
          :disabled="isShipingAgain"
          :rules="[{ required: true, message: '请输入联系电话' }]"
        ></Field>
        <Field
          v-model="form.buzTyperName"
          label="业务类型"
          is-link
          readonly
          required
          placeholder="请选择"
          :disabled="isShipingAgain || disableVoucher"
          :rules="[{ required: true, message: '请选择业务类型' }]"
          @click="
            isShipingAgain || disableVoucher ? (showTyperPicker = false) : (showTyperPicker = true)
          "
        />
        <Field
          v-model="form.customerName"
          label="客户名称"
          is-link
          readonly
          required
          placeholder="请选择"
          :disabled="isShipingAgain && form.buzTyper !== 2"
          :rules="[{ required: true, message: '请选择客户名称' }]"
          @click="
            isShipingAgain && form.buzTyper !== 2
              ? (showCustomerPicker = false)
              : (showCustomerPicker = true)
          "
        />
        <Field
          v-show="!form.voucherId || (form.voucherId && !form.voucherId.includes('ZYD'))"
          v-model="form.voucherId"
          label="作业凭证"
          is-link
          readonly
          required
          placeholder="请选择"
          :disabled="isShipingAgain"
          :rules="[{ required: true, message: '请选择作业凭证' }]"
          @click="isShipingAgain ? (showVoucherPicker = false) : (showVoucherPicker = true)"
        />
        <Field
          v-show="form.voucherId && form.voucherId.includes('ZYD')"
          v-model="inOutCode"
          label="作业凭证"
          is-link
          readonly
          placeholder="请选择"
          :disabled="!form.customerId || isShipingAgain"
          @click="
            !form.customerId || isShipingAgain
              ? (showVoucherPicker = false)
              : (showVoucherPicker = true)
          "
        />
        <div class="remainNum" v-if="form.unDostorehouse">
          剩余未执行数量：{{ form.unDostorehouse * 1000 }} 公斤
        </div>
        <div v-if="showTips" class="tips-error">当前选择凭证已超过计划数量</div>
        <Field
          v-model="form.foodCategoryName"
          label="粮油品种"
          is-link
          readonly
          required
          placeholder="请选择"
          :disabled="disabledFoodCategory || isShipingAgain"
          :rules="[{ required: true, message: '请选择粮油品种' }]"
          @click="
            disabledFoodCategory || isShipingAgain
              ? inOutCode
                ? (showCategoryPicker1 = false)
                : (showCategoryPicker = false)
              : inOutCode
              ? (showCategoryPicker1 = true)
              : (showCategoryPicker = true)
          "
        />
        <Field
          v-if="!isOut"
          v-model="form.harvestYear"
          label="收获年份"
          is-link
          readonly
          required
          placeholder="请选择"
          :disabled="isShipingAgain || isGetFromBack"
          :rules="[{ required: true, message: '请选择收获年份' }]"
          @click="
            isShipingAgain || isGetFromBack ? (showYearPicker = false) : (showYearPicker = true)
          "
        />
        <Field
          v-if="isOut"
          v-model="form.counterPre"
          label="预提货数量"
          placeholder="请输入"
          :disabled="isShipingAgain"
        />
        <Field
          v-else
          v-model="form.counterOrg"
          label="原发数量(公斤)"
          placeholder="请输入"
          :disabled="isShipingAgain"
        />
        <Field
          v-model="form.foodReserveName"
          label="粮油性质"
          is-link
          readonly
          required
          placeholder="请选择"
          :disabled="(!!form.foodReserveName && foodReserveDisabled) || isShipingAgain"
          :rules="[{ required: true, message: '请选择粮油性质' }]"
          @click="
            (!!form.foodReserveName && foodReserveDisabled) || isShipingAgain
              ? (showReservePicker = false)
              : (showReservePicker = true)
          "
        />
        <Field
          v-if="form.buzTyper == 2 && checkPermission('job-scheduling:rotation-year')"
          v-model="form.rotationYear"
          label="轮换年度"
          is-link
          readonly
          required
          placeholder="请选择"
          :disabled="isShipingAgain"
          :rules="[{ required: true, message: '请选择轮换年度' }]"
          @click="isShipingAgain ? (showRotateYearPicker = false) : (showRotateYearPicker = true)"
        />
        <Field
          v-model="form.storageMethod"
          label="储存方式"
          is-link
          readonly
          required
          placeholder="请选择"
          :disabled="isShipingAgain"
          :rules="[{ required: true, message: '请选择储存方式' }]"
          @click="isShipingAgain ? (showStorageMethod = false) : (showStorageMethod = true)"
        />
        <Field
          v-if="form.storageMethod === '包装'"
          v-model="form.packagingType"
          label="包装物"
          is-link
          readonly
          required
          placeholder="请选择"
          :disabled="isShipingAgain"
          @click="isShipingAgain ? (showPackaging = false) : (showPackaging = true)"
        />
        <Field
          v-model="form.originName"
          label="产地"
          is-link
          readonly
          required
          placeholder="请选择"
          :rules="[{ required: true, message: '请选择产地' }]"
          @click="showOriginPicker = true"
        />
        <Field
          v-model="form.loadingPlace"
          :label="(isOut ? '卸粮' : '装粮') + '地点'"
          placeholder="请输入"
          :disabled="isShipingAgain"
        />
        <Field
          v-if="form.buzTyper == '4'"
          v-model="form.actualPickingUnit"
          label="实际提货单位"
          placeholder="请输入"
        />

        <Field
          v-if="isOut"
          v-model="form.transportCarrierName"
          label="承运单位"
          placeholder="请输入"
          :disabled="isShipingAgain"
        />
        <Field
          v-if="isOut"
          v-model="form.transportArrivalName"
          label="到站单位"
          placeholder="请输入"
          :disabled="isShipingAgain"
        />

        <Field
          v-if="showDryEnabled"
          label="是否需要烘干"
          :disabled="isShipingAgain"
          class="slot-field"
        >
          <template #input>
            <RadioGroup v-model="form.dryEnabled" direction="horizontal" :disabled="isShipingAgain">
              <Radio name="1">需要</Radio>
              <Radio name="2">不需要</Radio>
            </RadioGroup>
          </template>
        </Field>
        <Field
          v-show="form.dryEnabled == '1'"
          v-model="form.showDryTowerName"
          label="烘干塔"
          is-link
          placeholder="请选择"
          :disabled="isShipingAgain"
          @click="isShipingAgain ? (showTowerPicker = false) : (showTowerPicker = true)"
        />
        <Field
          label="是否单车多次称重"
          label-width="9.2em"
          required
          :disabled="isShipingAgain"
          :rules="[{ required: true, message: '请选择是否单车多次称重' }]"
          class="slot-field"
        >
          <template #input>
            <RadioGroup
              v-model="form.isWeighRepeatedly"
              direction="horizontal"
              :disabled="isShipingAgain"
            >
              <Radio name="1">需要</Radio>
              <Radio name="0">不需要</Radio>
            </RadioGroup>
          </template>
        </Field>
        <Field
          v-if="false"
          v-model="form.discernTypeName"
          label="称重模式"
          is-link
          readonly
          :required="form.isWeighRepeatedly == '1'"
          placeholder="请选择"
          :disabled="isShipingAgain"
          :rules="[{ required: form.isWeighRepeatedly == '1', message: '请选择称重模式' }]"
          @click="isShipingAgain ? (showDiscernPicker = false) : (showDiscernPicker = true)"
        />
        <Field
          v-if="showPurificationEnabled"
          label="是否需要除杂"
          :disabled="isShipingAgain"
          class="slot-field"
        >
          <template #input>
            <RadioGroup
              v-model="form.isPurification"
              direction="horizontal"
              :disabled="isShipingAgain"
            >
              <Radio name="1">需要</Radio>
              <Radio name="0">不需要</Radio>
            </RadioGroup>
          </template>
        </Field>
        <Field
          v-if="showSharingOrder && checkPermission('job-scheduling:isSharingOrder')"
          label="是否需要拼车"
          :disabled="isShipingAgain"
          class="slot-field"
        >
          <template #input>
            <RadioGroup
              v-model="form.isSharingOrder"
              direction="horizontal"
              :disabled="isShipingAgain"
            >
              <Radio :name="true">需要</Radio>
              <Radio :name="false">不需要</Radio>
            </RadioGroup>
          </template>
        </Field>
        <ActionSheet v-model:show="showTyperPicker">
          <BizDictPicker
            dict="INBOUND_AND_OUTBOUND_BUSINESS_TYPE"
            title="业务类型"
            @cancel="showTyperPicker = false"
            @confirm="onTyperConfirm"
          ></BizDictPicker>
        </ActionSheet>
        <ActionSheet v-model:show="showCustomerPicker" title="客户名称">
          <van-search v-model="customerSearch" placeholder="请输入客户名称"></van-search>
          <Picker
            :columns="customerFilter(customerSearch)"
            :columns-field-names="fieldNames"
            @cancel="showCustomerPicker = false"
            @confirm="onCustomerConfirm"
          ></Picker>
        </ActionSheet>
        <ActionSheet v-model:show="showOriginPicker" title="产地">
          <Cascader
            :options="originPlaces"
            :field-names="fieldNames"
            :closeable="false"
            @close="showOriginPicker = false"
            @change="onOriginConfirm"
          ></Cascader>
        </ActionSheet>
        <Dialog
          v-model:show="showVoucherPicker"
          title="作业凭证"
          className="scroll-wrapper"
          @confirm="selectVoucher"
        >
          <List
            ref="listRef"
            v-model:loading="isLoading"
            :finished="finished"
            @load="fetchVoucherList"
          >
            <div
              class="voucher-list"
              v-for="item in voucherList"
              :key="item.voucherIdAndDetailId"
              @click="selectedVoucherChange(item)"
            >
              <div
                class="voucher-id"
                v-bind:style="{
                  color:
                    selectedVoucher.voucherIdAndDetailId === item.voucherIdAndDetailId
                      ? '#1677FF'
                      : '#3A4056',
                }"
              >
                {{
                  item.voucherId
                    ? item.voucherId.includes('ZYD')
                      ? getSubStrCode(item.voucherId)
                      : item.voucherId
                    : ''
                }}
              </div>
              <div class="info-row">
                <div>粮油品种：{{ item.foodCategoryName }}</div>
                <div>
                  计划数量：
                  <HFixedNumber :ratio="1000" :fractionDigits="0">
                    {{ item.quantity }}
                  </HFixedNumber>
                </div>
              </div>
              <div class="info-row">
                <div>仓号：{{ item.storeHouseName }}</div>
                <div>
                  剩余未执行数量：
                  <HFixedNumber :ratio="1000" :fractionDigits="0">
                    {{ item.unDostorehouse }}
                  </HFixedNumber>
                </div>
              </div>
              <div class="info-row">
                <div>订单类型：</div>
                <div>
                  <BizDictName dict="ORDER_TYPE" :value="item.orderTyper"></BizDictName>
                </div>
              </div>
            </div>
          </List>
        </Dialog>

        <ActionSheet v-model:show="showCategoryPicker" title="粮油品种">
          <Cascader
            :options="categoryList"
            :field-names="fieldNames"
            :closeable="false"
            @close="showCategoryPicker = false"
            @change="onCategoryConfirm"
          ></Cascader>
        </ActionSheet>
        <ActionSheet v-model:show="showCategoryPicker1" title="粮油品种">
          <Cascader
            :options="categoryOptions"
            :field-names="fieldNames"
            :closeable="false"
            @close="showCategoryPicker1 = false"
            @change="onCategoryConfirm"
          ></Cascader>
        </ActionSheet>
        <ActionSheet v-model:show="showStorageMethod" title="储存方式">
          <Picker
            :columns="storageMethodData"
            @cancel="showStorageMethod = false"
            @confirm="onStorageMethod"
          ></Picker>
        </ActionSheet>
        <ActionSheet v-model:show="showPackaging" title="包装物">
          <Picker
            :columns="['麻袋', '编织袋', '其他']"
            @cancel="showPackaging = false"
            @confirm="packagingeMethod"
          ></Picker>
        </ActionSheet>

        <ActionSheet v-model:show="showYearPicker" title="收获年份">
          <Picker
            :columns="yearColumns"
            @cancel="showYearPicker = false"
            @confirm="onYearConfirm"
          ></Picker>
        </ActionSheet>

        <ActionSheet v-model:show="showRotateYearPicker" title="轮换年度">
          <Picker
            :columns="yearColumns"
            @cancel="showRotateYearPicker = false"
            @confirm="onRotateYearConfirm"
          ></Picker>
        </ActionSheet>
        <ActionSheet v-model:show="showReservePicker">
          <BizDictPicker
            dict="FOOD_PERPROTIES"
            title="粮油性质"
            :hiddenValues="hiddenFoodReserveIdValues"
            @cancel="showReservePicker = false"
            @confirm="onReserveConfirm"
          ></BizDictPicker>
        </ActionSheet>
        <ActionSheet v-model:show="showTowerPicker" title="烘干塔">
          <DryTowerSelect
            @cancel="showTowerPicker = false"
            @confirm="onTowerConfirm"
          ></DryTowerSelect>
        </ActionSheet>
        <ActionSheet v-model:show="showDiscernPicker" title="称重模式">
          <Picker
            :columns="discernTypeList"
            @cancel="showDiscernPicker = false"
            @confirm="onDiscernConfirm"
          ></Picker>
        </ActionSheet>
      </HCard>
      <div v-if="active === 1" class="car-warpper">
        <HCard
          v-for="(item, index) in form.jobSchedulingDetailDtoList"
          :key="index"
          class="car-card"
        >
          <div class="scheduling-row">
            <div class="text">调度号 {{ item.schedulingNo?.split('_')[0] }}</div>
            <div>
              <Button type="primary" round size="mini" @click="onShowQrcode(index)"
                >打印调度二维码</Button
              >
              <Button
                v-if="index != 0"
                round
                size="mini"
                class="delete-button"
                :disabled="!item.isAdd && isShipingAgain"
                @click="onDeleteRow(item)"
              >
                删除
              </Button>
            </div>
          </div>
          <Field
            v-model="item.transportVehicleNo"
            label="作业车牌"
            is-link
            readonly
            required
            placeholder="请选择"
            :disabled="index === 0 || (!item.isAdd && isShipingAgain)"
            :rules="[{ required: true, message: '请选择作业车牌' }]"
            @click="carItemClick('showVehicleNoPicker', index, item)"
          />
          <Field v-model="item.transportVehicleDriver" label="承运人" readonly />
          <Field
            v-model="item.isWeightName"
            label="是否需要称重"
            label-width="8.2em"
            is-link
            readonly
            required
            placeholder="请选择"
            :rules="[{ required: true, message: '请选择是否需要称重' }]"
            @click="carItemClick('showIsWeightPicker', index, item)"
          />
          <Field
            v-model="item.testingItemName"
            label="质检方案"
            is-link
            readonly
            required
            placeholder="请选择"
            :disabled="!item.isAdd && isShipingAgain"
            :rules="[{ required: true, message: '请选择质检方案' }]"
            @click="carItemClick('showTestingPicker', index, item)"
          />
          <Field
            v-if="item.testingItemId === -2"
            v-model="item.useQualitySchedulingNoName"
            label="选择调度号"
            is-link
            readonly
            required
            placeholder="请选择"
            :disabled="!item.isAdd && isShipingAgain"
            :rules="[{ required: true, message: '请选择调度号' }]"
            @click="carItemClick('showQCProgram', index, item)"
          />
          <Field
            label="是否值仓"
            required
            :rules="[{ required: true, message: '请选择是否值仓' }]"
            class="slot-field"
          >
            <template #input>
              <RadioGroup v-model="item.isduty" direction="horizontal">
                <Radio name="1">需要</Radio>
                <Radio name="2">不需要</Radio>
              </RadioGroup>
            </template>
          </Field>
          <Field
            v-if="item.isduty === '1'"
            v-model="item.storeHouseName"
            label="值仓仓房"
            is-link
            readonly
            :required="item.isduty === '1'"
            placeholder="请选择"
            :disabled="disableStorehouse || (!item.isAdd && isShipingAgain)"
            :rules="[{ required: item.isduty === '1', message: '请选择值仓仓房' }]"
            @click="carItemClick('showHousePicker', index, item)"
          />
          <ActionSheet v-model:show="showVehicleNoPicker" title="作业车牌">
            <CarSelect
              @cancel="showVehicleNoPicker = false"
              @confirm="onVehicleNoConfirm"
            ></CarSelect>
          </ActionSheet>
          <ActionSheet v-model:show="showIsWeightPicker">
            <BizDictPicker
              dict="IF_NEED_WEIGHT"
              title="是否需要称重"
              :disabled-values="[
                '3',
                form.storageMethod == '包装' ? '' : '6',
                selectedVoucher.categoryType == '3' && isArtificial ? '' : '7',
              ]"
              @cancel="showIsWeightPicker = false"
              @confirm="onIsWeightConfirm"
            ></BizDictPicker>
          </ActionSheet>
          <ActionSheet v-model:show="showTestingPicker" title="质检方案">
            <InspectionSchemeSelect
              :food-category="form.foodCategoryId"
              @cancel="showTestingPicker = false"
              @confirm="onTestingConfirm"
            ></InspectionSchemeSelect>
          </ActionSheet>
          <ActionSheet v-model:show="showQCProgram" title="选择调度号">
            <van-picker
              :columns="schedulingNosWithInspection"
              @cancel="showQCProgram = false"
              @confirm="onDispatchConfirm"
            ></van-picker>
          </ActionSheet>
          <ActionSheet v-model:show="showHousePicker" title="值仓仓房">
            <CargoSelect
              v-if="hasCargoSpace"
              :food-category-id="form.foodCategoryId"
              @cancel="showHousePicker = false"
              @confirm="onHouseConfirm"
            />
            <HouseSelect
              v-else
              :food-category-id="form.foodCategoryId"
              @cancel="showHousePicker = false"
              @confirm="onHouseConfirm"
            ></HouseSelect>
          </ActionSheet>
        </HCard>
        <Button class="add-button" @click="addRow" :loading="loadingSchedulingNo">
          + 新增车辆
        </Button>
      </div>
    </Form>
    <SchedulingTicket
      v-model:show="visible"
      :records="schedulingTickets"
      :showQrcodeIndex="showQrcodeIndex"
      :jobSchedulingDetailDtoList="form.jobSchedulingDetailDtoList"
    ></SchedulingTicket>
    <div class="bottom-warpper">
      <div class="buttons">
        <Button class="cancel-button" @click="onClose">取消</Button>
        <Button v-if="active === 0" class="next-button" @click="onNext">下一步</Button>
        <Button v-if="active === 1" class="next-button" @click="onSubmit" :loading="loadingSubmit">
          提交
        </Button>
      </div>
      <div class="bar"></div>
    </div>
  </div>
</template>

<script>
import {
  Form,
  Cell,
  Field,
  ActionSheet,
  Picker,
  Dialog,
  List,
  Cascader,
  Button,
  RadioGroup,
  Radio,
  Toast,
} from 'vant';
import {
  SchedulingSteps,
  BizDictName,
  BizDictPicker,
  DryTowerSelect,
  CarSelect,
  InspectionSchemeSelect,
  HouseSelect,
  SchedulingTicket,
  CargoSelect,
} from '@/views/InoutManage/common';
import { reactive, toRefs, onMounted, computed, ref, watch } from 'vue';
import { HCard, HFixedNumber } from '@/components';
import {
  getJobSchedulingDetail,
  findCrkCustomerRecords,
  getVoucherList,
  getFoodProperty,
  getNewSchedulingNo,
  saveScheduling,
  getPurchaseCategory,
  getListByCapacityCalculation,
} from '@/api/in-out-manage';
import { useStore } from 'vuex';
import { useRouter, useRoute } from 'vue-router';
import dayjs from 'dayjs';
import { checkPermission } from '@/utils/permission';
export default {
  components: {
    Form,
    Cell,
    Field,
    ActionSheet,
    Picker,
    Dialog: Dialog.Component,
    List,
    Cascader,
    Button,
    RadioGroup,
    Radio,
    SchedulingSteps,
    BizDictName,
    BizDictPicker,
    DryTowerSelect,
    CarSelect,
    InspectionSchemeSelect,
    HouseSelect,
    // eslint-disable-next-line vue/no-unused-components
    SchedulingTicket,
    HCard,
    HFixedNumber,
    CargoSelect,
  },
  setup() {
    const router = useRouter();
    const route = useRoute();
    const store = useStore();
    const categoryList = store.state['food-category'].tree;
    const originPlaces = store.state['origin-place'].tree;
    const infoForm = ref(null);
    const categoryOptions = ref([]);
    const state = reactive({
      active: 0,
      form: {}, // 表单数据
      isShipingAgain: false, // 是否再次船运
      disableVoucher: false, // 预约带入的作业凭证不能修改
      customerList: [], // 客户列表
      voucherList: [], // 作业凭证列表
      showTyperPicker: false, // 显示业务类型选择
      showCustomerPicker: false, // 显示客户选项
      showVoucherPicker: false, // 显示作业凭证选择
      showCategoryPicker: false, // 显示粮油品种选择
      showCategoryPicker1: false, //显示选择凭证后粮油品种选择
      showYearPicker: false, // 显示收获年份选择
      showRotateYearPicker: false, // 显示轮换年度选择
      showReservePicker: false, // 显示粮油性质选择
      showTowerPicker: false, // 显示烘干塔选择
      showDiscernPicker: false, // 显示称重模式选择
      showVehicleNoPicker: false, // 显示作业车辆选择
      showIsWeightPicker: false, // 显示是否需要称重
      showTestingPicker: false, // 显示质检方案选择
      showHousePicker: false, // 显示值仓仓房选择
      showQCProgram: false, // 显示复用质检方案选择
      showStorageMethod: false, // 显示储存方式选择
      showPackaging: false, // 显示储存方式选择
      showOriginPicker: false, // 显示产地选择
      customerSearch: '', // 客户选择过滤输入框
      isLoading: false,
      storageMethodData: ['散装', '包装'], //储存方式
      finished: false,
      selectedVoucher: {}, // 选中的作业凭证
      isGetFromBack: false, // 数据是否来源后端，不可修改
      foodProperty: {},
      foodReserveDisabled: false,
      isChangeVariety: true, // 凭证带的是否可以改变品种的字段
      carIndex: 0,
      loadingSchedulingNo: false,
      loadingSubmit: false,
      showQrcodeIndex: 0,
      visible: false, // 调度二维码
      isArtificial: false, //仓房选中油罐时
    });
    const hasCargoSpace = computed(() => checkPermission(['cargo_space']));
    let carIndex = 0;
    const fieldNames = {
      text: 'name',
      value: 'id',
      children: 'children',
    };
    const discernTypeList = [
      {
        text: 'RFID',
        value: '1',
      },
      {
        text: '车牌识别',
        value: '2',
      },
      {
        text: '二维码',
        value: '3',
      },
    ];
    const inOutCode = computed(() => {
      var code = state.form.voucherId ? getSubStrCode(state.form.voucherId) : '';
      return code;
    });
    const disabledFoodCategory = computed(() => {
      return !state.isChangeVariety;
    });
    const yearColumns = computed(() => {
      const nowYear = new Date().getFullYear();
      let list = [];
      for (let i = 0; i < 7; i++) {
        list.push(nowYear - i);
      }
      return list;
    });
    // 采购入库、销售出库不显示烘干
    const showDryEnabled = computed(() => {
      return ['2', '5', '6'].includes(state.form.buzTyper);
    });
    // 收购入库显示是否需要除杂
    const showPurificationEnabled = computed(() => {
      return ['2'].includes(state.form.buzTyper);
    });
    const isShowCustomerPicker = computed(() => {
      return ['2'].includes(state.form.buzTyper) || state.disableVoucher;
    });
    const disableStorehouse = computed(() => {
      return ['3', '4'].includes(state.form.buzTyper);
    });
    // 收购入库并且出入库凭证选择售粮卡时显示是否拼车
    const showSharingOrder = computed(() => {
      return (
        ['2'].includes(state.form.buzTyper) &&
        state.form.voucherId &&
        state.form.voucherId.indexOf('GSC') > -1
      );
    });
    // const isSharingOrder = () => {
    //   return this.hasPermission('job-scheduling:isSharingOrder');
    // };
    const isOut = computed(() => {
      const dict = store.getters['dict/reserveDictOf']('INBOUND_AND_OUTBOUND_BUSINESS_TYPE');
      const { buzTyper } = state.form;
      if (dict && buzTyper) {
        const type = dict.find((it) => it.value == buzTyper);
        return type && type.label.indexOf('出库') !== -1;
      }
      return false;
    });
    const schedulingTickets = computed(() => {
      const { form } = state;
      const {
        serialNo,
        storeHouseName,
        schedulingNo,
        customerName,
        transportVehicleNo,
        buzTyperName,
      } = form;
      const time = dayjs().format('YYYY-MM-DD HH:mm:ss');
      const tickets = [
        {
          serialNo,
          storeHouseName,
          schedulingNo,
          customer: customerName,
          transportVehicleNo,
          buzTypeName: buzTyperName,
          time,
        },
      ];
      console.log(tickets, 'tickets值仓仓房');
      return tickets;
    });
    const hiddenFoodReserveIdValues = computed(() => {
      const defaultValues = ['5']; // 超标粮不显示
      let hiddenValues = [];
      switch (state.form.buzTyper) {
        case '2':
          hiddenValues = ['1', '2', '3'];
          break;
        case '3':
          hiddenValues = ['4', '6'];
          break;
        case '4':
          hiddenValues = ['1', '2', '3', '6'];
          break;
      }
      return [...hiddenValues, ...defaultValues];
    });
    // 展示tips
    const showTips = computed(() => {
      return Number(state.form.unDostorehouse) < 0;
    });
    onMounted(async () => {
      initData();
    });
    // 截取出入库作业单编码
    const getSubStrCode = (str) => {
      var i = str.indexOf('ZYD');
      return str.substr(i);
    };
    const initData = async () => {
      const schedulingNo = route.params.schedulingNo;
      const data = await getJobSchedulingDetail({ schedulingNo });
      console.log(data, '++++++++++++');
      const {
        typeShipping,
        counterPre,
        counterOrg,
        dryEnabled,
        transportVehicleNo,
        transportVehicleDriver,
        transportVehicleTel,
        schedulingOrderNo,
        voucherId,
        jobSchedulingDetailDtoList,
        isWeighRepeatedly,
        storageMethod,
        producingArea,
        producingAreaName,
      } = data;
      state.form = {
        ...data,
        counterPre: counterPre ? counterPre * 1000 : null,
        counterOrg: counterOrg ? counterOrg * 1000 : null,
        dryEnabled: dryEnabled ? dryEnabled : '2',
        storageMethod: storageMethod ? storageMethod : '散装',
        isWeighRepeatedly: isWeighRepeatedly ? isWeighRepeatedly : '0',
        jobSchedulingDetailDtoList: jobSchedulingDetailDtoList
          ? jobSchedulingDetailDtoList.map((item) => {
              return {
                ...item,
                storeHouseName:
                  hasCargoSpace.value && item.cargoSpaceName
                    ? `${item.storeHouseName}_${item.cargoSpaceName}`
                    : item.storeHouseName,
              };
            })
          : [
              {
                isWeight: '2',
                isWeightName: '称毛,称皮',
                isduty: '1',
                storeHouseId: null,
                storeHouseName: null,
                transportVehicleNo: transportVehicleNo,
                transportVehicleDriver: transportVehicleDriver,
                transportVehicleTel: transportVehicleTel,
                schedulingNo: schedulingNo,
                schedulingOrderNo: schedulingOrderNo,
                testingItemId: 0,
                testingItemName: '待定',
              },
            ],
        origin: producingArea,
        originName: producingAreaName,
      };
      state.isShipingAgain =
        typeShipping == '2' && jobSchedulingDetailDtoList && jobSchedulingDetailDtoList.length > 0;
      state.disableVoucher = !!voucherId;
      searchCustomer();
    };
    const searchCustomer = async (customerName) => {
      const list = await findCrkCustomerRecords(customerName, state.form.buzTyper || undefined);
      state.customerList = list;
    };
    const onTyperConfirm = async (value) => {
      console.log(value, '(((((((((((((((');
      state.form.buzTyper = value.value;
      state.form.buzTyperName = value.label;
      if (isOut.value) {
        state.form.jobSchedulingDetailDtoList.forEach((it) => {
          it.testingItemId = -1;
          it.testingItemName = '(不做质检)';
        });
      } else {
        state.form.jobSchedulingDetailDtoList.forEach((it) => {
          it.testingItemId = 0;
          it.testingItemName = '待定';
        });
      }
      // 清除已选择的客户
      state.form.customerId = null;
      // 清除已选择的调度号
      state.form.useQualitySchedulingNo = null;
      state.form.useQualitySchedulingNoName = null;
      state.form.customerName = null;
      state.form.voucherId = null;
      // 清除已选择粮油性质
      state.form.foodReserveId = null;
      state.form.foodReserveName = null;
      // 清空实际提货单位
      state.form.actualPickingUnit = null;
      // 清空粮食品种
      state.form.foodCategoryId = null;
      state.form.foodCategoryName = null;
      // 清空收获年份
      state.form.harvestYear = null;
      // 清空轮换年度
      state.form.rotationYear = null;
      //清空到站单位
      state.form.transportArrivalName = null;
      //清空承运单位
      state.form.transportCarrierName = null;
      // 清空储存方式
      state.form.storageMethod = '散装';
      //清空装粮地点
      state.form.loadingPlace = null;
      // 清空是否除杂
      state.form.isPurification = undefined;
      state.isGetFromBack = false;
      // 销售出库/收购入库/采购入库默认商品粮
      if (['2', '3', '4'].includes(state.form.buzTyper)) {
        state.form.foodReserveId = '4';
        state.form.foodReserveName = '商品粮';
      }
      // 调拨入库调拨出入库其它入库其它出库粮油性质可手动选择
      state.foodReserveDisabled = !['1', '5', '6', '7'].includes(state.form.buzTyper);
      await searchCustomer();
      state.showTyperPicker = false;
    };
    const onCustomerConfirm = (value) => {
      state.form.customerId = value.id;
      state.form.customerName = value.name;
      // 清除已选择的作业凭证
      state.form.voucherId = null;
      // 清空粮食品种
      state.form.foodCategoryId = null;
      state.form.foodCategoryName = null;
      // 清除已选择粮油性质
      state.form.foodReserveId = null;
      state.form.foodReserveName = null;
      // 清空收获年份
      state.form.harvestYear = null;
      // 清空储存方式
      state.form.storageMethod = '散装';
      //清空轮换年度
      state.form.rotationYear = null;
      fetchVoucherList();
      state.showCustomerPicker = false;
    };
    const customerFilter = (value) => {
      const customerList = state.customerList;
      if (!value) {
        return customerList;
      }
      const filterCustomerList = customerList.filter((item) => item.name.indexOf(value) > -1);
      return filterCustomerList;
    };
    const fetchVoucherList = async () => {
      state.isLoading = true;
      state.finished = false;
      const list = await getVoucherList({
        customerId: state.form.customerId,
        typer: state.form.buzTyper,
      });
      state.voucherList = list.map((v) => {
        return {
          ...v,
          quantity: parseFloat(v.quantity) || 0,
          unDostorehouse: parseFloat(v.unDostorehouse) || 0,
          voucherIdAndDetailId: v.voucherId + '_' + v.voucherDetailId,
        };
      });
      state.isLoading = false;
      state.finished = true;
    };
    const selectedVoucherChange = (item) => {
      state.selectedVoucher = item;
      console.log(item, 'item-----');
    };
    const selectVoucher = async () => {
      const { selectedVoucher } = state;
      if (!selectedVoucher?.voucherId) {
        state.showVoucherPicker = false;
        return;
      }
      state.isChangeVariety = selectedVoucher.isChangeVariety === '1';
      state.form.voucherId = selectedVoucher.voucherId;
      state.form.isWorkSheet = selectedVoucher.isWorkSheet;
      state.form.voucherDetailId = selectedVoucher.voucherDetailId;
      state.form.harvestYear = selectedVoucher.harvestYear;
      state.form.foodLevel = selectedVoucher.grade ? `${selectedVoucher.grade}` : undefined;
      state.form.foodCategoryId = selectedVoucher.foodCategoryId;
      state.form.foodCategoryName = selectedVoucher.foodCategoryName;
      state.form.unDostorehouse = selectedVoucher.unDostorehouse;
      if (selectedVoucher.harvestYear) {
        state.isGetFromBack = true;
      } else {
        state.isGetFromBack = false;
      }
      const data = await getFoodProperty({
        buzTyper: state.form.buzTyper,
        foodCategoryId: selectedVoucher.foodCategoryId,
        voucherId: selectedVoucher.voucherId,
        voucherDetailId: selectedVoucher.voucherDetailId,
      });
      const foodProperty = {
        ...data,
        storeHouseId: parseInt(selectedVoucher.storeHouseId) || undefined,
        storeHouseName: selectedVoucher.storeHouseName,
      };
      if (selectedVoucher.categoryType == 3) {
        const list = await getListByCapacityCalculation();
        state.isArtificial = list.some((o) => o.id == parseInt(selectedVoucher.storeHouseId));
        console.log(state.isArtificial, 'state.isArtificial----');
      }
      state.foodProperty = foodProperty;
      state.form.foodReserveId = foodProperty.foodReserveId;
      state.form.foodReserveName = foodProperty.foodReserveName;
      state.form.origin = foodProperty.producingArea;
      state.form.originName = foodProperty.producingAreaName;
      if (state.form.buzTyper == '7' && foodProperty.foodReserveId != '1') {
        state.foodReserveDisabled = false;
        state.form.foodReserveId = '4';
        state.form.foodReserveName = '商品粮';
      } else if (state.form.buzTyper == '1' && foodProperty.foodReserveId != '1') {
        state.foodReserveDisabled = false;
      } else if (state.form.buzTyper == '5' || state.form.buzTyper == '6') {
        state.foodReserveDisabled = false;
      } else if (state.form.buzTyper == '3' && selectedVoucher.foodProperties) {
        state.foodReserveDisabled = true;
        const dict = store.getters['dict/reserveDictOf']('FOOD_PERPROTIES');
        state.form.foodReserveId = selectedVoucher.foodProperties;
        state.form.foodReserveName = dict.find(
          (item) => item.value == selectedVoucher.foodProperties,
        )?.label;
      } else {
        state.foodReserveDisabled = true;
      }

      if (foodProperty.storeHouseId) {
        state.form.jobSchedulingDetailDtoList = state.form.jobSchedulingDetailDtoList.map((v) => {
          return {
            ...v,
            storeHouseId: selectedVoucher.storeHouseId,
            storeHouseName: selectedVoucher.storeHouseName,
          };
        });
      }
      state.selectedVoucher = {};
      state.showVoucherPicker = false;
      // 获取对应凭证可选粮油品种
      getCategoryOptions();
    };
    const getCategoryOptions = async () => {
      let list = await getPurchaseCategory({ voucherId: state.form?.voucherId });
      categoryOptions.value = list.map((it) => {
        return {
          id: +it.foodCategoryId,
          name: it.foodCategoryName,
          key: +it.foodCategoryId,
          value: +it.foodCategoryId,
          text: it.foodCategoryName,
          title: it.foodCategoryName,
        };
      });
    };
    const onCategoryConfirm = ({ selectedOptions: list }) => {
      if (list.length === 0) {
        state.form.foodCategoryId = '';
        state.form.foodCategoryName = '';
        return;
      }
      const item = list[list.length - 1];
      state.form.foodCategoryId = item.id;
      state.form.foodCategoryName = item.name;
    };
    const onStorageMethod = (value) => {
      state.form.storageMethod = value;
      state.showStorageMethod = false;
    };
    const packagingeMethod = (value) => {
      state.form.packagingType = value;
      state.showPackaging = false;
    };
    const onYearConfirm = (value) => {
      state.form.harvestYear = value;
      state.showYearPicker = false;
    };
    const onRotateYearConfirm = (value) => {
      state.form.rotationYear = value;
      state.showRotateYearPicker = false;
    };
    const onReserveConfirm = (value) => {
      state.form.foodReserveId = value.value;
      state.form.foodReserveName = value.label;
      state.showReservePicker = false;
    };
    const onTowerConfirm = (value) => {
      state.form.showDryTowerName = value.name;
      state.form.dryTowerName = value.id;
      state.showTowerPicker = false;
    };
    const onDiscernConfirm = (value) => {
      state.form.discernType = value.value;
      state.form.discernTypeName = value.text;
      state.showDiscernPicker = false;
    };
    const carItemClick = (key, index, item) => {
      console.log(key, index, item);
      if (key === 'showIsWeightPicker') {
        carIndex = index;
        state[key] = true;
      } else if (key === 'showVehicleNoPicker') {
        if (index === 0 || (!item.isAdd && state.isShipingAgain)) {
          return;
        }
        carIndex = index;
        state[key] = true;
      } else if (key === 'showTestingPicker') {
        if (!item.isAdd && state.isShipingAgain) {
          return;
        }
        carIndex = index;
        state[key] = true;
      } else if (key === 'showHousePicker') {
        if (disableStorehouse.value || (!item.isAdd && state.isShipingAgain)) {
          return;
        }
        carIndex = index;
        state[key] = true;
      } else if (key === 'showQCProgram') {
        // console.log('进来了啊啊', schedulingNosWithInspection);
        // if (disableStorehouse.value || (!item.isAdd && state.isShipingAgain)) {
        //   return;
        // }
        carIndex = index;
        state[key] = true;
      }
    };
    const onVehicleNoConfirm = (value) => {
      state.form.jobSchedulingDetailDtoList[carIndex].transportVehicleNo = value.transportVehicleNo;
      state.form.jobSchedulingDetailDtoList[carIndex].transportVehicleDriver =
        value.transportVehicleDriver;
      state.showVehicleNoPicker = false;
    };
    const onIsWeightConfirm = (value) => {
      state.form.jobSchedulingDetailDtoList[carIndex].isWeight = value.value;
      state.form.jobSchedulingDetailDtoList[carIndex].isWeightName = value.label;
      state.showIsWeightPicker = false;
    };
    const onTestingConfirm = (value) => {
      state.form.jobSchedulingDetailDtoList[carIndex].testingItemId = value.id;
      state.form.jobSchedulingDetailDtoList[carIndex].testingItemName = value.name;
      state.showTestingPicker = false;
    };
    const onDispatchConfirm = (value) => {
      console.log(value);
      state.form.jobSchedulingDetailDtoList[carIndex].useQualitySchedulingNo = value.value;
      state.form.jobSchedulingDetailDtoList[carIndex].useQualitySchedulingNoName = value.text;
      state.showQCProgram = false;
    };
    const getShortSchedulingNo = (schedulingNo) => {
      return schedulingNo.split('_')[0] || '-';
    };
    const schedulingNosWithInspection = computed(() => {
      return state.form.jobSchedulingDetailDtoList
        ?.filter((it) => it.testingItemId > 0)
        ?.map((it) => ({
          text: getShortSchedulingNo(it.schedulingNo),
          value: it.schedulingNo,
        }));
    });

    const onHouseConfirm = (value) => {
      if (hasCargoSpace.value) {
        state.form.jobSchedulingDetailDtoList[carIndex].cargoSpaceId = value.id;
        state.form.jobSchedulingDetailDtoList[carIndex].storeHouseId = value.storeHouseId;
        state.form.jobSchedulingDetailDtoList[carIndex].storeHouseName = value.houseAndCargo;
      } else {
        state.form.jobSchedulingDetailDtoList[carIndex].storeHouseId = value.id;
        state.form.jobSchedulingDetailDtoList[carIndex].storeHouseName = value.name;
      }
      state.showHousePicker = false;
    };
    const addRow = async () => {
      state.loadingSchedulingNo = true;
      const lastDetail = state.form.jobSchedulingDetailDtoList.slice(-1).pop();
      const { schedulingNo, storeHouseId, storeHouseName, cargoSpaceId } = lastDetail;
      const newSchedulingNo = await getNewSchedulingNo(schedulingNo);
      const detail = {
        isWeight: '2',
        isWeightName: '称毛,称皮',
        isduty: '1',
        storeHouseId: state.isShipingAgain ? storeHouseId : state.foodProperty.storeHouseId,
        storeHouseName: state.isShipingAgain ? storeHouseName : state.foodProperty.storeHouseName,
        transportVehicle: undefined,
        transportVehicleDriver: undefined,
        transportVehicleNo: undefined,
        transportVehicleTel: state.form.transportVehicleTel,
        schedulingNo: newSchedulingNo,
        schedulingOrderNo: state.form.schedulingOrderNo,
        testingItemId: isOut.value ? -1 : 0,
        testingItemName: isOut.value ? '(不做质检)' : '待定',
        isAdd: true,
        cargoSpaceId: hasCargoSpace.value ? cargoSpaceId : undefined,
      };
      state.form.jobSchedulingDetailDtoList.push(detail);
      state.loadingSchedulingNo = false;
    };
    const onShowQrcode = (index) => {
      state.showQrcodeIndex = index;
      state.visible = true;
    };
    const onDeleteRow = (item) => {
      state.form.jobSchedulingDetailDtoList = state.form.jobSchedulingDetailDtoList.filter(
        (it) => it.schedulingNo != item.schedulingNo,
      );
    };
    const onClose = () => {
      router.back();
    };
    const onNext = async () => {
      await infoForm.value.validate();
      state.active = 1;
    };
    const onSubmit = async () => {
      state.loadingSubmit = true;
      try {
        await infoForm.value.validate();
      } catch (error) {
        state.loadingSubmit = false;
        return;
      }
      const { form } = state;
      const { jobSchedulingDetailDtoList } = form;
      let data = {
        ...form,
        counterOrg: form.counterOrg / 1000,
        counterPre: form.counterPre / 1000,
        packagingType:
          form.packagingType === '麻袋' ? '1' : form.packagingType === '编织袋' ? '2' : '4',
        customerName: form.customerId, // web端之前已经用错，只能按错的来了
        discernTypeName: form.isWeighRepeatedly === '1' ? undefined : '二维码',
        dryTowerName: form.dryEnabled == '1' ? form.dryTowerName : '',
        foodReserveName: form.foodReserveId, // web端之前已经用错，只能按错的来了
        voucherNo: form.voucherId, // 接口使用字段不同
        jobSchedulingDetailDtoList: jobSchedulingDetailDtoList.map((it) => {
          const { isduty, storeHouseId, cargoSpaceId, ...rest } = it;
          return {
            ...rest,
            isduty: isduty,
            storeHouseId: isduty === '1' ? storeHouseId : null,
            cargoSpaceId: isduty === '1' && hasCargoSpace.value ? cargoSpaceId : null,
          };
        }),
        producingArea: form.origin,
      };
      // 删除烘干信息
      if (!showDryEnabled.value) {
        Reflect.deleteProperty(data, 'dryEnabled');
        Reflect.deleteProperty(data, 'dryTowerName');
      }
      // 删除除杂信息
      if (!showPurificationEnabled.value) {
        Reflect.deleteProperty(data, 'isPurification');
      }
      try {
        console.log(data, '9999999999999999999999');
        await saveScheduling(data);
        Toast.success('保存成功');
        onClose();
      } catch (e) {
        // Toast.fail('保存失败');
      } finally {
        state.loadingSubmit = false;
      }
    };
    //产地确定
    const onOriginConfirm = ({ selectedOptions: list }) => {
      if (list.length === 0) {
        state.form.origin = '';
        state.form.originName = '';
        return;
      }
      const item = list[list.length - 1];
      state.form.origin = item.value;
      state.form.originName = item.label;
    };
    watch(route.params.schedulingNo, initData);
    return {
      showSharingOrder,
      ...toRefs(state),
      infoForm,
      inOutCode,
      disabledFoodCategory,
      fieldNames,
      discernTypeList,
      yearColumns,
      showDryEnabled,
      showPurificationEnabled,
      isShowCustomerPicker,
      disableStorehouse,
      isOut,
      schedulingTickets,
      hiddenFoodReserveIdValues,
      onTyperConfirm,
      onCustomerConfirm,
      customerFilter,
      fetchVoucherList,
      getSubStrCode,
      selectedVoucherChange,
      selectVoucher,
      onCategoryConfirm,
      categoryList,
      categoryOptions,
      originPlaces,
      onOriginConfirm,
      onYearConfirm,
      onRotateYearConfirm,
      onStorageMethod,
      packagingeMethod,
      onReserveConfirm,
      onTowerConfirm,
      onDiscernConfirm,
      carItemClick,
      onVehicleNoConfirm,
      onIsWeightConfirm,
      onTestingConfirm,
      onDispatchConfirm,
      onHouseConfirm,
      addRow,
      onShowQrcode,
      onDeleteRow,
      onClose,
      onNext,
      onSubmit,
      showTips,
      schedulingNosWithInspection,
      checkPermission,
      hasCargoSpace,
    };
  },
};
</script>

<style lang="scss" scoped>
@import '@/views/InoutManage/style/index';
.info-card {
  margin-top: 10px;
  margin-bottom: 120px;
  ::v-deep(.bold-text) {
    font-weight: bold;
  }
  .voucher-list {
    display: flex;
    flex-direction: column;
    padding: 15px;
    .voucher-id {
      font-size: 16px;
      font-weight: 500;
      color: #3a4056;
      line-height: 18px;
    }
    .info-row {
      width: 100%;
      display: flex;
      flex-direction: row;
      justify-content: space-between;
      font-size: 14px;
      font-weight: 400;
      color: #686b73;
      line-height: 18px;
    }
  }
}
.car-warpper {
  margin-top: 10px;
  margin-bottom: 120px;
  .car-card {
    margin-bottom: 10px;
    .scheduling-row {
      display: flex;
      justify-content: space-between;
      padding: 10px 16px;
      .text {
        font-size: 16px;
        font-weight: bold;
        color: #111111;
        line-height: 22px;
      }
      .delete-button {
        color: #ff5722;
        border: 1px solid #ff5722;
      }
    }
  }
  .add-button {
    width: 100%;
    height: 40px;
    background: #e8f3ff;
    border-radius: 2px;
    font-size: 14px;
    font-weight: 500;
    color: #1677ff;
    line-height: 40px;
    text-align: center;
  }
}
.remainNum {
  margin-left: 20px;
  font-size: 14px;
}
.tips-error {
  color: red;
  font-size: 12px;
  margin-left: 20px;
}
::v-deep(.scroll-wrapper) {
  max-height: 60vh;
  min-width: 360px;
  overflow-y: auto;
  -webkit-overflow-scrolling: touch;
}
::v-deep(.scroll-wrapper::-webkit-scrollbar-track-piece) {
  background-color: rgba(0, 0, 0, 0);
  border-left: 0.013333rem solid rgba(0, 0, 0, 0);
}
::v-deep(.scroll-wrapper::-webkit-scrollbar) {
  width: 0.04rem;
  height: 0.173333rem;
  -webkit-border-radius: 0.066667rem;
  -moz-border-radius: 0.066667rem;
  border-radius: 0.066667rem;
}
::v-deep(.scroll-wrapper::-webkit-scrollbar-thumb) {
  background-color: rgba(0, 0, 0, 0.5);
  background-clip: padding-box;
  -webkit-border-radius: 0.066667rem;
  -moz-border-radius: 0.066667rem;
  border-radius: 0.066667rem;
  min-height: 0.373333rem;
}
::v-deep(.scroll-wrapper::-webkit-scrollbar-thumb:hover) {
  background-color: rgba(0, 0, 0, 0.5);
  -webkit-border-radius: 0.066667rem;
  -moz-border-radius: 0.066667rem;
  border-radius: 0.066667rem;
}
</style>
