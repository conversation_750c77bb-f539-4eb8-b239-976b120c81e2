import { request, reserveRequest } from '@/utils/request';

export async function getOrderPublicTotal({ areaCode, year }) {
  return request().get('/flow-sign-order/getSignOrderMobileSum', {
    params: {
      areaCode,
      year,
    },
  });
}

export async function getOrderPublicList({ areaCode, year }) {
  return request().get('/flow-sign-order/getSignOrderMobileVOList', {
    params: {
      areaCode,
      year,
    },
  });
}

//订单清册分页查询
export async function getOrderInventoryList(params) {
  return reserveRequest().get('/api/orderInventory/infoZZD', {
    params,
  });
}

//订单清册分页详情查询
export function getOrderInventoryByIdPage(params) {
  return reserveRequest().get('/api/orderInventory/detailZZD', {
    params,
  });
}

// 根据Id查看部门
export function getUnitTreeByUserId(params) {
  return reserveRequest().get('/api/flowCheckResults/infoByUserId', {
    params,
  });
}

// 所有公司，包含代储公司
export async function getCompany() {
  return reserveRequest().get('/api/unit/getCompany');
}

export async function getCompanyByUser(params) {
  return reserveRequest().get('/api/unit/findByUser', {
    params,
  });
}

// 获取浙江储备用户id和组织id
export function getUserIdAndDeptId(params) {
  return reserveRequest().get('/api/users/getUserIdAndDeptIdRegister', {
    params,
  });
}
