<template>
  <div class="stock-overview">
    <div class="content">
      <router-view></router-view>
    </div>
    <Tabbar route v-if="false">
      <TabbarItem replace :to="{ name: 'StockStatistics' }">
        <template #icon>
          <SvgIcon size="26" name="chart" />
        </template>
        <span>库存统计</span>
      </TabbarItem>
      <TabbarItem replace :to="{ name: 'StockDetail' }">
        <template #icon>
          <SvgIcon size="26" name="list" />
        </template>
        <span>库存详情</span>
      </TabbarItem>
    </Tabbar>
  </div>
</template>

<script setup>
import { Tabbar, TabbarItem } from 'vant';
import SvgIcon from '@/components/SvgIcon/SvgIcon';
import { useStore } from 'vuex';
import { onMounted } from 'vue';

const store = useStore();

onMounted(() => {
  store.dispatch('stock-overview/fetchData');
});
</script>

<style scoped></style>
