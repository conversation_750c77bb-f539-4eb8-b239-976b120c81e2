const inquirer = require('inquirer');
const { spawn } = require('child_process');

const choices = [
  { label: '浙江', value: 'zhejiang' },
  { label: '内蒙古', value: 'neimenggu' },
  { label: '湖北', value: 'hubei' },
  { label: '湖北-鄂汇办', value: 'hubei-ehb' },
  { label: '四川', value: 'sichuan' },
  { label: '安徽', value: 'anhui' },
].map((app) => ({
  name: `客户：${app.label} -> ${app.value}`,
  value: app,
}));

const clients = [
  {
    type: 'list',
    name: 'app',
    message: `请选择客户：`,
    choices,
  },
];

const envs = [
  {
    type: 'list',
    name: 'env',
    message: `请选择需要启动的环境：`,
    choices: ['test', 'prod'],
  },
];

function invokeVueCLI(env, app) {
  const webpackPath = require.resolve('.bin/vue-cli-service');
  const childProcess = spawn(
    'cross-env',
    [`VUE_APP_API_ENV=${env}`, webpackPath, 'serve', '--mode', app.value],
    {
      stdio: 'inherit',
      shell: true,
    },
  );
  ['SIGINT', 'SIGTERM'].forEach((signal) => {
    process.on(signal, () => {
      childProcess.kill('SIGKILL');
    });
  });
}

inquirer.prompt(clients).then(({ app }) => {
  inquirer.prompt(envs).then(({ env }) => {
    invokeVueCLI(env, app);
  });
});
