<template>
  <div class="px-2 py-2">
    <div class="py-4 mb-3 bg-white rounded-md" v-for="item in acquisitionWaring" :key="item.id">
      <h1 class="flex justify-between px-3 mb-4">
        <p class="font-[20px] font-bold text-blue-400">{{ item.warningName }}</p>
        <p>待处理</p>
      </h1>
      <van-cell-group class="cells">
        <van-cell class="" title="地区：" :value="item.areaName" />
        <van-cell class="" title="收储公司：" :value="item.unitName" />
        <van-cell class="" title="库点名称：" :value="item.storeName" />
      </van-cell-group>

      <!-- <div class="flex justify-between mx-3 mt-4">
        <van-button plain type="primary" size="small">已读</van-button>
        <van-button type="primary" size="small">已核查</van-button>
      </div> -->

      <div class="px-4 mt-4 overflow-hidden text-right">
        <van-button
          v-p="[`app-early-warning-approval:detail`]"
          @click="(showPopup = true), (detailData = item)"
          type="primary"
          size="small"
          >详情</van-button
        >
      </div>
    </div>
    <EmptyHolder v-if="acquisitionWaring.length === 0" />
  </div>

  <van-popup
    v-model:show="showPopup"
    closeable
    class="!bg-gray-50"
    position="bottom"
    :style="{ height: '100%' }"
  >
    <div class="h-[50px]">
      <h2 class="p-3 pl-5 font-bold text-blue-400">{{ detailData.warningName }}</h2>
    </div>
    <!-- <h1 class="flex justify-between px-3 mb-4 mt-14">
      <p class="font-[20px] font-bold text-blue-400">{{ detailData.warningName }}</p>
      <p>待处理</p>
    </h1> -->
    <van-cell-group class="cells" inset>
      <van-cell class="" title="状态：" value="待处理" />
      <van-cell class="" title="地区：" :value="detailData.areaName" />
      <van-cell class="" title="收储公司：" :value="detailData.unitName" />
      <van-cell class="" title="库点名称：" :value="detailData.storeName" />
    </van-cell-group>

    <div v-for="(item, index) in JSON.parse(detailData?.detail ?? [])" :key="item">
      <h2 class="p-3 pl-5 mt-3 font-bold">预警详情 {{ index + 1 }}</h2>
      <van-cell-group class="cells" inset>
        <van-cell class="" title="库点：" :value="item.storeName" />
        <van-cell class="" title="仓廒：" :value="item.cargoName" />
        <van-cell class="" title="粮油品种：" :value="item.foodCategoryName" />
      </van-cell-group>
    </div>

    <van-sticky :offset-bottom="10" class="mt-5" position="bottom">
      <van-row gutter="20" class="px-4">
        <van-col span="12"
          ><van-button
            type="danger"
            v-p="[`app-early-warning-approval:reject`]"
            :disabled="detailData.state === 2"
            plain
            block
            @click="finish(detailData.id, 3)"
            >误报</van-button
          ></van-col
        >
        <van-col span="12"
          ><van-button
            type="primary"
            v-p="[`app-early-warning-approval:finish`]"
            :disabled="detailData.state === 2"
            block
            @click="finish(detailData.id, 2)"
            >完成核验</van-button
          ></van-col
        >
      </van-row>
    </van-sticky>
  </van-popup>
</template>

<script setup>
import { ref, onMounted } from 'vue';
import { hrpsRequest } from '@/utils/request';
import { useStore } from 'vuex';
import EmptyHolder from '@/views/common/EmptyHolder';
// import dayjs from 'dayjs';

const showPopup = ref(false);
const store = useStore();
const userInfo = store.getters['user/userInfo'];
const detailData = ref({});
const acquisitionWaring = ref([]);
const getList = async () => {
  const params = {
    // areaCode: userInfo?.provinceCode,
    state: 1,
    pageSize: 50,
    startPos: 0,
    deptPath: userInfo?.dept?.path,
  };
  acquisitionWaring.value = await hrpsRequest().get('/hrps/acquisition/waring/query', { params });
};
onMounted(async () => {
  getList();
});

const finish = async (id, state) => {
  await hrpsRequest().get('/hrps/acquisition/waring/updateState', {
    params: {
      id,
      state,
      deptId: userInfo?.dept?.id,
    },
  });
  detailData.value.state = state;
  showPopup.value = false;
  getList();
};
</script>

<style lang="scss" scoped>
::v-deep(.van-cell) {
  // padding: 6px 16px;
}
</style>
