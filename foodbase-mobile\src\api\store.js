import { reserveRequest } from '@/utils/request';

export async function getStoreByCityCompany(areaCode) {
  // return request().get('/info-store/getStoreByCityCompany', {
  //   params: { areaCode, companyId, level },
  // });
  return reserveRequest().get('/api/store/getStoreByAreaApp', {
    params: { areaCode },
  });
}

export async function getStoreFindByUser(unitId) {
  return reserveRequest().get('/api/store/findByUser', {
    params: { unitId },
  });
}
