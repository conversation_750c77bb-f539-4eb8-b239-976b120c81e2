<template>
  <Tabs v-model:active="category" swipeable>
    <Tab :name="1" title="原粮">
      <CategoryStockStatistics :category-type="1"></CategoryStockStatistics>
    </Tab>
    <Tab :name="2" title="成品粮">
      <CategoryStockStatistics :category-type="2"></CategoryStockStatistics>
    </Tab>
    <Tab :name="3" :title="oilTitle">
      <CategoryStockStatistics :category-type="3"></CategoryStockStatistics>
    </Tab>
  </Tabs>
</template>

<script setup>
import { Tabs, Tab } from 'vant';
import CategoryStockStatistics from '@/views/StockOverview/common/CategoryStockStatistics';
import { computed, watch, ref, provide } from 'vue';
import { useStore } from 'vuex';

const store = useStore();

const oilTitle = ref(process.env.VUE_APP_MODE === 'hubei' ? '储备油' : '食用油');

const category = computed({
  get: () => {
    return store.state['stock-overview'].category;
  },
  set: (value) => {
    store.commit('stock-overview/setCategory', value);
  },
});

provide('category', category);

watch(category, (newValue, oldValue) => {
  if (newValue != oldValue) {
    store.dispatch('stock-overview/fetchStockVarietyStructureData');
    store.dispatch('stock-overview/fetchStockLeaveStructureData');
  }
});
</script>

<style scoped></style>
