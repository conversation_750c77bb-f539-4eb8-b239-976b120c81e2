<template>
  <div class="logout">退出中...</div>
</template>

<script setup>
import { onMounted } from 'vue';
import { useStore } from 'vuex';
import { useRouter } from 'vue-router';

const store = useStore();
const router = useRouter();

onMounted(async () => {
  await store.commit('setBaseDataLoaded', false);
  await store.dispatch('user/logout');
  history.go(-(history.length - 1));
  router.replace({ name: 'Login' });
});
</script>

<style scoped></style>
