<template>
  <div
    class="videojs-component"
    :id="component_id"
    :class="{
      fit: fit,
      fullscreen_control: fullscreen_control,
    }"
    @click="click_decision"
    @dblclick="double_clicked"
    @mouseover="hovering = true"
    @mouseout="hovering = false"
  >
    <div :id="container_id" class="videojs-container-plastic">
      <!-- <div
                  id="vid_uCXbR"
                  muted="true"
                  autoplay="true" preload="auto"
                  class="the-video video-js vjs-controls-enabled
                          vjs-workinghover vjs-v7 vjs-live vid_uCXbR-dimensions
                          vjs-has-started vjs-playing vjs-user-inactive vjs-waiting"
                  tabindex="-1" lang="en" role="region" aria-label="Video Player"
                  style="width: 100%; height: 100%; position: relative;">

                  <video muted controls autoplay
                    id="videojs-flvjs-player"
                    class="video-js vjs-default-skin vjs-big-play-centered"
                    width="1024" height="768"></video>

                  <full screen controls>

                </div>

                <video-controls>
                  <div class="right_title">云台控制</div>

                  <div class="button-group">
                    <div class="btn-circular">
                      <img class="btnTop"
                        id="controlCamera_up_${key}"
                        src="./src/components/Videojs/images/btnTop.png"
                        style="position: absolute;left: 17%;background: border-box;width: 66%;height: 33%"
                      />
                      <img class="btnLeft"
                        id="controlCamera_left_${key}"
                        src="./src/components/Videojs/images/btnleft.png"
                        style="position: absolute; left: 0;top: 16%;background: border-box;width: 33%;height: 66%;"
                      />
                      <img class="btnRight"
                        id="controlCamera_right_${key}"
                        src="./src/components/Videojs/images/btnRight.png"
                        style="position: absolute;top: 16%;left: 67%;background: border-box;width: 33%;height: 66%;"
                      />
                      <img class="btnBottom"
                        id="controlCamera_down_${key}"
                        src="./src/components/Videojs/images/btnBottom.png"
                        style="position: absolute;top: 66%;left: 17%;background: border-box;width: 66%;height: 33%"
                      />
                    </div>

                    <div class="btn-sub">
                      <button class="btn-one"
                        id="controlCamera_zoomin_${key}"
                        >+&nbsp;&nbsp;放大</button>
                      <button class="btn-one" style="margin-left:5%"
                        id="controlCamera_zoomout_${key}"
                        >-&nbsp;&nbsp;缩小</button>
                    </div>
                  </div>

                </video-controls>


            -->
      <img
        v-if="closeable && config.videoUrl"
        @click="close_vid"
        src="./images/close.png"
        class="close_button"
        alt=""
      />

      <img
        class="ddd cover-full"
        v-if="(!config.videoUrl && placeholder == 'choose') || playable"
        src="./images/placeholder.png"
        alt
      />

      <img
        class="ddd"
        v-if="config.videoUrl && loading && animation == 'ddd'"
        src="./images/ddd.gif"
        alt
      />
      <img
        class="ddd scanner"
        v-if="config.videoUrl && optionalAnimation == 'scanner' && scanning"
        src="./images/scanning1.gif"
        alt
      />
    </div>
  </div>
</template>

<script>
import axios from 'axios';
import { pushStreamByCode } from '@/api/supervision';

export default {
  name: 'Videojs',
  props: {
    config: {
      type: Object,
      default: function () {
        return {
          src: '',
          serviceIp: '',
          cameraUserName: 'admin',
          cameraPassword: 'admin123',
        };
      },
    },

    // src: String, //http://**********:11938/live/222.flv
    // serviceIp:{
    //   type: String,
    //   default: ""
    // },
    // cameraUserName:{
    //   type: String,
    //   default: "admin"
    // },
    // cameraPassword:{
    //   type: String,
    //   default: "admin123"
    // },

    placeholder: {
      type: String,
      default: '',
    },
    closeable: {
      type: Boolean,
      default: false,
    },
    optionalAnimation: {
      type: String,
      default: '',
    },

    small: {
      type: Boolean,
      default: true,
    },
    animation: {
      type: String,
      default: 'ddd',
    },
  },
  watch: {
    config: async function (to, from) {
      if (from.videoUrl == to.videoUrl) {
        return;
      }
      if (from.videoUrl) {
        this.dispose();
        this.scanning = false;
      }
      if (to) {
        if (to.rmDeviceEquipmentId && to.playFlag === 0) {
          const data = await pushStreamByCode({ code: to.rmDeviceEquipmentId });
          if (!data.record) {
            this.playable = true;
            this.$message.warning('视频播放失败');
          } else {
            this.playable = false;
            setTimeout(() => {
              this.setup_videojs();
            }, 500);
          }
        } else {
          this.playable = false;
          setTimeout(() => {
            this.setup_videojs();
          }, 500);
        }
      }
    },
  },
  data: function () {
    return {
      player: null,
      key: this.makeid(5),
      loading: false,

      fullscreen: false,

      // 控制->控制器 是否显示
      hovering: false,
      // testing:true,
      scanning: false,

      timeouts: [],
      fit: true,
      playable: false,
    };
  },
  mounted: async function () {
    // this.remove_titles = this.debounce(this.remove_titles,1000);

    if (this && this.config && this.config.videoUrl) {
      if (this.config.rmDeviceEquipmentId && this.config.playFlag === 0) {
        const data = await pushStreamByCode({ code: this.config.rmDeviceEquipmentId });
        if (!data.record) {
          this.playable = true;
          this.$message.warning('视频播放失败');
        } else {
          this.playable = false;
          setTimeout(() => {
            this.setup_videojs();
          }, 500);
        }
      } else {
        this.playable = false;
        setTimeout(() => {
          this.setup_videojs();
        }, 500);
      }
    }
  },
  beforeUnmount: function () {
    this.dispose();

    this.timeouts.forEach((el) => {
      clearTimeout(el);
    });

    // window["controlCamera_up_"+this.key].removeEventListener("click",this.ctrl_cam_up);
    // window["controlCamera_left_"+this.key].removeEventListener("click",this.ctrl_cam_left);
    // window["controlCamera_right_"+this.key].removeEventListener("click",this.ctrl_cam_right);
    // window["controlCamera_down_"+this.key].removeEventListener("click",this.ctrl_cam_down);
    // window["controlCamera_zoomin_"+this.key].removeEventListener("click",this.ctrl_cam_zoomin);
    // window["controlCamera_zoomout_"+this.key].removeEventListener("click",this.ctrl_cam_zoomout);
  },
  computed: {
    fullscreen_control() {
      // return this.fullscreen && this.serviceIp;
      var wtf = this.fullscreen && this.config.rmDeviceTypeId == 'BALLHEAD-CAMERA';
      // console.log("fullscreen result--",this.fullscreen,this.serviceIp,wtf);
      return wtf;
    },
    component_id: function () {
      return 'videojs-component_' + this.key;
    },
    container_id: function () {
      return 'video_container_' + this.key;
    },
    vid_wrapper_id: function () {
      return 'vid_' + this.key;
    },
    vid_id() {
      return this.vid_wrapper_id + '_flvjs_api';
    },
    vid_strech_id() {
      return 'stretch_' + this.key;
    },
  },
  methods: {
    // remove_titles(){
    //   console.log('removing titles---');

    //   var tar = window[this.component_id] || document;
    //   var buttons = tar.getElementsByClassName("vjs-button");
    //   for(var i=0;i<buttons.length;i++){
    //     buttons[i].title=""
    //   };
    // },
    close_vid() {
      this.$emit('close_vid');
    },
    dispose() {
      // console.log(this.player);
      if (!this.player) {
        return;
      }
      this.player.dispose();
    },
    double_clicked() {
      // console.log('double_clicked----',e);
      setTimeout(() => {
        this.click_decision();
      }, 1000);
    },
    click_decision(e) {
      // console.log('click_decision----');
      if (e) {
        var target = e.target.parentElement.getAttribute('class');
        if (target.includes('vjs-fullscreen-control') && !this.fullscreen) {
          this.setup_fullscreen();
        }
        // debugger
        if (e.target.playerId) {
          if (!(window[this.vid_id] && window[this.vid_id].play)) {
            return;
          }
          window[this.vid_id].play();
          // var name = this.config.equipmentName?this.config.equipmentName:"";
          // var message = window[this.vid_id].paused?name+"  停止播放":name+"  正在播放 ";
          // this.$Message.info(message);
        }
      } else {
        // debugger
        if (document.fullscreenElement && document.fullscreenElement.id == this.vid_wrapper_id) {
          this.setup_fullscreen();
        }
      }
    },
    setup_fullscreen() {
      this.fullscreen = true;
      this.$emit('fullscreen', true);
      document.addEventListener('fullscreenchange', this.fullscreen_listener);
    },
    fullscreen_listener() {
      if (!document.fullscreenElement) {
        this.fullscreen = false;
        this.$emit('fullscreen', false);
        document.removeEventListener('fullscreenchange', this.fullscreen_listener);
      }
    },
    setup_ctrl_listeners() {
      setTimeout(() => {
        if (
          !window['controlCamera_up_' + this.key] ||
          !window['controlCamera_up_' + this.key].addEventListener
        ) {
          return;
        }
        // console.log(this.key,window["controlCamera_up_"+this.key], window["controlCamera_up_"+this.key].addEventListener);
        window['controlCamera_up_' + this.key].addEventListener('click', this.ctrl_cam_up);
        window['controlCamera_left_' + this.key].addEventListener('click', this.ctrl_cam_left);
        window['controlCamera_right_' + this.key].addEventListener('click', this.ctrl_cam_right);
        window['controlCamera_down_' + this.key].addEventListener('click', this.ctrl_cam_down);
        window['controlCamera_zoomin_' + this.key].addEventListener('click', this.ctrl_cam_zoomin);
        window['controlCamera_zoomout_' + this.key].addEventListener(
          'click',
          this.ctrl_cam_zoomout,
        );
      }, 0);
    },
    ctrl_cam_up() {
      this.controlCamera('up');
    },
    ctrl_cam_left() {
      this.controlCamera('left');
    },
    ctrl_cam_right() {
      this.controlCamera('right');
    },
    ctrl_cam_down() {
      this.controlCamera('down');
    },
    ctrl_cam_zoomin() {
      this.controlCamera('zoomin');
    },
    ctrl_cam_zoomout() {
      this.controlCamera('zoomout');
    },

    setup_stretch() {
      console.log(this.component_id);
      var tar = document.createElement('button');
      tar.classList.add('stretch');
      // vidContent.setAttribute("autoplay","true");
      tar.id = this.vid_strech_id;
      tar.innerHTML = '标准';

      var vid = window[this.component_id];
      var ctrl = vid.getElementsByClassName('vjs-control-bar')[0];
      var last = ctrl.childNodes[ctrl.childNodes.length - 1];

      ctrl.insertBefore(tar, last);

      tar.addEventListener('click', () => {
        this.fit = !this.fit;
        // window[this.vid_strech_id].innerHTML
        if (this.fit) {
          tar.innerHTML = '标准';
        }
        if (!this.fit) {
          tar.innerHTML = '拉伸';
        }
      });
    },

    setup_videojs: function () {
      // var flvUrl = "https://mister-ben.github.io/videojs-flvjs/bbb.flv";
      // var flvUrl = "http://fed.dev.hzmantu.com/oa-project/bce0c613e364122715270faef1874251.flv";
      // <!-- <video muted controls autoplay id="videojs-flvjs-player" class="video-js vjs-default-skin vjs-big-play-centered"  width="1024" height="768"> </video> -->
      // console.log("here--",this.config);
      // debugger
      if (!this || !this.config || !this.config.videoUrl) {
        return;
      }

      var vidContent = document.createElement('video');
      vidContent.classList.add('the-video');
      vidContent.classList.add('video-js');
      vidContent.id = this.vid_wrapper_id;
      vidContent.setAttribute('preload', 'auto');
      vidContent.setAttribute('autoplay', 'true');
      vidContent.setAttribute('controls', 'true');
      vidContent.setAttribute('muted', 'true');
      this.loading = true;

      if (!window[this.container_id]) {
        return;
      }
      window[this.container_id].prepend(vidContent);

      setTimeout(() => {
        // 视频 宽高
        var target = document.getElementById(this.vid_wrapper_id);
        if (!target) {
          return;
        }
        target.style.width = '100%';
        target.style.height = '100%';
        target.style.position = 'relative';

        // var fullscreen_control_wrapper = document.createElement('div');
        // fullscreen_control_wrapper.classList.add('right_fixedfullScreenVideoWrapper');
        // fullscreen_control_wrapper.id = 'right_fixedfullScreenVideoWrapper_' + this.key;
        // fullscreen_control_wrapper.innerHTML = this.fullscreen_controls(this.key);
        //
        // var vid_wrapper = document.getElementById(this.vid_wrapper_id);
        // vid_wrapper.append(fullscreen_control_wrapper);

        this.setup_ctrl_listeners();

        this.setup_stretch();
      }, 0);

      // console.log("from videojs--", this.config, this.config.videoUrl);
      this.player = window.videojs(
        this.vid_wrapper_id,
        {
          techOrder: ['html5', 'flvjs'],
          flvjs: {
            mediaDataSource: {
              enableStashBuffer: true,
              isLive: true,
              cors: true,
              withCredentials: false,
              hasAudio: false,
              hasVideo: true,
              stashInitialSize: 100,
            },
          },
          sources: [
            {
              src: this.config.videoUrl,
              // type: 'video/x-flv',
              type: 'application/x-mpegURL',
            },
          ],
          controls: true,
          preload: true,
        },
        () => {
          // console.log('player ready onPlayerReady');
          // this.loading = false;
        },
      );
      this.player.one('timeupdate', () => {
        // console.log('player time updating???????????');
        this.loading = false;
        this.$emit('timeupdateone');
        this.scanning = true;
        var timeout = setTimeout(() => {
          this.$emit('timeoutfiveseconds');
          this.scanning = false;
        }, 1000 * 5);
        this.timeouts.push(timeout);
        // this.remove_titles();
      });
      // this.player.on('timeupdte',()=>{
      //     if(document.fullscreenElement){this.remove_titles();};
      // });
    },
    play() {
      this.player.load();
      this.player.play();
    },
    // reset(){
    //   this.player.videoUrl({
    //     src: this.config.videoUrl,
    //     type: 'video/x-flv'
    //   });
    // },
    controlCamera: function (dir) {
      if (!this.config) {
        return;
      }
      var ctrl = {
        serviceIp: this.config.serviceIp,
        cameraUserName: this.config.cameraUsername || this.config.cameraUserName,
        cameraPassword: this.config.cameraPassword,
        command: dir,
        speed: '0.15',
      };
      // console.log("..videojs config", this.config, this.config.cameraUsername, this.config.cameraPassword);
      axios.post('/remote/camera/moveCamera.do', ctrl);
    },

    makeid(length) {
      var result = '';
      var characters = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789';
      var charactersLength = characters.length;
      for (var i = 0; i < length; i++) {
        result += characters.charAt(Math.floor(Math.random() * charactersLength));
      }
      return result;
    },
    fullscreen_controls: function (key) {
      // <div class="right_fixedfullScreenVideoWrapper"
      //   id="right_fixedfullScreenVideoWrapper">
      return `
          <div class="right_title">云台控制</div>

          <div class="button-group">
            <div class="btn-circular">
              <img class="btnTop"
                id="controlCamera_up_${key}"
                src="./images/btnTop.png"
                style="position: absolute;left: 17%;background: border-box;width: 66%;height: 33%"
              />
              <img class="btnLeft"
                id="controlCamera_left_${key}"
                src="./images/btnleft.png"
                style="position: absolute; left: 0;top: 16%;background: border-box;width: 33%;height: 66%;"
              />
              <img class="btnRight"
                id="controlCamera_right_${key}"
                src="./images/btnRight.png"
                style="position: absolute;top: 16%;left: 67%;background: border-box;width: 33%;height: 66%;"
              />
              <img class="btnBottom"
                id="controlCamera_down_${key}"
                src="./images/btnBottom.png"
                style="position: absolute;top: 66%;left: 17%;background: border-box;width: 66%;height: 33%"
              />
            </div>

            <div class="btn-sub">
              <button class="btn-one"
                id="controlCamera_zoomin_${key}"
                >+&nbsp;&nbsp;放大</button>
              <button class="btn-one" style="margin-left:5%"
                id="controlCamera_zoomout_${key}"
                >-&nbsp;&nbsp;缩小</button>
            </div>
          </div>
      `;
      // </div>
    },
  },
};
</script>

<style lang="scss">
.videojs-component {
  width: 100%;
  height: 100%;
  position: relative;

  .vjs-fullscreen-control {
    outline: none;
    .vjs-control-text {
      display: none !important;
    }
  }

  .vjs-icon-placeholder:before {
    display: inline-flex;
    justify-content: center;
    align-items: center;
  }
  &.fullscreen_control {
    video,
    .vjs-control-bar {
      width: calc(100% - 24vw) !important;
    }
    .right_fixedfullScreenVideoWrapper {
      display: block !important;
      position: absolute;
      right: 0;
    }
  }
  .videojs-container-plastic {
    width: 100%;
    height: 100%;
    position: relative;
    .the-video.video-js {
      position: relative;
      display: flex;
      video {
        width: 100%;
        height: 100%;
      }
      .right_fixedfullScreenVideoWrapper {
        width: 24vw;
        height: 100%;
        background: #093655;
        display: none;
        .right_title {
          font-size: 18px;
          font-weight: 400;
          color: rgba(32, 194, 255, 1);
          line-height: 20px;
          padding-top: 2vh;
          padding-left: 1.5vw;
        }
        .button-group {
          position: relative;
          top: calc((100% - 30vh) / 2);
          text-align: center;
          .btn-circular {
            position: relative;
            width: 10.05vw;
            height: 10.05vw;
            margin: auto;

            .btnTop,
            .btnLeft,
            .btnBottom,
            .btnRight {
              border-right: 1px solid transparent;
              cursor: pointer;
            }
            .btnTop:active {
              filter: drop-shadow(0 0 0 #20c2ff);
            }
            .btnLeft:active {
              filter: drop-shadow(0 0 0 #20c2ff);
            }
            .btnRight:active {
              filter: drop-shadow(0 0 0 #20c2ff);
            }
            .btnBottom:active {
              filter: drop-shadow(0 0 0 #20c2ff);
            }
          }
          .btn-sub {
            display: inline-block;
            position: relative;
            width: 14vw;
            min-width: 153px;
            margin-top: 8%;
            .btn-one {
              width: 45%;
              cursor: pointer;
              height: 2.3vw;
              background: rgba(25, 157, 191, 0.2);
              border-radius: 2px;
              border: 1px solid rgba(32, 194, 255, 1);
              font-size: 14px;
              font-weight: 400;
              color: white;
              min-width: 55px;
              min-height: 25.3px;
              outline: none;
              &:active {
                filter: drop-shadow(0 0 0 #20c2ff);
              }
            }
          }
        }
      }
    }
  }
}
.videojs-component {
  .vjs-live-control.vjs-control,
  .vjs-live-display {
    background: unset;
  }
  .vjs-modal-dialog-content,
  .vjs-error-display,
  .vjs-modal-dialog {
    display: none;
  }
  .vjs-loading-spinner,
  .vjs-big-play-button,
  .vjs-text-track-display {
    display: none;
  }
  .vjs-volume-panel.vjs-control.vjs-volume-panel-horizontal {
    display: none;
  }
  .vjs-picture-in-picture-control.vjs-control.vjs-button {
    display: none;
  }
  .ddd {
    position: absolute;
    margin: auto;
    left: 0;
    right: 0;
    top: 0;
    bottom: 0;
    background-color: unset;
    max-height: 100%;
    max-width: 100%;
  }
  .cover-full {
    width: 100%;
    height: 100%;
  }

  .vjs-control-bar {
    .stretch {
      position: relative;
      text-align: center;
      margin: 0;
      padding: 0;
      height: 100%;
      width: 4em;
      cursor: pointer;
      flex: none;
      outline: none;
    }
  }

  .vjs-control-bar {
    opacity: 0 !important;
  }
  &:hover {
    .close_button {
      display: block;
    }
    .vjs-control-bar {
      opacity: 1 !important;
    }
  }
  .close_button {
    display: none;
    height: 23px;
    position: absolute;
    right: 3px;
    top: 3px;
    cursor: pointer;
  }
  .scanner {
    position: absolute;
    width: calc(100% - 6px * 2 - 8.3vw);
    // left: calc((6px * 2 + 8.3vw) / 2);
    // width: calc(100% - 6px * 2 - 8.3vw);
    // height: calc(100% - 5px * 2);
    // max-height: 100%;
    // max-width: 100%;
  }
  &.fit {
    video {
      object-fit: fill;
    }
  }
}
</style>
