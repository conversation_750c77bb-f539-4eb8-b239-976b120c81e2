<template>
  <div class="area-picker">
    <div class="area-picker-trigger" @click="showPicker = true">
      <span class="area-picker-label" :class="{ 'h-picker-placeholder': placeholder }">
        {{ currentLabel }}
      </span>
      <Icon name="arrow-down" />
    </div>
    <Popup v-model:show="showPicker" round position="bottom" teleport="body">
      <Cascader
        :value="cascaderValue"
        :title="props.title || '请选择地区'"
        :options="filterTopArea ? filterOptions(options) : options"
        active-color="#1989fa"
        @close="showPicker = false"
        @change="onChange"
        @finish="onFinish"
      />
    </Popup>
  </div>
</template>

<script setup>
import { Popup, Icon, Cascader } from 'vant';
import { computed, ref } from 'vue';
import { useStore } from 'vuex';
import { getNode } from '@/utils/collection';

const emits = defineEmits(['update:value']);

const props = defineProps({
  title: String,
  value: [String, Number],
  allAreaSelect: Boolean,
  topAreaCode: [String, Number],
  placeholder: String,
  filterTopArea: Boolean,
});

const store = useStore();

const showPicker = ref(false);

const filterOptions = (children) => {
  const filterIsTop = (children) => {
    return children.filter((i) => {
      if (i.children) {
        i.children = filterOptions(i.children);
      }

      return !i.value.endsWith('01');
    });
  };
  return filterIsTop(children);
};

const options = computed(() => {
  const areaStore = store.state.area;
  let areaTree = props.allAreaSelect ? areaStore.areaTreeWidthAllArea : areaStore.areaTree;
  if (props.topAreaCode) {
    const userAreaNode = getNode(areaTree, (it) => it.value === props.topAreaCode);
    areaTree = userAreaNode ? [userAreaNode] : [];
  }
  return areaTree;
});

const areaMap = computed(() => {
  return store.state.area.areaMap;
});

const currentLabel = computed(() => {
  const { value, placeholder = '请选择' } = props;
  const currentNode = areaMap.value.get(value);
  return currentNode?.text || placeholder;
});

const placeholder = computed(() => {
  return !areaMap.value.has(props.value);
});

const cascaderValue = computed(() => {
  return props.value;
});

const onChange = () => {
  //
};

const onFinish = ({ value }) => {
  showPicker.value = false;
  emits('update:value', value);
};
</script>

<style lang="scss">
.area-picker {
  background-color: #ffffff;
  line-height: 32px;

  &-trigger {
    border: 1px solid var(--van-gray-5);
    padding: 0 10px;
    border-radius: 4px;
    display: flex;
    align-items: center;
    justify-content: space-between;
  }

  &-label {
    margin-right: 4px;
    overflow: hidden;
    white-space: nowrap;
  }
}
</style>
