<template>
  <span @click="handleScan" class="text-white">
    设备详情 <van-icon class="ml-1" name="scan" />
  </span>
</template>

<script setup>
import { useRouter } from 'vue-router';
const router = useRouter();

const handleScan = () => {
  window.cordova?.plugins.barcodeScanner.scan(
    (result) => {
      if (result.text) {
        router.push(`/safety-production-management/device-detail?id=${result.text}`);
      }
    },
    (error) => {
      alert('扫码失败 ' + error);
    },
    {
      prompt: '请将二维码放在扫码框中',
      resultDisplayDuration: 300,
    },
  );
};
</script>

<style lang="scss" scoped></style>
