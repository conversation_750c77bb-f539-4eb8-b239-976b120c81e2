<template>
  <div class="h-field">
    <div class="label" v-bind:class="{ 'field-required': required }">{{ label }}</div>
    <slot></slot>
  </div>
</template>

<script>
export default {
  name: '<PERSON><PERSON>ield',
  props: {
    label: {
      type: String,
      default: () => '',
    },
    required: {
      type: Boolean,
      default: () => false,
    },
  },
  data() {
    return {};
  },
};
</script>

<style lang="scss" scoped>
.h-field {
  position: relative;
  box-sizing: border-box;
  width: 100%;
  padding: var(--van-cell-vertical-padding) var(--van-cell-horizontal-padding);
  overflow: hidden;
  color: var(--van-cell-text-color);
  font-size: var(--van-cell-font-size);
  line-height: var(--van-cell-line-height);
  background: var(--van-background-color-light);
  &::after {
    position: absolute;
    box-sizing: border-box;
    content: ' ';
    pointer-events: none;
    right: var(--van-padding-md);
    bottom: 0;
    left: var(--van-padding-md);
    border-bottom: 1px solid var(--van-cell-border-color);
    transform: scaleY(0.5);
  }
  .label {
    font-size: var(--van-cell-font-size);
    color: var(--van-field-label-color);
    line-height: var(--van-cell-line-height);
  }
  .field-required {
    &::before {
      margin-right: 2px;
      color: var(--van-field-required-mark-color);
      content: '*';
    }
  }
}
</style>
