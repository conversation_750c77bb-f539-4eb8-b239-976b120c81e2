<script>
import HFixedNumber from '@/components/HFixedNumber/HFixedNumber';
import AreaPicker from '@/views/common/AreaPicker';

export default {
  name: 'HouseCapacityDistribution',
  components: {
    AreaPicker,
    HFixedNumber,
  },
};
</script>

<script setup>
import { reactive, watch } from 'vue';
import { getHouseCapacityDistribution } from '@/api/house-capacity-distribution';

const numberVarMap = {
  one: '储备',
  two: '收纳',
  three: '中转',
  four: '储备兼收纳',
  five: '其他',
};

const state = reactive({
  queryForm: {
    areaCode: '',
  },
  dataList: [],
});

const getHouseCapacityList = async (code) => {
  const queryData = {
    provinceCode: '',
    cityCode: '',
    areaCode: '',
  };
  if (code.endsWith('0000')) {
    queryData.provinceCode = code;
  } else if (code.endsWith('00')) {
    queryData.cityCode = code;
  } else {
    queryData.areaCode = code;
  }
  const res = await getHouseCapacityDistribution(queryData);
  state.dataList = res;
  console.log('res', res);
};

const switchHouseDetailsVisible = (item) => {
  item.detailVisible = !item.detailVisible;
  console.log('item', item);
};

watch(
  () => state?.queryForm?.areaCode,
  (val) => {
    console.log('state.queryForm.areaCode', val);
    getHouseCapacityList(val);
  },
);
</script>

<template>
  <div>
    <div class="p-2 mb-2">
      <AreaPicker
        v-model:value="state.queryForm.areaCode"
        all-area-select
        filter-top-area
        placeholder="请选择地区"
      />
    </div>
    <div class="grid gap-3">
      <div v-for="(item, idx) in state.dataList" class="p-4 bg-white card-content h-fit" :key="idx">
        <div class="mb-2 font-bold">{{ item.name }}</div>
        <div class="text-[15px]">
          <div>
            <van-icon
              class="ml-2"
              v-if="item.detailVisible"
              name="arrow-up"
              @click="switchHouseDetailsVisible(item)"
            />
            <van-icon
              class="ml-2"
              v-else
              name="arrow-down"
              @click="switchHouseDetailsVisible(item)"
            />
            <span class="ml-1 text-[#909090]">仓容统计：</span>
            <span>
              <HFixedNumber :fraction-digits="3">
                {{ item.storehouseCapacity }}
              </HFixedNumber>
              吨
            </span>
          </div>
          <van-form label-width="76" label-align="right" colon>
            <van-cell-group inset>
              <van-field
                v-for="([key, value], i) in item.detailVisible ? Object.entries(numberVarMap) : []"
                :key="i"
                :name="value"
                :label="value"
              >
                <template #input>
                  <HFixedNumber :fraction-digits="3">
                    {{ item[key] }}
                  </HFixedNumber>
                  <span>&nbsp;吨</span>
                </template>
              </van-field>
            </van-cell-group>
          </van-form>
        </div>
        <div class="text-[15px] mt-[2px]">
          <span class="ml-7 text-[#909090]">罐容统计：</span>
          <span>
            <HFixedNumber :fraction-digits="3">
              {{ item.tankCapacity }}
            </HFixedNumber>
            吨
          </span>
        </div>
      </div>
    </div>
  </div>
</template>

<style>
.card-content {
  box-shadow: 0 0 4px 0.1px rgba(0, 0, 0, 0.1);
  word-wrap: break-word;
}
.van-cell {
  padding: 2px 0;
}
.van-cell::after {
  display: none;
}

label {
  color: #909090;
}
</style>
