// import { advancewarning } from '@/utils/request';
import { reserveRequest } from '@/utils/request';
import { hxdiframeworkMobeilRequest } from '@/utils/request';

// import axios from 'axios';
export async function getWarninglog(params) {
  return hxdiframeworkMobeilRequest().post('/api/grainJobWarnLog/info', params, {
    Headers: {
      withCredentials: {},
    },
  });
}

export async function addWarninglog(params) {
  return hxdiframeworkMobeilRequest().post('/api/grainJobWarnLog/journal/alarms', params, {
    Headers: {
      withCredentials: {},
    },
  });
}
export async function getObtainWarehouse() {
  return reserveRequest().get('/api/storeHouse/getCurrentEnabledStoreHouse');
}
// export async function getWarninglog(params) {
//   return axios.post('/hxdiframework/api/grainJobWarnLog/info', params, {
//     Headers: {
//       withCredentials: {},
//     },
//   });
// }

// export async function addWarninglog(params) {
//   return axios.post('/hxdiframework/api/grainJobWarnLog/journal/alarms', params, {
//     Headers: {
//       withCredentials: {},
//     },
//   });
// }
// export async function getObtainWarehouse() {
//   return reserveRequest().get('/api/storeHouse/getCurrentEnabledStoreHouse');
// }
