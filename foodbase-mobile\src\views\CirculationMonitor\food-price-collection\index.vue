<template>
  <div class="circulation-monitor">
    <div class="basic-info">
      <div class="title mb-5">基础信息</div>
      <div class="basic-item">
        <span class="basic-item-label">监测点：</span>
        <span class="basic-item-value">{{ monitorData.monitorPointName || '-' }}</span>
      </div>
      <div class="basic-item">
        <span class="basic-item-label">监测点地区：</span>
        <span class="basic-item-value">{{ monitorData.area || '-' }}</span>
      </div>
      <div class="basic-item">
        <span class="basic-item-label">上报日期：</span>
        <span class="basic-item-value">{{ monitorData.monitorTime || '-' }}</span>
      </div>
      <div class="basic-item" v-if="!actionDisabled">
        <Field
            v-model="monitorData.remark"
            type="textarea"
            name="remark"
            placeholder="请输入备注"
            label="备注："
            :disabled="actionDisabled"
            maxlength="256"
          >
          </Field>
      </div>
      <div class="basic-item" v-else>
        <span class="basic-item-label">备注：</span>
        <span class="basic-item-value">{{ monitorData.remark || '-' }}</span>
      </div>
    </div>
    <div class="price-info" v-for="(item, index) in priceInfoData" :key="index">
      <div class="price-info-title">
        <strong>{{ item.foodCategoryName }}</strong>
        <Tag :color="grainTypeMap[Number(item.foodCategoryType)]?.color" size="medium">{{
          grainTypeMap[Number(item.foodCategoryType)]?.label
        }}</Tag>
      </div>
      <Form :model="item">
        <CellGroup inset style="margin: 0">
          <Field
            v-model="item.levelName"
            required
            name="levelName"
            label="等级名称"
            placeholder="等级名称"
            :rules="[{ required: true, message: '请填写等级名称' }]"
            @click="handleShowLevelPicker(item, index)"
            :disabled="item.state === 2"
          />
          <Field
            v-if="item.foodCategoryType === '1'"
            required
            v-model="item.purchasePrice"
            name="purchasePrice"
            label="收购价"
            placeholder="收购价"
            :rules="[{ required: true, message: '请填写收购价' }]"
            :disabled="item.state === 2"
          >
            <template #right-icon>
              <span>元/吨</span>
            </template>
          </Field>
          <Field
            v-if="item.foodCategoryType === '1'"
            required
            v-model="item.salePrice"
            name="salePrice"
            label="出库价"
            placeholder="出库价"
            :rules="[{ required: true, message: '请填写出库价' }]"
            :disabled="item.state === 2"
          >
            <template #right-icon>
              <span>元/吨</span>
            </template>
          </Field>
          <Field
            v-if="item.foodCategoryType !== '1'"
            required
            v-model="item.exitPrice"
            name="exitPrice"
            label="出厂价"
            placeholder="出厂价"
            :rules="[{ required: true, message: '请填写出厂价' }]"
            :disabled="item.state === 2"
          >
            <template #right-icon>
              <span>{{ item.foodCategoryType === '2' ? '元/公斤' : '元/5L' }}</span>
            </template>
          </Field>
          <Field
            v-if="item.foodCategoryType !== '1'"
            required
            v-model="item.wholesalePrice"
            name="wholesalePrice"
            label="批发价"
            placeholder="批发价"
            :rules="[{ required: true, message: '请填写批发价' }]"
            :disabled="item.state === 2"
          >
            <template #right-icon>
              <span>{{ item.foodCategoryType === '2' ? '元/公斤' : '元/5L' }}</span>
            </template>
          </Field>
          <Field
            v-if="item.foodCategoryType !== '1'"
            required
            v-model="item.retailPrice"
            name="retailPrice"
            label="零售价"
            placeholder="零售价"
            :rules="[{ required: true, message: '请填写零售价' }]"
            :disabled="item.state === 2"
          >
            <template #right-icon>
              <span>{{ item.foodCategoryType === '2' ? '元/公斤' : '元/5L' }}</span>
            </template>
          </Field>
          <Field
            v-model="item.brand"
            :required="item.foodCategoryType !== '1' ? true : false"
            name="brand"
            label="品牌"
            placeholder="品牌"
            :rules="[
              item.foodCategoryType !== '1' ? { required: true, message: '请填写品牌' } : {},
            ]"
            :disabled="item.state === 2"
          />
          <Field
            v-model="item.originName"
            :required="item.foodCategoryType !== '1' ? true : false"
            name="originName"
            label="产地"
            placeholder="产地"
            :rules="[
              item.foodCategoryType !== '1' ? { required: true, message: '请填写产地' } : {},
            ]"
            :disabled="item.state === 2"
            @click="handleShowOriginPicker(item, index)"
          />
        </CellGroup>
      </Form>
    </div>
    <div class="bottom-warpper" v-if="!actionDisabled">
      <Button class="button submit-button" plain type="primary" @click="handleDeal('submit')">
        提交
      </Button>
      <Button class="button next-button" type="primary" @click="handleAction('save')">
        保存
      </Button>
    </div>
    <ActionSheet v-model:show="showLevelPicker" title="等级">
      <BizDictPicker
        dict="GRAIN_AND_OIL_QUALITY_INSPECTION_GRADE"
        title="粮油等级"
        :hiddenValues="hiddenLevel"
        @cancel="showLevelPicker = false"
        @confirm="onLevelConfirm"
        :defaultValue="defaultLevelValue"
      ></BizDictPicker>
    </ActionSheet>
    <ActionSheet v-model:show="showOriginPicker" title="产地">
      <Cascader
        :options="originPlaces"
        :field-names="fieldNames"
        :closeable="false"
        @close="showOriginPicker = false"
        @change="onOriginConfirm"
        v-model="selectedValue"
      ></Cascader>
    </ActionSheet>
  </div>
</template>
<script setup>
import { ref, reactive, onMounted, computed } from 'vue';
import { useStore } from 'vuex';
import dayjs from 'dayjs';
import { debounce } from 'lodash';
import {
  getUserWatchInfo,
  handleAddPriceBatch,
  handleSubmitPriceBatch,
  handleUpdatePriceBatch,
} from '@/api/circulation-monitor';
import { BizDictPicker } from '@/views/InoutManage/common';
import { Form, Field, Tag, Cascader, Dialog, Toast, Button, ActionSheet, CellGroup } from 'vant';
const store = useStore();

const originPlaces = store.state['origin-place'].tree;

const selectedValue = ref();

const grainTypeMap = ref({
  1: {
    label: '原粮',
    color: '#317cf4',
  },
  2: {
    label: '成品粮',
    color: '#27bb8a',
  },
  3: {
    label: '食用油及油料',
    color: '#f2826a',
  },
});

const hiddenLevel = ref([]);

const defaultLevelValue = ref();

const monitorData = reactive({
  monitorPointName: '',
  areaCode: '',
  monitorTime: null,
});

const priceInfoData = ref();

const pointUserInfo = ref({});

const showOriginPicker = ref(false);
const showLevelPicker = ref(false);

const currentRow = ref();
const currentIndex = ref();

const actionDisabled = ref();

const fieldNames = {
  text: 'label',
  value: 'value',
  children: 'children',
};

const handleShowLevelPicker = (item, index) => {
  if (item.state === 2) {
    return;
  }
  showLevelPicker.value = true;
  currentRow.value = item;
  currentIndex.value = index;
  if (item.foodCategoryType !== '3') {
    hiddenLevel.value = categoryLevels.value
      ?.filter((i) => i.value > 6)
      ?.map((i) => {
        return i.value;
      });
  } else {
    hiddenLevel.value = categoryLevels.value
      ?.filter((i) => i.value <= 6)
      ?.map((i) => {
        return i.value;
      });
  }
  defaultLevelValue.value = item.level;
};

const onLevelConfirm = (value) => {
  priceInfoData.value[currentIndex.value].level = value.value;
  priceInfoData.value[currentIndex.value].levelName = value.label;
  showLevelPicker.value = false;
};

const handleShowOriginPicker = (item, index) => {
  if (item.state === 2) {
    return;
  }
  showOriginPicker.value = true;
  currentIndex.value = index;
  selectedValue.value = Number(item.origin);
};

const onOriginConfirm = ({ selectedOptions: list }) => {
  if (list.length === 0) {
    priceInfoData.value[currentIndex.value].origin = '';
    priceInfoData.value[currentIndex.value].originName = '';
    return;
  }
  const item = list[list.length - 1];
  priceInfoData.value[currentIndex.value].origin = item.value;
  priceInfoData.value[currentIndex.value].originName = item.label;
  if (!item.children) {
    showOriginPicker.value = false;
  }
};
const initAddInfo = async () => {
  let data = await getUserWatchInfo();
  pointUserInfo.value = data.id ? data : null;
  if (!data.isCurrent) {
    data.categoryList = data.categoryList.map((i) => {
      return {
        ...i,
        id: null,
        monitorTime: null,
        state: null,
      };
    });
  }
  priceInfoData.value = data?.categoryList?.map((i) => {
    if (i.foodCategoryType === 1) {
      i.purchasePrice = i.purchasePrice ? i.purchasePrice * 1000 : '';
      i.salePrice = i.salePrice ? i.salePrice * 1000 : '';
    }
    i.foodCategoryType = String(i.foodCategoryType);
    i.levelName = categoryLevels.value?.find((m) => m.value === i.level)?.label;
    return i;
  });
  monitorData.monitorPointId = data.id;
  monitorData.monitorPointName = data.name;
  monitorData.monitorTime =
    data?.categoryList?.[0]?.monitorTime?.split(' ')?.[0] || dayjs().format('YYYY-MM-DD');
  monitorData.area = (data?.provinceName || '') + (data?.cityName || '') + (data?.countyName || '');
  monitorData.remark = data?.categoryList?.[0]?.remark || null
  const copy = JSON.parse(JSON.stringify(priceInfoData.value));
  actionDisabled.value = copy.filter((i) => i.state === 2)?.length === copy.length;
};

const handleSave = async (data) => {
  const addList = data.filter((i) => !i.id);
  const editList = data.filter((i) => i.id);
  if (addList?.length > 0) {
    await handleAddPriceBatch(addList);
    await initAddInfo();
  }
  if (editList?.length > 0) {
    await handleUpdatePriceBatch(editList);
  }
};

const handleSubmit = async (data) => {
  const submitList = data
    .filter((i) => i.id && i.state !== 2)
    .map((m) => {
      return m.id;
    });
  await handleSubmitPriceBatch(submitList);
};

const handleDeal = (type) => {
  Dialog.confirm({
    title: '提示',
    message: '上报数据将不允许修改，是否确认上报',
  })
    .then(() => {
      handleAction(type);
    })
    .catch(() => {});
};

const handleAction = debounce((type) => {
  const fn = async (type) => {
    priceInfoData.value.forEach((i) => {
      const priceReg = /^(([1-9][0-9]*)|(([0]\.\d{1,2}|[1-9][0-9]*\.\d{1,2})))$/;
      if (!i.foodCategoryType) {
        Toast.fail('粮油品种类型不能为空');
        throw new Error('粮油品种类型不能为空');
      }
      if (!i.foodCategoryId) {
        Toast.fail('品种名称不能为空');
        throw new Error('品种名称不能为空');
      }
      if (!i.level) {
        Toast.fail('等级名称不能为空');
        throw new Error('等级名称不能为空');
      }
      if (i.foodCategoryType !== '1' && !i.origin) {
        Toast.fail('成品粮或食用油及油料下的子品种产地不能为空');
        throw new Error('成品粮或食用油及油料下的子品种产地不能为空');
      }
      if (i.foodCategoryType !== '1' && !i.brand) {
        Toast.fail('成品粮或食用油及油料下的子品种品牌不能为空');
        throw new Error('成品粮或食用油及油料下的子品种品牌不能为空');
      }
      const purchasePriceInvalid = i.purchasePrice && !String(i.purchasePrice).match(priceReg);
      const salePriceInvalid = i.salePrice && !String(i.salePrice).match(priceReg);
      const exitPriceInvalid = i.exitPrice && !String(i.exitPrice).match(priceReg);
      const retailPriceInvalid = i.retailPrice && !String(i.retailPrice).match(priceReg);
      const wholesalePriceInvalid = i.wholesalePrice && !String(i.wholesalePrice).match(priceReg);

      if (i.foodCategoryType === '1' && (!i.purchasePrice || !i.salePrice)) {
        Toast.fail('价格不能为空');
        throw new Error('价格不能为空');
      } else if (
        i.foodCategoryType !== '1' &&
        (!i.exitPrice || !i.retailPrice || !i.wholesalePrice)
      ) {
        Toast.fail('价格不能为空');
        throw new Error('价格不能为空');
      }

      if (
        purchasePriceInvalid ||
        salePriceInvalid ||
        exitPriceInvalid ||
        retailPriceInvalid ||
        wholesalePriceInvalid
      ) {
        Toast.fail('价格必须为正数,小数点后最多2位');
        throw new Error('价格必须为正数,小数点后最多2位');
      }
    });
    const copyPriceInfoData = JSON.parse(JSON.stringify(priceInfoData.value));
    const data = copyPriceInfoData.map((i) => {
      if (i.foodCategoryType === '1') {
        i.purchasePrice = (i.purchasePrice / 1000).toFixed(3);
        i.salePrice = (i.salePrice / 1000).toFixed(3);
      }
      return {
        ...i,
        ...monitorData,
        monitorTime: priceInfoData.value?.[0].monitorTime || dayjs().format('YYYY-MM-DD HH:mm:ss'),
        foodCategoryType: i.foodCategoryType[0],
        foodCategoryId:
          typeof i.foodCategoryId === 'object' ? i.foodCategoryId[0] : i.foodCategoryId,
      };
    });
    if (type === 'save') {
      await handleSave(data);
      initAddInfo();
      Toast.success('保存成功');
    }
    if (type === 'submit') {
      await handleSave(data);
      await handleSubmit(priceInfoData.value);
      initAddInfo();
      Toast.success('提交成功');
    }
  };
  fn(type);
}, 500);

const categoryLevels = computed(() => {
  return store.getters['dict/reserveDictOf']('GRAIN_AND_OIL_QUALITY_INSPECTION_GRADE');
});

onMounted(async () => {
  initAddInfo();
});
</script>

<style lang="scss" scoped>
.basic-item {
  ::v-deep {
    .van-cell__title{
      color: #888;
      font-size:16px;
      width:90px;
    }
    .van-cell{
      padding:0 !important;
      margin:0 !important;
    }
  }
}

.basic-info {
  background: #fff;
  padding: 10px;
  margin: 10px 0;

  .title {
    position: relative;
    padding-left: 12px; /* 左侧留出竖线的空间 */
  }

  .title::before {
    content: '';
    position: absolute;
    left: 0;
    top: 50%; /* 竖线垂直居中 */
    transform: translateY(-50%);
    height: 80%; /* 竖线高度与容器高度相同 */
    width: 5px; /* 竖线宽度 */
    background: #23b49c; /* 竖线颜色 */
  }

  .basic-item {
    display: flex;
    margin-bottom: 5px;
    .basic-item-label {
      min-width: 100px !important;
      color: #888;
    }
  }
}

.price-info {
  background: #fff;
  padding: 10px;
  margin-bottom: 10px;
  .price-info-title {
    display: flex;
    justify-content: space-between;
  }
}
.bottom-warpper {
  background: #ffffff;
  display: flex;
  justify-content: space-around;
  padding: 10px 5px;

  .button {
    width: 48%;
  }
}
</style>
