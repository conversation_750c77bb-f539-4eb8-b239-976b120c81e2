<template>
  <Dialog
    class="scheduling-ticket"
    :show-cancel-button="true"
    confirm-button-text="保存至手机"
    confirm-button-color="#1677ff"
    @confirm="saveTicket"
    v-bind="$attrs"
  >
    <template #default>
      <div v-for="(item, index) in records" :key="index">
        <div class="header">
          <span class="time">{{ item.time }}</span>
        </div>
        <div class="content">
          <div class="item">
            <span class="label">序号:</span>
            <span class="value">{{ item.serialNo }}</span>
          </div>
          <div class="item">
            <span class="label">值仓仓房:</span>
            <span class="value">{{
              jobSchedulingDetailDtoList[showQrcodeIndex]?.storeHouseName
            }}</span>
          </div>

          <div class="item">
            <span class="label">调度号:</span>
            <span class="value">
              <short-schedule-no>{{ item.schedulingNo }}</short-schedule-no>
            </span>
          </div>
          <div class="item">
            <span class="label">客户名称:</span>
            <span class="value">{{ item.customer }}</span>
          </div>
          <div class="item">
            <span class="label">车船号:</span>
            <span class="value">{{
              jobSchedulingDetailDtoList[showQrcodeIndex]?.transportVehicleNo
            }}</span>
          </div>
          <div class="item">
            <span class="label">业务类型:</span>
            <span class="value">{{ item.buzTypeName }}</span>
          </div>
        </div>
        <div class="qrcode">
          <h-qr-code ref="qrCode" :value="item.schedulingNo"></h-qr-code>
          <div class="remark">请截图或将图片保存至手机</div>
        </div>
      </div>
    </template>
  </Dialog>
</template>

<script>
import { Dialog } from 'vant';
import { ShortScheduleNo } from '@/views/InoutManage/common';

export default {
  name: 'SchedulingTicket',
  props: {
    records: {
      type: Array,
      default: () => [],
    },
    jobSchedulingDetailDtoList: {
      type: Array,
      default: () => [],
    },
    showQrcodeIndex: {
      type: Number,
      default: 0,
    },
  },
  data() {
    return {};
  },
  components: {
    Dialog: Dialog.Component,
    ShortScheduleNo,
  },
  methods: {
    saveTicket() {
      this.records.forEach((item, index) => {
        this.$refs.qrCode[index].saveImage();
      });
    },
  },
};
</script>

<style lang="scss" scoped>
.scheduling-ticket {
  background: transparent;
  overflow: auto;
  .header {
    width: 100%;
    height: 45px;
    background: linear-gradient(180deg, #e3ecf5 0%, #f3f7fb 100%);
    font-size: 16px;
    font-weight: 500;
    color: #272727;
    line-height: 45px;
    .time {
      margin-left: 20px;
    }
  }
  .content {
    width: 100%;
    display: flex;
    flex-wrap: wrap;
    border-radius: 0px 0px 12px 12px;
    background: #ffffff;
    .item {
      width: calc(50% - 20px);
      //height: 30px;
      font-size: 14px;
      font-weight: 400;
      line-height: 30px;
      margin-left: 20px;
      .label {
        color: #686b73;
      }
      .value {
        color: #272727;
      }
    }
  }
  .qrcode {
    width: 100%;
    height: 245px;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    border-radius: 12px 12px 0px 0px;
    background: #ffffff;
    .h-qr-code {
      width: 160px;
      height: 160px;
    }
    .remark {
      font-size: 16px;
      font-weight: 500;
      color: #333333;
      line-height: 24px;
      margin-top: 20px;
    }
  }
}
</style>
