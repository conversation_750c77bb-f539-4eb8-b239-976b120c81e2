<template>
  <div class="supervision-storehouse">
    <div class="title">清查事项：</div>
    <table v-for="item in props.data" :key="item.storehouseName">
      <tbody>
        <tr>
          <td>
            <div class="storehouse-name">
              {{ item.storehouseName }}
            </div>
          </td>
          <td class="check-result">储备库存 {{ item.stockSum }} 吨</td>
        </tr>
        <tr>
          <td class="item-name">数量清查：</td>
          <td class="check-result">
            <template v-if="item.quantityCheck === 1">
              达标<Icon class="checked" name="checked" />
            </template>
            <template v-else-if="item.quantityCheck === 2">
              不达标<Icon class="warning" name="warning" />
            </template>
            <template v-else> - </template>
          </td>
        </tr>
        <tr>
          <td class="item-name">质量清查：</td>
          <td class="check-result">
            <template v-if="item.qualityCheck === 1">
              合格<Icon class="checked" name="checked" />
            </template>
            <template v-else-if="item.qualityCheck === 2">
              不合格<Icon class="warning" name="warning" />
            </template>
            <template v-else> - </template>
          </td>
        </tr>
        <tr>
          <td class="item-name">安全清查：</td>
          <td class="check-result">
            <template v-if="item.securityCheck === 1">
              正常<Icon class="checked" name="checked" />
            </template>
            <template v-else-if="item.securityCheck === 2">
              异常<Icon class="warning" name="warning" />
            </template>
            <template v-else> - </template>
          </td>
        </tr>
        <tr>
          <td class="item-name">异常详情：</td>
          <td class="check-result">
            <template v-if="item.warnDetail">
              {{ item.warnDetail }}
            </template>
            <template v-else> - </template>
          </td>
        </tr>
      </tbody>
    </table>
  </div>
</template>

<script setup>
import { Icon } from 'vant';

const props = defineProps({
  data: {
    type: Array,
    default: () => [],
  },
});
</script>

<style scoped lang="scss">
.supervision-storehouse {
  padding: 16px;
  font-size: 18px;

  .title {
    font-weight: 500;
    color: var(--van-gray-8);
    line-height: 25px;
  }

  table {
    width: 100%;
    border-collapse: collapse;
    color: var(--van-gray-7);
    margin-top: 16px;

    tr {
      border: 1px solid var(--van-gray-5);
    }
    td {
      line-height: 22px;
      padding: 8px 16px;

      &.store-name {
        font-size: 16px;
        padding: 8px;
        white-space: nowrap;
        text-overflow: ellipsis;
        overflow: hidden;
      }
    }
    .item-name {
      //
    }
    .check-result {
      text-align: right;
    }
  }

  .storehouse-name {
    display: flex;
    color: var(--van-gray-8);
    font-weight: bold;
  }

  .van-icon-arrow {
    margin-left: auto;
    line-height: 22px;
  }

  .link {
    color: #1492ff;
  }

  .not-access {
    margin-left: auto;
    color: #aaa;
  }

  .checked {
    margin-left: 10px;
    color: var(--van-green);
  }
  .warning {
    margin-left: 10px;
    color: var(--van-orange);
  }
}
</style>
