import { reserveRequest } from '@/utils/request';

const getData = async (value) => {
  return await reserveRequest().get('/api/lawSupervision/inputTestResult/page', {
    params: {
      page: 1,
      size: 50,
      quarter: value,
      isWeb: 0,
    },
  });
};
const getDetail = async (params) => {
  return await reserveRequest().get('/api/lawSupervision/inputTestResult/findById', {
    params,
  });
};
const saveData = async (data) => {
  return await reserveRequest().post('/api/lawSupervision/inputTestResult/enter', data);
};

const submitSupervisedcheck = async (id) => {
  return await reserveRequest().post(`/api/lawSupervision/inputTestResult/completeCheck?id=${id}`);
};

export { getData, getDetail, saveData, submitSupervisedcheck };
