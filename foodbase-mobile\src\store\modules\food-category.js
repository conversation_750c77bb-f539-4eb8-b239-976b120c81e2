import { getFoodCategory } from '@/api/in-out-manage';
import { keyBy, forEach } from 'lodash-es';

function generateFoodCategoryTree(areaList) {
  const neatList = areaList.map((it) => {
    return {
      id: it.id,
      pid: it.pid,
      name: it.name,
      key: it.id,
      value: it.id,
      label: it.name,
      title: it.name,
      categoryType: it.categoryType,
    };
  });
  const foodMap = keyBy(neatList, 'id');
  const tree = [];
  forEach(foodMap, (food) => {
    const { pid } = food;
    delete food.pid;
    if (pid !== 0) {
      const parent = foodMap[pid];
      if (parent) {
        if (!parent.children) {
          parent.children = [];
        }
        parent.children.push(food);
        return;
      }
    }
    tree.push(food);
  });
  return tree;
}

export default {
  namespaced: true,
  state: {
    tree: [],
    list: [],
  },
  getters: {
    secondaryCategories: (state) => {
      console.warn('已弃用的 getter，直接使用 FoodCategorySelect');
      return state.list.map((it) => {
        return {
          id: it.id,
          name: it.name,
          value: it.id,
          label: it.name,
          title: it.name,
          key: it.id,
        };
      });
    },
    oilCategoryIds: (state) => {
      return state.list.filter((it) => it.categoryType === 3).map((it) => it.id);
    },
    grainCategoryIds: (state) => {
      // 粮列表
      return state.list.filter((it) => it.categoryType !== 3).map((it) => it.id);
    },
    foodCategoryMap: (state) => {
      return keyBy(state.list, 'id');
    },
  },
  mutations: {
    setTree(state, tree) {
      state.tree = tree;
    },
    setList(state, list) {
      state.list = list;
    },
  },
  actions: {
    async load({ state, commit }, forceReload = false) {
      if (state.tree.length > 0 && !forceReload) {
        return;
      }
      const foodCategory = await getFoodCategory();
      commit('setList', foodCategory);
      commit('setTree', generateFoodCategoryTree(foodCategory));
    },
  },
};
