<template>
  <div class="order-public-detail">
    <PullRefresh @refresh="onRefresh" v-model="refreshLoading">
      <List
        ref="listRef"
        v-model:loading="isLoading"
        :finished="finished"
        finished-text="没有更多了"
        @load="getList"
      >
        <EmptyHolder v-if="list.length === 0 && finished" />
        <div v-else>
          <HCard class="detail-card" v-for="(item, index) in list" :key="index">
            <div class="content">
              <div class="summary">
                <div class="item">
                  <SvgIcon name="farmer" />
                  <span>农户：</span>
                  <span>{{ item.farmerName }}</span>
                </div>
                <div class="item">
                  <SvgIcon name="fields" />
                  <span>农业核定面积（亩）：</span>
                  <HFixedNumber :fraction-digits="3">
                    {{ item.cultivatedAcreageVerification }}
                  </HFixedNumber>
                </div>
                <div class="item">
                  <SvgIcon name="fields" />
                  <span>订单面积（亩）：</span>
                  <HFixedNumber :fraction-digits="3">
                    {{ item.allocatedCultivatedAcreage }}
                  </HFixedNumber>
                </div>
                <div class="item">
                  <SvgIcon name="sum" />
                  <span>订单数量（吨）：</span>
                  <HFixedNumber :ratio="1" :fraction-digits="3">
                    {{ item.allocatedGrowingNumber }}
                  </HFixedNumber>
                </div>
              </div>
            </div>
          </HCard>
        </div>
      </List>
    </PullRefresh>
  </div>
</template>

<script setup>
import { ref, reactive, computed } from 'vue';
import { useStore } from 'vuex';
import { List, PullRefresh } from 'vant';
import { HCard, HFixedNumber } from '@/components';
import SvgIcon from '@/components/SvgIcon/SvgIcon';
import EmptyHolder from '@/views/common/EmptyHolder';
import { useRoute } from 'vue-router';
import { getOrderInventoryByIdPage } from '@/api/order-public';

const route = useRoute();
const store = useStore();
const deptId = computed(() => {
  return store.state.user?.reserverIds?.deptId;
});
const level = computed(() => {
  return store.state.user?.reserverIds?.level;
});

const list = ref([]);
const pagination = reactive({
  page: 0,
  size: 10,
  total: 0,
});
const isLoading = ref(false);
const finished = ref(false);
const refreshLoading = ref(false);

const onRefresh = () => {
  if (refreshLoading.value) {
    pagination.page = 0;
    pagination.total = 0;
    list.value = [];
    refreshLoading.value = false;
  }
  finished.value = false;
  isLoading.value = true;
  getList();
};
// 列表
const getList = async () => {
  const { categoryId, year, purchasingStation, orderType } = route.query;
  const { items, page, total } = await getOrderInventoryByIdPage({
    categoryId: categoryId,
    deptIdZZD: deptId.value,
    levelZZD: level.value,
    year: year,
    purchasingStation: purchasingStation,
    orderType: orderType,
    page: pagination.page + 1,
    size: pagination.size,
  });
  list.value.push(...items);
  pagination.page = page;
  pagination.total = total;
  // 加载状态结束
  isLoading.value = false;

  // 数据全部加载完成
  if (list.value.length >= total) {
    finished.value = true;
  }
};
</script>

<style scoped lang="scss">
.query-form {
  margin: 8px 16px;
}
.detail-card {
  margin-bottom: 10px;
}

.header {
  padding: 15px 0;
  border-bottom: 1px solid var(--van-gray-4);
  text-align: center;
  color: var(--van-text-color);
  background-color: #ffffff;

  .title {
    font-size: 20px;
    font-weight: 500;
    line-height: 28px;
    margin-bottom: 8px;
  }

  .sub-title {
    font-size: 16px;
    font-weight: 500;
    color: var(--van-gray-6);
    line-height: 25px;
  }
}

.content {
  padding: 16px;
  color: var(--van-gray-6);
  font-size: 18px;
  background-color: #ffffff;

  .summary {
    font-weight: bold;
    color: var(--van-gray-7);
    line-height: 32px;
    margin-bottom: 8px;

    .item {
      display: flex;
      align-items: center;

      .svg-icon {
        margin-right: 4px;
      }
    }
  }

  table {
    width: 100%;
    border-collapse: collapse;

    th,
    td {
      border: 1px solid var(--van-gray-6);
      line-height: 1.5;
      padding: 8px 4px;
    }

    th {
      background-color: var(--van-gray-3);
    }
  }
}
</style>
