<template>
  <div class="px-2 py-2">
    <HPicker class="w-1/3 mb-3" :options="jobTypes" v-model:value="jobType"></HPicker>
    <div class="py-3 mb-3 bg-white rounded-md" v-for="item in listData.items" :key="item.id">
      <h1 class="flex justify-between px-4 mb-4">
        <p class="font-[20px] font-bold text-blue-400">{{ item.jobName }}</p>
        <p>{{ statusList[item.bizStatus] }}</p>
      </h1>
      <van-cell-group class="cells">
        <van-cell class="" title="作业票类型：" :value="item.jobTicketType" />
        <!-- <van-cell class="" title="作业地点：" value="09" /> -->
        <van-cell class="" title="作业开始时间：" :value="item.jobStartTime" />
        <van-cell class="" title="作业结束时间：" :value="item.jobEndTime" />
        <van-cell class="" title="负责人：" :value="item.headName" />
      </van-cell-group>

      <div class="px-4 mt-4 overflow-hidden text-right">
        <van-button
          @click="(showPopup = true), (detailData = item)"
          class=""
          type="primary"
          size="small"
          >详情</van-button
        >
      </div>
    </div>
  </div>

  <van-popup
    v-model:show="showPopup"
    closeable
    position="bottom"
    class="!bg-gray-50 pop-content h-screen overflow-hidden"
  >
    <div class="h-[50px]">
      <h2 class="p-4 font-bold">录入结果</h2>
    </div>
    <div class="pb-10 overflow-y-auto" style="height: calc(100vh - 50px)">
      <h2 class="px-4 py-2 text-sm text-gray-400">{{ detailData.jobName }}</h2>
      <van-cell-group inset>
        <van-cell class="" title="作业票类型：" :value="detailData.jobTicketType" />
        <!-- <van-cell class="" title="作业地点：" value="09" /> -->
        <van-cell class="" title="作业开始时间：" :value="detailData.jobStartTime" />
        <van-cell class="" title="作业结束时间：" :value="detailData.jobEndTime" />
        <van-cell class="" title="负责人：" :value="detailData.headName" />
        <van-cell title="联系方式：" :value="detailData.headPhone" />
        <van-cell title="现场人员：" :value="detailData.operatorNameList" />
      </van-cell-group>

      <h2 class="px-4 py-2 mt-3 text-sm text-gray-400">相关图片</h2>
      <van-form ref="form">
        <van-cell-group inset>
          <van-field
            name="uploader"
            center
            label="现场图片："
            :rules="[{ required: true, message: '请添加现场图片' }]"
          >
            <template #input>
              <van-uploader
                v-model="operateDonePics"
                :max-count="1"
                @click-upload="(e) => getPicture(e, operateDonePics)"
              />
            </template>
          </van-field>

          <van-field
            name="uploader"
            center
            label="完工图片："
            :rules="[{ required: true, message: '请添加完工图片' }]"
          >
            <template #input>
              <van-uploader
                v-model="operatingPics"
                :max-count="1"
                @click-upload="(e) => getPicture(e, operatingPics)"
              />
            </template>
          </van-field>
        </van-cell-group>

        <h2 class="px-4 py-2 mt-3 text-sm text-gray-400">作业结果</h2>
        <van-cell-group inset>
          <van-field
            v-model="workResult"
            rows="3"
            autosize
            label="结果："
            type="textarea"
            placeholder="请输入作业结果"
            :rules="[{ required: true, message: '请输入作业结果' }]"
          />
        </van-cell-group>
      </van-form>

      <van-sticky :offset-bottom="10" class="mt-5" position="bottom">
        <van-row gutter="20" class="px-4">
          <van-col span="8"
            ><van-button
              type="danger"
              plain
              block
              @click="save(5)"
              :disabled="outsourceJobExecuteNotSendedOrder || isUploadingPic"
              >终止作业</van-button
            ></van-col
          >
          <van-col span="8">
            <van-button
              v-p="[`app-safety-production-management:save`]"
              type="primary"
              plain
              block
              :disabled="outsourceJobExecuteNotSendedOrder || isUploadingPic"
              @click="save(null)"
              >保存</van-button
            >
          </van-col>
          <van-col span="8"
            ><van-button
              type="primary"
              block
              @click="save(1)"
              :disabled="outsourceJobExecuteNotSendedOrder || isUploadingPic"
              >完成作业</van-button
            ></van-col
          >
        </van-row>
      </van-sticky>
    </div>
  </van-popup>
</template>

<script setup>
import { ref, onMounted, watch, computed } from 'vue';
import { getList, cameraGetPicture, update, taskSubmit } from './apis';
import { Toast } from 'vant';

const showPopup = ref(false);
const workResult = ref('');
const operateDonePics = ref([]);
const operatingPics = ref([]);
const detailData = ref({});
const listData = ref([]);

const statusList = {
  0: '审批通过',
  1: '执行中',
  2: '待审批',
  3: '审批不通过',
  4: '已完成',
  5: '已终止',
};

const getListFn = async () => {
  listData.value = await getList(jobType.value);
};
const jobTypes = ref([
  { text: '外包作业', value: 'outsourceJobExecute' },
  { text: '危险作业', value: 'dangerJobExecute' },
]);
const jobType = ref('outsourceJobExecute');

const outsourceJobExecuteNotSendedOrder = computed(() => {
  return jobType.value == 'outsourceJobExecute' && ![1, 3].includes(detailData.value?.bizStatus);
});
watch(
  () => jobType.value,
  () => {
    getListFn();
  },
);

watch(
  () => showPopup.value,
  () => {
    if (showPopup.value) {
      operateDonePics.value = detailData.value?.operateDonePicUrlList
        ? detailData.value?.operateDonePicUrlList.map((i) => ({ url: i }))
        : [];
      operatingPics.value = detailData.value?.operatingPicUrlList
        ? detailData.value?.operatingPicUrlList.map((i) => ({ url: i }))
        : [];
      workResult.value = detailData.value?.workResult || '';
    }
  },
);

onMounted(() => {
  getListFn();
});

const isUploadingPic = ref(false);
const getPicture = async (e, pics) => {
  e.preventDefault();
  isUploadingPic.value = true;
  await cameraGetPicture(pics);
  isUploadingPic.value = false;
};

const form = ref(null);
const save = async (status) => {
  if (status) {
    await form.value.validate();
  }

  const formData = {
    operateDonePicList: operateDonePics.value.map((i) => i.url),
    operatingPicList: operatingPics.value.map((i) => i.url),
    workResult: workResult.value,
    bizStatus: status,
  };

  await update(
    {
      ...formData,
      id: detailData.value.id,
      workBizType: detailData.value.workBizType,
      jobTicketType:
        jobType.value == 'dangerJobExecute' ? detailData.value.jobTicketType : undefined,
    },
    jobType.value,
  );

  if (status === 1) {
    await taskSubmit(
      {
        id: detailData.value.id,
        // coder: jobType.value,
      },
      jobType.value,
    );
  }

  Toast('操作成功');
  showPopup.value = false;
  getListFn();
};
</script>

<style lang="scss" scoped>
// ::v-deep(.van-cell) {
//   // padding: 6px 16px;
// }
</style>
