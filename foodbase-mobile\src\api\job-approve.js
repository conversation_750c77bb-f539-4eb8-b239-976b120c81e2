import { reserveRequest } from '@/utils/request';

export async function getApproveList(data) {
  let url = '/api/activiti/task/getListToBeApprove';
  if (data.coder) {
    url = url + '?coder=' + data.coder;
  } else {
    url = url + '?bizType=' + data.bizType;
  }
  return reserveRequest().post(url);
}

//年度轮换计划详情
export async function getYearRotatePlanDetail(params) {
  return reserveRequest().get('/api/rotation/program/infoByBatchId', { params });
}

//轮入轮出 方案详请查询
export function getSchemeDetailById(params) {
  return reserveRequest().get('/api/rotation/schemeDetail/infoById', {
    params,
  });
}

//竞价销售、招标采购成交结果详情
export async function getSellExpoundResultDetail(data) {
  return reserveRequest().post('/api/rotation/transactionResult/infoTransactionResultDetail', data);
}

//招标采购、竞价销售方案标的详情
export async function getBiddingProcurementPlanList(params) {
  return reserveRequest().get('/api/rotation/signs/infoByPage', { params });
}

//招标采购、竞价销售方案标的列表
export async function getAuctionsalesDetail(data) {
  return reserveRequest().post('/api/rotation/signs/infoSign', data);
}

// 查询审批意见-备注
export function getApproveRemark(params) {
  return reserveRequest().post(
    `/api/activiti/task/getApproveRemark?id=${params.id}&coder=${params.coder}`,
  );
}

// 轮入配仓结果详情查询
export function getAllocationResultById(params) {
  return reserveRequest().get('/api/rotation/program/in/info/getAllocationResultById', {
    params,
  });
}

// 审批
export function activitiTaskApprove(params) {
  return reserveRequest().post('/api/activiti/task/approve', null, { params });
}

// 通知书详请
export function getDonoticeDeportationById(params) {
  return reserveRequest().get('/api/donoticeDeportation/id', {
    params,
  });
}

// 提货单详情
export function getBillLadingById(params) {
  return reserveRequest().get('/api/billLading/billLading/id', { params });
}
