/**
 * 验证身份证号码
 * 测试可用号码
 * 110105199001017191
 * 110105199001013916
 * 110105199001015997
 * 110105199001011515
 * 110105199001010133
 */
function validateIdCard(identityCardNumber) {
  if (identityCardNumber.length !== 18) {
    return false;
  }
  let factor = [7, 9, 10, 5, 8, 4, 2, 1, 6, 3, 7, 9, 10, 5, 8, 4, 2];
  let verifyList = ['1', '0', 'X', '9', '8', '7', '6', '5', '4', '3', '2'];
  let sum = 0;

  for (let index = 0; index < 17; index++) {
    sum += parseInt(identityCardNumber[index]) * factor[index];
  }

  let checkDigit = sum % 11;

  return verifyList[checkDigit] === identityCardNumber[17].toLocaleUpperCase();
}

/**
 * 验证组织代码
 */
function validateOrgCode(str) {
  /* eslint-disable */
  const mapStr1 = '0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZ';
  const firstKeys = [3, 7, 9, 10, 5, 8, 4, 2];
  const mapStr2 = '0123456789ABCDEFGHJKLMNPQRTUWXY';
  const secondKeys = [1, 3, 9, 27, 19, 26, 16, 17, 20, 29, 25, 13, 8, 24, 10, 30, 28];

  /* eslint-enable */
  function calc(code, array1, array2, b) {
    let count = 0;
    for (let i = 0; i < array2.length; i++) {
      const a = code[i];
      count += array2[i] * array1.indexOf(a);
    }
    const remainder = count % b;
    return remainder === 0 ? 0 : b - remainder;
  }

  /**
   * 统一社会信用代码由十八位的阿拉伯数字或大写英文字母（不使用I、O、Z、S、V）组成。
   * 第1位：登记管理部门代码（共一位字符）
   * 第2位：机构类别代码（共一位字符）
   * 第3位~第8位：登记管理机关行政区划码（共六位阿拉伯数字）
   * 第9位~第17位：主体标识码（组织机构代码）（共九位字符）
   * 第18位：校验码（共一位字符）
   */
  const code = str.toUpperCase();
  if (code.length !== 18) {
    return false;
  }

  let reg = /^\w\w\d{6}\w{9}\w$/;
  if (!reg.test(code)) {
    return false;
  }

  /**
   * 主体标识码校验位
   */
  const firstKey = calc(code.substr(8), mapStr1, firstKeys, 11);
  let firstWord;
  if (firstKey < 10) {
    firstWord = firstKey;
  } else if (firstKey === 10) {
    firstWord = 'X';
  } else if (firstKey === 11) {
    firstWord = '0';
  }
  if (firstWord.toString() !== code.substr(16, 1)) {
    return false;
  }

  /**
   * 校验码
   */
  const secondKey = calc(code, mapStr2, secondKeys, 31);
  const secondWord = mapStr2.substr(secondKey, 1);
  if (!secondWord || secondWord !== code.substr(17, 1)) {
    return false;
  }
  const word = code.substr(0, 16) + firstWord + secondWord;
  return code === word;
}

export { validateIdCard, validateOrgCode };
