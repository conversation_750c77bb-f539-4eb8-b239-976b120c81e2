<template>
  <div class="reserve-stock">
    <div class="background-box">
      <div class="total">
        <div class="number">{{ totalStock }}</div>
        <div class="title">总库存</div>
      </div>
      <div class="devide-line"></div>
      <div class="type">
        <div class="number">{{ typeStock }}</div>
        <div class="title">{{ categoryName }}库存</div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { computed } from 'vue';
import { useStore } from 'vuex';

const props = defineProps({
  categoryType: Number, // 1 原粮，2 成品粮， 3 食用油
});

const store = useStore();

const categoryName = computed(() => {
  switch (props.categoryType) {
    case 1:
      return '原粮';
    case 2:
      return '成品粮';
    case 3:
      return '食用油';
    default:
      return '';
  }
});
// const isSichuan = computed(() => {
//   return process.env.VUE_APP_MODE === 'sichuan';
// });
const totalStock = computed(() => {
  let num = 0;
  //总库存取品种库存总和,暂不区分省份
  num = store.state['stock-overview'].stockSumAllCategory;
  return num.toLocaleString();

  // if (isSichuan.value) {
  //   num = store.state['stock-overview'].stockSumAllCategory;
  //   return num.toLocaleString();
  // } else {
  //   num = store.state['stock-overview'].stockSum;
  //   return num.toLocaleString();
  // }
});
const typeStock = computed(() => {
  const num = Math.round(store.state['stock-overview'].topStockSumMap[props.categoryType]);
  return num.toLocaleString();
});
</script>

<style lang="scss" scoped>
.reserve-stock {
  padding: 7px 16px;
  .background-box {
    height: 78px;
    background: linear-gradient(123deg, #439dff 0%, #68c0ff 100%);
    border-radius: 4px;
    display: flex;
    align-items: center;
    .total {
      width: calc((100% - 1px) / 17 * 8);
      display: flex;
      flex-direction: column;
      justify-content: center;
      align-items: center;
    }
    .type {
      width: calc((100% - 1px) / 17 * 9);
      display: flex;
      flex-direction: column;
      justify-content: center;
      align-items: center;
    }
    .devide-line {
      width: 1px;
      height: 42px;
      border: 1px solid #ffffff;
    }
    .number {
      font-size: 20px;
      font-family: PingFangSC, PingFang SC;
      font-weight: 600;
      color: #ffffff;
      line-height: 28px;
    }
    .title {
      font-size: 16px;
      font-family: PingFangSC, PingFang SC;
      font-weight: 400;
      color: #ffffff;
      line-height: 22px;
    }
  }
}
</style>
