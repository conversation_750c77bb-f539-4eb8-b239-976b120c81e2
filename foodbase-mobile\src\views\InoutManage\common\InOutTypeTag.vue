<template>
  <span class="tag" :style="style">
    <slot></slot>
  </span>
</template>

<script setup>
import { computed } from 'vue';

const props = defineProps({
  colorType: [Number, String], // 1,2,3,4,5
});
const colors = [
  '',
  '#F25E4A',
  '#39AA02',
  '#FB9A00',
  '#FB9A00',
  '#FB9A00',
  '#FB9A00',
  '#FB9A00',
  '#FB9A00',
  '#F25E4A',
  '#39AA02',
  '#F25E4A',
  '#39AA02',
];
const backgroundColors = [
  '',
  '#FDEAE8',
  '#E0F3CD',
  '#FFE8C3',
  '#FFE8C3',
  '#FFE8C3',
  '#FFE8C3',
  '#FFE8C3',
  '#FFE8C3',
  '#FDEAE8',
  '#E0F3CD',
  '#FDEAE8',
  '#E0F3CD',
];

const style = computed(() => {
  return {
    color: colors[props.colorType || 1],
    backgroundColor: backgroundColors[props.colorType || 1],
  };
});
</script>

<style scoped>
.tag {
  display: inline-block;
  line-height: 25px;
  border-radius: 4px;
  padding: 0 8px;
  font-weight: bold;
  margin-left: auto;
  font-size: 12px;
}
</style>
