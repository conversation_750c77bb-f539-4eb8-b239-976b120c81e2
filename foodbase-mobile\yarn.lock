# THIS IS AN AUTOGENERATED FILE. DO NOT EDIT THIS FILE DIRECTLY.
# yarn lockfile v1


"@alloc/quick-lru@^5.2.0":
  "integrity" "sha512-UrcABB+4bUrFABwbluTIBErXwvbsU/V7TZWfmbgJfbkwiBuziS9gxdODUyuiecfdGQ85jglMW6juS3+z5TsKLw=="
  "resolved" "http://castle-npm.cp.hxdi.cn/@alloc%2fquick-lru/-/quick-lru-5.2.0.tgz"
  "version" "5.2.0"

"@antfu/utils@^0.7.5":
  "integrity" "sha512-pvFiLP2BeOKA/ZOS6jxx4XhKzdVLHDhGlFEaZ2flWWYf2xOqVniqpk38I04DFRyz+L0ASggl7SkItTc+ZLju4w=="
  "resolved" "http://castle-npm.cp.hxdi.cn/@antfu%2futils/-/utils-0.7.6.tgz"
  "version" "0.7.6"

"@babel/code-frame@^7.0.0", "@babel/code-frame@^7.16.0":
  "integrity" "sha1-DfyAMJvuyEEeZecGRhxAiwu5tDE="
  "resolved" "https://registry.npmmirror.com/@babel/code-frame/download/@babel/code-frame-7.16.0.tgz"
  "version" "7.16.0"
  dependencies:
    "@babel/highlight" "^7.16.0"

"@babel/code-frame@7.12.11":
  "integrity" "sha1-9K1DWqJj25NbjxDyxVLSP7cWpj8="
  "resolved" "https://registry.npmmirror.com/@babel/code-frame/download/@babel/code-frame-7.12.11.tgz"
  "version" "7.12.11"
  dependencies:
    "@babel/highlight" "^7.10.4"

"@babel/compat-data@^7.13.11", "@babel/compat-data@^7.16.0", "@babel/compat-data@^7.16.4":
  "integrity" "sha512-1o/jo7D+kC9ZjHX5v+EHrdjl3PhxMrLSOTGsOdHJ+KL8HCaEK6ehrVL2RS6oHDZp+L7xLirLrPmQtEng769J/Q=="
  "resolved" "https://registry.npmmirror.com/@babel/compat-data/download/@babel/compat-data-7.16.4.tgz?cache=0&sync_timestamp=1637105418856&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2F%40babel%2Fcompat-data%2Fdownload%2F%40babel%2Fcompat-data-7.16.4.tgz"
  "version" "7.16.4"

"@babel/core@^7.0.0", "@babel/core@^7.0.0-0", "@babel/core@^7.12.0", "@babel/core@^7.12.16", "@babel/core@^7.13.0", "@babel/core@^7.4.0-0", "@babel/core@>=7.11.0":
  "integrity" "sha1-xP9EBG9f4xBSXMnrTvUUfwxTdNQ="
  "resolved" "https://registry.npmmirror.com/@babel/core/download/@babel/core-7.16.0.tgz?cache=0&sync_timestamp=1635561090259&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2F%40babel%2Fcore%2Fdownload%2F%40babel%2Fcore-7.16.0.tgz"
  "version" "7.16.0"
  dependencies:
    "@babel/code-frame" "^7.16.0"
    "@babel/generator" "^7.16.0"
    "@babel/helper-compilation-targets" "^7.16.0"
    "@babel/helper-module-transforms" "^7.16.0"
    "@babel/helpers" "^7.16.0"
    "@babel/parser" "^7.16.0"
    "@babel/template" "^7.16.0"
    "@babel/traverse" "^7.16.0"
    "@babel/types" "^7.16.0"
    "convert-source-map" "^1.7.0"
    "debug" "^4.1.0"
    "gensync" "^1.0.0-beta.2"
    "json5" "^2.1.2"
    "semver" "^6.3.0"
    "source-map" "^0.5.0"

"@babel/eslint-parser@^7.12.16":
  "integrity" "sha512-iB4ElZT0jAt7PKVaeVulOECdGe6UnmA/O0P9jlF5g5GBOwDVbna8AXhHRu4s27xQf6OkveyA8iTDv1jHdDejgQ=="
  "resolved" "https://registry.npmmirror.com/@babel/eslint-parser/download/@babel/eslint-parser-7.16.3.tgz"
  "version" "7.16.3"
  dependencies:
    "eslint-scope" "^5.1.1"
    "eslint-visitor-keys" "^2.1.0"
    "semver" "^6.3.0"

"@babel/generator@^7.16.0":
  "integrity" "sha1-1A89HVB15i01ALzLZ/PaqKlSZbI="
  "resolved" "https://registry.npmmirror.com/@babel/generator/download/@babel/generator-7.16.0.tgz"
  "version" "7.16.0"
  dependencies:
    "@babel/types" "^7.16.0"
    "jsesc" "^2.5.1"
    "source-map" "^0.5.0"

"@babel/helper-annotate-as-pure@^7.16.0":
  "integrity" "sha1-mh8OvNpT2aLQAQjEzqzmpdXx8I0="
  "resolved" "https://registry.npmmirror.com/@babel/helper-annotate-as-pure/download/@babel/helper-annotate-as-pure-7.16.0.tgz"
  "version" "7.16.0"
  dependencies:
    "@babel/types" "^7.16.0"

"@babel/helper-builder-binary-assignment-operator-visitor@^7.16.0":
  "integrity" "sha1-8aaGuS2nlAIMJlguuFLprM0NeII="
  "resolved" "https://registry.npmmirror.com/@babel/helper-builder-binary-assignment-operator-visitor/download/@babel/helper-builder-binary-assignment-operator-visitor-7.16.0.tgz"
  "version" "7.16.0"
  dependencies:
    "@babel/helper-explode-assignable-expression" "^7.16.0"
    "@babel/types" "^7.16.0"

"@babel/helper-compilation-targets@^7.12.16", "@babel/helper-compilation-targets@^7.13.0", "@babel/helper-compilation-targets@^7.16.0", "@babel/helper-compilation-targets@^7.16.3":
  "integrity" "sha512-vKsoSQAyBmxS35JUOOt+07cLc6Nk/2ljLIHwmq2/NM6hdioUaqEXq/S+nXvbvXbZkNDlWOymPanJGOc4CBjSJA=="
  "resolved" "https://registry.npmmirror.com/@babel/helper-compilation-targets/download/@babel/helper-compilation-targets-7.16.3.tgz"
  "version" "7.16.3"
  dependencies:
    "@babel/compat-data" "^7.16.0"
    "@babel/helper-validator-option" "^7.14.5"
    "browserslist" "^4.17.5"
    "semver" "^6.3.0"

"@babel/helper-create-class-features-plugin@^7.16.0":
  "integrity" "sha1-CQ1NFms0KgOp/sN+9P1a65x8aks="
  "resolved" "https://registry.npmmirror.com/@babel/helper-create-class-features-plugin/download/@babel/helper-create-class-features-plugin-7.16.0.tgz?cache=0&sync_timestamp=1635561094951&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2F%40babel%2Fhelper-create-class-features-plugin%2Fdownload%2F%40babel%2Fhelper-create-class-features-plugin-7.16.0.tgz"
  "version" "7.16.0"
  dependencies:
    "@babel/helper-annotate-as-pure" "^7.16.0"
    "@babel/helper-function-name" "^7.16.0"
    "@babel/helper-member-expression-to-functions" "^7.16.0"
    "@babel/helper-optimise-call-expression" "^7.16.0"
    "@babel/helper-replace-supers" "^7.16.0"
    "@babel/helper-split-export-declaration" "^7.16.0"

"@babel/helper-create-regexp-features-plugin@^7.16.0":
  "integrity" "sha1-BrI0jON/zMT14Y3NjXUFPyp8RP8="
  "resolved" "https://registry.npmmirror.com/@babel/helper-create-regexp-features-plugin/download/@babel/helper-create-regexp-features-plugin-7.16.0.tgz"
  "version" "7.16.0"
  dependencies:
    "@babel/helper-annotate-as-pure" "^7.16.0"
    "regexpu-core" "^4.7.1"

"@babel/helper-define-polyfill-provider@^0.3.0":
  "integrity" "sha512-7hfT8lUljl/tM3h+izTX/pO3W3frz2ok6Pk+gzys8iJqDfZrZy2pXjRTZAvG2YmfHun1X4q8/UZRLatMfqc5Tg=="
  "resolved" "https://registry.npmmirror.com/@babel/helper-define-polyfill-provider/download/@babel/helper-define-polyfill-provider-0.3.0.tgz?cache=0&sync_timestamp=1636799792651&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2F%40babel%2Fhelper-define-polyfill-provider%2Fdownload%2F%40babel%2Fhelper-define-polyfill-provider-0.3.0.tgz"
  "version" "0.3.0"
  dependencies:
    "@babel/helper-compilation-targets" "^7.13.0"
    "@babel/helper-module-imports" "^7.12.13"
    "@babel/helper-plugin-utils" "^7.13.0"
    "@babel/traverse" "^7.13.0"
    "debug" "^4.1.1"
    "lodash.debounce" "^4.0.8"
    "resolve" "^1.14.2"
    "semver" "^6.1.2"

"@babel/helper-explode-assignable-expression@^7.16.0":
  "integrity" "sha1-dTAXM3oV9G+cCfZ0z/EM7pudd3g="
  "resolved" "https://registry.npmmirror.com/@babel/helper-explode-assignable-expression/download/@babel/helper-explode-assignable-expression-7.16.0.tgz"
  "version" "7.16.0"
  dependencies:
    "@babel/types" "^7.16.0"

"@babel/helper-function-name@^7.16.0":
  "integrity" "sha1-t90Hl9ALv+5PB+nE6lsOMMi7FIE="
  "resolved" "https://registry.npmmirror.com/@babel/helper-function-name/download/@babel/helper-function-name-7.16.0.tgz"
  "version" "7.16.0"
  dependencies:
    "@babel/helper-get-function-arity" "^7.16.0"
    "@babel/template" "^7.16.0"
    "@babel/types" "^7.16.0"

"@babel/helper-get-function-arity@^7.16.0":
  "integrity" "sha1-AIjHSGspqctdlIsaHeRttm4InPo="
  "resolved" "https://registry.npmmirror.com/@babel/helper-get-function-arity/download/@babel/helper-get-function-arity-7.16.0.tgz"
  "version" "7.16.0"
  dependencies:
    "@babel/types" "^7.16.0"

"@babel/helper-hoist-variables@^7.16.0":
  "integrity" "sha1-TJAjwvHe9+KP9G/B2802o5vqqBo="
  "resolved" "https://registry.npmmirror.com/@babel/helper-hoist-variables/download/@babel/helper-hoist-variables-7.16.0.tgz"
  "version" "7.16.0"
  dependencies:
    "@babel/types" "^7.16.0"

"@babel/helper-member-expression-to-functions@^7.16.0":
  "integrity" "sha1-KShwQO/Rl8d2Nu91GI6B2ovM1aQ="
  "resolved" "https://registry.npmmirror.com/@babel/helper-member-expression-to-functions/download/@babel/helper-member-expression-to-functions-7.16.0.tgz"
  "version" "7.16.0"
  dependencies:
    "@babel/types" "^7.16.0"

"@babel/helper-module-imports@^7.0.0", "@babel/helper-module-imports@^7.12.13", "@babel/helper-module-imports@^7.16.0":
  "integrity" "sha1-kFOOYLZy7PG0SPX09UM9N+eaPsM="
  "resolved" "https://registry.npmmirror.com/@babel/helper-module-imports/download/@babel/helper-module-imports-7.16.0.tgz?cache=0&sync_timestamp=1635561095960&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2F%40babel%2Fhelper-module-imports%2Fdownload%2F%40babel%2Fhelper-module-imports-7.16.0.tgz"
  "version" "7.16.0"
  dependencies:
    "@babel/types" "^7.16.0"

"@babel/helper-module-transforms@^7.16.0":
  "integrity" "sha1-HIKo3UyzRXdQLr0pCWmbGUw+m7U="
  "resolved" "https://registry.npmmirror.com/@babel/helper-module-transforms/download/@babel/helper-module-transforms-7.16.0.tgz"
  "version" "7.16.0"
  dependencies:
    "@babel/helper-module-imports" "^7.16.0"
    "@babel/helper-replace-supers" "^7.16.0"
    "@babel/helper-simple-access" "^7.16.0"
    "@babel/helper-split-export-declaration" "^7.16.0"
    "@babel/helper-validator-identifier" "^7.15.7"
    "@babel/template" "^7.16.0"
    "@babel/traverse" "^7.16.0"
    "@babel/types" "^7.16.0"

"@babel/helper-optimise-call-expression@^7.16.0":
  "integrity" "sha1-zs2xRdcMVAlrFWT46fEM19GTszg="
  "resolved" "https://registry.npmmirror.com/@babel/helper-optimise-call-expression/download/@babel/helper-optimise-call-expression-7.16.0.tgz"
  "version" "7.16.0"
  dependencies:
    "@babel/types" "^7.16.0"

"@babel/helper-plugin-utils@^7.0.0", "@babel/helper-plugin-utils@^7.10.4", "@babel/helper-plugin-utils@^7.12.13", "@babel/helper-plugin-utils@^7.13.0", "@babel/helper-plugin-utils@^7.14.5", "@babel/helper-plugin-utils@^7.8.0", "@babel/helper-plugin-utils@^7.8.3":
  "integrity" "sha1-WsgizpfuxGdBq3ClF5ceRDpwxak="
  "resolved" "https://registry.npmmirror.com/@babel/helper-plugin-utils/download/@babel/helper-plugin-utils-7.14.5.tgz"
  "version" "7.14.5"

"@babel/helper-remap-async-to-generator@^7.16.0", "@babel/helper-remap-async-to-generator@^7.16.4":
  "integrity" "sha512-vGERmmhR+s7eH5Y/cp8PCVzj4XEjerq8jooMfxFdA5xVtAk9Sh4AQsrWgiErUEBjtGrBtOFKDUcWQFW4/dFwMA=="
  "resolved" "https://registry.npmmirror.com/@babel/helper-remap-async-to-generator/download/@babel/helper-remap-async-to-generator-7.16.4.tgz?cache=0&sync_timestamp=1637106668633&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2F%40babel%2Fhelper-remap-async-to-generator%2Fdownload%2F%40babel%2Fhelper-remap-async-to-generator-7.16.4.tgz"
  "version" "7.16.4"
  dependencies:
    "@babel/helper-annotate-as-pure" "^7.16.0"
    "@babel/helper-wrap-function" "^7.16.0"
    "@babel/types" "^7.16.0"

"@babel/helper-replace-supers@^7.16.0":
  "integrity" "sha1-cwVejTz5vLqN21XK2T/tyGD2jxc="
  "resolved" "https://registry.npmmirror.com/@babel/helper-replace-supers/download/@babel/helper-replace-supers-7.16.0.tgz"
  "version" "7.16.0"
  dependencies:
    "@babel/helper-member-expression-to-functions" "^7.16.0"
    "@babel/helper-optimise-call-expression" "^7.16.0"
    "@babel/traverse" "^7.16.0"
    "@babel/types" "^7.16.0"

"@babel/helper-simple-access@^7.16.0":
  "integrity" "sha1-IdaidiDjg+N1NM9sELugGab5BRc="
  "resolved" "https://registry.npmmirror.com/@babel/helper-simple-access/download/@babel/helper-simple-access-7.16.0.tgz"
  "version" "7.16.0"
  dependencies:
    "@babel/types" "^7.16.0"

"@babel/helper-skip-transparent-expression-wrappers@^7.16.0":
  "integrity" "sha1-DuM4gHAUfDrgUeSH7KPrsOLouwk="
  "resolved" "https://registry.npmmirror.com/@babel/helper-skip-transparent-expression-wrappers/download/@babel/helper-skip-transparent-expression-wrappers-7.16.0.tgz"
  "version" "7.16.0"
  dependencies:
    "@babel/types" "^7.16.0"

"@babel/helper-split-export-declaration@^7.16.0":
  "integrity" "sha1-KWcvQ2Y+k23zcKrrIr7ds7rsdDg="
  "resolved" "https://registry.npmmirror.com/@babel/helper-split-export-declaration/download/@babel/helper-split-export-declaration-7.16.0.tgz"
  "version" "7.16.0"
  dependencies:
    "@babel/types" "^7.16.0"

"@babel/helper-validator-identifier@^7.15.7":
  "integrity" "sha1-Ig35k7/pBKSmsCq08zhaXr9uI4k="
  "resolved" "https://registry.npmmirror.com/@babel/helper-validator-identifier/download/@babel/helper-validator-identifier-7.15.7.tgz"
  "version" "7.15.7"

"@babel/helper-validator-option@^7.14.5":
  "integrity" "sha1-bnKh//GNXfy4eOHmLxoCHEty1aM="
  "resolved" "https://registry.npmmirror.com/@babel/helper-validator-option/download/@babel/helper-validator-option-7.14.5.tgz"
  "version" "7.14.5"

"@babel/helper-wrap-function@^7.16.0":
  "integrity" "sha1-s88xivzndN/nW4Z2fNbWjzSC5Xw="
  "resolved" "https://registry.npmmirror.com/@babel/helper-wrap-function/download/@babel/helper-wrap-function-7.16.0.tgz"
  "version" "7.16.0"
  dependencies:
    "@babel/helper-function-name" "^7.16.0"
    "@babel/template" "^7.16.0"
    "@babel/traverse" "^7.16.0"
    "@babel/types" "^7.16.0"

"@babel/helpers@^7.16.0":
  "integrity" "sha512-Xn8IhDlBPhvYTvgewPKawhADichOsbkZuzN7qz2BusOM0brChsyXMDJvldWaYMMUNiCQdQzNEioXTp3sC8Nt8w=="
  "resolved" "https://registry.npmmirror.com/@babel/helpers/download/@babel/helpers-7.16.3.tgz"
  "version" "7.16.3"
  dependencies:
    "@babel/template" "^7.16.0"
    "@babel/traverse" "^7.16.3"
    "@babel/types" "^7.16.0"

"@babel/highlight@^7.10.4", "@babel/highlight@^7.16.0":
  "integrity" "sha1-bOsysspLj182H7f9gh4/3fShclo="
  "resolved" "https://registry.npmmirror.com/@babel/highlight/download/@babel/highlight-7.16.0.tgz"
  "version" "7.16.0"
  dependencies:
    "@babel/helper-validator-identifier" "^7.15.7"
    "chalk" "^2.0.0"
    "js-tokens" "^4.0.0"

"@babel/parser@^7.15.8", "@babel/parser@^7.16.0", "@babel/parser@^7.16.3", "@babel/parser@^7.16.4":
  "integrity" "sha512-6V0qdPUaiVHH3RtZeLIsc+6pDhbYzHR8ogA8w+f+Wc77DuXto19g2QUwveINoS34Uw+W8/hQDGJCx+i4n7xcng=="
  "resolved" "https://registry.npmmirror.com/@babel/parser/download/@babel/parser-7.16.4.tgz?cache=0&sync_timestamp=1637105418396&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2F%40babel%2Fparser%2Fdownload%2F%40babel%2Fparser-7.16.4.tgz"
  "version" "7.16.4"

"@babel/plugin-bugfix-safari-id-destructuring-collision-in-function-expression@^7.16.2":
  "integrity" "sha1-KXf8qbIS2xU8GVZ05Xz6uAdzMYM="
  "resolved" "https://registry.npmmirror.com/@babel/plugin-bugfix-safari-id-destructuring-collision-in-function-expression/download/@babel/plugin-bugfix-safari-id-destructuring-collision-in-function-expression-7.16.2.tgz?cache=0&sync_timestamp=1635837571373&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2F%40babel%2Fplugin-bugfix-safari-id-destructuring-collision-in-function-expression%2Fdownload%2F%40babel%2Fplugin-bugfix-safari-id-destructuring-collision-in-function-expression-7.16.2.tgz"
  "version" "7.16.2"
  dependencies:
    "@babel/helper-plugin-utils" "^7.14.5"

"@babel/plugin-bugfix-v8-spread-parameters-in-optional-chaining@^7.16.0":
  "integrity" "sha1-NYly6qsAb16wgmGDsMk8vK8T4eI="
  "resolved" "https://registry.npmmirror.com/@babel/plugin-bugfix-v8-spread-parameters-in-optional-chaining/download/@babel/plugin-bugfix-v8-spread-parameters-in-optional-chaining-7.16.0.tgz"
  "version" "7.16.0"
  dependencies:
    "@babel/helper-plugin-utils" "^7.14.5"
    "@babel/helper-skip-transparent-expression-wrappers" "^7.16.0"
    "@babel/plugin-proposal-optional-chaining" "^7.16.0"

"@babel/plugin-proposal-async-generator-functions@^7.16.4":
  "integrity" "sha512-/CUekqaAaZCQHleSK/9HajvcD/zdnJiKRiuUFq8ITE+0HsPzquf53cpFiqAwl/UfmJbR6n5uGPQSPdrmKOvHHg=="
  "resolved" "https://registry.npmmirror.com/@babel/plugin-proposal-async-generator-functions/download/@babel/plugin-proposal-async-generator-functions-7.16.4.tgz"
  "version" "7.16.4"
  dependencies:
    "@babel/helper-plugin-utils" "^7.14.5"
    "@babel/helper-remap-async-to-generator" "^7.16.4"
    "@babel/plugin-syntax-async-generators" "^7.8.4"

"@babel/plugin-proposal-class-properties@^7.12.13", "@babel/plugin-proposal-class-properties@^7.16.0":
  "integrity" "sha1-wClhgmfd68coD6KG4PjKKieKLRo="
  "resolved" "https://registry.npmmirror.com/@babel/plugin-proposal-class-properties/download/@babel/plugin-proposal-class-properties-7.16.0.tgz"
  "version" "7.16.0"
  dependencies:
    "@babel/helper-create-class-features-plugin" "^7.16.0"
    "@babel/helper-plugin-utils" "^7.14.5"

"@babel/plugin-proposal-class-static-block@^7.16.0":
  "integrity" "sha1-UpaULFZNgUTIPuo0fQqooLiRcOc="
  "resolved" "https://registry.npmmirror.com/@babel/plugin-proposal-class-static-block/download/@babel/plugin-proposal-class-static-block-7.16.0.tgz"
  "version" "7.16.0"
  dependencies:
    "@babel/helper-create-class-features-plugin" "^7.16.0"
    "@babel/helper-plugin-utils" "^7.14.5"
    "@babel/plugin-syntax-class-static-block" "^7.14.5"

"@babel/plugin-proposal-decorators@^7.12.13":
  "integrity" "sha512-RESBNX16eNqnBeEVR5sCJpnW0mHiNLNNvGA8PrRuK/4ZJ4TO+6bHleRUuGQYDERVySOKtOhSya/C4MIhwAMAgg=="
  "resolved" "https://registry.npmmirror.com/@babel/plugin-proposal-decorators/download/@babel/plugin-proposal-decorators-7.16.4.tgz"
  "version" "7.16.4"
  dependencies:
    "@babel/helper-create-class-features-plugin" "^7.16.0"
    "@babel/helper-plugin-utils" "^7.14.5"
    "@babel/plugin-syntax-decorators" "^7.16.0"

"@babel/plugin-proposal-dynamic-import@^7.16.0":
  "integrity" "sha1-eD7KYdUFJiAvmylglUU5d+iGWfE="
  "resolved" "https://registry.npmmirror.com/@babel/plugin-proposal-dynamic-import/download/@babel/plugin-proposal-dynamic-import-7.16.0.tgz"
  "version" "7.16.0"
  dependencies:
    "@babel/helper-plugin-utils" "^7.14.5"
    "@babel/plugin-syntax-dynamic-import" "^7.8.3"

"@babel/plugin-proposal-export-namespace-from@^7.16.0":
  "integrity" "sha1-nAHe5Auda4R7ZWqvSjl2pxdA8iI="
  "resolved" "https://registry.npmmirror.com/@babel/plugin-proposal-export-namespace-from/download/@babel/plugin-proposal-export-namespace-from-7.16.0.tgz"
  "version" "7.16.0"
  dependencies:
    "@babel/helper-plugin-utils" "^7.14.5"
    "@babel/plugin-syntax-export-namespace-from" "^7.8.3"

"@babel/plugin-proposal-json-strings@^7.16.0":
  "integrity" "sha1-yuNale0dKn+inE3EFUC4SnLpqyU="
  "resolved" "https://registry.npmmirror.com/@babel/plugin-proposal-json-strings/download/@babel/plugin-proposal-json-strings-7.16.0.tgz"
  "version" "7.16.0"
  dependencies:
    "@babel/helper-plugin-utils" "^7.14.5"
    "@babel/plugin-syntax-json-strings" "^7.8.3"

"@babel/plugin-proposal-logical-assignment-operators@^7.16.0":
  "integrity" "sha1-pxG4zrP/3dPviNOknobb08x9s/0="
  "resolved" "https://registry.npmmirror.com/@babel/plugin-proposal-logical-assignment-operators/download/@babel/plugin-proposal-logical-assignment-operators-7.16.0.tgz"
  "version" "7.16.0"
  dependencies:
    "@babel/helper-plugin-utils" "^7.14.5"
    "@babel/plugin-syntax-logical-assignment-operators" "^7.10.4"

"@babel/plugin-proposal-nullish-coalescing-operator@^7.16.0":
  "integrity" "sha1-ROHM4I/iQnSCz0RqkbtFFSjtBZY="
  "resolved" "https://registry.npmmirror.com/@babel/plugin-proposal-nullish-coalescing-operator/download/@babel/plugin-proposal-nullish-coalescing-operator-7.16.0.tgz"
  "version" "7.16.0"
  dependencies:
    "@babel/helper-plugin-utils" "^7.14.5"
    "@babel/plugin-syntax-nullish-coalescing-operator" "^7.8.3"

"@babel/plugin-proposal-numeric-separator@^7.16.0":
  "integrity" "sha1-XUGOT7v4ubfQMSXTpScwQzo3NzQ="
  "resolved" "https://registry.npmmirror.com/@babel/plugin-proposal-numeric-separator/download/@babel/plugin-proposal-numeric-separator-7.16.0.tgz"
  "version" "7.16.0"
  dependencies:
    "@babel/helper-plugin-utils" "^7.14.5"
    "@babel/plugin-syntax-numeric-separator" "^7.10.4"

"@babel/plugin-proposal-object-rest-spread@^7.16.0":
  "integrity" "sha1-X7MvbZJNbmcSgQNipg4SomCYcuY="
  "resolved" "https://registry.npmmirror.com/@babel/plugin-proposal-object-rest-spread/download/@babel/plugin-proposal-object-rest-spread-7.16.0.tgz"
  "version" "7.16.0"
  dependencies:
    "@babel/compat-data" "^7.16.0"
    "@babel/helper-compilation-targets" "^7.16.0"
    "@babel/helper-plugin-utils" "^7.14.5"
    "@babel/plugin-syntax-object-rest-spread" "^7.8.3"
    "@babel/plugin-transform-parameters" "^7.16.0"

"@babel/plugin-proposal-optional-catch-binding@^7.16.0":
  "integrity" "sha1-WRAIWBGrTCiwDW6/+kqwJ00eXxY="
  "resolved" "https://registry.npmmirror.com/@babel/plugin-proposal-optional-catch-binding/download/@babel/plugin-proposal-optional-catch-binding-7.16.0.tgz"
  "version" "7.16.0"
  dependencies:
    "@babel/helper-plugin-utils" "^7.14.5"
    "@babel/plugin-syntax-optional-catch-binding" "^7.8.3"

"@babel/plugin-proposal-optional-chaining@^7.16.0":
  "integrity" "sha1-VtvDlwglaDYI6e+1XqgsKi1sjcA="
  "resolved" "https://registry.npmmirror.com/@babel/plugin-proposal-optional-chaining/download/@babel/plugin-proposal-optional-chaining-7.16.0.tgz"
  "version" "7.16.0"
  dependencies:
    "@babel/helper-plugin-utils" "^7.14.5"
    "@babel/helper-skip-transparent-expression-wrappers" "^7.16.0"
    "@babel/plugin-syntax-optional-chaining" "^7.8.3"

"@babel/plugin-proposal-private-methods@^7.16.0":
  "integrity" "sha1-tNr7nHF+QwHFd2sw0IDWODyJr/Y="
  "resolved" "https://registry.npmmirror.com/@babel/plugin-proposal-private-methods/download/@babel/plugin-proposal-private-methods-7.16.0.tgz"
  "version" "7.16.0"
  dependencies:
    "@babel/helper-create-class-features-plugin" "^7.16.0"
    "@babel/helper-plugin-utils" "^7.14.5"

"@babel/plugin-proposal-private-property-in-object@^7.16.0":
  "integrity" "sha1-aek1ssXHnSSIES2IbwxOJ5D+528="
  "resolved" "https://registry.npmmirror.com/@babel/plugin-proposal-private-property-in-object/download/@babel/plugin-proposal-private-property-in-object-7.16.0.tgz"
  "version" "7.16.0"
  dependencies:
    "@babel/helper-annotate-as-pure" "^7.16.0"
    "@babel/helper-create-class-features-plugin" "^7.16.0"
    "@babel/helper-plugin-utils" "^7.14.5"
    "@babel/plugin-syntax-private-property-in-object" "^7.14.5"

"@babel/plugin-proposal-unicode-property-regex@^7.16.0", "@babel/plugin-proposal-unicode-property-regex@^7.4.4":
  "integrity" "sha1-iQSC38XqN45C4Zpx5wlyjKvxhhI="
  "resolved" "https://registry.npmmirror.com/@babel/plugin-proposal-unicode-property-regex/download/@babel/plugin-proposal-unicode-property-regex-7.16.0.tgz"
  "version" "7.16.0"
  dependencies:
    "@babel/helper-create-regexp-features-plugin" "^7.16.0"
    "@babel/helper-plugin-utils" "^7.14.5"

"@babel/plugin-syntax-async-generators@^7.8.4":
  "integrity" "sha1-qYP7Gusuw/btBCohD2QOkOeG/g0="
  "resolved" "https://registry.npmmirror.com/@babel/plugin-syntax-async-generators/download/@babel/plugin-syntax-async-generators-7.8.4.tgz"
  "version" "7.8.4"
  dependencies:
    "@babel/helper-plugin-utils" "^7.8.0"

"@babel/plugin-syntax-class-properties@^7.12.13":
  "integrity" "sha1-tcmHJ0xKOoK4lxR5aTGmtTVErhA="
  "resolved" "https://registry.npmmirror.com/@babel/plugin-syntax-class-properties/download/@babel/plugin-syntax-class-properties-7.12.13.tgz"
  "version" "7.12.13"
  dependencies:
    "@babel/helper-plugin-utils" "^7.12.13"

"@babel/plugin-syntax-class-static-block@^7.14.5":
  "integrity" "sha1-GV34mxRrS3izv4l/16JXyEZZ1AY="
  "resolved" "https://registry.npmmirror.com/@babel/plugin-syntax-class-static-block/download/@babel/plugin-syntax-class-static-block-7.14.5.tgz"
  "version" "7.14.5"
  dependencies:
    "@babel/helper-plugin-utils" "^7.14.5"

"@babel/plugin-syntax-decorators@^7.16.0":
  "integrity" "sha1-642BHN0QYPasPACVa/P2M1UFoy8="
  "resolved" "https://registry.npmmirror.com/@babel/plugin-syntax-decorators/download/@babel/plugin-syntax-decorators-7.16.0.tgz?cache=0&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2F%40babel%2Fplugin-syntax-decorators%2Fdownload%2F%40babel%2Fplugin-syntax-decorators-7.16.0.tgz"
  "version" "7.16.0"
  dependencies:
    "@babel/helper-plugin-utils" "^7.14.5"

"@babel/plugin-syntax-dynamic-import@^7.8.3":
  "integrity" "sha1-Yr+Ysto80h1iYVT8lu5bPLaOrLM="
  "resolved" "https://registry.npmmirror.com/@babel/plugin-syntax-dynamic-import/download/@babel/plugin-syntax-dynamic-import-7.8.3.tgz"
  "version" "7.8.3"
  dependencies:
    "@babel/helper-plugin-utils" "^7.8.0"

"@babel/plugin-syntax-export-namespace-from@^7.8.3":
  "integrity" "sha1-AolkqbqA28CUyRXEh618TnpmRlo="
  "resolved" "https://registry.npmmirror.com/@babel/plugin-syntax-export-namespace-from/download/@babel/plugin-syntax-export-namespace-from-7.8.3.tgz"
  "version" "7.8.3"
  dependencies:
    "@babel/helper-plugin-utils" "^7.8.3"

"@babel/plugin-syntax-json-strings@^7.8.3":
  "integrity" "sha1-AcohtmjNghjJ5kDLbdiMVBKyyWo="
  "resolved" "https://registry.npmmirror.com/@babel/plugin-syntax-json-strings/download/@babel/plugin-syntax-json-strings-7.8.3.tgz"
  "version" "7.8.3"
  dependencies:
    "@babel/helper-plugin-utils" "^7.8.0"

"@babel/plugin-syntax-jsx@^7.0.0", "@babel/plugin-syntax-jsx@^7.12.13", "@babel/plugin-syntax-jsx@^7.2.0":
  "integrity" "sha1-+WJDlDFzZamojII1jT+EcRVGmPE="
  "resolved" "https://registry.npmmirror.com/@babel/plugin-syntax-jsx/download/@babel/plugin-syntax-jsx-7.16.0.tgz"
  "version" "7.16.0"
  dependencies:
    "@babel/helper-plugin-utils" "^7.14.5"

"@babel/plugin-syntax-logical-assignment-operators@^7.10.4":
  "integrity" "sha1-ypHvRjA1MESLkGZSusLp/plB9pk="
  "resolved" "https://registry.npmmirror.com/@babel/plugin-syntax-logical-assignment-operators/download/@babel/plugin-syntax-logical-assignment-operators-7.10.4.tgz"
  "version" "7.10.4"
  dependencies:
    "@babel/helper-plugin-utils" "^7.10.4"

"@babel/plugin-syntax-nullish-coalescing-operator@^7.8.3":
  "integrity" "sha1-Fn7XA2iIYIH3S1w2xlqIwDtm0ak="
  "resolved" "https://registry.npmmirror.com/@babel/plugin-syntax-nullish-coalescing-operator/download/@babel/plugin-syntax-nullish-coalescing-operator-7.8.3.tgz"
  "version" "7.8.3"
  dependencies:
    "@babel/helper-plugin-utils" "^7.8.0"

"@babel/plugin-syntax-numeric-separator@^7.10.4":
  "integrity" "sha1-ubBws+M1cM2f0Hun+pHA3Te5r5c="
  "resolved" "https://registry.npmmirror.com/@babel/plugin-syntax-numeric-separator/download/@babel/plugin-syntax-numeric-separator-7.10.4.tgz"
  "version" "7.10.4"
  dependencies:
    "@babel/helper-plugin-utils" "^7.10.4"

"@babel/plugin-syntax-object-rest-spread@^7.8.3":
  "integrity" "sha1-YOIl7cvZimQDMqLnLdPmbxr1WHE="
  "resolved" "https://registry.npmmirror.com/@babel/plugin-syntax-object-rest-spread/download/@babel/plugin-syntax-object-rest-spread-7.8.3.tgz"
  "version" "7.8.3"
  dependencies:
    "@babel/helper-plugin-utils" "^7.8.0"

"@babel/plugin-syntax-optional-catch-binding@^7.8.3":
  "integrity" "sha1-YRGiZbz7Ag6579D9/X0mQCue1sE="
  "resolved" "https://registry.npmmirror.com/@babel/plugin-syntax-optional-catch-binding/download/@babel/plugin-syntax-optional-catch-binding-7.8.3.tgz"
  "version" "7.8.3"
  dependencies:
    "@babel/helper-plugin-utils" "^7.8.0"

"@babel/plugin-syntax-optional-chaining@^7.8.3":
  "integrity" "sha1-T2nCq5UWfgGAzVM2YT+MV4j31Io="
  "resolved" "https://registry.npmmirror.com/@babel/plugin-syntax-optional-chaining/download/@babel/plugin-syntax-optional-chaining-7.8.3.tgz"
  "version" "7.8.3"
  dependencies:
    "@babel/helper-plugin-utils" "^7.8.0"

"@babel/plugin-syntax-private-property-in-object@^7.14.5":
  "integrity" "sha1-DcZnHsDqIrbpShEU+FeXDNOd4a0="
  "resolved" "https://registry.npmmirror.com/@babel/plugin-syntax-private-property-in-object/download/@babel/plugin-syntax-private-property-in-object-7.14.5.tgz"
  "version" "7.14.5"
  dependencies:
    "@babel/helper-plugin-utils" "^7.14.5"

"@babel/plugin-syntax-top-level-await@^7.14.5":
  "integrity" "sha1-wc/a3DWmRiQAAfBhOCR7dBw02Uw="
  "resolved" "https://registry.npmmirror.com/@babel/plugin-syntax-top-level-await/download/@babel/plugin-syntax-top-level-await-7.14.5.tgz"
  "version" "7.14.5"
  dependencies:
    "@babel/helper-plugin-utils" "^7.14.5"

"@babel/plugin-transform-arrow-functions@^7.16.0":
  "integrity" "sha1-lRcG+LRJyDTtB71HTAkkyUS5Wo4="
  "resolved" "https://registry.npmmirror.com/@babel/plugin-transform-arrow-functions/download/@babel/plugin-transform-arrow-functions-7.16.0.tgz"
  "version" "7.16.0"
  dependencies:
    "@babel/helper-plugin-utils" "^7.14.5"

"@babel/plugin-transform-async-to-generator@^7.16.0":
  "integrity" "sha1-3xJjf5Yw3foO+dehG8QU1inThgQ="
  "resolved" "https://registry.npmmirror.com/@babel/plugin-transform-async-to-generator/download/@babel/plugin-transform-async-to-generator-7.16.0.tgz"
  "version" "7.16.0"
  dependencies:
    "@babel/helper-module-imports" "^7.16.0"
    "@babel/helper-plugin-utils" "^7.14.5"
    "@babel/helper-remap-async-to-generator" "^7.16.0"

"@babel/plugin-transform-block-scoped-functions@^7.16.0":
  "integrity" "sha1-xhh2MjOtAoR4BavKxMNFzp3nFF0="
  "resolved" "https://registry.npmmirror.com/@babel/plugin-transform-block-scoped-functions/download/@babel/plugin-transform-block-scoped-functions-7.16.0.tgz"
  "version" "7.16.0"
  dependencies:
    "@babel/helper-plugin-utils" "^7.14.5"

"@babel/plugin-transform-block-scoping@^7.16.0":
  "integrity" "sha1-vPQz+0gv6MPTtOimaxxKjnfTfBY="
  "resolved" "https://registry.npmmirror.com/@babel/plugin-transform-block-scoping/download/@babel/plugin-transform-block-scoping-7.16.0.tgz"
  "version" "7.16.0"
  dependencies:
    "@babel/helper-plugin-utils" "^7.14.5"

"@babel/plugin-transform-classes@^7.16.0":
  "integrity" "sha1-VM9f8LIkLGVz11PNS/xwd6iygvU="
  "resolved" "https://registry.npmmirror.com/@babel/plugin-transform-classes/download/@babel/plugin-transform-classes-7.16.0.tgz"
  "version" "7.16.0"
  dependencies:
    "@babel/helper-annotate-as-pure" "^7.16.0"
    "@babel/helper-function-name" "^7.16.0"
    "@babel/helper-optimise-call-expression" "^7.16.0"
    "@babel/helper-plugin-utils" "^7.14.5"
    "@babel/helper-replace-supers" "^7.16.0"
    "@babel/helper-split-export-declaration" "^7.16.0"
    "globals" "^11.1.0"

"@babel/plugin-transform-computed-properties@^7.16.0":
  "integrity" "sha1-4MOFUH0h4bCwdtZr7W1SMbhRELc="
  "resolved" "https://registry.npmmirror.com/@babel/plugin-transform-computed-properties/download/@babel/plugin-transform-computed-properties-7.16.0.tgz"
  "version" "7.16.0"
  dependencies:
    "@babel/helper-plugin-utils" "^7.14.5"

"@babel/plugin-transform-destructuring@^7.16.0":
  "integrity" "sha1-rT1+dFhK1epOrbHmZCFGxZDe4zw="
  "resolved" "https://registry.npmmirror.com/@babel/plugin-transform-destructuring/download/@babel/plugin-transform-destructuring-7.16.0.tgz"
  "version" "7.16.0"
  dependencies:
    "@babel/helper-plugin-utils" "^7.14.5"

"@babel/plugin-transform-dotall-regex@^7.16.0", "@babel/plugin-transform-dotall-regex@^7.4.4":
  "integrity" "sha1-ULqwDBCEthYtClioGAMc9XeY4G8="
  "resolved" "https://registry.npmmirror.com/@babel/plugin-transform-dotall-regex/download/@babel/plugin-transform-dotall-regex-7.16.0.tgz?cache=0&sync_timestamp=1635567493193&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2F%40babel%2Fplugin-transform-dotall-regex%2Fdownload%2F%40babel%2Fplugin-transform-dotall-regex-7.16.0.tgz"
  "version" "7.16.0"
  dependencies:
    "@babel/helper-create-regexp-features-plugin" "^7.16.0"
    "@babel/helper-plugin-utils" "^7.14.5"

"@babel/plugin-transform-duplicate-keys@^7.16.0":
  "integrity" "sha1-i8LiGBPj6J5eW/O2CqX8RYV1oXY="
  "resolved" "https://registry.npmmirror.com/@babel/plugin-transform-duplicate-keys/download/@babel/plugin-transform-duplicate-keys-7.16.0.tgz?cache=0&sync_timestamp=1635566910658&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2F%40babel%2Fplugin-transform-duplicate-keys%2Fdownload%2F%40babel%2Fplugin-transform-duplicate-keys-7.16.0.tgz"
  "version" "7.16.0"
  dependencies:
    "@babel/helper-plugin-utils" "^7.14.5"

"@babel/plugin-transform-exponentiation-operator@^7.16.0":
  "integrity" "sha1-oYDNKIHjUzzvnTkB5I2tD77/S+Q="
  "resolved" "https://registry.npmmirror.com/@babel/plugin-transform-exponentiation-operator/download/@babel/plugin-transform-exponentiation-operator-7.16.0.tgz"
  "version" "7.16.0"
  dependencies:
    "@babel/helper-builder-binary-assignment-operator-visitor" "^7.16.0"
    "@babel/helper-plugin-utils" "^7.14.5"

"@babel/plugin-transform-for-of@^7.16.0":
  "integrity" "sha1-96us7RVSYOJGE1m7x8ckispea9I="
  "resolved" "https://registry.npmmirror.com/@babel/plugin-transform-for-of/download/@babel/plugin-transform-for-of-7.16.0.tgz"
  "version" "7.16.0"
  dependencies:
    "@babel/helper-plugin-utils" "^7.14.5"

"@babel/plugin-transform-function-name@^7.16.0":
  "integrity" "sha1-AuNpnChMYmIjZZn3UQZcXV8fQA4="
  "resolved" "https://registry.npmmirror.com/@babel/plugin-transform-function-name/download/@babel/plugin-transform-function-name-7.16.0.tgz"
  "version" "7.16.0"
  dependencies:
    "@babel/helper-function-name" "^7.16.0"
    "@babel/helper-plugin-utils" "^7.14.5"

"@babel/plugin-transform-literals@^7.16.0":
  "integrity" "sha1-eXEeZw/86zG9KYIp1Q82IfeYDKw="
  "resolved" "https://registry.npmmirror.com/@babel/plugin-transform-literals/download/@babel/plugin-transform-literals-7.16.0.tgz"
  "version" "7.16.0"
  dependencies:
    "@babel/helper-plugin-utils" "^7.14.5"

"@babel/plugin-transform-member-expression-literals@^7.16.0":
  "integrity" "sha1-UlG0zOAer4MUQD0hrtsmnXn15ks="
  "resolved" "https://registry.npmmirror.com/@babel/plugin-transform-member-expression-literals/download/@babel/plugin-transform-member-expression-literals-7.16.0.tgz"
  "version" "7.16.0"
  dependencies:
    "@babel/helper-plugin-utils" "^7.14.5"

"@babel/plugin-transform-modules-amd@^7.16.0":
  "integrity" "sha1-CavUHhjc9P1HnFmMHO97056xM34="
  "resolved" "https://registry.npmmirror.com/@babel/plugin-transform-modules-amd/download/@babel/plugin-transform-modules-amd-7.16.0.tgz?cache=0&sync_timestamp=1635566912764&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2F%40babel%2Fplugin-transform-modules-amd%2Fdownload%2F%40babel%2Fplugin-transform-modules-amd-7.16.0.tgz"
  "version" "7.16.0"
  dependencies:
    "@babel/helper-module-transforms" "^7.16.0"
    "@babel/helper-plugin-utils" "^7.14.5"
    "babel-plugin-dynamic-import-node" "^2.3.3"

"@babel/plugin-transform-modules-commonjs@^7.16.0":
  "integrity" "sha1-rdWOY4yN3Eh1vZqey1xZRhP2ySI="
  "resolved" "https://registry.npmmirror.com/@babel/plugin-transform-modules-commonjs/download/@babel/plugin-transform-modules-commonjs-7.16.0.tgz"
  "version" "7.16.0"
  dependencies:
    "@babel/helper-module-transforms" "^7.16.0"
    "@babel/helper-plugin-utils" "^7.14.5"
    "@babel/helper-simple-access" "^7.16.0"
    "babel-plugin-dynamic-import-node" "^2.3.3"

"@babel/plugin-transform-modules-systemjs@^7.16.0":
  "integrity" "sha1-qSzyQK/rYF9MoWZwRTAkQl5CHqQ="
  "resolved" "https://registry.npmmirror.com/@babel/plugin-transform-modules-systemjs/download/@babel/plugin-transform-modules-systemjs-7.16.0.tgz?cache=0&sync_timestamp=1635566913014&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2F%40babel%2Fplugin-transform-modules-systemjs%2Fdownload%2F%40babel%2Fplugin-transform-modules-systemjs-7.16.0.tgz"
  "version" "7.16.0"
  dependencies:
    "@babel/helper-hoist-variables" "^7.16.0"
    "@babel/helper-module-transforms" "^7.16.0"
    "@babel/helper-plugin-utils" "^7.14.5"
    "@babel/helper-validator-identifier" "^7.15.7"
    "babel-plugin-dynamic-import-node" "^2.3.3"

"@babel/plugin-transform-modules-umd@^7.16.0":
  "integrity" "sha1-GV8mwq1tajkbcIgO/84YzmJeBqc="
  "resolved" "https://registry.npmmirror.com/@babel/plugin-transform-modules-umd/download/@babel/plugin-transform-modules-umd-7.16.0.tgz?cache=0&sync_timestamp=1635566913244&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2F%40babel%2Fplugin-transform-modules-umd%2Fdownload%2F%40babel%2Fplugin-transform-modules-umd-7.16.0.tgz"
  "version" "7.16.0"
  dependencies:
    "@babel/helper-module-transforms" "^7.16.0"
    "@babel/helper-plugin-utils" "^7.14.5"

"@babel/plugin-transform-named-capturing-groups-regex@^7.16.0":
  "integrity" "sha1-09thzF1bl5hlWZZ81eqD5cMglso="
  "resolved" "https://registry.npmmirror.com/@babel/plugin-transform-named-capturing-groups-regex/download/@babel/plugin-transform-named-capturing-groups-regex-7.16.0.tgz?cache=0&sync_timestamp=1635566913695&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2F%40babel%2Fplugin-transform-named-capturing-groups-regex%2Fdownload%2F%40babel%2Fplugin-transform-named-capturing-groups-regex-7.16.0.tgz"
  "version" "7.16.0"
  dependencies:
    "@babel/helper-create-regexp-features-plugin" "^7.16.0"

"@babel/plugin-transform-new-target@^7.16.0":
  "integrity" "sha1-r4I6tXb3UiFaSZN3eaQcplglqzU="
  "resolved" "https://registry.npmmirror.com/@babel/plugin-transform-new-target/download/@babel/plugin-transform-new-target-7.16.0.tgz?cache=0&sync_timestamp=1635566913480&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2F%40babel%2Fplugin-transform-new-target%2Fdownload%2F%40babel%2Fplugin-transform-new-target-7.16.0.tgz"
  "version" "7.16.0"
  dependencies:
    "@babel/helper-plugin-utils" "^7.14.5"

"@babel/plugin-transform-object-super@^7.16.0":
  "integrity" "sha1-+yDVgG3GSRoGKWrBTqjo1v7dpys="
  "resolved" "https://registry.npmmirror.com/@babel/plugin-transform-object-super/download/@babel/plugin-transform-object-super-7.16.0.tgz?cache=0&sync_timestamp=1635567544813&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2F%40babel%2Fplugin-transform-object-super%2Fdownload%2F%40babel%2Fplugin-transform-object-super-7.16.0.tgz"
  "version" "7.16.0"
  dependencies:
    "@babel/helper-plugin-utils" "^7.14.5"
    "@babel/helper-replace-supers" "^7.16.0"

"@babel/plugin-transform-parameters@^7.16.0", "@babel/plugin-transform-parameters@^7.16.3":
  "integrity" "sha512-3MaDpJrOXT1MZ/WCmkOFo7EtmVVC8H4EUZVrHvFOsmwkk4lOjQj8rzv8JKUZV4YoQKeoIgk07GO+acPU9IMu/w=="
  "resolved" "https://registry.npmmirror.com/@babel/plugin-transform-parameters/download/@babel/plugin-transform-parameters-7.16.3.tgz"
  "version" "7.16.3"
  dependencies:
    "@babel/helper-plugin-utils" "^7.14.5"

"@babel/plugin-transform-property-literals@^7.16.0":
  "integrity" "sha1-qVxVIYmpagAFn2d23E4A42kMeNE="
  "resolved" "https://registry.npmmirror.com/@babel/plugin-transform-property-literals/download/@babel/plugin-transform-property-literals-7.16.0.tgz"
  "version" "7.16.0"
  dependencies:
    "@babel/helper-plugin-utils" "^7.14.5"

"@babel/plugin-transform-regenerator@^7.16.0":
  "integrity" "sha1-6u5CLISwIy0Drqfbmcl97q9hJaQ="
  "resolved" "https://registry.npmmirror.com/@babel/plugin-transform-regenerator/download/@babel/plugin-transform-regenerator-7.16.0.tgz?cache=0&sync_timestamp=1635567545407&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2F%40babel%2Fplugin-transform-regenerator%2Fdownload%2F%40babel%2Fplugin-transform-regenerator-7.16.0.tgz"
  "version" "7.16.0"
  dependencies:
    "regenerator-transform" "^0.14.2"

"@babel/plugin-transform-reserved-words@^7.16.0":
  "integrity" "sha1-//S53LGeEmGTlL2hctFPLQTAN5w="
  "resolved" "https://registry.npmmirror.com/@babel/plugin-transform-reserved-words/download/@babel/plugin-transform-reserved-words-7.16.0.tgz?cache=0&sync_timestamp=1635566914972&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2F%40babel%2Fplugin-transform-reserved-words%2Fdownload%2F%40babel%2Fplugin-transform-reserved-words-7.16.0.tgz"
  "version" "7.16.0"
  dependencies:
    "@babel/helper-plugin-utils" "^7.14.5"

"@babel/plugin-transform-runtime@^7.12.15":
  "integrity" "sha512-pru6+yHANMTukMtEZGC4fs7XPwg35v8sj5CIEmE+gEkFljFiVJxEWxx/7ZDkTK+iZRYo1bFXBtfIN95+K3cJ5A=="
  "resolved" "https://registry.npmmirror.com/@babel/plugin-transform-runtime/download/@babel/plugin-transform-runtime-7.16.4.tgz?cache=0&sync_timestamp=1637106640762&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2F%40babel%2Fplugin-transform-runtime%2Fdownload%2F%40babel%2Fplugin-transform-runtime-7.16.4.tgz"
  "version" "7.16.4"
  dependencies:
    "@babel/helper-module-imports" "^7.16.0"
    "@babel/helper-plugin-utils" "^7.14.5"
    "babel-plugin-polyfill-corejs2" "^0.3.0"
    "babel-plugin-polyfill-corejs3" "^0.4.0"
    "babel-plugin-polyfill-regenerator" "^0.3.0"
    "semver" "^6.3.0"

"@babel/plugin-transform-shorthand-properties@^7.16.0":
  "integrity" "sha1-CQNy4xQffMMk7XCz2vU3nfL6OE0="
  "resolved" "https://registry.npmmirror.com/@babel/plugin-transform-shorthand-properties/download/@babel/plugin-transform-shorthand-properties-7.16.0.tgz?cache=0&sync_timestamp=1635567545787&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2F%40babel%2Fplugin-transform-shorthand-properties%2Fdownload%2F%40babel%2Fplugin-transform-shorthand-properties-7.16.0.tgz"
  "version" "7.16.0"
  dependencies:
    "@babel/helper-plugin-utils" "^7.14.5"

"@babel/plugin-transform-spread@^7.16.0":
  "integrity" "sha1-0hygmbvVOrMHqGIeAZp70PQM3Ps="
  "resolved" "https://registry.npmmirror.com/@babel/plugin-transform-spread/download/@babel/plugin-transform-spread-7.16.0.tgz?cache=0&sync_timestamp=1635567545995&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2F%40babel%2Fplugin-transform-spread%2Fdownload%2F%40babel%2Fplugin-transform-spread-7.16.0.tgz"
  "version" "7.16.0"
  dependencies:
    "@babel/helper-plugin-utils" "^7.14.5"
    "@babel/helper-skip-transparent-expression-wrappers" "^7.16.0"

"@babel/plugin-transform-sticky-regex@^7.16.0":
  "integrity" "sha1-w16jGgLYa+SF9qpRAYS2d6kXOP0="
  "resolved" "https://registry.npmmirror.com/@babel/plugin-transform-sticky-regex/download/@babel/plugin-transform-sticky-regex-7.16.0.tgz?cache=0&sync_timestamp=1635567546180&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2F%40babel%2Fplugin-transform-sticky-regex%2Fdownload%2F%40babel%2Fplugin-transform-sticky-regex-7.16.0.tgz"
  "version" "7.16.0"
  dependencies:
    "@babel/helper-plugin-utils" "^7.14.5"

"@babel/plugin-transform-template-literals@^7.16.0":
  "integrity" "sha1-qOztOo57ji1A7E7EVIpFkSYw0wI="
  "resolved" "https://registry.npmmirror.com/@babel/plugin-transform-template-literals/download/@babel/plugin-transform-template-literals-7.16.0.tgz?cache=0&sync_timestamp=1635567546366&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2F%40babel%2Fplugin-transform-template-literals%2Fdownload%2F%40babel%2Fplugin-transform-template-literals-7.16.0.tgz"
  "version" "7.16.0"
  dependencies:
    "@babel/helper-plugin-utils" "^7.14.5"

"@babel/plugin-transform-typeof-symbol@^7.16.0":
  "integrity" "sha1-ixmiRMb4ydZo3Kam91Stbq0RKPI="
  "resolved" "https://registry.npmmirror.com/@babel/plugin-transform-typeof-symbol/download/@babel/plugin-transform-typeof-symbol-7.16.0.tgz?cache=0&sync_timestamp=1635566920452&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2F%40babel%2Fplugin-transform-typeof-symbol%2Fdownload%2F%40babel%2Fplugin-transform-typeof-symbol-7.16.0.tgz"
  "version" "7.16.0"
  dependencies:
    "@babel/helper-plugin-utils" "^7.14.5"

"@babel/plugin-transform-unicode-escapes@^7.16.0":
  "integrity" "sha1-GjVAZLTEVmOjIzT0b6DPYQC1sfM="
  "resolved" "https://registry.npmmirror.com/@babel/plugin-transform-unicode-escapes/download/@babel/plugin-transform-unicode-escapes-7.16.0.tgz?cache=0&sync_timestamp=1635566920674&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2F%40babel%2Fplugin-transform-unicode-escapes%2Fdownload%2F%40babel%2Fplugin-transform-unicode-escapes-7.16.0.tgz"
  "version" "7.16.0"
  dependencies:
    "@babel/helper-plugin-utils" "^7.14.5"

"@babel/plugin-transform-unicode-regex@^7.16.0":
  "integrity" "sha1-KTuAlQF3yMha7eh87ygCWfuZVAI="
  "resolved" "https://registry.npmmirror.com/@babel/plugin-transform-unicode-regex/download/@babel/plugin-transform-unicode-regex-7.16.0.tgz?cache=0&sync_timestamp=1635567547071&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2F%40babel%2Fplugin-transform-unicode-regex%2Fdownload%2F%40babel%2Fplugin-transform-unicode-regex-7.16.0.tgz"
  "version" "7.16.0"
  dependencies:
    "@babel/helper-create-regexp-features-plugin" "^7.16.0"
    "@babel/helper-plugin-utils" "^7.14.5"

"@babel/preset-env@^7.12.16":
  "integrity" "sha512-v0QtNd81v/xKj4gNKeuAerQ/azeNn/G1B1qMLeXOcV8+4TWlD2j3NV1u8q29SDFBXx/NBq5kyEAO+0mpRgacjA=="
  "resolved" "https://registry.npmmirror.com/@babel/preset-env/download/@babel/preset-env-7.16.4.tgz?cache=0&sync_timestamp=1637106640412&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2F%40babel%2Fpreset-env%2Fdownload%2F%40babel%2Fpreset-env-7.16.4.tgz"
  "version" "7.16.4"
  dependencies:
    "@babel/compat-data" "^7.16.4"
    "@babel/helper-compilation-targets" "^7.16.3"
    "@babel/helper-plugin-utils" "^7.14.5"
    "@babel/helper-validator-option" "^7.14.5"
    "@babel/plugin-bugfix-safari-id-destructuring-collision-in-function-expression" "^7.16.2"
    "@babel/plugin-bugfix-v8-spread-parameters-in-optional-chaining" "^7.16.0"
    "@babel/plugin-proposal-async-generator-functions" "^7.16.4"
    "@babel/plugin-proposal-class-properties" "^7.16.0"
    "@babel/plugin-proposal-class-static-block" "^7.16.0"
    "@babel/plugin-proposal-dynamic-import" "^7.16.0"
    "@babel/plugin-proposal-export-namespace-from" "^7.16.0"
    "@babel/plugin-proposal-json-strings" "^7.16.0"
    "@babel/plugin-proposal-logical-assignment-operators" "^7.16.0"
    "@babel/plugin-proposal-nullish-coalescing-operator" "^7.16.0"
    "@babel/plugin-proposal-numeric-separator" "^7.16.0"
    "@babel/plugin-proposal-object-rest-spread" "^7.16.0"
    "@babel/plugin-proposal-optional-catch-binding" "^7.16.0"
    "@babel/plugin-proposal-optional-chaining" "^7.16.0"
    "@babel/plugin-proposal-private-methods" "^7.16.0"
    "@babel/plugin-proposal-private-property-in-object" "^7.16.0"
    "@babel/plugin-proposal-unicode-property-regex" "^7.16.0"
    "@babel/plugin-syntax-async-generators" "^7.8.4"
    "@babel/plugin-syntax-class-properties" "^7.12.13"
    "@babel/plugin-syntax-class-static-block" "^7.14.5"
    "@babel/plugin-syntax-dynamic-import" "^7.8.3"
    "@babel/plugin-syntax-export-namespace-from" "^7.8.3"
    "@babel/plugin-syntax-json-strings" "^7.8.3"
    "@babel/plugin-syntax-logical-assignment-operators" "^7.10.4"
    "@babel/plugin-syntax-nullish-coalescing-operator" "^7.8.3"
    "@babel/plugin-syntax-numeric-separator" "^7.10.4"
    "@babel/plugin-syntax-object-rest-spread" "^7.8.3"
    "@babel/plugin-syntax-optional-catch-binding" "^7.8.3"
    "@babel/plugin-syntax-optional-chaining" "^7.8.3"
    "@babel/plugin-syntax-private-property-in-object" "^7.14.5"
    "@babel/plugin-syntax-top-level-await" "^7.14.5"
    "@babel/plugin-transform-arrow-functions" "^7.16.0"
    "@babel/plugin-transform-async-to-generator" "^7.16.0"
    "@babel/plugin-transform-block-scoped-functions" "^7.16.0"
    "@babel/plugin-transform-block-scoping" "^7.16.0"
    "@babel/plugin-transform-classes" "^7.16.0"
    "@babel/plugin-transform-computed-properties" "^7.16.0"
    "@babel/plugin-transform-destructuring" "^7.16.0"
    "@babel/plugin-transform-dotall-regex" "^7.16.0"
    "@babel/plugin-transform-duplicate-keys" "^7.16.0"
    "@babel/plugin-transform-exponentiation-operator" "^7.16.0"
    "@babel/plugin-transform-for-of" "^7.16.0"
    "@babel/plugin-transform-function-name" "^7.16.0"
    "@babel/plugin-transform-literals" "^7.16.0"
    "@babel/plugin-transform-member-expression-literals" "^7.16.0"
    "@babel/plugin-transform-modules-amd" "^7.16.0"
    "@babel/plugin-transform-modules-commonjs" "^7.16.0"
    "@babel/plugin-transform-modules-systemjs" "^7.16.0"
    "@babel/plugin-transform-modules-umd" "^7.16.0"
    "@babel/plugin-transform-named-capturing-groups-regex" "^7.16.0"
    "@babel/plugin-transform-new-target" "^7.16.0"
    "@babel/plugin-transform-object-super" "^7.16.0"
    "@babel/plugin-transform-parameters" "^7.16.3"
    "@babel/plugin-transform-property-literals" "^7.16.0"
    "@babel/plugin-transform-regenerator" "^7.16.0"
    "@babel/plugin-transform-reserved-words" "^7.16.0"
    "@babel/plugin-transform-shorthand-properties" "^7.16.0"
    "@babel/plugin-transform-spread" "^7.16.0"
    "@babel/plugin-transform-sticky-regex" "^7.16.0"
    "@babel/plugin-transform-template-literals" "^7.16.0"
    "@babel/plugin-transform-typeof-symbol" "^7.16.0"
    "@babel/plugin-transform-unicode-escapes" "^7.16.0"
    "@babel/plugin-transform-unicode-regex" "^7.16.0"
    "@babel/preset-modules" "^0.1.5"
    "@babel/types" "^7.16.0"
    "babel-plugin-polyfill-corejs2" "^0.3.0"
    "babel-plugin-polyfill-corejs3" "^0.4.0"
    "babel-plugin-polyfill-regenerator" "^0.3.0"
    "core-js-compat" "^3.19.1"
    "semver" "^6.3.0"

"@babel/preset-modules@^0.1.5":
  "integrity" "sha1-75Odbn8miCfhhBY43G/5VRXhFdk="
  "resolved" "https://registry.npmmirror.com/@babel/preset-modules/download/@babel/preset-modules-0.1.5.tgz"
  "version" "0.1.5"
  dependencies:
    "@babel/helper-plugin-utils" "^7.0.0"
    "@babel/plugin-proposal-unicode-property-regex" "^7.4.4"
    "@babel/plugin-transform-dotall-regex" "^7.4.4"
    "@babel/types" "^7.4.4"
    "esutils" "^2.0.2"

"@babel/runtime@^7.0.0", "@babel/runtime@^7.12.13", "@babel/runtime@^7.17.2", "@babel/runtime@^7.8.4":
  "integrity" "sha512-8jI69toZqqcsnqGGqwGS4Qb1VwLOEp4hz+CXPywcvjs60u3B4Pom/U/7rm4W8tMOYEB+E9wgD0mW1l3r8qlI9Q=="
  "resolved" "http://castle-npm.cp.hxdi.cn/@babel%2fruntime/-/runtime-7.21.5.tgz"
  "version" "7.21.5"
  dependencies:
    "regenerator-runtime" "^0.13.11"

"@babel/template@^7.0.0", "@babel/template@^7.16.0":
  "integrity" "sha1-0Wo16/TNdOICCDNW+rId2JNj3dY="
  "resolved" "https://registry.npmmirror.com/@babel/template/download/@babel/template-7.16.0.tgz?cache=0&sync_timestamp=1635561093969&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2F%40babel%2Ftemplate%2Fdownload%2F%40babel%2Ftemplate-7.16.0.tgz"
  "version" "7.16.0"
  dependencies:
    "@babel/code-frame" "^7.16.0"
    "@babel/parser" "^7.16.0"
    "@babel/types" "^7.16.0"

"@babel/traverse@^7.0.0", "@babel/traverse@^7.13.0", "@babel/traverse@^7.16.0", "@babel/traverse@^7.16.3":
  "integrity" "sha512-eolumr1vVMjqevCpwVO99yN/LoGL0EyHiLO5I043aYQvwOJ9eR5UsZSClHVCzfhBduMAsSzgA/6AyqPjNayJag=="
  "resolved" "https://registry.npmmirror.com/@babel/traverse/download/@babel/traverse-7.16.3.tgz"
  "version" "7.16.3"
  dependencies:
    "@babel/code-frame" "^7.16.0"
    "@babel/generator" "^7.16.0"
    "@babel/helper-function-name" "^7.16.0"
    "@babel/helper-hoist-variables" "^7.16.0"
    "@babel/helper-split-export-declaration" "^7.16.0"
    "@babel/parser" "^7.16.3"
    "@babel/types" "^7.16.0"
    "debug" "^4.1.0"
    "globals" "^11.1.0"

"@babel/types@^7.0.0", "@babel/types@^7.16.0", "@babel/types@^7.4.4":
  "integrity" "sha1-2zsxOAT5aq3Qt3bEgj4SetZyibo="
  "resolved" "https://registry.npmmirror.com/@babel/types/download/@babel/types-7.16.0.tgz?cache=0&sync_timestamp=1635561094640&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2F%40babel%2Ftypes%2Fdownload%2F%40babel%2Ftypes-7.16.0.tgz"
  "version" "7.16.0"
  dependencies:
    "@babel/helper-validator-identifier" "^7.15.7"
    "to-fast-properties" "^2.0.0"

"@eslint/eslintrc@^0.4.3":
  "integrity" "sha1-nkKYHvA1vrPdSa3ResuW6P9vOUw="
  "resolved" "https://registry.npmmirror.com/@eslint/eslintrc/download/@eslint/eslintrc-0.4.3.tgz"
  "version" "0.4.3"
  dependencies:
    "ajv" "^6.12.4"
    "debug" "^4.1.1"
    "espree" "^7.3.0"
    "globals" "^13.9.0"
    "ignore" "^4.0.6"
    "import-fresh" "^3.2.1"
    "js-yaml" "^3.13.1"
    "minimatch" "^3.0.4"
    "strip-json-comments" "^3.1.1"

"@hapi/hoek@^9.0.0":
  "integrity" "sha1-lVEUKhmAUDdSU2tQUP2Z9KfxOxc="
  "resolved" "https://registry.npmmirror.com/@hapi/hoek/download/@hapi/hoek-9.2.1.tgz"
  "version" "9.2.1"

"@hapi/topo@^5.0.0":
  "integrity" "sha1-3ESOMyxsbjek3AL9hLqNRLmvsBI="
  "resolved" "https://registry.npmmirror.com/@hapi/topo/download/@hapi/topo-5.1.0.tgz"
  "version" "5.1.0"
  dependencies:
    "@hapi/hoek" "^9.0.0"

"@humanwhocodes/config-array@^0.5.0":
  "integrity" "sha1-FAeWfUxu7Nc4j4Os8er00Mbljvk="
  "resolved" "https://registry.npmmirror.com/@humanwhocodes/config-array/download/@humanwhocodes/config-array-0.5.0.tgz"
  "version" "0.5.0"
  dependencies:
    "@humanwhocodes/object-schema" "^1.2.0"
    "debug" "^4.1.1"
    "minimatch" "^3.0.4"

"@humanwhocodes/object-schema@^1.2.0":
  "integrity" "sha1-tSBSnsIdjllFoYUd/Rwy6U45/0U="
  "resolved" "https://registry.npmmirror.com/@humanwhocodes/object-schema/download/@humanwhocodes/object-schema-1.2.1.tgz"
  "version" "1.2.1"

"@jridgewell/gen-mapping@^0.3.2":
  "integrity" "sha512-HLhSWOLRi875zjjMG/r+Nv0oCW8umGb0BgEhyX3dDX3egwZtB8PqLnjz3yedt8R5StBrzcg4aBpnh8UA9D1BoQ=="
  "resolved" "http://castle-npm.cp.hxdi.cn/@jridgewell%2fgen-mapping/-/gen-mapping-0.3.3.tgz"
  "version" "0.3.3"
  dependencies:
    "@jridgewell/set-array" "^1.0.1"
    "@jridgewell/sourcemap-codec" "^1.4.10"
    "@jridgewell/trace-mapping" "^0.3.9"

"@jridgewell/resolve-uri@^3.1.0":
  "integrity" "sha512-dSYZh7HhCDtCKm4QakX0xFpsRDqjjtZf/kjI/v3T3Nwt5r8/qz/M19F9ySyOqU94SXBmeG9ttTul+YnR4LOxFA=="
  "resolved" "http://castle-npm.cp.hxdi.cn/@jridgewell%2fresolve-uri/-/resolve-uri-3.1.1.tgz"
  "version" "3.1.1"

"@jridgewell/set-array@^1.0.1":
  "integrity" "sha512-xnkseuNADM0gt2bs+BvhO0p78Mk762YnZdsuzFV018NoG1Sj1SCQvpSqa7XUaTam5vAGasABV9qXASMKnFMwMw=="
  "resolved" "http://castle-npm.cp.hxdi.cn/@jridgewell%2fset-array/-/set-array-1.1.2.tgz"
  "version" "1.1.2"

"@jridgewell/sourcemap-codec@^1.4.10", "@jridgewell/sourcemap-codec@^1.4.14", "@jridgewell/sourcemap-codec@^1.4.15":
  "integrity" "sha512-eF2rxCRulEKXHTRiDrDy6erMYWqNw4LPdQ8UQA4huuxaQsVeRPFl2oM8oDGxMFhJUWZf9McpLtJasDDZb/Bpeg=="
  "resolved" "http://castle-npm.cp.hxdi.cn/@jridgewell%2fsourcemap-codec/-/sourcemap-codec-1.4.15.tgz"
  "version" "1.4.15"

"@jridgewell/trace-mapping@^0.3.9":
  "integrity" "sha512-kf37QtfW+Hwx/buWGMPcR60iF9ziHa6r/CZJIHbmcm4+0qrXiVdxegAH0F6yddEVQ7zdkjcGCgCzUu+BcbhQxw=="
  "resolved" "http://castle-npm.cp.hxdi.cn/@jridgewell%2ftrace-mapping/-/trace-mapping-0.3.19.tgz"
  "version" "0.3.19"
  dependencies:
    "@jridgewell/resolve-uri" "^3.1.0"
    "@jridgewell/sourcemap-codec" "^1.4.14"

"@nodelib/fs.scandir@2.1.5":
  "integrity" "sha1-dhnC6yGyVIP20WdUi0z9WnSIw9U="
  "resolved" "https://registry.npmmirror.com/@nodelib/fs.scandir/download/@nodelib/fs.scandir-2.1.5.tgz"
  "version" "2.1.5"
  dependencies:
    "@nodelib/fs.stat" "2.0.5"
    "run-parallel" "^1.1.9"

"@nodelib/fs.stat@^2.0.2", "@nodelib/fs.stat@2.0.5":
  "integrity" "sha1-W9Jir5Tp0lvR5xsF3u1Eh2oiLos="
  "resolved" "https://registry.npmmirror.com/@nodelib/fs.stat/download/@nodelib/fs.stat-2.0.5.tgz"
  "version" "2.0.5"

"@nodelib/fs.walk@^1.2.3":
  "integrity" "sha1-6Vc36LtnRt3t9pxVaVNJTxlv5po="
  "resolved" "https://registry.npmmirror.com/@nodelib/fs.walk/download/@nodelib/fs.walk-1.2.8.tgz"
  "version" "1.2.8"
  dependencies:
    "@nodelib/fs.scandir" "2.1.5"
    "fastq" "^1.6.0"

"@polka/url@^1.0.0-next.20":
  "integrity" "sha1-XeWiOFo1MJQn9gEZkrVEUU1VmqE="
  "resolved" "https://registry.npmmirror.com/@polka/url/download/@polka/url-1.0.0-next.21.tgz"
  "version" "1.0.0-next.21"

"@popperjs/core@^2.9.2":
  "integrity" "sha512-zrsUxjLOKAzdewIDRWy9nsV1GQsKBCWaGwsZQlCgr6/q+vjyZhFgqedLfFBuI9anTPEUT4APq9Mu0SZBTzIcGQ=="
  "resolved" "https://registry.npmmirror.com/@popperjs/core/download/@popperjs/core-2.11.0.tgz"
  "version" "2.11.0"

"@rollup/pluginutils@^5.0.2":
  "integrity" "sha512-0KJnIoRI8A+a1dqOYLxH8vBf8bphDmty5QvIm2hqm7oFCFYKCAZWWd2hXgMibaPsNDhI0AtpYfQZJG47pt/k4g=="
  "resolved" "http://castle-npm.cp.hxdi.cn/@rollup%2fpluginutils/-/pluginutils-5.0.4.tgz"
  "version" "5.0.4"
  dependencies:
    "@types/estree" "^1.0.0"
    "estree-walker" "^2.0.2"
    "picomatch" "^2.3.1"

"@sideway/address@^4.1.3":
  "integrity" "sha512-8ncEUtmnTsMmL7z1YPB47kPUq7LpKWJNFPsRzHiIajGC5uXlWGn+AmkYPcHNl8S4tcEGx+cnORnNYaw2wvL+LQ=="
  "resolved" "https://registry.npmmirror.com/@sideway/address/download/@sideway/address-4.1.3.tgz"
  "version" "4.1.3"
  dependencies:
    "@hapi/hoek" "^9.0.0"

"@sideway/formula@^3.0.0":
  "integrity" "sha1-/hWK7jLmvV3oUES+YVvAhHigoTw="
  "resolved" "https://registry.npmmirror.com/@sideway/formula/download/@sideway/formula-3.0.0.tgz"
  "version" "3.0.0"

"@sideway/pinpoint@^2.0.0":
  "integrity" "sha1-z/j/rcNyrSn9P3gneusp5jLMcN8="
  "resolved" "https://registry.npmmirror.com/@sideway/pinpoint/download/@sideway/pinpoint-2.0.0.tgz"
  "version" "2.0.0"

"@soda/friendly-errors-webpack-plugin@^1.8.0":
  "integrity" "sha512-h2ooWqP8XuFqTXT+NyAFbrArzfQA7R6HTezADrvD9Re8fxMLTPPniLdqVTdDaO0eIoLaAwKT+d6w+5GeTk7Vbg=="
  "resolved" "https://registry.npmmirror.com/@soda/friendly-errors-webpack-plugin/download/@soda/friendly-errors-webpack-plugin-1.8.1.tgz?cache=0&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2F%40soda%2Ffriendly-errors-webpack-plugin%2Fdownload%2F%40soda%2Ffriendly-errors-webpack-plugin-1.8.1.tgz"
  "version" "1.8.1"
  dependencies:
    "chalk" "^3.0.0"
    "error-stack-parser" "^2.0.6"
    "string-width" "^4.2.3"
    "strip-ansi" "^6.0.1"

"@soda/get-current-script@^1.0.2":
  "integrity" "sha1-pTUV2yXYA4N0OBtzryC7Ty5QjYc="
  "resolved" "https://registry.npmmirror.com/@soda/get-current-script/download/@soda/get-current-script-1.0.2.tgz"
  "version" "1.0.2"

"@trysound/sax@0.2.0":
  "integrity" "sha1-zMqrdYr1Z2Hre/N69vA/Mm3XmK0="
  "resolved" "https://registry.npmmirror.com/@trysound/sax/download/@trysound/sax-0.2.0.tgz"
  "version" "0.2.0"

"@types/body-parser@*":
  "integrity" "sha512-ALYone6pm6QmwZoAgeyNksccT9Q4AWZQ6PvfwR37GT6r6FWUPguq6sUmNGSMV2Wr761oQoBxwGGa6DR5o1DC9g=="
  "resolved" "https://registry.npmmirror.com/@types/body-parser/download/@types/body-parser-1.19.2.tgz?cache=0&sync_timestamp=1637267126594&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2F%40types%2Fbody-parser%2Fdownload%2F%40types%2Fbody-parser-1.19.2.tgz"
  "version" "1.19.2"
  dependencies:
    "@types/connect" "*"
    "@types/node" "*"

"@types/bonjour@*":
  "integrity" "sha1-PMTlE127WUD8YFFgSAkjRhL4nLQ="
  "resolved" "https://registry.npmmirror.com/@types/bonjour/download/@types/bonjour-3.5.9.tgz"
  "version" "3.5.9"
  dependencies:
    "@types/node" "*"

"@types/connect-history-api-fallback@*":
  "integrity" "sha1-0feooJ0O1aV67lrpwYq5uAMgXa4="
  "resolved" "https://registry.npmmirror.com/@types/connect-history-api-fallback/download/@types/connect-history-api-fallback-1.3.5.tgz"
  "version" "1.3.5"
  dependencies:
    "@types/express-serve-static-core" "*"
    "@types/node" "*"

"@types/connect@*":
  "integrity" "sha1-X89q5EXkAh0fwiGaSHPMc6O7KtE="
  "resolved" "https://registry.npmmirror.com/@types/connect/download/@types/connect-3.4.35.tgz?cache=0&sync_timestamp=1637267103323&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2F%40types%2Fconnect%2Fdownload%2F%40types%2Fconnect-3.4.35.tgz"
  "version" "3.4.35"
  dependencies:
    "@types/node" "*"

"@types/cssnano@^4.0.1":
  "integrity" "sha1-Z/qRJ1PYCXOgFudoSkf+3zOKrP8="
  "resolved" "https://registry.npmmirror.com/@types/cssnano/download/@types/cssnano-4.0.1.tgz"
  "version" "4.0.1"
  dependencies:
    "postcss" "5 - 7"

"@types/eslint-scope@^3.7.0":
  "integrity" "sha1-jcOQp7T53Z8ShGKe/OmC5BYSEW4="
  "resolved" "https://registry.npmmirror.com/@types/eslint-scope/download/@types/eslint-scope-3.7.1.tgz"
  "version" "3.7.1"
  dependencies:
    "@types/eslint" "*"
    "@types/estree" "*"

"@types/eslint@*", "@types/eslint@^7.28.2":
  "integrity" "sha512-VNcvioYDH8/FxaeTKkM4/TiTwt6pBV9E3OfGmvaw8tPl0rrHCJ4Ll15HRT+pMiFAf/MLQvAzC+6RzUMEL9Ceng=="
  "resolved" "https://registry.npmmirror.com/@types/eslint/download/@types/eslint-7.29.0.tgz"
  "version" "7.29.0"
  dependencies:
    "@types/estree" "*"
    "@types/json-schema" "*"

"@types/estree@*", "@types/estree@^0.0.50":
  "integrity" "sha1-Hgyqk2TT/M0pMcPtlv2+ql1MyoM="
  "resolved" "https://registry.npmmirror.com/@types/estree/download/@types/estree-0.0.50.tgz"
  "version" "0.0.50"

"@types/estree@^1.0.0":
  "integrity" "sha512-LG4opVs2ANWZ1TJoKc937iMmNstM/d0ae1vNbnBvBhqCSezgVUOzcLCqbI5elV8Vy6WKwKjaqR+zO9VKirBBCA=="
  "resolved" "http://castle-npm.cp.hxdi.cn/@types%2festree/-/estree-1.0.1.tgz"
  "version" "1.0.1"

"@types/express-serve-static-core@*", "@types/express-serve-static-core@^4.17.18":
  "integrity" "sha512-zeu3tpouA043RHxW0gzRxwCHchMgftE8GArRsvYT0ByDMbn19olQHx5jLue0LxWY6iYtXb7rXmuVtSkhy9YZvQ=="
  "resolved" "https://registry.npmmirror.com/@types/express-serve-static-core/download/@types/express-serve-static-core-4.17.26.tgz"
  "version" "4.17.26"
  dependencies:
    "@types/node" "*"
    "@types/qs" "*"
    "@types/range-parser" "*"

"@types/express@*":
  "integrity" "sha1-p24plXKJmbq1GjP6vOHXBaNwkDQ="
  "resolved" "https://registry.npmmirror.com/@types/express/download/@types/express-4.17.13.tgz"
  "version" "4.17.13"
  dependencies:
    "@types/body-parser" "*"
    "@types/express-serve-static-core" "^4.17.18"
    "@types/qs" "*"
    "@types/serve-static" "*"

"@types/html-minifier-terser@^6.0.0":
  "integrity" "sha512-oh/6byDPnL1zeNXFrDXFLyZjkr1MsBG667IM792caf1L2UPOOMf65NFzjUH/ltyfwjAGfs1rsX1eftK0jC/KIg=="
  "resolved" "https://registry.npmmirror.com/@types/html-minifier-terser/download/@types/html-minifier-terser-6.1.0.tgz"
  "version" "6.1.0"

"@types/http-proxy@^1.17.5":
  "integrity" "sha1-MOqFzCyGg2g1Kjfw0NNYHiSDTG8="
  "resolved" "https://registry.npmmirror.com/@types/http-proxy/download/@types/http-proxy-1.17.7.tgz"
  "version" "1.17.7"
  dependencies:
    "@types/node" "*"

"@types/json-schema@*", "@types/json-schema@^7.0.5", "@types/json-schema@^7.0.8", "@types/json-schema@^7.0.9":
  "integrity" "sha1-l+3JA36gw4WFMgsolk3eOznkZg0="
  "resolved" "https://registry.npmmirror.com/@types/json-schema/download/@types/json-schema-7.0.9.tgz"
  "version" "7.0.9"

"@types/mime@^1":
  "integrity" "sha1-k+Jb+e51/g/YC1lLxP6w6GIRG1o="
  "resolved" "https://registry.npmmirror.com/@types/mime/download/@types/mime-1.3.2.tgz?cache=0&sync_timestamp=1637267508704&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2F%40types%2Fmime%2Fdownload%2F%40types%2Fmime-1.3.2.tgz"
  "version" "1.3.2"

"@types/minimist@^1.2.0":
  "integrity" "sha1-7nceK6Sz3Fs3KTXVSf2WF780W4w="
  "resolved" "https://registry.npmmirror.com/@types/minimist/download/@types/minimist-1.2.2.tgz"
  "version" "1.2.2"

"@types/node@*":
  "integrity" "sha512-+2Iggwg7PxoO5Kyhvsq9VarmPbIelXP070HMImEpbtGCoyWNINQj4wzjbQCXzdHTRXnqufutJb5KAURZANNBAw=="
  "resolved" "https://registry.npmmirror.com/@types/node/download/@types/node-16.11.12.tgz"
  "version" "16.11.12"

"@types/normalize-package-data@^2.4.0":
  "integrity" "sha1-0zV0eaD9/dWQf+Z+F+CoXJBuEwE="
  "resolved" "https://registry.npmmirror.com/@types/normalize-package-data/download/@types/normalize-package-data-2.4.1.tgz"
  "version" "2.4.1"

"@types/parse-json@^4.0.0":
  "integrity" "sha1-L4u0QUNNFjs1+4/9zNcTiSf/uMA="
  "resolved" "https://registry.npmmirror.com/@types/parse-json/download/@types/parse-json-4.0.0.tgz"
  "version" "4.0.0"

"@types/qs@*":
  "integrity" "sha1-Y7t9Bn2xB8weRXwwO8JdUR/r9ss="
  "resolved" "https://registry.npmmirror.com/@types/qs/download/@types/qs-6.9.7.tgz?cache=0&sync_timestamp=1637271732530&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2F%40types%2Fqs%2Fdownload%2F%40types%2Fqs-6.9.7.tgz"
  "version" "6.9.7"

"@types/range-parser@*":
  "integrity" "sha1-zWZ7z90CUhOq+3ylkVqTJZCs3Nw="
  "resolved" "https://registry.npmmirror.com/@types/range-parser/download/@types/range-parser-1.2.4.tgz?cache=0&sync_timestamp=1637271734639&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2F%40types%2Frange-parser%2Fdownload%2F%40types%2Frange-parser-1.2.4.tgz"
  "version" "1.2.4"

"@types/retry@^0.12.0":
  "integrity" "sha1-2PHA0Nwjr61twWqemToIZXdLQGU="
  "resolved" "https://registry.npmmirror.com/@types/retry/download/@types/retry-0.12.1.tgz?cache=0&sync_timestamp=1637271758191&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2F%40types%2Fretry%2Fdownload%2F%40types%2Fretry-0.12.1.tgz"
  "version" "0.12.1"

"@types/serve-index@*":
  "integrity" "sha1-G16FNwoZLAHsbOxHNc8pFzN6Yng="
  "resolved" "https://registry.npmmirror.com/@types/serve-index/download/@types/serve-index-1.9.1.tgz?cache=0&sync_timestamp=1637271751383&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2F%40types%2Fserve-index%2Fdownload%2F%40types%2Fserve-index-1.9.1.tgz"
  "version" "1.9.1"
  dependencies:
    "@types/express" "*"

"@types/serve-static@*":
  "integrity" "sha1-9eDOh5fS18xevtpIpSyWxPpHqNk="
  "resolved" "https://registry.npmmirror.com/@types/serve-static/download/@types/serve-static-1.13.10.tgz?cache=0&sync_timestamp=1637271751561&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2F%40types%2Fserve-static%2Fdownload%2F%40types%2Fserve-static-1.13.10.tgz"
  "version" "1.13.10"
  dependencies:
    "@types/mime" "^1"
    "@types/node" "*"

"@types/webpack-dev-middleware@*":
  "integrity" "sha1-D2ZWbCyn1ISJG0VSyKe2SjBE4+I="
  "resolved" "https://registry.npmmirror.com/@types/webpack-dev-middleware/download/@types/webpack-dev-middleware-5.0.2.tgz"
  "version" "5.0.2"
  dependencies:
    "@types/connect" "*"
    "tapable" "^2.1.1"
    "webpack" "^5.38.1"

"@types/webpack-dev-server@^4.1.0":
  "integrity" "sha512-HMb6pZPANObue3LwbdpQLWzQyF9O0wntiPyXj4vGutlAbNKTXH4hDCHaZyfvfZDmFn+5HprrWHm1TGt3awNr/A=="
  "resolved" "https://registry.npmmirror.com/@types/webpack-dev-server/download/@types/webpack-dev-server-4.5.0.tgz"
  "version" "4.5.0"
  dependencies:
    "@types/bonjour" "*"
    "@types/connect-history-api-fallback" "*"
    "@types/express" "*"
    "@types/serve-index" "*"
    "@types/serve-static" "*"
    "@types/webpack-dev-middleware" "*"
    "chokidar" "^3.5.1"
    "http-proxy-middleware" "^2.0.0"
    "webpack" "*"

"@vant/auto-import-resolver@^1.0.1":
  "integrity" "sha512-BmCM5eUONQGXY0DgpPJlzMBNyYzIbzkduI9FiEe6F5Tf0OfYRue5VwHXsBsnRq/RZoY+tiKZSAcyGreL+/8lQg=="
  "resolved" "http://castle-npm.cp.hxdi.cn/@vant%2fauto-import-resolver/-/auto-import-resolver-1.0.1.tgz"
  "version" "1.0.1"

"@vant/icons@^1.7.1":
  "integrity" "sha1-Sa5CAwK1WB5U5olIkeWgW8dun4c="
  "resolved" "https://registry.npmmirror.com/@vant/icons/download/@vant/icons-1.7.1.tgz"
  "version" "1.7.1"

"@vant/popperjs@^1.1.0":
  "integrity" "sha1-tO3uW7+m+xhwWYbjE9T9XxeUKg8="
  "resolved" "https://registry.npmmirror.com/@vant/popperjs/download/@vant/popperjs-1.1.0.tgz"
  "version" "1.1.0"
  dependencies:
    "@popperjs/core" "^2.9.2"

"@vant/use@^1.3.4":
  "integrity" "sha512-XvZkPCjcmEBhD+T3vB68thOG6P9jazld6aBTMenhbAQd4FT/x9AiKIWPJx4MvhYoSIWt7fju6K01XTJldWs1hw=="
  "resolved" "https://registry.npmmirror.com/@vant/use/download/@vant/use-1.3.4.tgz"
  "version" "1.3.4"

"@vue/babel-helper-vue-jsx-merge-props@^1.2.1":
  "integrity" "sha1-MWJKelBfsU2h1YAjclpMXycOaoE="
  "resolved" "https://registry.npmmirror.com/@vue/babel-helper-vue-jsx-merge-props/download/@vue/babel-helper-vue-jsx-merge-props-1.2.1.tgz"
  "version" "1.2.1"

"@vue/babel-helper-vue-transform-on@^1.0.2":
  "integrity" "sha1-m5xpHNBvyFUiGiR1w8yDHXdLx9w="
  "resolved" "https://registry.npmmirror.com/@vue/babel-helper-vue-transform-on/download/@vue/babel-helper-vue-transform-on-1.0.2.tgz"
  "version" "1.0.2"

"@vue/babel-plugin-jsx@^1.0.3":
  "integrity" "sha1-DFusJ4gNI/iYlM0Daje1XvYd38E="
  "resolved" "https://registry.npmmirror.com/@vue/babel-plugin-jsx/download/@vue/babel-plugin-jsx-1.1.1.tgz"
  "version" "1.1.1"
  dependencies:
    "@babel/helper-module-imports" "^7.0.0"
    "@babel/plugin-syntax-jsx" "^7.0.0"
    "@babel/template" "^7.0.0"
    "@babel/traverse" "^7.0.0"
    "@babel/types" "^7.0.0"
    "@vue/babel-helper-vue-transform-on" "^1.0.2"
    "camelcase" "^6.0.0"
    "html-tags" "^3.1.0"
    "svg-tags" "^1.0.0"

"@vue/babel-plugin-transform-vue-jsx@^1.2.1":
  "integrity" "sha1-ZGBGxlLC8CQnJ/NFGdkXsGQEHtc="
  "resolved" "https://registry.npmmirror.com/@vue/babel-plugin-transform-vue-jsx/download/@vue/babel-plugin-transform-vue-jsx-1.2.1.tgz"
  "version" "1.2.1"
  dependencies:
    "@babel/helper-module-imports" "^7.0.0"
    "@babel/plugin-syntax-jsx" "^7.2.0"
    "@vue/babel-helper-vue-jsx-merge-props" "^1.2.1"
    "html-tags" "^2.0.0"
    "lodash.kebabcase" "^4.1.1"
    "svg-tags" "^1.0.0"

"@vue/babel-preset-app@^5.0.0-rc.1":
  "integrity" "sha512-inJV1lxC+dUvqwPy9gbEjELX+Vk/Ys961S61aIn52gSCZBLHfk8/7mM7B3PamgHSG8yQmgrd3BOZsKSSsAMNJQ=="
  "resolved" "https://registry.npmmirror.com/@vue/babel-preset-app/download/@vue/babel-preset-app-5.0.0-rc.1.tgz?cache=0&sync_timestamp=1637121136778&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2F%40vue%2Fbabel-preset-app%2Fdownload%2F%40vue%2Fbabel-preset-app-5.0.0-rc.1.tgz"
  "version" "5.0.0-rc.1"
  dependencies:
    "@babel/core" "^7.12.16"
    "@babel/helper-compilation-targets" "^7.12.16"
    "@babel/helper-module-imports" "^7.12.13"
    "@babel/plugin-proposal-class-properties" "^7.12.13"
    "@babel/plugin-proposal-decorators" "^7.12.13"
    "@babel/plugin-syntax-dynamic-import" "^7.8.3"
    "@babel/plugin-syntax-jsx" "^7.12.13"
    "@babel/plugin-transform-runtime" "^7.12.15"
    "@babel/preset-env" "^7.12.16"
    "@babel/runtime" "^7.12.13"
    "@vue/babel-plugin-jsx" "^1.0.3"
    "@vue/babel-preset-jsx" "^1.1.2"
    "babel-plugin-dynamic-import-node" "^2.3.3"
    "core-js" "^3.8.3"
    "core-js-compat" "^3.8.3"
    "semver" "^7.3.4"

"@vue/babel-preset-jsx@^1.1.2":
  "integrity" "sha1-kv6nnbbxOwHoDToAmeKSS9y+Toc="
  "resolved" "https://registry.npmmirror.com/@vue/babel-preset-jsx/download/@vue/babel-preset-jsx-1.2.4.tgz"
  "version" "1.2.4"
  dependencies:
    "@vue/babel-helper-vue-jsx-merge-props" "^1.2.1"
    "@vue/babel-plugin-transform-vue-jsx" "^1.2.1"
    "@vue/babel-sugar-composition-api-inject-h" "^1.2.1"
    "@vue/babel-sugar-composition-api-render-instance" "^1.2.4"
    "@vue/babel-sugar-functional-vue" "^1.2.2"
    "@vue/babel-sugar-inject-h" "^1.2.2"
    "@vue/babel-sugar-v-model" "^1.2.3"
    "@vue/babel-sugar-v-on" "^1.2.3"

"@vue/babel-sugar-composition-api-inject-h@^1.2.1":
  "integrity" "sha1-BdbgxDJxDjdYKyvppgSbaJtvA+s="
  "resolved" "https://registry.npmmirror.com/@vue/babel-sugar-composition-api-inject-h/download/@vue/babel-sugar-composition-api-inject-h-1.2.1.tgz"
  "version" "1.2.1"
  dependencies:
    "@babel/plugin-syntax-jsx" "^7.2.0"

"@vue/babel-sugar-composition-api-render-instance@^1.2.4":
  "integrity" "sha1-5MvGmXw0T6wnF4WteikyXFHWjRk="
  "resolved" "https://registry.npmmirror.com/@vue/babel-sugar-composition-api-render-instance/download/@vue/babel-sugar-composition-api-render-instance-1.2.4.tgz"
  "version" "1.2.4"
  dependencies:
    "@babel/plugin-syntax-jsx" "^7.2.0"

"@vue/babel-sugar-functional-vue@^1.2.2":
  "integrity" "sha1-JnqayNeHyW7b8Dzj85LEnam9Jlg="
  "resolved" "https://registry.npmmirror.com/@vue/babel-sugar-functional-vue/download/@vue/babel-sugar-functional-vue-1.2.2.tgz"
  "version" "1.2.2"
  dependencies:
    "@babel/plugin-syntax-jsx" "^7.2.0"

"@vue/babel-sugar-inject-h@^1.2.2":
  "integrity" "sha1-1zjTyJM2fshJHcu2abAAkZKT46o="
  "resolved" "https://registry.npmmirror.com/@vue/babel-sugar-inject-h/download/@vue/babel-sugar-inject-h-1.2.2.tgz"
  "version" "1.2.2"
  dependencies:
    "@babel/plugin-syntax-jsx" "^7.2.0"

"@vue/babel-sugar-v-model@^1.2.3":
  "integrity" "sha1-+h8pulHr8KoabDX6ZtU5vEWaGPI="
  "resolved" "https://registry.npmmirror.com/@vue/babel-sugar-v-model/download/@vue/babel-sugar-v-model-1.2.3.tgz"
  "version" "1.2.3"
  dependencies:
    "@babel/plugin-syntax-jsx" "^7.2.0"
    "@vue/babel-helper-vue-jsx-merge-props" "^1.2.1"
    "@vue/babel-plugin-transform-vue-jsx" "^1.2.1"
    "camelcase" "^5.0.0"
    "html-tags" "^2.0.0"
    "svg-tags" "^1.0.0"

"@vue/babel-sugar-v-on@^1.2.3":
  "integrity" "sha1-NCNnF4WGpp85LwS/ujICHQKROto="
  "resolved" "https://registry.npmmirror.com/@vue/babel-sugar-v-on/download/@vue/babel-sugar-v-on-1.2.3.tgz"
  "version" "1.2.3"
  dependencies:
    "@babel/plugin-syntax-jsx" "^7.2.0"
    "@vue/babel-plugin-transform-vue-jsx" "^1.2.1"
    "camelcase" "^5.0.0"

"@vue/cli-overlay@^5.0.0-rc.1":
  "integrity" "sha512-pJ8DxDlFiM73FaNlpKqOi3haG15c/6jPLiFZKIGJDoMxLLufSxEZ8aSfvbFRjqwGy/o3I4jzg086Yy+3ZhdG1g=="
  "resolved" "https://registry.npmmirror.com/@vue/cli-overlay/download/@vue/cli-overlay-5.0.0-rc.1.tgz?cache=0&sync_timestamp=1637121187458&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2F%40vue%2Fcli-overlay%2Fdownload%2F%40vue%2Fcli-overlay-5.0.0-rc.1.tgz"
  "version" "5.0.0-rc.1"

"@vue/cli-plugin-babel@~5.0.0-beta.6":
  "integrity" "sha512-ldVuIkPDAuW831l4u7QMkqEhn4/Gil5Wv617i6h4rwDYAAXPgdf8Ce6b7jH4XEyQ2HCu93TzAK1i2njkWV1wrw=="
  "resolved" "https://registry.npmmirror.com/@vue/cli-plugin-babel/download/@vue/cli-plugin-babel-5.0.0-rc.1.tgz?cache=0&sync_timestamp=1637121137770&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2F%40vue%2Fcli-plugin-babel%2Fdownload%2F%40vue%2Fcli-plugin-babel-5.0.0-rc.1.tgz"
  "version" "5.0.0-rc.1"
  dependencies:
    "@babel/core" "^7.12.16"
    "@vue/babel-preset-app" "^5.0.0-rc.1"
    "@vue/cli-shared-utils" "^5.0.0-rc.1"
    "babel-loader" "^8.2.2"
    "thread-loader" "^3.0.0"
    "webpack" "^5.54.0"

"@vue/cli-plugin-eslint@~5.0.0-beta.6":
  "integrity" "sha512-CK/T00XeibQCMHzSenBWDROB11HbCL69bEc1nwT8gjXq5MMw9AB8qlaZzSuoHbbUE0PDyD272V/RDePO1ExT7w=="
  "resolved" "https://registry.npmmirror.com/@vue/cli-plugin-eslint/download/@vue/cli-plugin-eslint-5.0.0-rc.1.tgz"
  "version" "5.0.0-rc.1"
  dependencies:
    "@vue/cli-shared-utils" "^5.0.0-rc.1"
    "eslint-webpack-plugin" "^3.1.0"
    "globby" "^11.0.2"
    "webpack" "^5.54.0"
    "yorkie" "^2.0.0"

"@vue/cli-plugin-router@^5.0.0-rc.1", "@vue/cli-plugin-router@~5.0.0-beta.6":
  "integrity" "sha512-eRMeUNY89PbyH/Gkt0IGfNT2qK4fZhTdBDKrR/hwyk57TBDse/h2jZ9inPqfSnrdIrMOqYsLU2ixzSzJVXXOVw=="
  "resolved" "https://registry.npmmirror.com/@vue/cli-plugin-router/download/@vue/cli-plugin-router-5.0.0-rc.1.tgz?cache=0&sync_timestamp=1637121138678&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2F%40vue%2Fcli-plugin-router%2Fdownload%2F%40vue%2Fcli-plugin-router-5.0.0-rc.1.tgz"
  "version" "5.0.0-rc.1"
  dependencies:
    "@vue/cli-shared-utils" "^5.0.0-rc.1"

"@vue/cli-plugin-vuex@^5.0.0-rc.1", "@vue/cli-plugin-vuex@~5.0.0-beta.6":
  "integrity" "sha512-ShfPIvuD+AFUtUcTw5okLUnBy8o4NrC0ICPpD7z+KG4gbfWzZMmT2TAkjqO9pp21cI+bYzigsBZ/FoXiU5+h7Q=="
  "resolved" "https://registry.npmmirror.com/@vue/cli-plugin-vuex/download/@vue/cli-plugin-vuex-5.0.0-rc.1.tgz?cache=0&sync_timestamp=1637131562626&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2F%40vue%2Fcli-plugin-vuex%2Fdownload%2F%40vue%2Fcli-plugin-vuex-5.0.0-rc.1.tgz"
  "version" "5.0.0-rc.1"

"@vue/cli-service@^3.0.0 || ^4.0.0 || ^5.0.0-0", "@vue/cli-service@~5.0.0-beta.6":
  "integrity" "sha512-xQIHBJzSK+tuOrMnfywRAqfcshClBG/3z/6L/ep8m9O1egsnrkjf2252znJqNaSXvER6b5exDr8M4wnNvhinBw=="
  "resolved" "https://registry.npmmirror.com/@vue/cli-service/download/@vue/cli-service-5.0.0-rc.1.tgz?cache=0&sync_timestamp=1637121140369&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2F%40vue%2Fcli-service%2Fdownload%2F%40vue%2Fcli-service-5.0.0-rc.1.tgz"
  "version" "5.0.0-rc.1"
  dependencies:
    "@babel/helper-compilation-targets" "^7.12.16"
    "@soda/friendly-errors-webpack-plugin" "^1.8.0"
    "@soda/get-current-script" "^1.0.2"
    "@types/minimist" "^1.2.0"
    "@types/webpack-dev-server" "^4.1.0"
    "@vue/cli-overlay" "^5.0.0-rc.1"
    "@vue/cli-plugin-router" "^5.0.0-rc.1"
    "@vue/cli-plugin-vuex" "^5.0.0-rc.1"
    "@vue/cli-shared-utils" "^5.0.0-rc.1"
    "@vue/component-compiler-utils" "^3.3.0"
    "@vue/vue-loader-v15" "npm:vue-loader@^15.9.7"
    "@vue/web-component-wrapper" "^1.3.0"
    "acorn" "^8.0.5"
    "acorn-walk" "^8.0.2"
    "address" "^1.1.2"
    "autoprefixer" "^10.2.4"
    "browserslist" "^4.16.3"
    "cache-loader" "^4.1.0"
    "case-sensitive-paths-webpack-plugin" "^2.3.0"
    "cli-highlight" "^2.1.10"
    "clipboardy" "^2.3.0"
    "cliui" "^7.0.4"
    "copy-webpack-plugin" "^9.0.1"
    "css-loader" "^6.5.0"
    "css-minimizer-webpack-plugin" "^3.0.2"
    "cssnano" "^5.0.0"
    "debug" "^4.1.1"
    "default-gateway" "^6.0.3"
    "dotenv" "^10.0.0"
    "dotenv-expand" "^5.1.0"
    "fs-extra" "^9.1.0"
    "globby" "^11.0.2"
    "hash-sum" "^2.0.0"
    "html-webpack-plugin" "^5.1.0"
    "is-file-esm" "^1.0.0"
    "launch-editor-middleware" "^2.2.1"
    "lodash.defaultsdeep" "^4.6.1"
    "lodash.mapvalues" "^4.6.0"
    "mini-css-extract-plugin" "^2.4.3"
    "minimist" "^1.2.5"
    "module-alias" "^2.2.2"
    "portfinder" "^1.0.26"
    "postcss" "^8.2.6"
    "postcss-loader" "^6.1.1"
    "progress-webpack-plugin" "^1.0.12"
    "ssri" "^8.0.1"
    "terser-webpack-plugin" "^5.1.1"
    "thread-loader" "^3.0.0"
    "vue-loader" "^16.8.2"
    "vue-style-loader" "^4.1.3"
    "webpack" "^5.54.0"
    "webpack-bundle-analyzer" "^4.4.0"
    "webpack-chain" "^6.5.1"
    "webpack-dev-server" "^4.1.0"
    "webpack-merge" "^5.7.3"
    "webpack-virtual-modules" "^0.4.2"
    "whatwg-fetch" "^3.6.2"

"@vue/cli-shared-utils@^5.0.0-rc.1":
  "integrity" "sha512-rZVTGfSQ0Sqw76BD/Z6iARfnO2r5ICSxt0iIpzjM8w9ohpRtI2B45NrngF0rILwy9d8we4A/n7gQQ0rqwkAX7g=="
  "resolved" "https://registry.npmmirror.com/@vue/cli-shared-utils/download/@vue/cli-shared-utils-5.0.0-rc.1.tgz?cache=0&sync_timestamp=1637121137255&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2F%40vue%2Fcli-shared-utils%2Fdownload%2F%40vue%2Fcli-shared-utils-5.0.0-rc.1.tgz"
  "version" "5.0.0-rc.1"
  dependencies:
    "chalk" "^4.1.2"
    "execa" "^1.0.0"
    "joi" "^17.4.0"
    "launch-editor" "^2.2.1"
    "lru-cache" "^6.0.0"
    "node-fetch" "^2.6.1"
    "node-ipc" "^9.1.1"
    "open" "^8.0.2"
    "ora" "^5.3.0"
    "read-pkg" "^5.1.1"
    "semver" "^7.3.4"
    "strip-ansi" "^6.0.0"

"@vue/compiler-core@3.2.26":
  "integrity" "sha512-N5XNBobZbaASdzY9Lga2D9Lul5vdCIOXvUMd6ThcN8zgqQhPKfCV+wfAJNNJKQkSHudnYRO2gEB+lp0iN3g2Tw=="
  "resolved" "https://registry.npmmirror.com/@vue/compiler-core/download/@vue/compiler-core-3.2.26.tgz"
  "version" "3.2.26"
  dependencies:
    "@babel/parser" "^7.16.4"
    "@vue/shared" "3.2.26"
    "estree-walker" "^2.0.2"
    "source-map" "^0.6.1"

"@vue/compiler-dom@3.2.26":
  "integrity" "sha512-smBfaOW6mQDxcT3p9TKT6mE22vjxjJL50GFVJiI0chXYGU/xzC05QRGrW3HHVuJrmLTLx5zBhsZ2dIATERbarg=="
  "resolved" "https://registry.npmmirror.com/@vue/compiler-dom/download/@vue/compiler-dom-3.2.26.tgz"
  "version" "3.2.26"
  dependencies:
    "@vue/compiler-core" "3.2.26"
    "@vue/shared" "3.2.26"

"@vue/compiler-sfc@^3.2.6", "@vue/compiler-sfc@3.2.26":
  "integrity" "sha512-ePpnfktV90UcLdsDQUh2JdiTuhV0Skv2iYXxfNMOK/F3Q+2BO0AulcVcfoksOpTJGmhhfosWfMyEaEf0UaWpIw=="
  "resolved" "https://registry.npmmirror.com/@vue/compiler-sfc/download/@vue/compiler-sfc-3.2.26.tgz"
  "version" "3.2.26"
  dependencies:
    "@babel/parser" "^7.16.4"
    "@vue/compiler-core" "3.2.26"
    "@vue/compiler-dom" "3.2.26"
    "@vue/compiler-ssr" "3.2.26"
    "@vue/reactivity-transform" "3.2.26"
    "@vue/shared" "3.2.26"
    "estree-walker" "^2.0.2"
    "magic-string" "^0.25.7"
    "postcss" "^8.1.10"
    "source-map" "^0.6.1"

"@vue/compiler-ssr@3.2.26":
  "integrity" "sha512-2mywLX0ODc4Zn8qBoA2PDCsLEZfpUGZcyoFRLSOjyGGK6wDy2/5kyDOWtf0S0UvtoyVq95OTSGIALjZ4k2q/ag=="
  "resolved" "https://registry.npmmirror.com/@vue/compiler-ssr/download/@vue/compiler-ssr-3.2.26.tgz"
  "version" "3.2.26"
  dependencies:
    "@vue/compiler-dom" "3.2.26"
    "@vue/shared" "3.2.26"

"@vue/component-compiler-utils@^3.1.0", "@vue/component-compiler-utils@^3.3.0":
  "integrity" "sha1-+fX7U0ZLDDeyyNLz+/5E32D2Hck="
  "resolved" "https://registry.npmmirror.com/@vue/component-compiler-utils/download/@vue/component-compiler-utils-3.3.0.tgz"
  "version" "3.3.0"
  dependencies:
    "consolidate" "^0.15.1"
    "hash-sum" "^1.0.2"
    "lru-cache" "^4.1.2"
    "merge-source-map" "^1.1.0"
    "postcss" "^7.0.36"
    "postcss-selector-parser" "^6.0.2"
    "source-map" "~0.6.1"
    "vue-template-es2015-compiler" "^1.9.0"
  optionalDependencies:
    "prettier" "^1.18.2 || ^2.0.0"

"@vue/devtools-api@^6.0.0-beta.11", "@vue/devtools-api@^6.0.0-beta.18":
  "integrity" "sha512-R2rfiRY+kZugzWh9ZyITaovx+jpU4vgivAEAiz80kvh3yviiTU3CBuGuyWpSwGz9/C7TkSWVM/FtQRGlZ16n8Q=="
  "resolved" "https://registry.npmmirror.com/@vue/devtools-api/download/@vue/devtools-api-6.0.0-beta.20.1.tgz"
  "version" "6.0.0-beta.20.1"

"@vue/eslint-config-prettier@^6.0.0":
  "integrity" "sha1-rVkSswj0rkaEWOAqKwXbC50kZwA="
  "resolved" "https://registry.npmmirror.com/@vue/eslint-config-prettier/download/@vue/eslint-config-prettier-6.0.0.tgz"
  "version" "6.0.0"
  dependencies:
    "eslint-config-prettier" "^6.0.0"

"@vue/reactivity-transform@3.2.26":
  "integrity" "sha512-XKMyuCmzNA7nvFlYhdKwD78rcnmPb7q46uoR00zkX6yZrUmcCQ5OikiwUEVbvNhL5hBJuvbSO95jB5zkUon+eQ=="
  "resolved" "https://registry.npmmirror.com/@vue/reactivity-transform/download/@vue/reactivity-transform-3.2.26.tgz"
  "version" "3.2.26"
  dependencies:
    "@babel/parser" "^7.16.4"
    "@vue/compiler-core" "3.2.26"
    "@vue/shared" "3.2.26"
    "estree-walker" "^2.0.2"
    "magic-string" "^0.25.7"

"@vue/reactivity@3.2.26":
  "integrity" "sha512-h38bxCZLW6oFJVDlCcAiUKFnXI8xP8d+eO0pcDxx+7dQfSPje2AO6M9S9QO6MrxQB7fGP0DH0dYQ8ksf6hrXKQ=="
  "resolved" "https://registry.npmmirror.com/@vue/reactivity/download/@vue/reactivity-3.2.26.tgz"
  "version" "3.2.26"
  dependencies:
    "@vue/shared" "3.2.26"

"@vue/runtime-core@3.2.26":
  "integrity" "sha512-BcYi7qZ9Nn+CJDJrHQ6Zsmxei2hDW0L6AB4vPvUQGBm2fZyC0GXd/4nVbyA2ubmuhctD5RbYY8L+5GUJszv9mQ=="
  "resolved" "https://registry.npmmirror.com/@vue/runtime-core/download/@vue/runtime-core-3.2.26.tgz"
  "version" "3.2.26"
  dependencies:
    "@vue/reactivity" "3.2.26"
    "@vue/shared" "3.2.26"

"@vue/runtime-dom@3.2.26":
  "integrity" "sha512-dY56UIiZI+gjc4e8JQBwAifljyexfVCkIAu/WX8snh8vSOt/gMSEGwPRcl2UpYpBYeyExV8WCbgvwWRNt9cHhQ=="
  "resolved" "https://registry.npmmirror.com/@vue/runtime-dom/download/@vue/runtime-dom-3.2.26.tgz"
  "version" "3.2.26"
  dependencies:
    "@vue/runtime-core" "3.2.26"
    "@vue/shared" "3.2.26"
    "csstype" "^2.6.8"

"@vue/server-renderer@3.2.26":
  "integrity" "sha512-Jp5SggDUvvUYSBIvYEhy76t4nr1vapY/FIFloWmQzn7UxqaHrrBpbxrqPcTrSgGrcaglj0VBp22BKJNre4aA1w=="
  "resolved" "https://registry.npmmirror.com/@vue/server-renderer/download/@vue/server-renderer-3.2.26.tgz"
  "version" "3.2.26"
  dependencies:
    "@vue/compiler-ssr" "3.2.26"
    "@vue/shared" "3.2.26"

"@vue/shared@3.2.26":
  "integrity" "sha512-vPV6Cq+NIWbH5pZu+V+2QHE9y1qfuTq49uNWw4f7FDEeZaDU2H2cx5jcUZOAKW7qTrUS4k6qZPbMy1x4N96nbA=="
  "resolved" "https://registry.npmmirror.com/@vue/shared/download/@vue/shared-3.2.26.tgz"
  "version" "3.2.26"

"@vue/vue-loader-v15@npm:vue-loader@^15.9.7":
  "integrity" "sha512-SaPHK1A01VrNthlix6h1hq4uJu7S/z0kdLUb6klubo738NeQoLbS6V9/d8Pv19tU0XdQKju3D1HSKuI8wJ5wMA=="
  "resolved" "https://registry.npmmirror.com/vue-loader/-/vue-loader-15.10.1.tgz"
  "version" "15.10.1"
  dependencies:
    "@vue/component-compiler-utils" "^3.1.0"
    "hash-sum" "^1.0.2"
    "loader-utils" "^1.1.0"
    "vue-hot-reload-api" "^2.3.0"
    "vue-style-loader" "^4.1.0"

"@vue/web-component-wrapper@^1.3.0":
  "integrity" "sha1-trQKdiVCnSvXwigd26YB7QXcfxo="
  "resolved" "https://registry.npmmirror.com/@vue/web-component-wrapper/download/@vue/web-component-wrapper-1.3.0.tgz"
  "version" "1.3.0"

"@webassemblyjs/ast@1.11.1":
  "integrity" "sha1-K/12fq4aaZb0Mv9+jX/HVnnAtqc="
  "resolved" "https://registry.npmmirror.com/@webassemblyjs/ast/download/@webassemblyjs/ast-1.11.1.tgz"
  "version" "1.11.1"
  dependencies:
    "@webassemblyjs/helper-numbers" "1.11.1"
    "@webassemblyjs/helper-wasm-bytecode" "1.11.1"

"@webassemblyjs/floating-point-hex-parser@1.11.1":
  "integrity" "sha1-9sYacF8P16auyqToGY8j2dwXnk8="
  "resolved" "https://registry.npmmirror.com/@webassemblyjs/floating-point-hex-parser/download/@webassemblyjs/floating-point-hex-parser-1.11.1.tgz"
  "version" "1.11.1"

"@webassemblyjs/helper-api-error@1.11.1":
  "integrity" "sha1-GmMZLYeI5cASgAump6RscFKI/RY="
  "resolved" "https://registry.npmmirror.com/@webassemblyjs/helper-api-error/download/@webassemblyjs/helper-api-error-1.11.1.tgz"
  "version" "1.11.1"

"@webassemblyjs/helper-buffer@1.11.1":
  "integrity" "sha1-gyqQDrREiEzemnytRn+BUA9eWrU="
  "resolved" "https://registry.npmmirror.com/@webassemblyjs/helper-buffer/download/@webassemblyjs/helper-buffer-1.11.1.tgz"
  "version" "1.11.1"

"@webassemblyjs/helper-numbers@1.11.1":
  "integrity" "sha1-ZNgdohn7u6HjvRv8dPboxOEKYq4="
  "resolved" "https://registry.npmmirror.com/@webassemblyjs/helper-numbers/download/@webassemblyjs/helper-numbers-1.11.1.tgz"
  "version" "1.11.1"
  dependencies:
    "@webassemblyjs/floating-point-hex-parser" "1.11.1"
    "@webassemblyjs/helper-api-error" "1.11.1"
    "@xtuc/long" "4.2.2"

"@webassemblyjs/helper-wasm-bytecode@1.11.1":
  "integrity" "sha1-8ygkHkHnsZnQsgwY6IQpxEMyleE="
  "resolved" "https://registry.npmmirror.com/@webassemblyjs/helper-wasm-bytecode/download/@webassemblyjs/helper-wasm-bytecode-1.11.1.tgz"
  "version" "1.11.1"

"@webassemblyjs/helper-wasm-section@1.11.1":
  "integrity" "sha1-Ie4GWntjXzGec48N1zv72igcCXo="
  "resolved" "https://registry.npmmirror.com/@webassemblyjs/helper-wasm-section/download/@webassemblyjs/helper-wasm-section-1.11.1.tgz"
  "version" "1.11.1"
  dependencies:
    "@webassemblyjs/ast" "1.11.1"
    "@webassemblyjs/helper-buffer" "1.11.1"
    "@webassemblyjs/helper-wasm-bytecode" "1.11.1"
    "@webassemblyjs/wasm-gen" "1.11.1"

"@webassemblyjs/ieee754@1.11.1":
  "integrity" "sha1-ljkp6bvQVwnn4SJDoJkYCBKZJhQ="
  "resolved" "https://registry.npmmirror.com/@webassemblyjs/ieee754/download/@webassemblyjs/ieee754-1.11.1.tgz"
  "version" "1.11.1"
  dependencies:
    "@xtuc/ieee754" "^1.2.0"

"@webassemblyjs/leb128@1.11.1":
  "integrity" "sha1-zoFLRVdOk9drrh+yZEq5zdlSeqU="
  "resolved" "https://registry.npmmirror.com/@webassemblyjs/leb128/download/@webassemblyjs/leb128-1.11.1.tgz"
  "version" "1.11.1"
  dependencies:
    "@xtuc/long" "4.2.2"

"@webassemblyjs/utf8@1.11.1":
  "integrity" "sha1-0fi3ZDaefG5rrjUOhU3smlnwo/8="
  "resolved" "https://registry.npmmirror.com/@webassemblyjs/utf8/download/@webassemblyjs/utf8-1.11.1.tgz"
  "version" "1.11.1"

"@webassemblyjs/wasm-edit@1.11.1":
  "integrity" "sha1-rSBuv0v5WgWM6YgKjAksXeyBk9Y="
  "resolved" "https://registry.npmmirror.com/@webassemblyjs/wasm-edit/download/@webassemblyjs/wasm-edit-1.11.1.tgz"
  "version" "1.11.1"
  dependencies:
    "@webassemblyjs/ast" "1.11.1"
    "@webassemblyjs/helper-buffer" "1.11.1"
    "@webassemblyjs/helper-wasm-bytecode" "1.11.1"
    "@webassemblyjs/helper-wasm-section" "1.11.1"
    "@webassemblyjs/wasm-gen" "1.11.1"
    "@webassemblyjs/wasm-opt" "1.11.1"
    "@webassemblyjs/wasm-parser" "1.11.1"
    "@webassemblyjs/wast-printer" "1.11.1"

"@webassemblyjs/wasm-gen@1.11.1":
  "integrity" "sha1-hsXqMEhJdZt9iMR6MvTwOa48j3Y="
  "resolved" "https://registry.npmmirror.com/@webassemblyjs/wasm-gen/download/@webassemblyjs/wasm-gen-1.11.1.tgz"
  "version" "1.11.1"
  dependencies:
    "@webassemblyjs/ast" "1.11.1"
    "@webassemblyjs/helper-wasm-bytecode" "1.11.1"
    "@webassemblyjs/ieee754" "1.11.1"
    "@webassemblyjs/leb128" "1.11.1"
    "@webassemblyjs/utf8" "1.11.1"

"@webassemblyjs/wasm-opt@1.11.1":
  "integrity" "sha1-ZXtMIgL0zzs0X4pMZGHIwkGJhfI="
  "resolved" "https://registry.npmmirror.com/@webassemblyjs/wasm-opt/download/@webassemblyjs/wasm-opt-1.11.1.tgz"
  "version" "1.11.1"
  dependencies:
    "@webassemblyjs/ast" "1.11.1"
    "@webassemblyjs/helper-buffer" "1.11.1"
    "@webassemblyjs/wasm-gen" "1.11.1"
    "@webassemblyjs/wasm-parser" "1.11.1"

"@webassemblyjs/wasm-parser@1.11.1":
  "integrity" "sha1-hspzRTT0F+m9PGfHocddi+QfsZk="
  "resolved" "https://registry.npmmirror.com/@webassemblyjs/wasm-parser/download/@webassemblyjs/wasm-parser-1.11.1.tgz"
  "version" "1.11.1"
  dependencies:
    "@webassemblyjs/ast" "1.11.1"
    "@webassemblyjs/helper-api-error" "1.11.1"
    "@webassemblyjs/helper-wasm-bytecode" "1.11.1"
    "@webassemblyjs/ieee754" "1.11.1"
    "@webassemblyjs/leb128" "1.11.1"
    "@webassemblyjs/utf8" "1.11.1"

"@webassemblyjs/wast-printer@1.11.1":
  "integrity" "sha1-0Mc77ajuxUJvEK6O9VzuXnCEwvA="
  "resolved" "https://registry.npmmirror.com/@webassemblyjs/wast-printer/download/@webassemblyjs/wast-printer-1.11.1.tgz"
  "version" "1.11.1"
  dependencies:
    "@webassemblyjs/ast" "1.11.1"
    "@xtuc/long" "4.2.2"

"@xtuc/ieee754@^1.2.0":
  "integrity" "sha1-7vAUoxRa5Hehy8AM0eVSM23Ot5A="
  "resolved" "https://registry.npmmirror.com/@xtuc/ieee754/download/@xtuc/ieee754-1.2.0.tgz"
  "version" "1.2.0"

"@xtuc/long@4.2.2":
  "integrity" "sha1-0pHGpOl5ibXGHZrPOWrk/hM6cY0="
  "resolved" "https://registry.npmmirror.com/@xtuc/long/download/@xtuc/long-4.2.2.tgz"
  "version" "4.2.2"

"accepts@~1.3.4", "accepts@~1.3.5", "accepts@~1.3.7":
  "integrity" "sha1-UxvHJlF6OytB+FACHGzBXqq1B80="
  "resolved" "https://registry.npmmirror.com/accepts/download/accepts-1.3.7.tgz"
  "version" "1.3.7"
  dependencies:
    "mime-types" "~2.1.24"
    "negotiator" "0.6.2"

"acorn-import-assertions@^1.7.6":
  "integrity" "sha1-uitZOc5iwjjbbZPYHJsRGym4Vek="
  "resolved" "https://registry.npmmirror.com/acorn-import-assertions/download/acorn-import-assertions-1.8.0.tgz"
  "version" "1.8.0"

"acorn-jsx@^5.2.0", "acorn-jsx@^5.3.1":
  "integrity" "sha1-ftW7VZCLOy8bxVxq8WU7rafweTc="
  "resolved" "https://registry.npmmirror.com/acorn-jsx/download/acorn-jsx-5.3.2.tgz"
  "version" "5.3.2"

"acorn-walk@^8.0.0", "acorn-walk@^8.0.2":
  "integrity" "sha1-dBIQ8uJCZFRQiFOi9E0KuDt/acE="
  "resolved" "https://registry.npmmirror.com/acorn-walk/download/acorn-walk-8.2.0.tgz"
  "version" "8.2.0"

"acorn@^6.0.0 || ^7.0.0 || ^8.0.0", "acorn@^8", "acorn@^8.0.4", "acorn@^8.0.5", "acorn@^8.4.1", "acorn@^8.5.0", "acorn@^8.9.0":
  "integrity" "sha512-F0SAmZ8iUtS//m8DmCTA0jlh6TDKkHQyK6xc6V4KDTyZKA9dnvX9/3sRTVQrWm79glUAZbnmmNcdYwUIHWVybw=="
  "resolved" "http://castle-npm.cp.hxdi.cn/acorn/-/acorn-8.10.0.tgz"
  "version" "8.10.0"

"acorn@^7.1.1":
  "integrity" "sha1-/q7SVZc9LndVW4PbwIhRpsY1IPo="
  "resolved" "https://registry.npmmirror.com/acorn/download/acorn-7.4.1.tgz?cache=0&sync_timestamp=1637225522161&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2Facorn%2Fdownload%2Facorn-7.4.1.tgz"
  "version" "7.4.1"

"acorn@^7.4.0":
  "integrity" "sha1-/q7SVZc9LndVW4PbwIhRpsY1IPo="
  "resolved" "https://registry.npmmirror.com/acorn/download/acorn-7.4.1.tgz?cache=0&sync_timestamp=1637225522161&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2Facorn%2Fdownload%2Facorn-7.4.1.tgz"
  "version" "7.4.1"

"address@^1.1.2":
  "integrity" "sha1-vxEWycdYxRt6kz0pa3LCIe2UKLY="
  "resolved" "https://registry.npmmirror.com/address/download/address-1.1.2.tgz"
  "version" "1.1.2"

"aggregate-error@^3.0.0":
  "integrity" "sha1-kmcP9Q9TWb23o+DUDQ7DDFc3aHo="
  "resolved" "https://registry.npmmirror.com/aggregate-error/download/aggregate-error-3.1.0.tgz"
  "version" "3.1.0"
  dependencies:
    "clean-stack" "^2.0.0"
    "indent-string" "^4.0.0"

"ajv-formats@^2.1.1":
  "integrity" "sha1-bmaUAGWet0lzu/LjMycYCgmWtSA="
  "resolved" "https://registry.npmmirror.com/ajv-formats/download/ajv-formats-2.1.1.tgz"
  "version" "2.1.1"
  dependencies:
    "ajv" "^8.0.0"

"ajv-keywords@^3.5.2":
  "integrity" "sha1-MfKdpatuANHC0yms97WSlhTVAU0="
  "resolved" "https://registry.npmmirror.com/ajv-keywords/download/ajv-keywords-3.5.2.tgz?cache=0&sync_timestamp=1637524807216&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2Fajv-keywords%2Fdownload%2Fajv-keywords-3.5.2.tgz"
  "version" "3.5.2"

"ajv-keywords@^5.0.0":
  "integrity" "sha512-YCS/JNFAUyr5vAuhk1DWm1CBxRHW9LbJ2ozWeemrIqpbsqKjHVxYPyi5GC0rjZIT5JxJ3virVTS8wk4i/Z+krw=="
  "resolved" "https://registry.npmmirror.com/ajv-keywords/download/ajv-keywords-5.1.0.tgz?cache=0&sync_timestamp=1637524807216&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2Fajv-keywords%2Fdownload%2Fajv-keywords-5.1.0.tgz"
  "version" "5.1.0"
  dependencies:
    "fast-deep-equal" "^3.1.3"

"ajv@^6.10.0", "ajv@^6.12.4", "ajv@^6.12.5", "ajv@^6.9.1":
  "integrity" "sha1-uvWmLoArB9l3A0WG+MO69a3ybfQ="
  "resolved" "https://registry.npmmirror.com/ajv/download/ajv-6.12.6.tgz?cache=0&sync_timestamp=1637523008505&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2Fajv%2Fdownload%2Fajv-6.12.6.tgz"
  "version" "6.12.6"
  dependencies:
    "fast-deep-equal" "^3.1.1"
    "fast-json-stable-stringify" "^2.0.0"
    "json-schema-traverse" "^0.4.1"
    "uri-js" "^4.2.2"

"ajv@^8.0.0":
  "integrity" "sha512-x9VuX+R/jcFj1DHo/fCp99esgGDWiHENrKxaCENuCxpoMCmAt/COCGVDwA7kleEpEzJjDnvh3yGoOuLu0Dtllw=="
  "resolved" "https://registry.npmmirror.com/ajv/download/ajv-8.8.2.tgz?cache=0&sync_timestamp=1637523008505&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2Fajv%2Fdownload%2Fajv-8.8.2.tgz"
  "version" "8.8.2"
  dependencies:
    "fast-deep-equal" "^3.1.1"
    "json-schema-traverse" "^1.0.0"
    "require-from-string" "^2.0.2"
    "uri-js" "^4.2.2"

"ajv@^8.0.1":
  "integrity" "sha512-x9VuX+R/jcFj1DHo/fCp99esgGDWiHENrKxaCENuCxpoMCmAt/COCGVDwA7kleEpEzJjDnvh3yGoOuLu0Dtllw=="
  "resolved" "https://registry.npmmirror.com/ajv/download/ajv-8.8.2.tgz?cache=0&sync_timestamp=1637523008505&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2Fajv%2Fdownload%2Fajv-8.8.2.tgz"
  "version" "8.8.2"
  dependencies:
    "fast-deep-equal" "^3.1.1"
    "json-schema-traverse" "^1.0.0"
    "require-from-string" "^2.0.2"
    "uri-js" "^4.2.2"

"ajv@^8.8.0", "ajv@^8.8.2":
  "integrity" "sha512-x9VuX+R/jcFj1DHo/fCp99esgGDWiHENrKxaCENuCxpoMCmAt/COCGVDwA7kleEpEzJjDnvh3yGoOuLu0Dtllw=="
  "resolved" "https://registry.npmmirror.com/ajv/download/ajv-8.8.2.tgz?cache=0&sync_timestamp=1637523008505&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2Fajv%2Fdownload%2Fajv-8.8.2.tgz"
  "version" "8.8.2"
  dependencies:
    "fast-deep-equal" "^3.1.1"
    "json-schema-traverse" "^1.0.0"
    "require-from-string" "^2.0.2"
    "uri-js" "^4.2.2"

"alphanum-sort@^1.0.2":
  "integrity" "sha1-l6ERlkmyEa0zaR2fn0hqjsn74KM="
  "resolved" "https://registry.npmmirror.com/alphanum-sort/download/alphanum-sort-1.0.2.tgz"
  "version" "1.0.2"

"ansi-colors@^4.1.1":
  "integrity" "sha1-y7muJWv3UK8eqzRPIpqif+lLo0g="
  "resolved" "https://registry.npmmirror.com/ansi-colors/download/ansi-colors-4.1.1.tgz"
  "version" "4.1.1"

"ansi-escapes@^3.0.0":
  "integrity" "sha1-h4C5j/nb9WOBUtHx/lwde0RCl2s="
  "resolved" "https://registry.npmmirror.com/ansi-escapes/download/ansi-escapes-3.2.0.tgz"
  "version" "3.2.0"

"ansi-escapes@^4.2.1":
  "integrity" "sha512-gKXj5ALrKWQLsYG9jlTRmR/xKluxHV+Z9QEwNIgCfM1/uwPMCuzVVnh5mwTd+OuBZcwSIMbqssNWRm1lE51QaQ=="
  "resolved" "http://castle-npm.cp.hxdi.cn/ansi-escapes/-/ansi-escapes-4.3.2.tgz"
  "version" "4.3.2"
  dependencies:
    "type-fest" "^0.21.3"

"ansi-html-community@^0.0.8":
  "integrity" "sha1-afvE1sy+OD+XNpNK40w/gpDxv0E="
  "resolved" "https://registry.npmmirror.com/ansi-html-community/download/ansi-html-community-0.0.8.tgz"
  "version" "0.0.8"

"ansi-regex@^2.0.0":
  "integrity" "sha1-w7M6te42DYbg5ijwRorn7yfWVN8="
  "resolved" "https://registry.npmmirror.com/ansi-regex/download/ansi-regex-2.1.1.tgz"
  "version" "2.1.1"

"ansi-regex@^3.0.0":
  "integrity" "sha1-7QMXwyIGT3lGbAKWa922Bas32Zg="
  "resolved" "https://registry.npmmirror.com/ansi-regex/download/ansi-regex-3.0.0.tgz"
  "version" "3.0.0"

"ansi-regex@^5.0.1":
  "integrity" "sha1-CCyyyJyf6GWaMRpTvWpNxTAdswQ="
  "resolved" "https://registry.npmmirror.com/ansi-regex/download/ansi-regex-5.0.1.tgz"
  "version" "5.0.1"

"ansi-regex@^6.0.1":
  "integrity" "sha1-MYPjj66aZdfLXlOUXNWJfQJgoGo="
  "resolved" "https://registry.npmmirror.com/ansi-regex/download/ansi-regex-6.0.1.tgz"
  "version" "6.0.1"

"ansi-styles@^2.2.1":
  "integrity" "sha1-tDLdM1i2NM914eRmQ2gkBTPB3b4="
  "resolved" "https://registry.npmmirror.com/ansi-styles/download/ansi-styles-2.2.1.tgz"
  "version" "2.2.1"

"ansi-styles@^3.2.1":
  "integrity" "sha1-QfuyAkPlCxK+DwS43tvwdSDOhB0="
  "resolved" "https://registry.npmmirror.com/ansi-styles/download/ansi-styles-3.2.1.tgz"
  "version" "3.2.1"
  dependencies:
    "color-convert" "^1.9.0"

"ansi-styles@^4.0.0":
  "integrity" "sha512-zbB9rCJAT1rbjiVDb2hqKFHNYLxgtk8NURxZ3IZwD3F6NtxbXZQCnnSi1Lkx+IDohdPlFp222wVALIheZJQSEg=="
  "resolved" "http://castle-npm.cp.hxdi.cn/ansi-styles/-/ansi-styles-4.3.0.tgz"
  "version" "4.3.0"
  dependencies:
    "color-convert" "^2.0.1"

"ansi-styles@^4.1.0":
  "integrity" "sha1-7dgDYornHATIWuegkG7a00tkiTc="
  "resolved" "https://registry.npmmirror.com/ansi-styles/download/ansi-styles-4.3.0.tgz"
  "version" "4.3.0"
  dependencies:
    "color-convert" "^2.0.1"

"any-promise@^1.0.0":
  "integrity" "sha1-q8av7tzqUugJzcA3au0845Y10X8="
  "resolved" "https://registry.npmmirror.com/any-promise/download/any-promise-1.3.0.tgz"
  "version" "1.3.0"

"anymatch@~3.1.2":
  "integrity" "sha1-wFV8CWrzLxBhmPT04qODU343hxY="
  "resolved" "https://registry.npmmirror.com/anymatch/download/anymatch-3.1.2.tgz"
  "version" "3.1.2"
  dependencies:
    "normalize-path" "^3.0.0"
    "picomatch" "^2.0.4"

"arch@^2.1.1":
  "integrity" "sha1-G8R4GPMFdk8jqzMGsL/AhsWinRE="
  "resolved" "https://registry.npmmirror.com/arch/download/arch-2.2.0.tgz"
  "version" "2.2.0"

"arg@^5.0.2":
  "integrity" "sha512-PYjyFOLKQ9y57JvQ6QLo8dAgNqswh8M1RMJYdQduT6xbWSgK36P/Z/v+p888pM69jMMfS8Xd8F6I1kQ/I9HUGg=="
  "resolved" "http://castle-npm.cp.hxdi.cn/arg/-/arg-5.0.2.tgz"
  "version" "5.0.2"

"argparse@^1.0.7":
  "integrity" "sha1-vNZ5HqWuCXJeF+WtmIE0zUCz2RE="
  "resolved" "https://registry.npmmirror.com/argparse/download/argparse-1.0.10.tgz"
  "version" "1.0.10"
  dependencies:
    "sprintf-js" "~1.0.2"

"arr-diff@^4.0.0":
  "integrity" "sha1-1kYQdP6/7HHn4VI1dhoyml3HxSA="
  "resolved" "https://registry.npmmirror.com/arr-diff/download/arr-diff-4.0.0.tgz"
  "version" "4.0.0"

"arr-flatten@^1.1.0":
  "integrity" "sha1-NgSLv/TntH4TZkQxbJlmnqWukfE="
  "resolved" "https://registry.npmmirror.com/arr-flatten/download/arr-flatten-1.1.0.tgz"
  "version" "1.1.0"

"arr-union@^3.1.0":
  "integrity" "sha1-45sJrqne+Gao8gbiiK9jkZuuOcQ="
  "resolved" "https://registry.npmmirror.com/arr-union/download/arr-union-3.1.0.tgz"
  "version" "3.1.0"

"array-flatten@^2.1.0":
  "integrity" "sha1-JO+AoowaiTYX4hSbDG0NeIKTsJk="
  "resolved" "https://registry.npmmirror.com/array-flatten/download/array-flatten-2.1.2.tgz"
  "version" "2.1.2"

"array-flatten@1.1.1":
  "integrity" "sha1-ml9pkFGx5wczKPKgCJaLZOopVdI="
  "resolved" "https://registry.npmmirror.com/array-flatten/download/array-flatten-1.1.1.tgz"
  "version" "1.1.1"

"array-union@^2.1.0":
  "integrity" "sha1-t5hCCtvrHego2ErNii4j0+/oXo0="
  "resolved" "https://registry.npmmirror.com/array-union/download/array-union-2.1.0.tgz"
  "version" "2.1.0"

"array-unique@^0.3.2":
  "integrity" "sha1-qJS3XUvE9s1nnvMkSp/Y9Gri1Cg="
  "resolved" "https://registry.npmmirror.com/array-unique/download/array-unique-0.3.2.tgz"
  "version" "0.3.2"

"assign-symbols@^1.0.0":
  "integrity" "sha1-WWZ/QfrdTyDMvCu5a41Pf3jsA2c="
  "resolved" "https://registry.npmmirror.com/assign-symbols/download/assign-symbols-1.0.0.tgz"
  "version" "1.0.0"

"astral-regex@^2.0.0":
  "integrity" "sha1-SDFDxWeu7UeFdZwIZXhtx319LjE="
  "resolved" "https://registry.npmmirror.com/astral-regex/download/astral-regex-2.0.0.tgz"
  "version" "2.0.0"

"async@^2.6.2":
  "integrity" "sha1-1yYl4jRKNlbjo61Pp0n6gymdgv8="
  "resolved" "https://registry.npmmirror.com/async/download/async-2.6.3.tgz"
  "version" "2.6.3"
  dependencies:
    "lodash" "^4.17.14"

"at-least-node@^1.0.0":
  "integrity" "sha1-YCzUtG6EStTv/JKoARo8RuAjjcI="
  "resolved" "https://registry.npmmirror.com/at-least-node/download/at-least-node-1.0.0.tgz"
  "version" "1.0.0"

"atob@^2.1.2":
  "integrity" "sha1-bZUX654DDSQ2ZmZR6GvZ9vE1M8k="
  "resolved" "https://registry.npmmirror.com/atob/download/atob-2.1.2.tgz"
  "version" "2.1.2"

"autoprefixer@^10.2.4", "autoprefixer@^10.4.15":
  "integrity" "sha512-KCuPB8ZCIqFdA4HwKXsvz7j6gvSDNhDP7WnUjBleRkKjPdvCmHFuQ77ocavI8FT6NdvlBnE2UFr2H4Mycn8Vew=="
  "resolved" "http://castle-npm.cp.hxdi.cn/autoprefixer/-/autoprefixer-10.4.15.tgz"
  "version" "10.4.15"
  dependencies:
    "browserslist" "^4.21.10"
    "caniuse-lite" "^1.0.30001520"
    "fraction.js" "^4.2.0"
    "normalize-range" "^0.1.2"
    "picocolors" "^1.0.0"
    "postcss-value-parser" "^4.2.0"

"axios@^0.23.0":
  "integrity" "sha1-sPpdCUio0ddePVY1I4tsRiWwUUk="
  "resolved" "https://registry.npmmirror.com/axios/download/axios-0.23.0.tgz"
  "version" "0.23.0"
  dependencies:
    "follow-redirects" "^1.14.4"

"babel-loader@^8.2.2":
  "integrity" "sha1-iYa0Dxpkys/LS4QpMgCF72ixNC0="
  "resolved" "https://registry.npmmirror.com/babel-loader/download/babel-loader-8.2.3.tgz?cache=0&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2Fbabel-loader%2Fdownload%2Fbabel-loader-8.2.3.tgz"
  "version" "8.2.3"
  dependencies:
    "find-cache-dir" "^3.3.1"
    "loader-utils" "^1.4.0"
    "make-dir" "^3.1.0"
    "schema-utils" "^2.6.5"

"babel-plugin-dynamic-import-node@^2.3.3":
  "integrity" "sha1-hP2hnJduxcbe/vV/lCez3vZuF6M="
  "resolved" "https://registry.npmmirror.com/babel-plugin-dynamic-import-node/download/babel-plugin-dynamic-import-node-2.3.3.tgz"
  "version" "2.3.3"
  dependencies:
    "object.assign" "^4.1.0"

"babel-plugin-import@^1.13.3":
  "integrity" "sha1-nbu6fRrHK9QSkXqDDUReAJQdJtc="
  "resolved" "https://registry.npmmirror.com/babel-plugin-import/download/babel-plugin-import-1.13.3.tgz"
  "version" "1.13.3"
  dependencies:
    "@babel/helper-module-imports" "^7.0.0"
    "@babel/runtime" "^7.0.0"

"babel-plugin-polyfill-corejs2@^0.3.0":
  "integrity" "sha512-wMDoBJ6uG4u4PNFh72Ty6t3EgfA91puCuAwKIazbQlci+ENb/UU9A3xG5lutjUIiXCIn1CY5L15r9LimiJyrSA=="
  "resolved" "https://registry.npmmirror.com/babel-plugin-polyfill-corejs2/download/babel-plugin-polyfill-corejs2-0.3.0.tgz?cache=0&sync_timestamp=1636799750341&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2Fbabel-plugin-polyfill-corejs2%2Fdownload%2Fbabel-plugin-polyfill-corejs2-0.3.0.tgz"
  "version" "0.3.0"
  dependencies:
    "@babel/compat-data" "^7.13.11"
    "@babel/helper-define-polyfill-provider" "^0.3.0"
    "semver" "^6.1.1"

"babel-plugin-polyfill-corejs3@^0.4.0":
  "integrity" "sha512-YxFreYwUfglYKdLUGvIF2nJEsGwj+RhWSX/ije3D2vQPOXuyMLMtg/cCGMDpOA7Nd+MwlNdnGODbd2EwUZPlsw=="
  "resolved" "https://registry.npmmirror.com/babel-plugin-polyfill-corejs3/download/babel-plugin-polyfill-corejs3-0.4.0.tgz?cache=0&sync_timestamp=1636799750451&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2Fbabel-plugin-polyfill-corejs3%2Fdownload%2Fbabel-plugin-polyfill-corejs3-0.4.0.tgz"
  "version" "0.4.0"
  dependencies:
    "@babel/helper-define-polyfill-provider" "^0.3.0"
    "core-js-compat" "^3.18.0"

"babel-plugin-polyfill-regenerator@^0.3.0":
  "integrity" "sha512-dhAPTDLGoMW5/84wkgwiLRwMnio2i1fUe53EuvtKMv0pn2p3S8OCoV1xAzfJPl0KOX7IB89s2ib85vbYiea3jg=="
  "resolved" "https://registry.npmmirror.com/babel-plugin-polyfill-regenerator/download/babel-plugin-polyfill-regenerator-0.3.0.tgz?cache=0&sync_timestamp=1636799750144&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2Fbabel-plugin-polyfill-regenerator%2Fdownload%2Fbabel-plugin-polyfill-regenerator-0.3.0.tgz"
  "version" "0.3.0"
  dependencies:
    "@babel/helper-define-polyfill-provider" "^0.3.0"

"balanced-match@^1.0.0":
  "integrity" "sha1-6D46fj8wCzTLnYf2FfoMvzV2kO4="
  "resolved" "https://registry.npmmirror.com/balanced-match/download/balanced-match-1.0.2.tgz"
  "version" "1.0.2"

"base@^0.11.1":
  "integrity" "sha1-e95c7RRbbVUakNuH+DxVi060io8="
  "resolved" "https://registry.npmmirror.com/base/download/base-0.11.2.tgz"
  "version" "0.11.2"
  dependencies:
    "cache-base" "^1.0.1"
    "class-utils" "^0.3.5"
    "component-emitter" "^1.2.1"
    "define-property" "^1.0.0"
    "isobject" "^3.0.1"
    "mixin-deep" "^1.2.0"
    "pascalcase" "^0.1.1"

"base64-js@^1.3.1":
  "integrity" "sha1-GxtEAWClv3rUC2UPCVljSBkDkwo="
  "resolved" "https://registry.npmmirror.com/base64-js/download/base64-js-1.5.1.tgz"
  "version" "1.5.1"

"batch@0.6.1":
  "integrity" "sha1-3DQxT05nkxgJP8dgJyUl+UvyXBY="
  "resolved" "https://registry.npmmirror.com/batch/download/batch-0.6.1.tgz"
  "version" "0.6.1"

"big.js@^5.2.2":
  "integrity" "sha1-ZfCvOC9Xi83HQr2cKB6cstd2gyg="
  "resolved" "https://registry.npmmirror.com/big.js/download/big.js-5.2.2.tgz"
  "version" "5.2.2"

"bignumber.js@^9.1.0":
  "integrity" "sha512-2/mKyZH9K85bzOEfhXDBFZTGd1CTs+5IHpeFQo9luiBG7hghdC851Pj2WAhb6E3R6b9tZj/XKhbg4fum+Kepug=="
  "resolved" "https://registry.npmjs.org/bignumber.js/-/bignumber.js-9.1.2.tgz"
  "version" "9.1.2"

"binary-extensions@^2.0.0":
  "integrity" "sha1-dfUC7q+f/eQvyYgpZFvk6na9ni0="
  "resolved" "https://registry.npmmirror.com/binary-extensions/download/binary-extensions-2.2.0.tgz"
  "version" "2.2.0"

"bl@^4.1.0":
  "integrity" "sha1-RRU1JkGCvsL7vIOmKrmM8R2fezo="
  "resolved" "https://registry.npmmirror.com/bl/download/bl-4.1.0.tgz"
  "version" "4.1.0"
  dependencies:
    "buffer" "^5.5.0"
    "inherits" "^2.0.4"
    "readable-stream" "^3.4.0"

"bluebird@^3.1.1", "bluebird@^3.5.0":
  "integrity" "sha1-nyKcFb4nJFT/qXOs4NvueaGww28="
  "resolved" "https://registry.npmmirror.com/bluebird/download/bluebird-3.7.2.tgz"
  "version" "3.7.2"

"body-parser@1.19.0":
  "integrity" "sha1-lrJwnlfJxOCab9Zqj9l5hE9p8Io="
  "resolved" "https://registry.npmmirror.com/body-parser/download/body-parser-1.19.0.tgz"
  "version" "1.19.0"
  dependencies:
    "bytes" "3.1.0"
    "content-type" "~1.0.4"
    "debug" "2.6.9"
    "depd" "~1.1.2"
    "http-errors" "1.7.2"
    "iconv-lite" "0.4.24"
    "on-finished" "~2.3.0"
    "qs" "6.7.0"
    "raw-body" "2.4.0"
    "type-is" "~1.6.17"

"bonjour@^3.5.0":
  "integrity" "sha1-jokKGD2O6aI5OzhExpGkK897yfU="
  "resolved" "https://registry.npmmirror.com/bonjour/download/bonjour-3.5.0.tgz"
  "version" "3.5.0"
  dependencies:
    "array-flatten" "^2.1.0"
    "deep-equal" "^1.0.1"
    "dns-equal" "^1.0.0"
    "dns-txt" "^2.0.2"
    "multicast-dns" "^6.0.1"
    "multicast-dns-service-types" "^1.1.0"

"boolbase@^1.0.0":
  "integrity" "sha1-aN/1++YMUes3cl6p4+0xDcwed24="
  "resolved" "https://registry.npmmirror.com/boolbase/download/boolbase-1.0.0.tgz"
  "version" "1.0.0"

"brace-expansion@^1.1.7":
  "integrity" "sha1-PH/L9SnYcibz0vUrlm/1Jx60Qd0="
  "resolved" "https://registry.npmmirror.com/brace-expansion/download/brace-expansion-1.1.11.tgz"
  "version" "1.1.11"
  dependencies:
    "balanced-match" "^1.0.0"
    "concat-map" "0.0.1"

"brace-expansion@^2.0.1":
  "integrity" "sha512-XnAIvQ8eM+kC6aULx6wuQiwVsnzsi9d3WxzV3FpWTGA19F621kwdbsAcFKXgKUHZWsy+mY6iL1sHTxWEFCytDA=="
  "resolved" "http://castle-npm.cp.hxdi.cn/brace-expansion/-/brace-expansion-2.0.1.tgz"
  "version" "2.0.1"
  dependencies:
    "balanced-match" "^1.0.0"

"braces@^2.2.2":
  "integrity" "sha1-WXn9PxTNUxVl5fot8av/8d+u5yk="
  "resolved" "https://registry.npmmirror.com/braces/download/braces-2.3.2.tgz"
  "version" "2.3.2"
  dependencies:
    "arr-flatten" "^1.1.0"
    "array-unique" "^0.3.2"
    "extend-shallow" "^2.0.1"
    "fill-range" "^4.0.0"
    "isobject" "^3.0.1"
    "repeat-element" "^1.1.2"
    "snapdragon" "^0.8.1"
    "snapdragon-node" "^2.0.1"
    "split-string" "^3.0.2"
    "to-regex" "^3.0.1"

"braces@^3.0.2", "braces@~3.0.2":
  "integrity" "sha1-NFThpGLujVmeI23zNs2epPiv4Qc="
  "resolved" "https://registry.npmmirror.com/braces/download/braces-3.0.2.tgz"
  "version" "3.0.2"
  dependencies:
    "fill-range" "^7.0.1"

"browserslist@^4.0.0", "browserslist@^4.14.5", "browserslist@^4.16.0", "browserslist@^4.16.3", "browserslist@^4.16.6", "browserslist@^4.17.5", "browserslist@^4.18.1", "browserslist@^4.21.10", "browserslist@>= 4.21.0":
  "integrity" "sha512-bipEBdZfVH5/pwrvqc+Ub0kUPVfGUhlKxbvfD+z1BDnPEO/X98ruXGA1WP5ASpAFKan7Qr6j736IacbZQuAlKQ=="
  "resolved" "http://castle-npm.cp.hxdi.cn/browserslist/-/browserslist-4.21.10.tgz"
  "version" "4.21.10"
  dependencies:
    "caniuse-lite" "^1.0.30001517"
    "electron-to-chromium" "^1.4.477"
    "node-releases" "^2.0.13"
    "update-browserslist-db" "^1.0.11"

"buffer-from@^1.0.0":
  "integrity" "sha1-KxRqb9cugLT1XSVfNe1Zo6mkG9U="
  "resolved" "https://registry.npmmirror.com/buffer-from/download/buffer-from-1.1.2.tgz"
  "version" "1.1.2"

"buffer-indexof@^1.0.0":
  "integrity" "sha1-Uvq8xqYG0aADAoAmSO9o9jnaJow="
  "resolved" "https://registry.npmmirror.com/buffer-indexof/download/buffer-indexof-1.1.1.tgz"
  "version" "1.1.1"

"buffer-json@^2.0.0":
  "integrity" "sha1-9z4TseQvGW/i/WfQAcfXEH7dfCM="
  "resolved" "https://registry.npmmirror.com/buffer-json/download/buffer-json-2.0.0.tgz"
  "version" "2.0.0"

"buffer@^5.5.0":
  "integrity" "sha1-umLnwTEzBTWCGXFghRqPZI6Z7tA="
  "resolved" "https://registry.npmmirror.com/buffer/download/buffer-5.7.1.tgz"
  "version" "5.7.1"
  dependencies:
    "base64-js" "^1.3.1"
    "ieee754" "^1.1.13"

"buffer@^6.0.3":
  "integrity" "sha512-FTiCpNxtwiZZHEZbcbTIcZjERVICn9yq/pDFkTl95/AxzD1naBctN7YO68riM/gLSDY7sdrMby8hofADYuuqOA=="
  "resolved" "https://registry.npmmirror.com/buffer/-/buffer-6.0.3.tgz"
  "version" "6.0.3"
  dependencies:
    "base64-js" "^1.3.1"
    "ieee754" "^1.2.1"

"bytes@3.0.0":
  "integrity" "sha1-0ygVQE1olpn4Wk6k+odV3ROpYEg="
  "resolved" "https://registry.npmmirror.com/bytes/download/bytes-3.0.0.tgz?cache=0&sync_timestamp=1637015097029&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2Fbytes%2Fdownload%2Fbytes-3.0.0.tgz"
  "version" "3.0.0"

"bytes@3.1.0":
  "integrity" "sha1-9s95M6Ng4FiPqf3oVlHNx/gF0fY="
  "resolved" "https://registry.npmmirror.com/bytes/download/bytes-3.1.0.tgz?cache=0&sync_timestamp=1637015097029&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2Fbytes%2Fdownload%2Fbytes-3.1.0.tgz"
  "version" "3.1.0"

"cache-base@^1.0.1":
  "integrity" "sha1-Cn9GQWgxyLZi7jb+TnxZ129marI="
  "resolved" "https://registry.npmmirror.com/cache-base/download/cache-base-1.0.1.tgz"
  "version" "1.0.1"
  dependencies:
    "collection-visit" "^1.0.0"
    "component-emitter" "^1.2.1"
    "get-value" "^2.0.6"
    "has-value" "^1.0.0"
    "isobject" "^3.0.1"
    "set-value" "^2.0.0"
    "to-object-path" "^0.3.0"
    "union-value" "^1.0.0"
    "unset-value" "^1.0.0"

"cache-loader@^4.1.0":
  "integrity" "sha1-mUjK41OuwKH8ser9ojAIFuyFOH4="
  "resolved" "https://registry.npmmirror.com/cache-loader/download/cache-loader-4.1.0.tgz"
  "version" "4.1.0"
  dependencies:
    "buffer-json" "^2.0.0"
    "find-cache-dir" "^3.0.0"
    "loader-utils" "^1.2.3"
    "mkdirp" "^0.5.1"
    "neo-async" "^2.6.1"
    "schema-utils" "^2.0.0"

"call-bind@^1.0.0", "call-bind@^1.0.2":
  "integrity" "sha1-sdTonmiBGcPJqQOtMKuy9qkZvjw="
  "resolved" "https://registry.npmmirror.com/call-bind/download/call-bind-1.0.2.tgz"
  "version" "1.0.2"
  dependencies:
    "function-bind" "^1.1.1"
    "get-intrinsic" "^1.0.2"

"callsites@^3.0.0":
  "integrity" "sha1-s2MKvYlDQy9Us/BRkjjjPNffL3M="
  "resolved" "https://registry.npmmirror.com/callsites/download/callsites-3.1.0.tgz"
  "version" "3.1.0"

"camel-case@^4.1.2":
  "integrity" "sha1-lygHKpVPgFIoIlpt7qazhGHhvVo="
  "resolved" "https://registry.npmmirror.com/camel-case/download/camel-case-4.1.2.tgz"
  "version" "4.1.2"
  dependencies:
    "pascal-case" "^3.1.2"
    "tslib" "^2.0.3"

"camelcase-css@^2.0.1":
  "integrity" "sha512-QOSvevhslijgYwRx6Rv7zKdMF8lbRmx+uQGx2+vDc+KI/eBnsy9kit5aj23AgGu3pa4t9AgwbnXWqS+iOY+2aA=="
  "resolved" "http://castle-npm.cp.hxdi.cn/camelcase-css/-/camelcase-css-2.0.1.tgz"
  "version" "2.0.1"

"camelcase@^5.0.0":
  "integrity" "sha1-48mzFWnhBoEd8kL3FXJaH0xJQyA="
  "resolved" "https://registry.npmmirror.com/camelcase/download/camelcase-5.3.1.tgz?cache=0&sync_timestamp=1636945183951&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2Fcamelcase%2Fdownload%2Fcamelcase-5.3.1.tgz"
  "version" "5.3.1"

"camelcase@^6.0.0":
  "integrity" "sha512-tVI4q5jjFV5CavAU8DXfza/TJcZutVKo/5Foskmsqcm0MsL91moHvwiGNnqaa2o6PF/7yT5ikDRcVcl8Rj6LCA=="
  "resolved" "https://registry.npmmirror.com/camelcase/download/camelcase-6.2.1.tgz?cache=0&sync_timestamp=1636945183951&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2Fcamelcase%2Fdownload%2Fcamelcase-6.2.1.tgz"
  "version" "6.2.1"

"caniuse-api@^3.0.0":
  "integrity" "sha1-Xk2Q4idJYdRikZl99Znj7QCO5MA="
  "resolved" "https://registry.npmmirror.com/caniuse-api/download/caniuse-api-3.0.0.tgz"
  "version" "3.0.0"
  dependencies:
    "browserslist" "^4.0.0"
    "caniuse-lite" "^1.0.0"
    "lodash.memoize" "^4.1.2"
    "lodash.uniq" "^4.5.0"

"caniuse-lite@^1.0.0", "caniuse-lite@^1.0.30001517", "caniuse-lite@^1.0.30001520":
  "integrity" "sha512-FbDFnNat3nMnrROzqrsg314zhqN5LGQ1kyyMk2opcrwGbVGpHRhgCWtAgD5YJUqNAiQ+dklreil/c3Qf1dfCTw=="
  "resolved" "http://castle-npm.cp.hxdi.cn/caniuse-lite/-/caniuse-lite-1.0.30001532.tgz"
  "version" "1.0.30001532"

"case-sensitive-paths-webpack-plugin@^2.3.0":
  "integrity" "sha1-22QGbGQi7tLgjMFLmGykN5bbxtQ="
  "resolved" "https://registry.npmmirror.com/case-sensitive-paths-webpack-plugin/download/case-sensitive-paths-webpack-plugin-2.4.0.tgz"
  "version" "2.4.0"

"chalk@^1.1.3":
  "integrity" "sha1-qBFcVeSnAv5NFQq9OHKCKn4J/Jg="
  "resolved" "https://registry.npmmirror.com/chalk/download/chalk-1.1.3.tgz"
  "version" "1.1.3"
  dependencies:
    "ansi-styles" "^2.2.1"
    "escape-string-regexp" "^1.0.2"
    "has-ansi" "^2.0.0"
    "strip-ansi" "^3.0.0"
    "supports-color" "^2.0.0"

"chalk@^2.0.0", "chalk@^2.1.0", "chalk@^2.3.0":
  "integrity" "sha1-zUJUFnelQzPPVBpJEIwUMrRMlCQ="
  "resolved" "https://registry.npmmirror.com/chalk/download/chalk-2.4.2.tgz"
  "version" "2.4.2"
  dependencies:
    "ansi-styles" "^3.2.1"
    "escape-string-regexp" "^1.0.5"
    "supports-color" "^5.3.0"

"chalk@^3.0.0":
  "integrity" "sha1-P3PCv1JlkfV0zEksUeJFY0n4ROQ="
  "resolved" "https://registry.npmmirror.com/chalk/download/chalk-3.0.0.tgz"
  "version" "3.0.0"
  dependencies:
    "ansi-styles" "^4.1.0"
    "supports-color" "^7.1.0"

"chalk@^4.0.0":
  "integrity" "sha1-qsTit3NKdAhnrrFr8CqtVWoeegE="
  "resolved" "https://registry.npmmirror.com/chalk/download/chalk-4.1.2.tgz"
  "version" "4.1.2"
  dependencies:
    "ansi-styles" "^4.1.0"
    "supports-color" "^7.1.0"

"chalk@^4.1.0":
  "integrity" "sha512-oKnbhFyRIXpUuez8iBMmyEa4nbj4IOQyuhc/wy9kY7/WVPcwIO9VA668Pu8RkO7+0G76SLROeyw9CpQ061i4mA=="
  "resolved" "http://castle-npm.cp.hxdi.cn/chalk/-/chalk-4.1.2.tgz"
  "version" "4.1.2"
  dependencies:
    "ansi-styles" "^4.1.0"
    "supports-color" "^7.1.0"

"chalk@^4.1.2":
  "integrity" "sha1-qsTit3NKdAhnrrFr8CqtVWoeegE="
  "resolved" "https://registry.npmmirror.com/chalk/download/chalk-4.1.2.tgz"
  "version" "4.1.2"
  dependencies:
    "ansi-styles" "^4.1.0"
    "supports-color" "^7.1.0"

"chardet@^0.7.0":
  "integrity" "sha512-mT8iDcrh03qDGRRmoA2hmBJnxpllMR+0/0qlzjqZES6NdiWDcZkCNAk4rPFZ9Q85r27unkiNNg8ZOiwZXBHwcA=="
  "resolved" "http://castle-npm.cp.hxdi.cn/chardet/-/chardet-0.7.0.tgz"
  "version" "0.7.0"

"chokidar@^3.5.1", "chokidar@^3.5.2", "chokidar@^3.5.3", "chokidar@>=3.0.0 <4.0.0":
  "integrity" "sha512-Dr3sfKRP6oTcjf2JmUmFJfeVMvXBdegxB0iVQ5eb2V10uFJUCAS8OByZdVAyVb8xXNz3GjjTgj9kLWsZTqE6kw=="
  "resolved" "http://castle-npm.cp.hxdi.cn/chokidar/-/chokidar-3.5.3.tgz"
  "version" "3.5.3"
  dependencies:
    "anymatch" "~3.1.2"
    "braces" "~3.0.2"
    "glob-parent" "~5.1.2"
    "is-binary-path" "~2.1.0"
    "is-glob" "~4.0.1"
    "normalize-path" "~3.0.0"
    "readdirp" "~3.6.0"
  optionalDependencies:
    "fsevents" "~2.3.2"

"chrome-trace-event@^1.0.2":
  "integrity" "sha1-EBXs7UdB4V0GZkqVfbv1DQQeJqw="
  "resolved" "https://registry.npmmirror.com/chrome-trace-event/download/chrome-trace-event-1.0.3.tgz"
  "version" "1.0.3"

"ci-info@^1.5.0":
  "integrity" "sha1-LKINu5zrMtRSSmgzAzE/AwSx5Jc="
  "resolved" "https://registry.npmmirror.com/ci-info/download/ci-info-1.6.0.tgz"
  "version" "1.6.0"

"class-utils@^0.3.5":
  "integrity" "sha1-+TNprouafOAv1B+q0MqDAzGQxGM="
  "resolved" "https://registry.npmmirror.com/class-utils/download/class-utils-0.3.6.tgz"
  "version" "0.3.6"
  dependencies:
    "arr-union" "^3.1.0"
    "define-property" "^0.2.5"
    "isobject" "^3.0.0"
    "static-extend" "^0.1.1"

"clean-css@^5.2.2":
  "integrity" "sha1-06fG7iURAR4FFxmDi9z4MU3EVI0="
  "resolved" "https://registry.npmmirror.com/clean-css/download/clean-css-5.2.2.tgz?cache=0&sync_timestamp=1634992314911&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2Fclean-css%2Fdownload%2Fclean-css-5.2.2.tgz"
  "version" "5.2.2"
  dependencies:
    "source-map" "~0.6.0"

"clean-stack@^2.0.0":
  "integrity" "sha1-7oRy27Ep5yezHooQpCfe6d/kAIs="
  "resolved" "https://registry.npmmirror.com/clean-stack/download/clean-stack-2.2.0.tgz"
  "version" "2.2.0"

"cli-cursor@^2.0.0":
  "integrity" "sha1-s12sN2R5+sw+lHR9QdDQ9SOP/LU="
  "resolved" "https://registry.npmmirror.com/cli-cursor/download/cli-cursor-2.1.0.tgz"
  "version" "2.1.0"
  dependencies:
    "restore-cursor" "^2.0.0"

"cli-cursor@^3.1.0":
  "integrity" "sha1-JkMFp65JDR0Dvwybp8kl0XU68wc="
  "resolved" "https://registry.npmmirror.com/cli-cursor/download/cli-cursor-3.1.0.tgz"
  "version" "3.1.0"
  dependencies:
    "restore-cursor" "^3.1.0"

"cli-highlight@^2.1.10":
  "integrity" "sha1-SXNvpFLwqvT65YDjCssmgo0twb8="
  "resolved" "https://registry.npmmirror.com/cli-highlight/download/cli-highlight-2.1.11.tgz"
  "version" "2.1.11"
  dependencies:
    "chalk" "^4.0.0"
    "highlight.js" "^10.7.1"
    "mz" "^2.4.0"
    "parse5" "^5.1.1"
    "parse5-htmlparser2-tree-adapter" "^6.0.0"
    "yargs" "^16.0.0"

"cli-spinners@^2.5.0":
  "integrity" "sha1-rclU6+KBw3pjGb+kAebdJIj/tw0="
  "resolved" "https://registry.npmmirror.com/cli-spinners/download/cli-spinners-2.6.1.tgz"
  "version" "2.6.1"

"cli-width@^3.0.0":
  "integrity" "sha512-FxqpkPPwu1HjuN93Omfm4h8uIanXofW0RxVEW3k5RKx+mJJYSthzNhp32Kzxxy3YAEZ/Dc/EWN1vZRY0+kOhbw=="
  "resolved" "http://castle-npm.cp.hxdi.cn/cli-width/-/cli-width-3.0.0.tgz"
  "version" "3.0.0"

"clipboardy@^2.3.0":
  "integrity" "sha1-PCkDZQxo5GqRs4iYW8J3QofbopA="
  "resolved" "https://registry.npmmirror.com/clipboardy/download/clipboardy-2.3.0.tgz"
  "version" "2.3.0"
  dependencies:
    "arch" "^2.1.1"
    "execa" "^1.0.0"
    "is-wsl" "^2.1.1"

"cliui@^6.0.0":
  "integrity" "sha512-t6wbgtoCXvAzst7QgXxJYqPt0usEfbgQdftEPbLL/cvv6HPE5VgvqCuAIDR0NgU52ds6rFwqrgakNLrHEjCbrQ=="
  "resolved" "http://castle-npm.cp.hxdi.cn/cliui/-/cliui-6.0.0.tgz"
  "version" "6.0.0"
  dependencies:
    "string-width" "^4.2.0"
    "strip-ansi" "^6.0.0"
    "wrap-ansi" "^6.2.0"

"cliui@^7.0.2", "cliui@^7.0.4":
  "integrity" "sha1-oCZe5lVHb8gHrqnfPfjfd4OAi08="
  "resolved" "https://registry.npmmirror.com/cliui/download/cliui-7.0.4.tgz"
  "version" "7.0.4"
  dependencies:
    "string-width" "^4.2.0"
    "strip-ansi" "^6.0.0"
    "wrap-ansi" "^7.0.0"

"clone-deep@^4.0.1":
  "integrity" "sha1-wZ/Zvbv4WUK0/ZechNz31fB8I4c="
  "resolved" "https://registry.npmmirror.com/clone-deep/download/clone-deep-4.0.1.tgz"
  "version" "4.0.1"
  dependencies:
    "is-plain-object" "^2.0.4"
    "kind-of" "^6.0.2"
    "shallow-clone" "^3.0.0"

"clone@^1.0.2":
  "integrity" "sha1-2jCcwmPfFZlMaIypAheco8fNfH4="
  "resolved" "https://registry.npmmirror.com/clone/download/clone-1.0.4.tgz"
  "version" "1.0.4"

"clone@^2.1.1":
  "integrity" "sha1-G39Ln1kfHo+DZwQBYANFoCiHQ18="
  "resolved" "https://registry.npmmirror.com/clone/download/clone-2.1.2.tgz"
  "version" "2.1.2"

"collection-visit@^1.0.0":
  "integrity" "sha1-S8A3PBZLwykbTTaMgpzxqApZ3KA="
  "resolved" "https://registry.npmmirror.com/collection-visit/download/collection-visit-1.0.0.tgz"
  "version" "1.0.0"
  dependencies:
    "map-visit" "^1.0.0"
    "object-visit" "^1.0.0"

"color-convert@^1.9.0":
  "integrity" "sha1-u3GFBpDh8TZWfeYp0tVHHe2kweg="
  "resolved" "https://registry.npmmirror.com/color-convert/download/color-convert-1.9.3.tgz"
  "version" "1.9.3"
  dependencies:
    "color-name" "1.1.3"

"color-convert@^2.0.1":
  "integrity" "sha1-ctOmjVmMm9s68q0ehPIdiWq9TeM="
  "resolved" "https://registry.npmmirror.com/color-convert/download/color-convert-2.0.1.tgz"
  "version" "2.0.1"
  dependencies:
    "color-name" "~1.1.4"

"color-name@~1.1.4":
  "integrity" "sha1-wqCah6y95pVD3m9j+jmVyCbFNqI="
  "resolved" "https://registry.npmmirror.com/color-name/download/color-name-1.1.4.tgz"
  "version" "1.1.4"

"color-name@1.1.3":
  "integrity" "sha1-p9BVi9icQveV3UIyj3QIMcpTvCU="
  "resolved" "https://registry.npmmirror.com/color-name/download/color-name-1.1.3.tgz"
  "version" "1.1.3"

"colord@^2.9.1":
  "integrity" "sha1-yWHqDv61fJ8PSDRFjybLnMSj+Q4="
  "resolved" "https://registry.npmmirror.com/colord/download/colord-2.9.1.tgz"
  "version" "2.9.1"

"colorette@^2.0.10":
  "integrity" "sha1-cTua+E/bAAE58EVGvUqT9ipQhdo="
  "resolved" "https://registry.npmmirror.com/colorette/download/colorette-2.0.16.tgz?cache=0&sync_timestamp=1633673127412&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2Fcolorette%2Fdownload%2Fcolorette-2.0.16.tgz"
  "version" "2.0.16"

"commander@^2.20.0":
  "integrity" "sha1-/UhehMA+tIgcIHIrpIA16FMa6zM="
  "resolved" "https://registry.npmmirror.com/commander/download/commander-2.20.3.tgz?cache=0&sync_timestamp=1634886503143&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2Fcommander%2Fdownload%2Fcommander-2.20.3.tgz"
  "version" "2.20.3"

"commander@^4.0.0":
  "integrity" "sha512-NOKm8xhkzAjzFx8B2v5OAHT+u5pRQc2UCa2Vq9jYL/31o2wi9mxBA7LIFs3sV5VSC49z6pEhfbMULvShKj26WA=="
  "resolved" "http://castle-npm.cp.hxdi.cn/commander/-/commander-4.1.1.tgz"
  "version" "4.1.1"

"commander@^7.2.0":
  "integrity" "sha1-o2y1fQtQHOEI5NIFWaFQo5HZerc="
  "resolved" "https://registry.npmmirror.com/commander/download/commander-7.2.0.tgz?cache=0&sync_timestamp=1634886503143&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2Fcommander%2Fdownload%2Fcommander-7.2.0.tgz"
  "version" "7.2.0"

"commander@^8.3.0":
  "integrity" "sha1-SDfqGy2me5xhamevuw+v7lZ7ymY="
  "resolved" "https://registry.npmmirror.com/commander/download/commander-8.3.0.tgz?cache=0&sync_timestamp=1634886503143&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2Fcommander%2Fdownload%2Fcommander-8.3.0.tgz"
  "version" "8.3.0"

"commondir@^1.0.1":
  "integrity" "sha1-3dgA2gxmEnOTzKWVDqloo6rxJTs="
  "resolved" "https://registry.npmmirror.com/commondir/download/commondir-1.0.1.tgz"
  "version" "1.0.1"

"component-emitter@^1.2.1":
  "integrity" "sha1-FuQHD7qK4ptnnyIVhT7hgasuq8A="
  "resolved" "https://registry.npmmirror.com/component-emitter/download/component-emitter-1.3.0.tgz"
  "version" "1.3.0"

"compressible@~2.0.16":
  "integrity" "sha1-r1PMprBw1MPAdQ+9dyhqbXzEb7o="
  "resolved" "https://registry.npmmirror.com/compressible/download/compressible-2.0.18.tgz"
  "version" "2.0.18"
  dependencies:
    "mime-db" ">= 1.43.0 < 2"

"compression@^1.7.4":
  "integrity" "sha1-lVI+/xcMpXwpoMpB5v4TH0Hlu48="
  "resolved" "https://registry.npmmirror.com/compression/download/compression-1.7.4.tgz"
  "version" "1.7.4"
  dependencies:
    "accepts" "~1.3.5"
    "bytes" "3.0.0"
    "compressible" "~2.0.16"
    "debug" "2.6.9"
    "on-headers" "~1.0.2"
    "safe-buffer" "5.1.2"
    "vary" "~1.1.2"

"concat-map@0.0.1":
  "integrity" "sha1-2Klr13/Wjfd5OnMDajug1UBdR3s="
  "resolved" "https://registry.npmmirror.com/concat-map/download/concat-map-0.0.1.tgz"
  "version" "0.0.1"

"connect-history-api-fallback@^1.6.0":
  "integrity" "sha1-izIIk1kwjRERFdgcrT/Oq4iPl7w="
  "resolved" "https://registry.npmmirror.com/connect-history-api-fallback/download/connect-history-api-fallback-1.6.0.tgz"
  "version" "1.6.0"

"consolidate@^0.15.1":
  "integrity" "sha1-IasEMjXHGgfUXZqtmFk7DbpWurc="
  "resolved" "https://registry.npmmirror.com/consolidate/download/consolidate-0.15.1.tgz"
  "version" "0.15.1"
  dependencies:
    "bluebird" "^3.1.1"

"content-disposition@0.5.3":
  "integrity" "sha1-4TDK9+cnkIfFYWwgB9BIVpiYT70="
  "resolved" "https://registry.npmmirror.com/content-disposition/download/content-disposition-0.5.3.tgz"
  "version" "0.5.3"
  dependencies:
    "safe-buffer" "5.1.2"

"content-type@~1.0.4":
  "integrity" "sha1-4TjMdeBAxyexlm/l5fjJruJW/js="
  "resolved" "https://registry.npmmirror.com/content-type/download/content-type-1.0.4.tgz"
  "version" "1.0.4"

"convert-source-map@^1.7.0":
  "integrity" "sha1-8zc8MtIbTXgN2ABFFGhPt5HKQ2k="
  "resolved" "https://registry.npmmirror.com/convert-source-map/download/convert-source-map-1.8.0.tgz?cache=0&sync_timestamp=1632741882507&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2Fconvert-source-map%2Fdownload%2Fconvert-source-map-1.8.0.tgz"
  "version" "1.8.0"
  dependencies:
    "safe-buffer" "~5.1.1"

"cookie-signature@1.0.6":
  "integrity" "sha1-4wOogrNCzD7oylE6eZmXNNqzriw="
  "resolved" "https://registry.npmmirror.com/cookie-signature/download/cookie-signature-1.0.6.tgz"
  "version" "1.0.6"

"cookie@0.4.0":
  "integrity" "sha1-vrQ35wIrO21JAZ0IhmUwPr6cFLo="
  "resolved" "https://registry.npmmirror.com/cookie/download/cookie-0.4.0.tgz"
  "version" "0.4.0"

"copy-descriptor@^0.1.0":
  "integrity" "sha1-Z29us8OZl8LuGsOpJP1hJHSPV40="
  "resolved" "https://registry.npmmirror.com/copy-descriptor/download/copy-descriptor-0.1.1.tgz"
  "version" "0.1.1"

"copy-text-to-clipboard@^3.0.1":
  "integrity" "sha512-PFM6BnjLnOON/lB3ta/Jg7Ywsv+l9kQGD4TWDCSlRBGmqnnTM5MrDkhAFgw+8HZt0wW6Q2BBE4cmy9sq+s9Qng=="
  "resolved" "http://castle-npm.cp.hxdi.cn/copy-text-to-clipboard/-/copy-text-to-clipboard-3.1.0.tgz"
  "version" "3.1.0"

"copy-webpack-plugin@^9.0.1":
  "integrity" "sha512-rxnR7PaGigJzhqETHGmAcxKnLZSR5u1Y3/bcIv/1FnqXedcL/E2ewK7ZCNrArJKCiSv8yVXhTqetJh8inDvfsA=="
  "resolved" "https://registry.npmmirror.com/copy-webpack-plugin/download/copy-webpack-plugin-9.1.0.tgz"
  "version" "9.1.0"
  dependencies:
    "fast-glob" "^3.2.7"
    "glob-parent" "^6.0.1"
    "globby" "^11.0.3"
    "normalize-path" "^3.0.0"
    "schema-utils" "^3.1.1"
    "serialize-javascript" "^6.0.0"

"core-js-compat@^3.18.0", "core-js-compat@^3.19.1", "core-js-compat@^3.8.3":
  "integrity" "sha512-59tYzuWgEEVU9r+SRgceIGXSSUn47JknoiXW6Oq7RW8QHjXWz3/vp8pa7dbtuVu40sewz3OP3JmQEcDdztrLhA=="
  "resolved" "https://registry.npmmirror.com/core-js-compat/download/core-js-compat-3.19.3.tgz"
  "version" "3.19.3"
  dependencies:
    "browserslist" "^4.18.1"
    "semver" "7.0.0"

"core-js@^3.11.0", "core-js@^3.8.3":
  "integrity" "sha512-LeLBMgEGSsG7giquSzvgBrTS7V5UL6ks3eQlUSbN8dJStlLFiRzUm5iqsRyzUB8carhfKjkJ2vzKqE6z1Vga9g=="
  "resolved" "https://registry.npmmirror.com/core-js/download/core-js-3.19.3.tgz"
  "version" "3.19.3"

"core-util-is@~1.0.0":
  "integrity" "sha1-pgQtNjTCsn6TKPg3uWX6yDgI24U="
  "resolved" "https://registry.npmmirror.com/core-util-is/download/core-util-is-1.0.3.tgz?cache=0&sync_timestamp=1632397251521&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2Fcore-util-is%2Fdownload%2Fcore-util-is-1.0.3.tgz"
  "version" "1.0.3"

"cosmiconfig@^7.0.0":
  "integrity" "sha1-cU11ZSLKzoZ4Z8y0R0xdAbuuXW0="
  "resolved" "https://registry.npmmirror.com/cosmiconfig/download/cosmiconfig-7.0.1.tgz"
  "version" "7.0.1"
  dependencies:
    "@types/parse-json" "^4.0.0"
    "import-fresh" "^3.2.1"
    "parse-json" "^5.0.0"
    "path-type" "^4.0.0"
    "yaml" "^1.10.0"

"cross-env@7.0.3":
  "integrity" "sha512-+/HKd6EgcQCJGh2PSjZuUitQBQynKor4wrFbRg4DtAgS1aWO+gU52xpH7M9ScGgXSYmAVS9bIJ8EzuaGw0oNAw=="
  "resolved" "http://castle-npm.cp.hxdi.cn/cross-env/-/cross-env-7.0.3.tgz"
  "version" "7.0.3"
  dependencies:
    "cross-spawn" "^7.0.1"

"cross-spawn@^5.0.1":
  "integrity" "sha1-6L0O/uWPz/b4+UUQoKVUu/ojVEk="
  "resolved" "https://registry.npmmirror.com/cross-spawn/download/cross-spawn-5.1.0.tgz"
  "version" "5.1.0"
  dependencies:
    "lru-cache" "^4.0.1"
    "shebang-command" "^1.2.0"
    "which" "^1.2.9"

"cross-spawn@^6.0.0":
  "integrity" "sha1-Sl7Hxk364iw6FBJNus3uhG2Ay8Q="
  "resolved" "https://registry.npmmirror.com/cross-spawn/download/cross-spawn-6.0.5.tgz"
  "version" "6.0.5"
  dependencies:
    "nice-try" "^1.0.4"
    "path-key" "^2.0.1"
    "semver" "^5.5.0"
    "shebang-command" "^1.2.0"
    "which" "^1.2.9"

"cross-spawn@^7.0.1", "cross-spawn@^7.0.2", "cross-spawn@^7.0.3":
  "integrity" "sha1-9zqFudXUHQRVUcF34ogtSshXKKY="
  "resolved" "https://registry.npmmirror.com/cross-spawn/download/cross-spawn-7.0.3.tgz"
  "version" "7.0.3"
  dependencies:
    "path-key" "^3.1.0"
    "shebang-command" "^2.0.0"
    "which" "^2.0.1"

"css-declaration-sorter@^6.0.3":
  "integrity" "sha1-6YUuTPlAunn1CdlCWxN9H5RDjcI="
  "resolved" "https://registry.npmmirror.com/css-declaration-sorter/download/css-declaration-sorter-6.1.3.tgz"
  "version" "6.1.3"
  dependencies:
    "timsort" "^0.3.0"

"css-loader@^6.5.0":
  "integrity" "sha1-DEPU++DZf2mckemBjLWFdZCR0bE="
  "resolved" "https://registry.npmmirror.com/css-loader/download/css-loader-6.5.1.tgz?cache=0&sync_timestamp=1635968462438&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2Fcss-loader%2Fdownload%2Fcss-loader-6.5.1.tgz"
  "version" "6.5.1"
  dependencies:
    "icss-utils" "^5.1.0"
    "postcss" "^8.2.15"
    "postcss-modules-extract-imports" "^3.0.0"
    "postcss-modules-local-by-default" "^4.0.0"
    "postcss-modules-scope" "^3.0.0"
    "postcss-modules-values" "^4.0.0"
    "postcss-value-parser" "^4.1.0"
    "semver" "^7.3.5"

"css-minimizer-webpack-plugin@^3.0.2":
  "integrity" "sha512-5q4myvkmm29jRlI73Fl8Mc008i6o6hCEKnV6/fOrzRVDWD6EFGwDRX+SM2qCVeZ7XiztRDKHpTGDUeUMAOOagg=="
  "resolved" "https://registry.npmmirror.com/css-minimizer-webpack-plugin/download/css-minimizer-webpack-plugin-3.2.0.tgz"
  "version" "3.2.0"
  dependencies:
    "@types/cssnano" "^4.0.1"
    "cssnano" "^5.0.6"
    "jest-worker" "^27.0.2"
    "postcss" "^8.3.5"
    "schema-utils" "^4.0.0"
    "serialize-javascript" "^6.0.0"
    "source-map" "^0.6.1"

"css-select@^4.1.3":
  "integrity" "sha1-pwRA9wMX8maRGK10/xBeZYSccGc="
  "resolved" "https://registry.npmmirror.com/css-select/download/css-select-4.1.3.tgz"
  "version" "4.1.3"
  dependencies:
    "boolbase" "^1.0.0"
    "css-what" "^5.0.0"
    "domhandler" "^4.2.0"
    "domutils" "^2.6.0"
    "nth-check" "^2.0.0"

"css-tree@^1.1.2", "css-tree@^1.1.3":
  "integrity" "sha1-60hw+2/XcHMn7JXC/yqwm16NuR0="
  "resolved" "https://registry.npmmirror.com/css-tree/download/css-tree-1.1.3.tgz"
  "version" "1.1.3"
  dependencies:
    "mdn-data" "2.0.14"
    "source-map" "^0.6.1"

"css-what@^5.0.0":
  "integrity" "sha1-P3tweq32M7r2LCzrhXm1RbtA9/4="
  "resolved" "https://registry.npmmirror.com/css-what/download/css-what-5.1.0.tgz?cache=0&sync_timestamp=1633864166376&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2Fcss-what%2Fdownload%2Fcss-what-5.1.0.tgz"
  "version" "5.1.0"

"cssesc@^3.0.0":
  "integrity" "sha1-N3QZGZA7hoVl4cCep0dEXNGJg+4="
  "resolved" "https://registry.npmmirror.com/cssesc/download/cssesc-3.0.0.tgz"
  "version" "3.0.0"

"cssnano-preset-default@^5.1.8":
  "integrity" "sha512-zWMlP0+AMPBVE852SqTrP0DnhTcTA2C1wAF92TKZ3Va+aUVqLIhkqKlnJIXXdqXD7RN+S1ujuWmNpvrJBiM/vg=="
  "resolved" "https://registry.npmmirror.com/cssnano-preset-default/download/cssnano-preset-default-5.1.8.tgz"
  "version" "5.1.8"
  dependencies:
    "css-declaration-sorter" "^6.0.3"
    "cssnano-utils" "^2.0.1"
    "postcss-calc" "^8.0.0"
    "postcss-colormin" "^5.2.1"
    "postcss-convert-values" "^5.0.2"
    "postcss-discard-comments" "^5.0.1"
    "postcss-discard-duplicates" "^5.0.1"
    "postcss-discard-empty" "^5.0.1"
    "postcss-discard-overridden" "^5.0.1"
    "postcss-merge-longhand" "^5.0.4"
    "postcss-merge-rules" "^5.0.3"
    "postcss-minify-font-values" "^5.0.1"
    "postcss-minify-gradients" "^5.0.3"
    "postcss-minify-params" "^5.0.2"
    "postcss-minify-selectors" "^5.1.0"
    "postcss-normalize-charset" "^5.0.1"
    "postcss-normalize-display-values" "^5.0.1"
    "postcss-normalize-positions" "^5.0.1"
    "postcss-normalize-repeat-style" "^5.0.1"
    "postcss-normalize-string" "^5.0.1"
    "postcss-normalize-timing-functions" "^5.0.1"
    "postcss-normalize-unicode" "^5.0.1"
    "postcss-normalize-url" "^5.0.3"
    "postcss-normalize-whitespace" "^5.0.1"
    "postcss-ordered-values" "^5.0.2"
    "postcss-reduce-initial" "^5.0.2"
    "postcss-reduce-transforms" "^5.0.1"
    "postcss-svgo" "^5.0.3"
    "postcss-unique-selectors" "^5.0.2"

"cssnano-utils@^2.0.1":
  "integrity" "sha1-hmCqKzfthp0uLyKRgZapqLZJjOI="
  "resolved" "https://registry.npmmirror.com/cssnano-utils/download/cssnano-utils-2.0.1.tgz"
  "version" "2.0.1"

"cssnano@^5.0.0", "cssnano@^5.0.6":
  "integrity" "sha512-U38V4x2iJ3ijPdeWqUrEr4eKBB5PbEKsNP5T8xcik2Au3LeMtiMHX0i2Hu9k51FcKofNZumbrcdC6+a521IUHg=="
  "resolved" "https://registry.npmmirror.com/cssnano/download/cssnano-5.0.12.tgz"
  "version" "5.0.12"
  dependencies:
    "cssnano-preset-default" "^5.1.8"
    "is-resolvable" "^1.1.0"
    "lilconfig" "^2.0.3"
    "yaml" "^1.10.2"

"csso@^4.2.0":
  "integrity" "sha1-6jpWE0bo3J9UbW/r7dUBh884lSk="
  "resolved" "https://registry.npmmirror.com/csso/download/csso-4.2.0.tgz"
  "version" "4.2.0"
  dependencies:
    "css-tree" "^1.1.2"

"csstype@^2.6.8":
  "integrity" "sha512-ZVxXaNy28/k3kJg0Fou5MiYpp88j7H9hLZp8PDC3jV0WFjfH5E9xHb56L0W59cPbKbcHXeP4qyT8PrHp8t6LcQ=="
  "resolved" "https://registry.npmmirror.com/csstype/download/csstype-2.6.19.tgz?cache=0&sync_timestamp=1637224446963&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2Fcsstype%2Fdownload%2Fcsstype-2.6.19.tgz"
  "version" "2.6.19"

"dayjs@^1.10.7":
  "integrity" "sha1-LPX5Gt0oEWdIRAhmoKHSbzps5Gg="
  "resolved" "https://registry.npmmirror.com/dayjs/download/dayjs-1.10.7.tgz"
  "version" "1.10.7"

"debug@^2.2.0":
  "integrity" "sha1-XRKFFd8TT/Mn6QpMk/Tgd6U2NB8="
  "resolved" "https://registry.npmmirror.com/debug/download/debug-2.6.9.tgz"
  "version" "2.6.9"
  dependencies:
    "ms" "2.0.0"

"debug@^2.3.3":
  "integrity" "sha1-XRKFFd8TT/Mn6QpMk/Tgd6U2NB8="
  "resolved" "https://registry.npmmirror.com/debug/download/debug-2.6.9.tgz"
  "version" "2.6.9"
  dependencies:
    "ms" "2.0.0"

"debug@^3.1.1":
  "integrity" "sha1-clgLfpFF+zm2Z2+cXl+xALk0F5o="
  "resolved" "https://registry.npmmirror.com/debug/download/debug-3.2.7.tgz"
  "version" "3.2.7"
  dependencies:
    "ms" "^2.1.1"

"debug@^4.0.1", "debug@^4.1.0", "debug@^4.1.1", "debug@^4.3.4":
  "integrity" "sha512-PRWFHuSU3eDtQJPvnNY7Jcket1j0t5OuOsFzPPzsekD52Zl8qUfFIPEiswXqIvHWGVHOgX+7G/vCNNhehwxfkQ=="
  "resolved" "http://castle-npm.cp.hxdi.cn/debug/-/debug-4.3.4.tgz"
  "version" "4.3.4"
  dependencies:
    "ms" "2.1.2"

"debug@2.6.9":
  "integrity" "sha1-XRKFFd8TT/Mn6QpMk/Tgd6U2NB8="
  "resolved" "https://registry.npmmirror.com/debug/download/debug-2.6.9.tgz"
  "version" "2.6.9"
  dependencies:
    "ms" "2.0.0"

"decamelize@^1.2.0":
  "integrity" "sha1-9lNNFRSCabIDUue+4m9QH5oZEpA="
  "resolved" "http://castle-npm.cp.hxdi.cn/decamelize/-/decamelize-1.2.0.tgz"
  "version" "1.2.0"

"decode-uri-component@^0.2.0":
  "integrity" "sha1-6zkTMzRYd1y4TNGh+uBiEGu4dUU="
  "resolved" "https://registry.npmmirror.com/decode-uri-component/download/decode-uri-component-0.2.0.tgz"
  "version" "0.2.0"

"deep-equal@^1.0.1":
  "integrity" "sha1-tcmMlCzv+vfLBR4k4UNKJaLmB2o="
  "resolved" "https://registry.npmmirror.com/deep-equal/download/deep-equal-1.1.1.tgz"
  "version" "1.1.1"
  dependencies:
    "is-arguments" "^1.0.4"
    "is-date-object" "^1.0.1"
    "is-regex" "^1.0.4"
    "object-is" "^1.0.1"
    "object-keys" "^1.1.1"
    "regexp.prototype.flags" "^1.2.0"

"deep-is@^0.1.3":
  "integrity" "sha1-pvLc5hL63S7x9Rm3NVHxfoUZmDE="
  "resolved" "https://registry.npmmirror.com/deep-is/download/deep-is-0.1.4.tgz"
  "version" "0.1.4"

"deepmerge@^1.5.2":
  "integrity" "sha1-EEmdhohEza1P7ghC34x/bwyVp1M="
  "resolved" "https://registry.npmmirror.com/deepmerge/download/deepmerge-1.5.2.tgz"
  "version" "1.5.2"

"deepmerge@1.3.2":
  "integrity" "sha1-FmNpFinU2/42T6EqKk8KqGqjoFA="
  "resolved" "https://registry.npmmirror.com/deepmerge/download/deepmerge-1.3.2.tgz"
  "version" "1.3.2"

"default-gateway@^6.0.3":
  "integrity" "sha1-gZSUyIgFO9t0PtvzQ9bN9/KUOnE="
  "resolved" "https://registry.npmmirror.com/default-gateway/download/default-gateway-6.0.3.tgz"
  "version" "6.0.3"
  dependencies:
    "execa" "^5.0.0"

"defaults@^1.0.3":
  "integrity" "sha1-xlYFHpgX2f8I7YgUd/P+QBnz730="
  "resolved" "https://registry.npmmirror.com/defaults/download/defaults-1.0.3.tgz"
  "version" "1.0.3"
  dependencies:
    "clone" "^1.0.2"

"define-lazy-prop@^2.0.0":
  "integrity" "sha1-P3rkIRKbyqrJvHSQXJigAJ7J7n8="
  "resolved" "https://registry.npmmirror.com/define-lazy-prop/download/define-lazy-prop-2.0.0.tgz"
  "version" "2.0.0"

"define-properties@^1.1.3":
  "integrity" "sha1-z4jabL7ib+bbcJT2HYcMvYTO6fE="
  "resolved" "https://registry.npmmirror.com/define-properties/download/define-properties-1.1.3.tgz"
  "version" "1.1.3"
  dependencies:
    "object-keys" "^1.0.12"

"define-property@^0.2.5":
  "integrity" "sha1-w1se+RjsPJkPmlvFe+BKrOxcgRY="
  "resolved" "https://registry.npmmirror.com/define-property/download/define-property-0.2.5.tgz"
  "version" "0.2.5"
  dependencies:
    "is-descriptor" "^0.1.0"

"define-property@^1.0.0":
  "integrity" "sha1-dp66rz9KY6rTr56NMEybvnm/sOY="
  "resolved" "https://registry.npmmirror.com/define-property/download/define-property-1.0.0.tgz"
  "version" "1.0.0"
  dependencies:
    "is-descriptor" "^1.0.0"

"define-property@^2.0.2":
  "integrity" "sha1-1Flono1lS6d+AqgX+HENcCyxbp0="
  "resolved" "https://registry.npmmirror.com/define-property/download/define-property-2.0.2.tgz"
  "version" "2.0.2"
  dependencies:
    "is-descriptor" "^1.0.2"
    "isobject" "^3.0.1"

"del@^6.0.0":
  "integrity" "sha1-C0DQMyzqdD8WFPgYvk/rcXcUyVI="
  "resolved" "https://registry.npmmirror.com/del/download/del-6.0.0.tgz?cache=0&sync_timestamp=1632753472941&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2Fdel%2Fdownload%2Fdel-6.0.0.tgz"
  "version" "6.0.0"
  dependencies:
    "globby" "^11.0.1"
    "graceful-fs" "^4.2.4"
    "is-glob" "^4.0.1"
    "is-path-cwd" "^2.2.0"
    "is-path-inside" "^3.0.2"
    "p-map" "^4.0.0"
    "rimraf" "^3.0.2"
    "slash" "^3.0.0"

"depd@~1.1.2":
  "integrity" "sha1-m81S4UwJd2PnSbJ0xDRu0uVgtak="
  "resolved" "https://registry.npmmirror.com/depd/download/depd-1.1.2.tgz"
  "version" "1.1.2"

"destroy@~1.0.4":
  "integrity" "sha1-l4hXRCxEdJ5CBmE+N5RiBYJqvYA="
  "resolved" "https://registry.npmmirror.com/destroy/download/destroy-1.0.4.tgz"
  "version" "1.0.4"

"detect-node@^2.0.4":
  "integrity" "sha1-yccHdaScPQO8LAbZpzvlUPl4+LE="
  "resolved" "https://registry.npmmirror.com/detect-node/download/detect-node-2.1.0.tgz"
  "version" "2.1.0"

"didyoumean@^1.2.2":
  "integrity" "sha512-gxtyfqMg7GKyhQmb056K7M3xszy/myH8w+B4RT+QXBQsvAOdc3XymqDDPHx1BgPgsdAA5SIifona89YtRATDzw=="
  "resolved" "http://castle-npm.cp.hxdi.cn/didyoumean/-/didyoumean-1.2.2.tgz"
  "version" "1.2.2"

"dijkstrajs@^1.0.1":
  "integrity" "sha512-QV6PMaHTCNmKSeP6QoXhVTw9snc9VD8MulTT0Bd99Pacp4SS1cjcrYPgBPmibqKVtMJJfqC6XvOXgPMEEPH/fg=="
  "resolved" "http://castle-npm.cp.hxdi.cn/dijkstrajs/-/dijkstrajs-1.0.2.tgz"
  "version" "1.0.2"

"dir-glob@^3.0.1":
  "integrity" "sha1-Vtv3PZkqSpO6FYT0U0Bj/S5BcX8="
  "resolved" "https://registry.npmmirror.com/dir-glob/download/dir-glob-3.0.1.tgz"
  "version" "3.0.1"
  dependencies:
    "path-type" "^4.0.0"

"dlv@^1.1.3":
  "integrity" "sha512-+HlytyjlPKnIG8XuRG8WvmBP8xs8P71y+SKKS6ZXWoEgLuePxtDoUEiH7WkdePWrQ5JBpE6aoVqfZfJUQkjXwA=="
  "resolved" "http://castle-npm.cp.hxdi.cn/dlv/-/dlv-1.1.3.tgz"
  "version" "1.1.3"

"dns-equal@^1.0.0":
  "integrity" "sha1-s55/HabrCnW6nBcySzR1PEfgZU0="
  "resolved" "https://registry.npmmirror.com/dns-equal/download/dns-equal-1.0.0.tgz"
  "version" "1.0.0"

"dns-packet@^1.3.1":
  "integrity" "sha1-40VQZYJKJQe6iGxVqJljuxB97G8="
  "resolved" "https://registry.npmmirror.com/dns-packet/download/dns-packet-1.3.4.tgz"
  "version" "1.3.4"
  dependencies:
    "ip" "^1.1.0"
    "safe-buffer" "^5.0.1"

"dns-txt@^2.0.2":
  "integrity" "sha1-uR2Ab10nGI5Ks+fRB9iBocxGQrY="
  "resolved" "https://registry.npmmirror.com/dns-txt/download/dns-txt-2.0.2.tgz"
  "version" "2.0.2"
  dependencies:
    "buffer-indexof" "^1.0.0"

"doctrine@^3.0.0":
  "integrity" "sha1-rd6+rXKmV023g2OdyHoSF3OXOWE="
  "resolved" "https://registry.npmmirror.com/doctrine/download/doctrine-3.0.0.tgz"
  "version" "3.0.0"
  dependencies:
    "esutils" "^2.0.2"

"dom-converter@^0.2.0":
  "integrity" "sha1-ZyGp2u4uKTaClVtq/kFncWJ7t2g="
  "resolved" "https://registry.npmmirror.com/dom-converter/download/dom-converter-0.2.0.tgz"
  "version" "0.2.0"
  dependencies:
    "utila" "~0.4"

"dom-serializer@^1.0.1":
  "integrity" "sha1-YgZDfTLO767HFhgDIwx6ILwbTZE="
  "resolved" "https://registry.npmmirror.com/dom-serializer/download/dom-serializer-1.3.2.tgz"
  "version" "1.3.2"
  dependencies:
    "domelementtype" "^2.0.1"
    "domhandler" "^4.2.0"
    "entities" "^2.0.0"

"dom-serializer@0":
  "integrity" "sha1-GvuB9TNxcXXUeGVd68XjMtn5u1E="
  "resolved" "https://registry.npmmirror.com/dom-serializer/download/dom-serializer-0.2.2.tgz"
  "version" "0.2.2"
  dependencies:
    "domelementtype" "^2.0.1"
    "entities" "^2.0.0"

"domelementtype@^1.3.1", "domelementtype@1":
  "integrity" "sha1-0EjESzew0Qp/Kj1f7j9DM9eQSB8="
  "resolved" "https://registry.npmmirror.com/domelementtype/download/domelementtype-1.3.1.tgz"
  "version" "1.3.1"

"domelementtype@^2.0.1", "domelementtype@^2.2.0":
  "integrity" "sha1-mgtsJ4LtahxzI9QiZxg9+b2LHVc="
  "resolved" "https://registry.npmmirror.com/domelementtype/download/domelementtype-2.2.0.tgz"
  "version" "2.2.0"

"domhandler@^2.3.0":
  "integrity" "sha1-iAUJfpM9ZehVRvcm1g9euItE+AM="
  "resolved" "https://registry.npmmirror.com/domhandler/download/domhandler-2.4.2.tgz"
  "version" "2.4.2"
  dependencies:
    "domelementtype" "1"

"domhandler@^4.0.0", "domhandler@^4.2.0":
  "integrity" "sha512-fC0aXNQXqKSFTr2wDNZDhsEYjCiYsDWl3D01kwt25hm1YIPyDGHvvi3rw+PLqHAl/m71MaiF7d5zvBr0p5UB2g=="
  "resolved" "https://registry.npmmirror.com/domhandler/download/domhandler-4.3.0.tgz"
  "version" "4.3.0"
  dependencies:
    "domelementtype" "^2.2.0"

"domready@1.0.8":
  "integrity" "sha1-kfJS5Ze2Wvd+dFriTdAYXV4m1Yw="
  "resolved" "https://registry.npmmirror.com/domready/download/domready-1.0.8.tgz"
  "version" "1.0.8"

"domutils@^1.5.1":
  "integrity" "sha1-Vuo0HoNOBuZ0ivehyyXaZ+qfjCo="
  "resolved" "https://registry.npmmirror.com/domutils/download/domutils-1.7.0.tgz"
  "version" "1.7.0"
  dependencies:
    "dom-serializer" "0"
    "domelementtype" "1"

"domutils@^2.5.2", "domutils@^2.6.0":
  "integrity" "sha1-RDfe9dtuLR9dbuhZvZXKfQIEgTU="
  "resolved" "https://registry.npmmirror.com/domutils/download/domutils-2.8.0.tgz"
  "version" "2.8.0"
  dependencies:
    "dom-serializer" "^1.0.1"
    "domelementtype" "^2.2.0"
    "domhandler" "^4.2.0"

"dot-case@^3.0.4":
  "integrity" "sha1-mytnDQCkMWZ6inW6Kc0bmICc51E="
  "resolved" "https://registry.npmmirror.com/dot-case/download/dot-case-3.0.4.tgz"
  "version" "3.0.4"
  dependencies:
    "no-case" "^3.0.4"
    "tslib" "^2.0.3"

"dotenv-expand@^5.1.0":
  "integrity" "sha1-P7rwIL/XlIhAcuomsel5HUWmKfA="
  "resolved" "https://registry.npmmirror.com/dotenv-expand/download/dotenv-expand-5.1.0.tgz"
  "version" "5.1.0"

"dotenv@^10.0.0":
  "integrity" "sha1-PUInuPuV+BCWzdK2ZlP7LHCFuoE="
  "resolved" "https://registry.npmmirror.com/dotenv/download/dotenv-10.0.0.tgz"
  "version" "10.0.0"

"duplexer@^0.1.2":
  "integrity" "sha1-Or5DrvODX4rgd9E23c4PJ2sEAOY="
  "resolved" "https://registry.npmmirror.com/duplexer/download/duplexer-0.1.2.tgz"
  "version" "0.1.2"

"easy-stack@^1.0.1":
  "integrity" "sha1-iv5CZGJpiMq7EfPHBMzQyDVBEGY="
  "resolved" "https://registry.npmmirror.com/easy-stack/download/easy-stack-1.0.1.tgz"
  "version" "1.0.1"

"echarts-liquidfill@^3.1.0":
  "integrity" "sha512-5Dlqs/jTsdTUAsd+K5LPLLTgrbbNORUSBQyk8PSy1Mg2zgHDWm83FmvA4s0ooNepCJojFYRITTQ4GU1UUSKYLw=="
  "resolved" "https://registry.npmmirror.com/echarts-liquidfill/-/echarts-liquidfill-3.1.0.tgz"
  "version" "3.1.0"

"echarts@^5.0.1", "echarts@^5.2.1":
  "integrity" "sha1-7DyLKhUcu6cbo8LHz5svIEfOQ3A="
  "resolved" "https://registry.npmmirror.com/echarts/download/echarts-5.2.2.tgz?cache=0&sync_timestamp=1635741845024&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2Fecharts%2Fdownload%2Fecharts-5.2.2.tgz"
  "version" "5.2.2"
  dependencies:
    "tslib" "2.3.0"
    "zrender" "5.2.1"

"ee-first@1.1.1":
  "integrity" "sha1-WQxhFWsK4vTwJVcyoViyZrxWsh0="
  "resolved" "https://registry.npmmirror.com/ee-first/download/ee-first-1.1.1.tgz"
  "version" "1.1.1"

"electron-to-chromium@^1.4.477":
  "integrity" "sha512-cOB0xcInjm+E5qIssHeXJ29BaUyWpMyFKT5RB3bsLENDheCja0wMkHJyiPl0NBE/VzDI7JDuNEQWhe6RitEUcw=="
  "resolved" "http://castle-npm.cp.hxdi.cn/electron-to-chromium/-/electron-to-chromium-1.4.513.tgz"
  "version" "1.4.513"

"emoji-regex@^8.0.0":
  "integrity" "sha1-6Bj9ac5cz8tARZT4QpY79TFkzDc="
  "resolved" "https://registry.npmmirror.com/emoji-regex/download/emoji-regex-8.0.0.tgz?cache=0&sync_timestamp=1632753445267&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2Femoji-regex%2Fdownload%2Femoji-regex-8.0.0.tgz"
  "version" "8.0.0"

"emojis-list@^3.0.0":
  "integrity" "sha1-VXBmIEatKeLpFucariYKvf9Pang="
  "resolved" "https://registry.npmmirror.com/emojis-list/download/emojis-list-3.0.0.tgz"
  "version" "3.0.0"

"encode-utf8@^1.0.3":
  "integrity" "sha512-ucAnuBEhUK4boH2HjVYG5Q2mQyPorvv0u/ocS+zhdw0S8AlHYY+GOFhP1Gio5z4icpP2ivFSvhtFjQi8+T9ppw=="
  "resolved" "http://castle-npm.cp.hxdi.cn/encode-utf8/-/encode-utf8-1.0.3.tgz"
  "version" "1.0.3"

"encodeurl@~1.0.2":
  "integrity" "sha1-rT/0yG7C0CkyL1oCw6mmBslbP1k="
  "resolved" "https://registry.npmmirror.com/encodeurl/download/encodeurl-1.0.2.tgz"
  "version" "1.0.2"

"end-of-stream@^1.1.0":
  "integrity" "sha1-WuZKX0UFe682JuwU2gyl5LJDHrA="
  "resolved" "https://registry.npmmirror.com/end-of-stream/download/end-of-stream-1.4.4.tgz"
  "version" "1.4.4"
  dependencies:
    "once" "^1.4.0"

"enhanced-resolve@^5.8.3":
  "integrity" "sha1-bVUtRlzOBCP1s9cYUR6lOCansvA="
  "resolved" "https://registry.npmmirror.com/enhanced-resolve/download/enhanced-resolve-5.8.3.tgz"
  "version" "5.8.3"
  dependencies:
    "graceful-fs" "^4.2.4"
    "tapable" "^2.2.0"

"enquirer@^2.3.5":
  "integrity" "sha1-Kn/l3WNKHkElqXXsmU/1RW3Dc00="
  "resolved" "https://registry.npmmirror.com/enquirer/download/enquirer-2.3.6.tgz"
  "version" "2.3.6"
  dependencies:
    "ansi-colors" "^4.1.1"

"entities@^1.1.1":
  "integrity" "sha1-vfpzUplmTfr9NFKe1PhSKidf6lY="
  "resolved" "https://registry.npmmirror.com/entities/download/entities-1.1.2.tgz"
  "version" "1.1.2"

"entities@^2.0.0":
  "integrity" "sha1-CY3JDruD2N/6CJ1VJWs1HTTE2lU="
  "resolved" "https://registry.npmmirror.com/entities/download/entities-2.2.0.tgz"
  "version" "2.2.0"

"error-ex@^1.3.1":
  "integrity" "sha1-tKxAZIEH/c3PriQvQovqihTU8b8="
  "resolved" "https://registry.npmmirror.com/error-ex/download/error-ex-1.3.2.tgz"
  "version" "1.3.2"
  dependencies:
    "is-arrayish" "^0.2.1"

"error-stack-parser@^2.0.6":
  "integrity" "sha1-WpmnB716TFinl5AtSNgoA+3mqtg="
  "resolved" "https://registry.npmmirror.com/error-stack-parser/download/error-stack-parser-2.0.6.tgz"
  "version" "2.0.6"
  dependencies:
    "stackframe" "^1.1.1"

"es-module-lexer@^0.9.0":
  "integrity" "sha1-bxPbAMw4QXE32vdDZvU1yOtDjxk="
  "resolved" "https://registry.npmmirror.com/es-module-lexer/download/es-module-lexer-0.9.3.tgz"
  "version" "0.9.3"

"escalade@^3.1.1":
  "integrity" "sha1-2M/ccACWXFoBdLSoLqpcBVJ0LkA="
  "resolved" "https://registry.npmmirror.com/escalade/download/escalade-3.1.1.tgz"
  "version" "3.1.1"

"escape-html@~1.0.3":
  "integrity" "sha1-Aljq5NPQwJdN4cFpGI7wBR0dGYg="
  "resolved" "https://registry.npmmirror.com/escape-html/download/escape-html-1.0.3.tgz"
  "version" "1.0.3"

"escape-string-regexp@^1.0.2", "escape-string-regexp@^1.0.5", "escape-string-regexp@1.0.5":
  "integrity" "sha1-G2HAViGQqN/2rjuyzwIAyhMLhtQ="
  "resolved" "https://registry.npmmirror.com/escape-string-regexp/download/escape-string-regexp-1.0.5.tgz"
  "version" "1.0.5"

"escape-string-regexp@^4.0.0":
  "integrity" "sha1-FLqDpdNz49MR5a/KKc9b+tllvzQ="
  "resolved" "https://registry.npmmirror.com/escape-string-regexp/download/escape-string-regexp-4.0.0.tgz"
  "version" "4.0.0"

"eslint-config-prettier@^6.0.0":
  "integrity" "sha1-f5P2y31FqS8VN6cOzAY2bhrG/tk="
  "resolved" "https://registry.npmmirror.com/eslint-config-prettier/download/eslint-config-prettier-6.15.0.tgz"
  "version" "6.15.0"
  dependencies:
    "get-stdin" "^6.0.0"

"eslint-plugin-prettier@^3.1.0", "eslint-plugin-prettier@^3.3.1":
  "integrity" "sha1-6d2yAO+289Bf/oOxZlpxavSjh+U="
  "resolved" "https://registry.npmmirror.com/eslint-plugin-prettier/download/eslint-plugin-prettier-3.4.1.tgz"
  "version" "3.4.1"
  dependencies:
    "prettier-linter-helpers" "^1.0.0"

"eslint-plugin-vue@^7.2.0":
  "integrity" "sha1-mMIYhaa/3wcTw6kpV6Wv6q7tklM="
  "resolved" "https://registry.npmmirror.com/eslint-plugin-vue/download/eslint-plugin-vue-7.20.0.tgz"
  "version" "7.20.0"
  dependencies:
    "eslint-utils" "^2.1.0"
    "natural-compare" "^1.4.0"
    "semver" "^6.3.0"
    "vue-eslint-parser" "^7.10.0"

"eslint-scope@^5.1.1", "eslint-scope@5.1.1":
  "integrity" "sha1-54blmmbLkrP2wfsNUIqrF0hI9Iw="
  "resolved" "https://registry.npmmirror.com/eslint-scope/download/eslint-scope-5.1.1.tgz?cache=0&sync_timestamp=1637465854389&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2Feslint-scope%2Fdownload%2Feslint-scope-5.1.1.tgz"
  "version" "5.1.1"
  dependencies:
    "esrecurse" "^4.3.0"
    "estraverse" "^4.1.1"

"eslint-utils@^2.1.0":
  "integrity" "sha1-0t5eA0JOcH3BDHQGjd7a5wh0Gyc="
  "resolved" "https://registry.npmmirror.com/eslint-utils/download/eslint-utils-2.1.0.tgz?cache=0&sync_timestamp=1632470817621&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2Feslint-utils%2Fdownload%2Feslint-utils-2.1.0.tgz"
  "version" "2.1.0"
  dependencies:
    "eslint-visitor-keys" "^1.1.0"

"eslint-visitor-keys@^1.1.0":
  "integrity" "sha1-MOvR73wv3/AcOk8VEESvJfqwUj4="
  "resolved" "https://registry.npmmirror.com/eslint-visitor-keys/download/eslint-visitor-keys-1.3.0.tgz?cache=0&sync_timestamp=1636378586224&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2Feslint-visitor-keys%2Fdownload%2Feslint-visitor-keys-1.3.0.tgz"
  "version" "1.3.0"

"eslint-visitor-keys@^1.3.0":
  "integrity" "sha1-MOvR73wv3/AcOk8VEESvJfqwUj4="
  "resolved" "https://registry.npmmirror.com/eslint-visitor-keys/download/eslint-visitor-keys-1.3.0.tgz?cache=0&sync_timestamp=1636378586224&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2Feslint-visitor-keys%2Fdownload%2Feslint-visitor-keys-1.3.0.tgz"
  "version" "1.3.0"

"eslint-visitor-keys@^2.0.0", "eslint-visitor-keys@^2.1.0":
  "integrity" "sha1-9lMoJZMFknOSyTjtROsKXJsr0wM="
  "resolved" "https://registry.npmmirror.com/eslint-visitor-keys/download/eslint-visitor-keys-2.1.0.tgz?cache=0&sync_timestamp=1636378586224&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2Feslint-visitor-keys%2Fdownload%2Feslint-visitor-keys-2.1.0.tgz"
  "version" "2.1.0"

"eslint-webpack-plugin@^3.1.0":
  "integrity" "sha512-xSucskTN9tOkfW7so4EaiFIkulWLXwCB/15H917lR6pTv0Zot6/fetFucmENRb7J5whVSFKIvwnrnsa78SG2yg=="
  "resolved" "https://registry.npmmirror.com/eslint-webpack-plugin/download/eslint-webpack-plugin-3.1.1.tgz?cache=0&sync_timestamp=1636732942505&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2Feslint-webpack-plugin%2Fdownload%2Feslint-webpack-plugin-3.1.1.tgz"
  "version" "3.1.1"
  dependencies:
    "@types/eslint" "^7.28.2"
    "jest-worker" "^27.3.1"
    "micromatch" "^4.0.4"
    "normalize-path" "^3.0.0"
    "schema-utils" "^3.1.1"

"eslint@^6.2.0 || ^7.0.0 || ^8.0.0", "eslint@^7.0.0 || ^8.0.0", "eslint@^7.20.0", "eslint@^7.5.0 || ^8.0.0", "eslint@>= 5.0.0", "eslint@>=3.14.1", "eslint@>=5.0.0", "eslint@>=7.5.0":
  "integrity" "sha1-xtMooUvj+wjI0dIeEsAv23oqgS0="
  "resolved" "https://registry.npmmirror.com/eslint/download/eslint-7.32.0.tgz"
  "version" "7.32.0"
  dependencies:
    "@babel/code-frame" "7.12.11"
    "@eslint/eslintrc" "^0.4.3"
    "@humanwhocodes/config-array" "^0.5.0"
    "ajv" "^6.10.0"
    "chalk" "^4.0.0"
    "cross-spawn" "^7.0.2"
    "debug" "^4.0.1"
    "doctrine" "^3.0.0"
    "enquirer" "^2.3.5"
    "escape-string-regexp" "^4.0.0"
    "eslint-scope" "^5.1.1"
    "eslint-utils" "^2.1.0"
    "eslint-visitor-keys" "^2.0.0"
    "espree" "^7.3.1"
    "esquery" "^1.4.0"
    "esutils" "^2.0.2"
    "fast-deep-equal" "^3.1.3"
    "file-entry-cache" "^6.0.1"
    "functional-red-black-tree" "^1.0.1"
    "glob-parent" "^5.1.2"
    "globals" "^13.6.0"
    "ignore" "^4.0.6"
    "import-fresh" "^3.0.0"
    "imurmurhash" "^0.1.4"
    "is-glob" "^4.0.0"
    "js-yaml" "^3.13.1"
    "json-stable-stringify-without-jsonify" "^1.0.1"
    "levn" "^0.4.1"
    "lodash.merge" "^4.6.2"
    "minimatch" "^3.0.4"
    "natural-compare" "^1.4.0"
    "optionator" "^0.9.1"
    "progress" "^2.0.0"
    "regexpp" "^3.1.0"
    "semver" "^7.2.1"
    "strip-ansi" "^6.0.0"
    "strip-json-comments" "^3.1.0"
    "table" "^6.0.9"
    "text-table" "^0.2.0"
    "v8-compile-cache" "^2.0.3"

"espree@^6.2.1":
  "integrity" "sha1-d/xy4f10SiBSwg84pbV1gy6Cc0o="
  "resolved" "https://registry.npmmirror.com/espree/download/espree-6.2.1.tgz"
  "version" "6.2.1"
  dependencies:
    "acorn" "^7.1.1"
    "acorn-jsx" "^5.2.0"
    "eslint-visitor-keys" "^1.1.0"

"espree@^7.3.0", "espree@^7.3.1":
  "integrity" "sha1-8t8zC3Usb1UBn4vYm3ZgA5wbu7Y="
  "resolved" "https://registry.npmmirror.com/espree/download/espree-7.3.1.tgz"
  "version" "7.3.1"
  dependencies:
    "acorn" "^7.4.0"
    "acorn-jsx" "^5.3.1"
    "eslint-visitor-keys" "^1.3.0"

"esprima@^4.0.0":
  "integrity" "sha1-E7BM2z5sXRnfkatph6hpVhmwqnE="
  "resolved" "https://registry.npmmirror.com/esprima/download/esprima-4.0.1.tgz"
  "version" "4.0.1"

"esquery@^1.4.0":
  "integrity" "sha1-IUj/w4uC6McFff7UhCWz5h8PJKU="
  "resolved" "https://registry.npmmirror.com/esquery/download/esquery-1.4.0.tgz"
  "version" "1.4.0"
  dependencies:
    "estraverse" "^5.1.0"

"esrecurse@^4.3.0":
  "integrity" "sha1-eteWTWeauyi+5yzsY3WLHF0smSE="
  "resolved" "https://registry.npmmirror.com/esrecurse/download/esrecurse-4.3.0.tgz"
  "version" "4.3.0"
  dependencies:
    "estraverse" "^5.2.0"

"estraverse@^4.1.1":
  "integrity" "sha1-OYrT88WiSUi+dyXoPRGn3ijNvR0="
  "resolved" "https://registry.npmmirror.com/estraverse/download/estraverse-4.3.0.tgz?cache=0&sync_timestamp=1635237800177&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2Festraverse%2Fdownload%2Festraverse-4.3.0.tgz"
  "version" "4.3.0"

"estraverse@^5.1.0":
  "integrity" "sha1-LupSkHAvJquP5TcDcP+GyWXSESM="
  "resolved" "https://registry.npmmirror.com/estraverse/download/estraverse-5.3.0.tgz?cache=0&sync_timestamp=1635237800177&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2Festraverse%2Fdownload%2Festraverse-5.3.0.tgz"
  "version" "5.3.0"

"estraverse@^5.2.0":
  "integrity" "sha1-LupSkHAvJquP5TcDcP+GyWXSESM="
  "resolved" "https://registry.npmmirror.com/estraverse/download/estraverse-5.3.0.tgz?cache=0&sync_timestamp=1635237800177&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2Festraverse%2Fdownload%2Festraverse-5.3.0.tgz"
  "version" "5.3.0"

"estree-walker@^2.0.2":
  "integrity" "sha1-UvAQF4wqTBF6d1fP6UKtt9LaTKw="
  "resolved" "https://registry.npmmirror.com/estree-walker/download/estree-walker-2.0.2.tgz"
  "version" "2.0.2"

"esutils@^2.0.2":
  "integrity" "sha1-dNLrTeC42hKTcRkQ1Qd1ubcQ72Q="
  "resolved" "https://registry.npmmirror.com/esutils/download/esutils-2.0.3.tgz"
  "version" "2.0.3"

"etag@~1.8.1":
  "integrity" "sha1-Qa4u62XvpiJorr/qg6x9eSmbCIc="
  "resolved" "https://registry.npmmirror.com/etag/download/etag-1.8.1.tgz"
  "version" "1.8.1"

"event-pubsub@4.3.0":
  "integrity" "sha1-9o2Ba8KfHsAsU53FjI3UDOcss24="
  "resolved" "https://registry.npmmirror.com/event-pubsub/download/event-pubsub-4.3.0.tgz"
  "version" "4.3.0"

"eventemitter3@^4.0.0":
  "integrity" "sha1-Lem2j2Uo1WRO9cWVJqG0oHMGFp8="
  "resolved" "https://registry.npmmirror.com/eventemitter3/download/eventemitter3-4.0.7.tgz"
  "version" "4.0.7"

"events@^3.2.0":
  "integrity" "sha1-Mala0Kkk4tLEGagTrrLE6HjqdAA="
  "resolved" "https://registry.npmmirror.com/events/download/events-3.3.0.tgz?cache=0&sync_timestamp=1636449286836&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2Fevents%2Fdownload%2Fevents-3.3.0.tgz"
  "version" "3.3.0"

"execa@^0.8.0":
  "integrity" "sha1-2NdrvBtVIX7RkP1t1J08d07PyNo="
  "resolved" "https://registry.npmmirror.com/execa/download/execa-0.8.0.tgz?cache=0&sync_timestamp=1637147236741&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2Fexeca%2Fdownload%2Fexeca-0.8.0.tgz"
  "version" "0.8.0"
  dependencies:
    "cross-spawn" "^5.0.1"
    "get-stream" "^3.0.0"
    "is-stream" "^1.1.0"
    "npm-run-path" "^2.0.0"
    "p-finally" "^1.0.0"
    "signal-exit" "^3.0.0"
    "strip-eof" "^1.0.0"

"execa@^1.0.0":
  "integrity" "sha1-xiNqW7TfbW8V6I5/AXeYIWdJ3dg="
  "resolved" "https://registry.npmmirror.com/execa/download/execa-1.0.0.tgz?cache=0&sync_timestamp=1637147236741&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2Fexeca%2Fdownload%2Fexeca-1.0.0.tgz"
  "version" "1.0.0"
  dependencies:
    "cross-spawn" "^6.0.0"
    "get-stream" "^4.0.0"
    "is-stream" "^1.1.0"
    "npm-run-path" "^2.0.0"
    "p-finally" "^1.0.0"
    "signal-exit" "^3.0.0"
    "strip-eof" "^1.0.0"

"execa@^5.0.0":
  "integrity" "sha1-+ArZy/Qpj3vR1MlVXCHpN0HEEd0="
  "resolved" "https://registry.npmmirror.com/execa/download/execa-5.1.1.tgz?cache=0&sync_timestamp=1637147236741&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2Fexeca%2Fdownload%2Fexeca-5.1.1.tgz"
  "version" "5.1.1"
  dependencies:
    "cross-spawn" "^7.0.3"
    "get-stream" "^6.0.0"
    "human-signals" "^2.1.0"
    "is-stream" "^2.0.0"
    "merge-stream" "^2.0.0"
    "npm-run-path" "^4.0.1"
    "onetime" "^5.1.2"
    "signal-exit" "^3.0.3"
    "strip-final-newline" "^2.0.0"

"expand-brackets@^2.1.4":
  "integrity" "sha1-t3c14xXOMPa27/D4OwQVGiJEliI="
  "resolved" "https://registry.npmmirror.com/expand-brackets/download/expand-brackets-2.1.4.tgz"
  "version" "2.1.4"
  dependencies:
    "debug" "^2.3.3"
    "define-property" "^0.2.5"
    "extend-shallow" "^2.0.1"
    "posix-character-classes" "^0.1.0"
    "regex-not" "^1.0.0"
    "snapdragon" "^0.8.1"
    "to-regex" "^3.0.1"

"express@^4.17.1":
  "integrity" "sha1-RJH8OGBc9R+GKdOcK10Cb5ikwTQ="
  "resolved" "https://registry.npmmirror.com/express/download/express-4.17.1.tgz"
  "version" "4.17.1"
  dependencies:
    "accepts" "~1.3.7"
    "array-flatten" "1.1.1"
    "body-parser" "1.19.0"
    "content-disposition" "0.5.3"
    "content-type" "~1.0.4"
    "cookie" "0.4.0"
    "cookie-signature" "1.0.6"
    "debug" "2.6.9"
    "depd" "~1.1.2"
    "encodeurl" "~1.0.2"
    "escape-html" "~1.0.3"
    "etag" "~1.8.1"
    "finalhandler" "~1.1.2"
    "fresh" "0.5.2"
    "merge-descriptors" "1.0.1"
    "methods" "~1.1.2"
    "on-finished" "~2.3.0"
    "parseurl" "~1.3.3"
    "path-to-regexp" "0.1.7"
    "proxy-addr" "~2.0.5"
    "qs" "6.7.0"
    "range-parser" "~1.2.1"
    "safe-buffer" "5.1.2"
    "send" "0.17.1"
    "serve-static" "1.14.1"
    "setprototypeof" "1.1.1"
    "statuses" "~1.5.0"
    "type-is" "~1.6.18"
    "utils-merge" "1.0.1"
    "vary" "~1.1.2"

"extend-shallow@^2.0.1":
  "integrity" "sha1-Ua99YUrZqfYQ6huvu5idaxxWiQ8="
  "resolved" "https://registry.npmmirror.com/extend-shallow/download/extend-shallow-2.0.1.tgz"
  "version" "2.0.1"
  dependencies:
    "is-extendable" "^0.1.0"

"extend-shallow@^3.0.0":
  "integrity" "sha1-Jqcarwc7OfshJxcnRhMcJwQCjbg="
  "resolved" "https://registry.npmmirror.com/extend-shallow/download/extend-shallow-3.0.2.tgz"
  "version" "3.0.2"
  dependencies:
    "assign-symbols" "^1.0.0"
    "is-extendable" "^1.0.1"

"extend-shallow@^3.0.2":
  "integrity" "sha1-Jqcarwc7OfshJxcnRhMcJwQCjbg="
  "resolved" "https://registry.npmmirror.com/extend-shallow/download/extend-shallow-3.0.2.tgz"
  "version" "3.0.2"
  dependencies:
    "assign-symbols" "^1.0.0"
    "is-extendable" "^1.0.1"

"external-editor@^3.0.3":
  "integrity" "sha512-hMQ4CX1p1izmuLYyZqLMO/qGNw10wSv9QDCPfzXfyFrOaCSSoRfqE1Kf1s5an66J5JZC62NewG+mK49jOCtQew=="
  "resolved" "http://castle-npm.cp.hxdi.cn/external-editor/-/external-editor-3.1.0.tgz"
  "version" "3.1.0"
  dependencies:
    "chardet" "^0.7.0"
    "iconv-lite" "^0.4.24"
    "tmp" "^0.0.33"

"extglob@^2.0.2":
  "integrity" "sha1-rQD+TcYSqSMuhxhxHcXLWrAoVUM="
  "resolved" "https://registry.npmmirror.com/extglob/download/extglob-2.0.4.tgz"
  "version" "2.0.4"
  dependencies:
    "array-unique" "^0.3.2"
    "define-property" "^1.0.0"
    "expand-brackets" "^2.1.4"
    "extend-shallow" "^2.0.1"
    "fragment-cache" "^0.2.1"
    "regex-not" "^1.0.0"
    "snapdragon" "^0.8.1"
    "to-regex" "^3.0.1"

"fast-deep-equal@^3.1.1", "fast-deep-equal@^3.1.3":
  "integrity" "sha1-On1WtVnWy8PrUSMlJE5hmmXGxSU="
  "resolved" "https://registry.npmmirror.com/fast-deep-equal/download/fast-deep-equal-3.1.3.tgz"
  "version" "3.1.3"

"fast-diff@^1.1.2":
  "integrity" "sha1-c+4RmC2Gyq95WYKNUZz+kn+sXwM="
  "resolved" "https://registry.npmmirror.com/fast-diff/download/fast-diff-1.2.0.tgz"
  "version" "1.2.0"

"fast-glob@^3.1.1", "fast-glob@^3.2.12", "fast-glob@^3.2.7", "fast-glob@^3.3.0":
  "integrity" "sha512-kNFPyjhh5cKjrUltxs+wFx+ZkbRaxxmZ+X0ZU31SOsxCEtP9VPgtq2teZw1DebupL5GmDaNQ6yKMMVcM41iqDg=="
  "resolved" "http://castle-npm.cp.hxdi.cn/fast-glob/-/fast-glob-3.3.1.tgz"
  "version" "3.3.1"
  dependencies:
    "@nodelib/fs.stat" "^2.0.2"
    "@nodelib/fs.walk" "^1.2.3"
    "glob-parent" "^5.1.2"
    "merge2" "^1.3.0"
    "micromatch" "^4.0.4"

"fast-json-stable-stringify@^2.0.0":
  "integrity" "sha1-h0v2nG9ATCtdmcSBNBOZ/VWJJjM="
  "resolved" "https://registry.npmmirror.com/fast-json-stable-stringify/download/fast-json-stable-stringify-2.1.0.tgz"
  "version" "2.1.0"

"fast-levenshtein@^2.0.6":
  "integrity" "sha1-PYpcZog6FqMMqGQ+hR8Zuqd5eRc="
  "resolved" "https://registry.npmmirror.com/fast-levenshtein/download/fast-levenshtein-2.0.6.tgz"
  "version" "2.0.6"

"fastq@^1.6.0":
  "integrity" "sha1-YWdg+Ip1Jr38WWt8q4wYk4w2uYw="
  "resolved" "https://registry.npmmirror.com/fastq/download/fastq-1.13.0.tgz"
  "version" "1.13.0"
  dependencies:
    "reusify" "^1.0.4"

"faye-websocket@^0.11.3":
  "integrity" "sha1-fw2Sdc/dhqHJY9yLZfzEUe3Lsdo="
  "resolved" "https://registry.npmmirror.com/faye-websocket/download/faye-websocket-0.11.4.tgz"
  "version" "0.11.4"
  dependencies:
    "websocket-driver" ">=0.5.1"

"figures@^2.0.0":
  "integrity" "sha1-OrGi0qYsi/tDGgyUy3l6L84nyWI="
  "resolved" "https://registry.npmmirror.com/figures/download/figures-2.0.0.tgz"
  "version" "2.0.0"
  dependencies:
    "escape-string-regexp" "^1.0.5"

"figures@^3.0.0":
  "integrity" "sha512-yaduQFRKLXYOGgEn6AZau90j3ggSOyiqXU0F9JZfeXYhNa+Jk4X+s45A2zg5jns87GAFa34BBm2kXw4XpNcbdg=="
  "resolved" "http://castle-npm.cp.hxdi.cn/figures/-/figures-3.2.0.tgz"
  "version" "3.2.0"
  dependencies:
    "escape-string-regexp" "^1.0.5"

"file-entry-cache@^6.0.1":
  "integrity" "sha1-IRst2WWcsDlLBz5zI6w8kz1SICc="
  "resolved" "https://registry.npmmirror.com/file-entry-cache/download/file-entry-cache-6.0.1.tgz"
  "version" "6.0.1"
  dependencies:
    "flat-cache" "^3.0.4"

"fill-range@^4.0.0":
  "integrity" "sha1-1USBHUKPmOsGpj3EAtJAPDKMOPc="
  "resolved" "https://registry.npmmirror.com/fill-range/download/fill-range-4.0.0.tgz"
  "version" "4.0.0"
  dependencies:
    "extend-shallow" "^2.0.1"
    "is-number" "^3.0.0"
    "repeat-string" "^1.6.1"
    "to-regex-range" "^2.1.0"

"fill-range@^7.0.1":
  "integrity" "sha1-GRmmp8df44ssfHflGYU12prN2kA="
  "resolved" "https://registry.npmmirror.com/fill-range/download/fill-range-7.0.1.tgz"
  "version" "7.0.1"
  dependencies:
    "to-regex-range" "^5.0.1"

"finalhandler@~1.1.2":
  "integrity" "sha1-t+fQAP/RGTjQ/bBTUG9uur6fWH0="
  "resolved" "https://registry.npmmirror.com/finalhandler/download/finalhandler-1.1.2.tgz"
  "version" "1.1.2"
  dependencies:
    "debug" "2.6.9"
    "encodeurl" "~1.0.2"
    "escape-html" "~1.0.3"
    "on-finished" "~2.3.0"
    "parseurl" "~1.3.3"
    "statuses" "~1.5.0"
    "unpipe" "~1.0.0"

"find-cache-dir@^3.0.0", "find-cache-dir@^3.3.1":
  "integrity" "sha1-swxbbv8HMHMa6pu9nb7L2AJW1ks="
  "resolved" "https://registry.npmmirror.com/find-cache-dir/download/find-cache-dir-3.3.2.tgz"
  "version" "3.3.2"
  dependencies:
    "commondir" "^1.0.1"
    "make-dir" "^3.0.2"
    "pkg-dir" "^4.1.0"

"find-up@^4.0.0", "find-up@^4.1.0":
  "integrity" "sha1-l6/n1s3AvFkoWEt8jXsW6KmqXRk="
  "resolved" "https://registry.npmmirror.com/find-up/download/find-up-4.1.0.tgz?cache=0&sync_timestamp=1633618659233&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2Ffind-up%2Fdownload%2Ffind-up-4.1.0.tgz"
  "version" "4.1.0"
  dependencies:
    "locate-path" "^5.0.0"
    "path-exists" "^4.0.0"

"flat-cache@^3.0.4":
  "integrity" "sha1-YbAzgwKy/p+Vfcwy/CqH8cMEixE="
  "resolved" "https://registry.npmmirror.com/flat-cache/download/flat-cache-3.0.4.tgz"
  "version" "3.0.4"
  dependencies:
    "flatted" "^3.1.0"
    "rimraf" "^3.0.2"

"flatted@^3.1.0":
  "integrity" "sha512-8/sOawo8tJ4QOBX8YlQBMxL8+RLZfxMQOif9o0KUKTNTjMYElWPE0r/m5VNFxTRd0NSw8qSy8dajrwX4RYI1Hw=="
  "resolved" "https://registry.npmmirror.com/flatted/download/flatted-3.2.4.tgz?cache=0&sync_timestamp=1636473805877&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2Fflatted%2Fdownload%2Fflatted-3.2.4.tgz"
  "version" "3.2.4"

"follow-redirects@^1.0.0", "follow-redirects@^1.14.4":
  "integrity" "sha512-fhUl5EwSJbbl8AR+uYL2KQDxLkdSjZGR36xy46AO7cOMTrCMON6Sa28FmAnC2tRTDbd/Uuzz3aJBv7EBN7JH8A=="
  "resolved" "https://registry.npmmirror.com/follow-redirects/download/follow-redirects-1.14.6.tgz"
  "version" "1.14.6"

"for-in@^1.0.2":
  "integrity" "sha1-gQaNKVqBQuwKxybG4iAMMPttXoA="
  "resolved" "https://registry.npmmirror.com/for-in/download/for-in-1.0.2.tgz"
  "version" "1.0.2"

"forwarded@0.2.0":
  "integrity" "sha1-ImmTZCiq1MFcfr6XeahL8LKoGBE="
  "resolved" "https://registry.npmmirror.com/forwarded/download/forwarded-0.2.0.tgz"
  "version" "0.2.0"

"fraction.js@^4.2.0":
  "integrity" "sha512-n2aZ9tNfYDwaHhvFTkhFErqOMIb8uyzSQ+vGJBjZyanAKZVbGUQ1sngfk9FdkBw7G26O7AgNjLcecLffD1c7eg=="
  "resolved" "http://castle-npm.cp.hxdi.cn/fraction.js/-/fraction.js-4.3.6.tgz"
  "version" "4.3.6"

"fragment-cache@^0.2.1":
  "integrity" "sha1-QpD60n8T6Jvn8zeZxrxaCr//DRk="
  "resolved" "https://registry.npmmirror.com/fragment-cache/download/fragment-cache-0.2.1.tgz"
  "version" "0.2.1"
  dependencies:
    "map-cache" "^0.2.2"

"fresh@0.5.2":
  "integrity" "sha1-PYyt2Q2XZWn6g1qx+OSyOhBWBac="
  "resolved" "https://registry.npmmirror.com/fresh/download/fresh-0.5.2.tgz"
  "version" "0.5.2"

"fs-extra@^9.1.0":
  "integrity" "sha1-WVRGDHZKjaIJS6NVS/g55rmnyG0="
  "resolved" "https://registry.npmmirror.com/fs-extra/download/fs-extra-9.1.0.tgz"
  "version" "9.1.0"
  dependencies:
    "at-least-node" "^1.0.0"
    "graceful-fs" "^4.2.0"
    "jsonfile" "^6.0.1"
    "universalify" "^2.0.0"

"fs-monkey@1.0.3":
  "integrity" "sha1-rjrJLVO7Mo7+DpodlUH2rY1I4tM="
  "resolved" "https://registry.npmmirror.com/fs-monkey/download/fs-monkey-1.0.3.tgz"
  "version" "1.0.3"

"fs.realpath@^1.0.0":
  "integrity" "sha1-FQStJSMVjKpA20onh8sBQRmU6k8="
  "resolved" "https://registry.npmmirror.com/fs.realpath/download/fs.realpath-1.0.0.tgz"
  "version" "1.0.0"

"function-bind@^1.1.1":
  "integrity" "sha1-pWiZ0+o8m6uHS7l3O3xe3pL0iV0="
  "resolved" "https://registry.npmmirror.com/function-bind/download/function-bind-1.1.1.tgz"
  "version" "1.1.1"

"functional-red-black-tree@^1.0.1":
  "integrity" "sha1-GwqzvVU7Kg1jmdKcDj6gslIHgyc="
  "resolved" "https://registry.npmmirror.com/functional-red-black-tree/download/functional-red-black-tree-1.0.1.tgz"
  "version" "1.0.1"

"gensync@^1.0.0-beta.2":
  "integrity" "sha1-MqbudsPX9S1GsrGuXZP+qFgKJeA="
  "resolved" "https://registry.npmmirror.com/gensync/download/gensync-1.0.0-beta.2.tgz"
  "version" "1.0.0-beta.2"

"get-caller-file@^2.0.1", "get-caller-file@^2.0.5":
  "integrity" "sha1-T5RBKoLbMvNuOwuXQfipf+sDH34="
  "resolved" "https://registry.npmmirror.com/get-caller-file/download/get-caller-file-2.0.5.tgz"
  "version" "2.0.5"

"get-intrinsic@^1.0.2":
  "integrity" "sha1-FfWfN2+FXERpY5SPDSTNNje0q8Y="
  "resolved" "https://registry.npmmirror.com/get-intrinsic/download/get-intrinsic-1.1.1.tgz"
  "version" "1.1.1"
  dependencies:
    "function-bind" "^1.1.1"
    "has" "^1.0.3"
    "has-symbols" "^1.0.1"

"get-stdin@^6.0.0":
  "integrity" "sha1-ngm/cSs2CrkiXoEgSPcf3pyJZXs="
  "resolved" "https://registry.npmmirror.com/get-stdin/download/get-stdin-6.0.0.tgz"
  "version" "6.0.0"

"get-stream@^3.0.0":
  "integrity" "sha1-jpQ9E1jcN1VQVOy+LtsFqhdO3hQ="
  "resolved" "https://registry.npmmirror.com/get-stream/download/get-stream-3.0.0.tgz"
  "version" "3.0.0"

"get-stream@^4.0.0":
  "integrity" "sha1-wbJVV189wh1Zv8ec09K0axw6VLU="
  "resolved" "https://registry.npmmirror.com/get-stream/download/get-stream-4.1.0.tgz"
  "version" "4.1.0"
  dependencies:
    "pump" "^3.0.0"

"get-stream@^6.0.0":
  "integrity" "sha1-omLY7vZ6ztV8KFKtYWdSakPL97c="
  "resolved" "https://registry.npmmirror.com/get-stream/download/get-stream-6.0.1.tgz"
  "version" "6.0.1"

"get-value@^2.0.3", "get-value@^2.0.6":
  "integrity" "sha1-3BXKHGcjh8p2vTesCjlbogQqLCg="
  "resolved" "https://registry.npmmirror.com/get-value/download/get-value-2.0.6.tgz"
  "version" "2.0.6"

"glob-parent@^5.1.2", "glob-parent@~5.1.2":
  "integrity" "sha1-hpgyxYA0/mikCTwX3BXoNA2EAcQ="
  "resolved" "https://registry.npmmirror.com/glob-parent/download/glob-parent-5.1.2.tgz"
  "version" "5.1.2"
  dependencies:
    "is-glob" "^4.0.1"

"glob-parent@^6.0.1":
  "integrity" "sha1-bSN9mQg5UMeSkPJMdkKj3poo+eM="
  "resolved" "https://registry.npmmirror.com/glob-parent/download/glob-parent-6.0.2.tgz"
  "version" "6.0.2"
  dependencies:
    "is-glob" "^4.0.3"

"glob-parent@^6.0.2":
  "integrity" "sha512-XxwI8EOhVQgWp6iDL+3b0r86f4d6AX6zSU55HfB4ydCEuXLXc5FcYeOu+nnGftS4TEju/11rt4KJPTMgbfmv4A=="
  "resolved" "http://castle-npm.cp.hxdi.cn/glob-parent/-/glob-parent-6.0.2.tgz"
  "version" "6.0.2"
  dependencies:
    "is-glob" "^4.0.3"

"glob-to-regexp@^0.4.1":
  "integrity" "sha1-x1KXCHyFG5pXi9IX3VmpL1n+VG4="
  "resolved" "https://registry.npmmirror.com/glob-to-regexp/download/glob-to-regexp-0.4.1.tgz"
  "version" "0.4.1"

"glob@^7.1.3":
  "integrity" "sha1-0VU1r3cy4C6Uj0xBYovZECk/YCM="
  "resolved" "https://registry.npmmirror.com/glob/download/glob-7.2.0.tgz"
  "version" "7.2.0"
  dependencies:
    "fs.realpath" "^1.0.0"
    "inflight" "^1.0.4"
    "inherits" "2"
    "minimatch" "^3.0.4"
    "once" "^1.3.0"
    "path-is-absolute" "^1.0.0"

"glob@7.1.6":
  "integrity" "sha512-LwaxwyZ72Lk7vZINtNNrywX0ZuLyStrdDtabefZKAY5ZGJhVtgdznluResxNmPitE0SAO+O26sWTHeKSI2wMBA=="
  "resolved" "http://castle-npm.cp.hxdi.cn/glob/-/glob-7.1.6.tgz"
  "version" "7.1.6"
  dependencies:
    "fs.realpath" "^1.0.0"
    "inflight" "^1.0.4"
    "inherits" "2"
    "minimatch" "^3.0.4"
    "once" "^1.3.0"
    "path-is-absolute" "^1.0.0"

"globals@^11.1.0":
  "integrity" "sha1-q4eVM4hooLq9hSV1gBjCp+uVxC4="
  "resolved" "https://registry.npmmirror.com/globals/download/globals-11.12.0.tgz?cache=0&sync_timestamp=1635390798667&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2Fglobals%2Fdownload%2Fglobals-11.12.0.tgz"
  "version" "11.12.0"

"globals@^13.6.0":
  "integrity" "sha1-TXM3YDBCMKAILtluIeXFZfiYCJ4="
  "resolved" "https://registry.npmmirror.com/globals/download/globals-13.12.0.tgz?cache=0&sync_timestamp=1635390798667&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2Fglobals%2Fdownload%2Fglobals-13.12.0.tgz"
  "version" "13.12.0"
  dependencies:
    "type-fest" "^0.20.2"

"globals@^13.9.0":
  "integrity" "sha1-TXM3YDBCMKAILtluIeXFZfiYCJ4="
  "resolved" "https://registry.npmmirror.com/globals/download/globals-13.12.0.tgz?cache=0&sync_timestamp=1635390798667&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2Fglobals%2Fdownload%2Fglobals-13.12.0.tgz"
  "version" "13.12.0"
  dependencies:
    "type-fest" "^0.20.2"

"globby@^11.0.1", "globby@^11.0.2", "globby@^11.0.3":
  "integrity" "sha1-LLr/d8Lypi5x6bKBOme5ejowAaU="
  "resolved" "https://registry.npmmirror.com/globby/download/globby-11.0.4.tgz"
  "version" "11.0.4"
  dependencies:
    "array-union" "^2.1.0"
    "dir-glob" "^3.0.1"
    "fast-glob" "^3.1.1"
    "ignore" "^5.1.4"
    "merge2" "^1.3.0"
    "slash" "^3.0.0"

"graceful-fs@^4.1.2", "graceful-fs@^4.1.6", "graceful-fs@^4.2.0", "graceful-fs@^4.2.4", "graceful-fs@^4.2.6":
  "integrity" "sha1-5BK40z9eAGWTy9PO5t+fLOu+gCo="
  "resolved" "https://registry.npmmirror.com/graceful-fs/download/graceful-fs-4.2.8.tgz"
  "version" "4.2.8"

"gzip-size@^6.0.0":
  "integrity" "sha1-BlNn/VDCOcBnHLy61b4+LusQ5GI="
  "resolved" "https://registry.npmmirror.com/gzip-size/download/gzip-size-6.0.0.tgz"
  "version" "6.0.0"
  dependencies:
    "duplexer" "^0.1.2"

"handle-thing@^2.0.0":
  "integrity" "sha1-hX95zjWVgMNA1DCBzGSJcNC7I04="
  "resolved" "https://registry.npmmirror.com/handle-thing/download/handle-thing-2.0.1.tgz"
  "version" "2.0.1"

"has-ansi@^2.0.0":
  "integrity" "sha1-NPUEnOHs3ysGSa8+8k5F7TVBbZE="
  "resolved" "https://registry.npmmirror.com/has-ansi/download/has-ansi-2.0.0.tgz"
  "version" "2.0.0"
  dependencies:
    "ansi-regex" "^2.0.0"

"has-flag@^1.0.0":
  "integrity" "sha1-nZ55MWXOAXoA8AQYxD+UKnsdEfo="
  "resolved" "https://registry.npmmirror.com/has-flag/download/has-flag-1.0.0.tgz"
  "version" "1.0.0"

"has-flag@^3.0.0":
  "integrity" "sha1-tdRU3CGZriJWmfNGfloH87lVuv0="
  "resolved" "https://registry.npmmirror.com/has-flag/download/has-flag-3.0.0.tgz"
  "version" "3.0.0"

"has-flag@^4.0.0":
  "integrity" "sha1-lEdx/ZyByBJlxNaUGGDaBrtZR5s="
  "resolved" "https://registry.npmmirror.com/has-flag/download/has-flag-4.0.0.tgz"
  "version" "4.0.0"

"has-symbols@^1.0.1", "has-symbols@^1.0.2":
  "integrity" "sha1-Fl0wcMADCXUqEjakeTMeOsVvFCM="
  "resolved" "https://registry.npmmirror.com/has-symbols/download/has-symbols-1.0.2.tgz"
  "version" "1.0.2"

"has-tostringtag@^1.0.0":
  "integrity" "sha1-fhM4GKfTlHNPlB5zw9P5KR5liyU="
  "resolved" "https://registry.npmmirror.com/has-tostringtag/download/has-tostringtag-1.0.0.tgz"
  "version" "1.0.0"
  dependencies:
    "has-symbols" "^1.0.2"

"has-value@^0.3.1":
  "integrity" "sha1-ex9YutpiyoJ+wKIHgCVlSEWZXh8="
  "resolved" "https://registry.npmmirror.com/has-value/download/has-value-0.3.1.tgz"
  "version" "0.3.1"
  dependencies:
    "get-value" "^2.0.3"
    "has-values" "^0.1.4"
    "isobject" "^2.0.0"

"has-value@^1.0.0":
  "integrity" "sha1-GLKB2lhbHFxR3vJMkw7SmgvmsXc="
  "resolved" "https://registry.npmmirror.com/has-value/download/has-value-1.0.0.tgz"
  "version" "1.0.0"
  dependencies:
    "get-value" "^2.0.6"
    "has-values" "^1.0.0"
    "isobject" "^3.0.0"

"has-values@^0.1.4":
  "integrity" "sha1-bWHeldkd/Km5oCCJrThL/49it3E="
  "resolved" "https://registry.npmmirror.com/has-values/download/has-values-0.1.4.tgz"
  "version" "0.1.4"

"has-values@^1.0.0":
  "integrity" "sha1-lbC2P+whRmGab+V/51Yo1aOe/k8="
  "resolved" "https://registry.npmmirror.com/has-values/download/has-values-1.0.0.tgz"
  "version" "1.0.0"
  dependencies:
    "is-number" "^3.0.0"
    "kind-of" "^4.0.0"

"has@^1.0.3":
  "integrity" "sha1-ci18v8H2qoJB8W3YFOAR4fQeh5Y="
  "resolved" "https://registry.npmmirror.com/has/download/has-1.0.3.tgz"
  "version" "1.0.3"
  dependencies:
    "function-bind" "^1.1.1"

"hash-sum@^1.0.2":
  "integrity" "sha1-M7QHd3VMZDJXPBIMw4CLvRDUfwQ="
  "resolved" "https://registry.npmmirror.com/hash-sum/download/hash-sum-1.0.2.tgz"
  "version" "1.0.2"

"hash-sum@^2.0.0":
  "integrity" "sha1-gdAbtd6OpKIUrV1urRtSNGCwtFo="
  "resolved" "https://registry.npmmirror.com/hash-sum/download/hash-sum-2.0.0.tgz"
  "version" "2.0.0"

"he@^1.1.1", "he@^1.2.0":
  "integrity" "sha1-hK5l+n6vsWX922FWauFLrwVmTw8="
  "resolved" "https://registry.npmmirror.com/he/download/he-1.2.0.tgz"
  "version" "1.2.0"

"highlight.js@^10.7.1":
  "integrity" "sha1-aXJy45kTVuQMPKxWanTu9oF1ZTE="
  "resolved" "https://registry.npmmirror.com/highlight.js/download/highlight.js-10.7.3.tgz"
  "version" "10.7.3"

"hosted-git-info@^2.1.4":
  "integrity" "sha1-3/wL+aIcAiCQkPKqaUKeFBTa8/k="
  "resolved" "https://registry.npmmirror.com/hosted-git-info/download/hosted-git-info-2.8.9.tgz"
  "version" "2.8.9"

"hpack.js@^2.1.6":
  "integrity" "sha1-h3dMCUnlE/QuhFdbPEVoH63ioLI="
  "resolved" "https://registry.npmmirror.com/hpack.js/download/hpack.js-2.1.6.tgz"
  "version" "2.1.6"
  dependencies:
    "inherits" "^2.0.1"
    "obuf" "^1.0.0"
    "readable-stream" "^2.0.1"
    "wbuf" "^1.1.0"

"html-entities@^2.3.2":
  "integrity" "sha1-dgtARoXLHXlOT0t0QzLjsA3P5Ig="
  "resolved" "https://registry.npmmirror.com/html-entities/download/html-entities-2.3.2.tgz?cache=0&sync_timestamp=1632754336653&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2Fhtml-entities%2Fdownload%2Fhtml-entities-2.3.2.tgz"
  "version" "2.3.2"

"html-minifier-terser@^6.0.2":
  "integrity" "sha512-YXxSlJBZTP7RS3tWnQw74ooKa6L9b9i9QYXY21eUEvhZ3u9XLfv6OnFsQq6RxkhHygsaUMvYsZRV5rU/OVNZxw=="
  "resolved" "https://registry.npmmirror.com/html-minifier-terser/download/html-minifier-terser-6.1.0.tgz"
  "version" "6.1.0"
  dependencies:
    "camel-case" "^4.1.2"
    "clean-css" "^5.2.2"
    "commander" "^8.3.0"
    "he" "^1.2.0"
    "param-case" "^3.0.4"
    "relateurl" "^0.2.7"
    "terser" "^5.10.0"

"html-tags@^2.0.0":
  "integrity" "sha1-ELMKOGCF9Dzt41PMj6fLDe7qZos="
  "resolved" "https://registry.npmmirror.com/html-tags/download/html-tags-2.0.0.tgz"
  "version" "2.0.0"

"html-tags@^3.1.0":
  "integrity" "sha1-e15vfmZen7QfMAB+2eDUHpf7IUA="
  "resolved" "https://registry.npmmirror.com/html-tags/download/html-tags-3.1.0.tgz"
  "version" "3.1.0"

"html-webpack-plugin@^5.1.0":
  "integrity" "sha1-w5EZNvV2gcH59Ni2jBWM2d/lL1A="
  "resolved" "https://registry.npmmirror.com/html-webpack-plugin/download/html-webpack-plugin-5.5.0.tgz"
  "version" "5.5.0"
  dependencies:
    "@types/html-minifier-terser" "^6.0.0"
    "html-minifier-terser" "^6.0.2"
    "lodash" "^4.17.21"
    "pretty-error" "^4.0.0"
    "tapable" "^2.0.0"

"htmlparser2@^3.8.3":
  "integrity" "sha1-vWedw/WYl7ajS7EHSchVu1OpOS8="
  "resolved" "https://registry.npmmirror.com/htmlparser2/download/htmlparser2-3.10.1.tgz?cache=0&sync_timestamp=1636640901879&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2Fhtmlparser2%2Fdownload%2Fhtmlparser2-3.10.1.tgz"
  "version" "3.10.1"
  dependencies:
    "domelementtype" "^1.3.1"
    "domhandler" "^2.3.0"
    "domutils" "^1.5.1"
    "entities" "^1.1.1"
    "inherits" "^2.0.1"
    "readable-stream" "^3.1.1"

"htmlparser2@^6.1.0":
  "integrity" "sha1-xNditsM3GgXb5l6UrkOp+EX7j7c="
  "resolved" "https://registry.npmmirror.com/htmlparser2/download/htmlparser2-6.1.0.tgz?cache=0&sync_timestamp=1636640901879&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2Fhtmlparser2%2Fdownload%2Fhtmlparser2-6.1.0.tgz"
  "version" "6.1.0"
  dependencies:
    "domelementtype" "^2.0.1"
    "domhandler" "^4.0.0"
    "domutils" "^2.5.2"
    "entities" "^2.0.0"

"http-deceiver@^1.2.7":
  "integrity" "sha1-+nFolEq5pRnTN8sL7HKE3D5yPYc="
  "resolved" "https://registry.npmmirror.com/http-deceiver/download/http-deceiver-1.2.7.tgz"
  "version" "1.2.7"

"http-errors@~1.6.2":
  "integrity" "sha1-i1VoC7S+KDoLW/TqLjhYC+HZMg0="
  "resolved" "https://registry.npmmirror.com/http-errors/download/http-errors-1.6.3.tgz?cache=0&sync_timestamp=1636932541985&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2Fhttp-errors%2Fdownload%2Fhttp-errors-1.6.3.tgz"
  "version" "1.6.3"
  dependencies:
    "depd" "~1.1.2"
    "inherits" "2.0.3"
    "setprototypeof" "1.1.0"
    "statuses" ">= 1.4.0 < 2"

"http-errors@~1.7.2", "http-errors@1.7.2":
  "integrity" "sha1-T1ApzxMjnzEDblsuVSkrz7zIXI8="
  "resolved" "https://registry.npmmirror.com/http-errors/download/http-errors-1.7.2.tgz?cache=0&sync_timestamp=1636932541985&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2Fhttp-errors%2Fdownload%2Fhttp-errors-1.7.2.tgz"
  "version" "1.7.2"
  dependencies:
    "depd" "~1.1.2"
    "inherits" "2.0.3"
    "setprototypeof" "1.1.1"
    "statuses" ">= 1.5.0 < 2"
    "toidentifier" "1.0.0"

"http-parser-js@>=0.5.1":
  "integrity" "sha512-x+JVEkO2PoM8qqpbPbOL3cqHPwerep7OwzK7Ay+sMQjKzaKCqWvjoXm5tqMP9tXWWTnTzAjIhXg+J99XYuPhPA=="
  "resolved" "https://registry.npmmirror.com/http-parser-js/download/http-parser-js-0.5.5.tgz"
  "version" "0.5.5"

"http-proxy-middleware@^2.0.0":
  "integrity" "sha1-fvNBekeft2ZqVx4Jlmxmo5vSwV8="
  "resolved" "https://registry.npmmirror.com/http-proxy-middleware/download/http-proxy-middleware-2.0.1.tgz"
  "version" "2.0.1"
  dependencies:
    "@types/http-proxy" "^1.17.5"
    "http-proxy" "^1.18.1"
    "is-glob" "^4.0.1"
    "is-plain-obj" "^3.0.0"
    "micromatch" "^4.0.2"

"http-proxy@^1.18.1":
  "integrity" "sha1-QBVB8FNIhLv5UmAzTnL4juOXZUk="
  "resolved" "https://registry.npmmirror.com/http-proxy/download/http-proxy-1.18.1.tgz"
  "version" "1.18.1"
  dependencies:
    "eventemitter3" "^4.0.0"
    "follow-redirects" "^1.0.0"
    "requires-port" "^1.0.0"

"human-signals@^2.1.0":
  "integrity" "sha1-3JH8ukLk0G5Kuu0zs+ejwC9RTqA="
  "resolved" "https://registry.npmmirror.com/human-signals/download/human-signals-2.1.0.tgz"
  "version" "2.1.0"

"iconv-lite@^0.4.24", "iconv-lite@0.4.24":
  "integrity" "sha1-ICK0sl+93CHS9SSXSkdKr+czkIs="
  "resolved" "https://registry.npmmirror.com/iconv-lite/download/iconv-lite-0.4.24.tgz"
  "version" "0.4.24"
  dependencies:
    "safer-buffer" ">= 2.1.2 < 3"

"icss-utils@^5.0.0", "icss-utils@^5.1.0":
  "integrity" "sha1-xr5oWKvQE9do6YNmrkfiXViHsa4="
  "resolved" "https://registry.npmmirror.com/icss-utils/download/icss-utils-5.1.0.tgz"
  "version" "5.1.0"

"ieee754@^1.1.13", "ieee754@^1.2.1":
  "integrity" "sha1-jrehCmP/8l0VpXsAFYbRd9Gw01I="
  "resolved" "https://registry.npmmirror.com/ieee754/download/ieee754-1.2.1.tgz"
  "version" "1.2.1"

"ignore@^4.0.6":
  "integrity" "sha1-dQ49tYYgh7RzfrrIIH/9HvJ7Jfw="
  "resolved" "https://registry.npmmirror.com/ignore/download/ignore-4.0.6.tgz?cache=0&sync_timestamp=1635927008473&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2Fignore%2Fdownload%2Fignore-4.0.6.tgz"
  "version" "4.0.6"

"ignore@^5.1.4":
  "integrity" "sha1-nsGly+jhRG7GDUQgBg1Dqm5zgvs="
  "resolved" "https://registry.npmmirror.com/ignore/download/ignore-5.1.9.tgz?cache=0&sync_timestamp=1635927008473&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2Fignore%2Fdownload%2Fignore-5.1.9.tgz"
  "version" "5.1.9"

"image-size@^0.5.1":
  "integrity" "sha1-Cd/Uq50g4p6xw+gLiZA3jfnjy5w="
  "resolved" "https://registry.npmmirror.com/image-size/download/image-size-0.5.5.tgz"
  "version" "0.5.5"

"immutable@^4.0.0":
  "integrity" "sha1-uG943mre82CDle+yaakUYnl+LCM="
  "resolved" "https://registry.npmmirror.com/immutable/download/immutable-4.0.0.tgz"
  "version" "4.0.0"

"import-fresh@^3.0.0", "import-fresh@^3.2.1":
  "integrity" "sha1-NxYsJfy566oublPVtNiM4X2eDCs="
  "resolved" "https://registry.npmmirror.com/import-fresh/download/import-fresh-3.3.0.tgz"
  "version" "3.3.0"
  dependencies:
    "parent-module" "^1.0.0"
    "resolve-from" "^4.0.0"

"imurmurhash@^0.1.4":
  "integrity" "sha1-khi5srkoojixPcT7a21XbyMUU+o="
  "resolved" "https://registry.npmmirror.com/imurmurhash/download/imurmurhash-0.1.4.tgz"
  "version" "0.1.4"

"indent-string@^4.0.0":
  "integrity" "sha1-Yk+PRJfWGbLZdoUx1Y9BIoVNclE="
  "resolved" "https://registry.npmmirror.com/indent-string/download/indent-string-4.0.0.tgz"
  "version" "4.0.0"

"inflight@^1.0.4":
  "integrity" "sha1-Sb1jMdfQLQwJvJEKEHW6gWW1bfk="
  "resolved" "https://registry.npmmirror.com/inflight/download/inflight-1.0.6.tgz"
  "version" "1.0.6"
  dependencies:
    "once" "^1.3.0"
    "wrappy" "1"

"inherits@^2.0.1", "inherits@^2.0.3", "inherits@^2.0.4", "inherits@~2.0.3", "inherits@2":
  "integrity" "sha1-D6LGT5MpF8NDOg3tVTY6rjdBa3w="
  "resolved" "https://registry.npmmirror.com/inherits/download/inherits-2.0.4.tgz"
  "version" "2.0.4"

"inherits@2.0.3":
  "integrity" "sha1-Yzwsg+PaQqUC9SRmAiSA9CCCYd4="
  "resolved" "https://registry.npmmirror.com/inherits/download/inherits-2.0.3.tgz"
  "version" "2.0.3"

"inquirer@8.0.0":
  "integrity" "sha512-ON8pEJPPCdyjxj+cxsYRe6XfCJepTxANdNnTebsTuQgXpRyZRRT9t4dJwjRubgmvn20CLSEnozRUayXyM9VTXA=="
  "resolved" "http://castle-npm.cp.hxdi.cn/inquirer/-/inquirer-8.0.0.tgz"
  "version" "8.0.0"
  dependencies:
    "ansi-escapes" "^4.2.1"
    "chalk" "^4.1.0"
    "cli-cursor" "^3.1.0"
    "cli-width" "^3.0.0"
    "external-editor" "^3.0.3"
    "figures" "^3.0.0"
    "lodash" "^4.17.21"
    "mute-stream" "0.0.8"
    "run-async" "^2.4.0"
    "rxjs" "^6.6.6"
    "string-width" "^4.1.0"
    "strip-ansi" "^6.0.0"
    "through" "^2.3.6"

"ip@^1.1.0":
  "integrity" "sha1-vd7XARQpCCjAoDnnLvJfWq7ENUo="
  "resolved" "https://registry.npmmirror.com/ip/download/ip-1.1.5.tgz"
  "version" "1.1.5"

"ipaddr.js@^2.0.1":
  "integrity" "sha1-7KJWp6h36Reus2iwp0l930LvgcA="
  "resolved" "https://registry.npmmirror.com/ipaddr.js/download/ipaddr.js-2.0.1.tgz"
  "version" "2.0.1"

"ipaddr.js@1.9.1":
  "integrity" "sha1-v/OFQ+64mEglB5/zoqjmy9RngbM="
  "resolved" "https://registry.npmmirror.com/ipaddr.js/download/ipaddr.js-1.9.1.tgz"
  "version" "1.9.1"

"is-absolute-url@^3.0.3":
  "integrity" "sha1-lsaiK2ojkpsR6gr7GDbDatSl1pg="
  "resolved" "https://registry.npmmirror.com/is-absolute-url/download/is-absolute-url-3.0.3.tgz"
  "version" "3.0.3"

"is-accessor-descriptor@^0.1.6":
  "integrity" "sha1-qeEss66Nh2cn7u84Q/igiXtcmNY="
  "resolved" "https://registry.npmmirror.com/is-accessor-descriptor/download/is-accessor-descriptor-0.1.6.tgz"
  "version" "0.1.6"
  dependencies:
    "kind-of" "^3.0.2"

"is-accessor-descriptor@^1.0.0":
  "integrity" "sha1-FpwvbT3x+ZJhgHI2XJsOofaHhlY="
  "resolved" "https://registry.npmmirror.com/is-accessor-descriptor/download/is-accessor-descriptor-1.0.0.tgz"
  "version" "1.0.0"
  dependencies:
    "kind-of" "^6.0.0"

"is-arguments@^1.0.4":
  "integrity" "sha1-FbP4j9oB8ql/7ITKdhpWDxI++ps="
  "resolved" "https://registry.npmmirror.com/is-arguments/download/is-arguments-1.1.1.tgz"
  "version" "1.1.1"
  dependencies:
    "call-bind" "^1.0.2"
    "has-tostringtag" "^1.0.0"

"is-arrayish@^0.2.1":
  "integrity" "sha1-d8mYQFJ6qOyxqLppe4BkWnqSap0="
  "resolved" "https://registry.npmmirror.com/is-arrayish/download/is-arrayish-0.2.1.tgz"
  "version" "0.2.1"

"is-binary-path@~2.1.0":
  "integrity" "sha1-6h9/O4DwZCNug0cPhsCcJU+0Wwk="
  "resolved" "https://registry.npmmirror.com/is-binary-path/download/is-binary-path-2.1.0.tgz"
  "version" "2.1.0"
  dependencies:
    "binary-extensions" "^2.0.0"

"is-buffer@^1.1.5":
  "integrity" "sha1-76ouqdqg16suoTqXsritUf776L4="
  "resolved" "https://registry.npmmirror.com/is-buffer/download/is-buffer-1.1.6.tgz"
  "version" "1.1.6"

"is-ci@^1.0.10":
  "integrity" "sha1-43ecjuF/zPQoSI9uKBGH8uYyhBw="
  "resolved" "https://registry.npmmirror.com/is-ci/download/is-ci-1.2.1.tgz?cache=0&sync_timestamp=1635261114993&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2Fis-ci%2Fdownload%2Fis-ci-1.2.1.tgz"
  "version" "1.2.1"
  dependencies:
    "ci-info" "^1.5.0"

"is-core-module@^2.13.0":
  "integrity" "sha512-Z7dk6Qo8pOCp3l4tsX2C5ZVas4V+UxwQodwZhLopL91TX8UyyHEXafPcyoeeWuLrwzHcr3igO78wNLwHJHsMCQ=="
  "resolved" "http://castle-npm.cp.hxdi.cn/is-core-module/-/is-core-module-2.13.0.tgz"
  "version" "2.13.0"
  dependencies:
    "has" "^1.0.3"

"is-data-descriptor@^0.1.4":
  "integrity" "sha1-C17mSDiOLIYCgueT8YVv7D8wG1Y="
  "resolved" "https://registry.npmmirror.com/is-data-descriptor/download/is-data-descriptor-0.1.4.tgz"
  "version" "0.1.4"
  dependencies:
    "kind-of" "^3.0.2"

"is-data-descriptor@^1.0.0":
  "integrity" "sha1-2Eh2Mh0Oet0DmQQGq7u9NrqSaMc="
  "resolved" "https://registry.npmmirror.com/is-data-descriptor/download/is-data-descriptor-1.0.0.tgz"
  "version" "1.0.0"
  dependencies:
    "kind-of" "^6.0.0"

"is-date-object@^1.0.1":
  "integrity" "sha1-CEHVU25yTCVZe/bqYuG9OCmN8x8="
  "resolved" "https://registry.npmmirror.com/is-date-object/download/is-date-object-1.0.5.tgz"
  "version" "1.0.5"
  dependencies:
    "has-tostringtag" "^1.0.0"

"is-descriptor@^0.1.0":
  "integrity" "sha1-Nm2CQN3kh8pRgjsaufB6EKeCUco="
  "resolved" "https://registry.npmmirror.com/is-descriptor/download/is-descriptor-0.1.6.tgz"
  "version" "0.1.6"
  dependencies:
    "is-accessor-descriptor" "^0.1.6"
    "is-data-descriptor" "^0.1.4"
    "kind-of" "^5.0.0"

"is-descriptor@^1.0.0", "is-descriptor@^1.0.2":
  "integrity" "sha1-OxWXRqZmBLBPjIFSS6NlxfFNhuw="
  "resolved" "https://registry.npmmirror.com/is-descriptor/download/is-descriptor-1.0.2.tgz"
  "version" "1.0.2"
  dependencies:
    "is-accessor-descriptor" "^1.0.0"
    "is-data-descriptor" "^1.0.0"
    "kind-of" "^6.0.2"

"is-docker@^2.0.0", "is-docker@^2.1.1":
  "integrity" "sha1-M+6r4jz+hvFL3kQIoCwM+4U6zao="
  "resolved" "https://registry.npmmirror.com/is-docker/download/is-docker-2.2.1.tgz"
  "version" "2.2.1"

"is-extendable@^0.1.0", "is-extendable@^0.1.1":
  "integrity" "sha1-YrEQ4omkcUGOPsNqYX1HLjAd/Ik="
  "resolved" "https://registry.npmmirror.com/is-extendable/download/is-extendable-0.1.1.tgz"
  "version" "0.1.1"

"is-extendable@^1.0.1":
  "integrity" "sha1-p0cPnkJnM9gb2B4RVSZOOjUHyrQ="
  "resolved" "https://registry.npmmirror.com/is-extendable/download/is-extendable-1.0.1.tgz"
  "version" "1.0.1"
  dependencies:
    "is-plain-object" "^2.0.4"

"is-extglob@^2.1.1":
  "integrity" "sha1-qIwCU1eR8C7TfHahueqXc8gz+MI="
  "resolved" "https://registry.npmmirror.com/is-extglob/download/is-extglob-2.1.1.tgz"
  "version" "2.1.1"

"is-file-esm@^1.0.0":
  "integrity" "sha1-mHCGsPWlMYF56dMPTy+NNzIeG18="
  "resolved" "https://registry.npmmirror.com/is-file-esm/download/is-file-esm-1.0.0.tgz"
  "version" "1.0.0"
  dependencies:
    "read-pkg-up" "^7.0.1"

"is-fullwidth-code-point@^2.0.0":
  "integrity" "sha1-o7MKXE8ZkYMWeqq5O+764937ZU8="
  "resolved" "https://registry.npmmirror.com/is-fullwidth-code-point/download/is-fullwidth-code-point-2.0.0.tgz"
  "version" "2.0.0"

"is-fullwidth-code-point@^3.0.0":
  "integrity" "sha1-8Rb4Bk/pCz94RKOJl8C3UFEmnx0="
  "resolved" "https://registry.npmmirror.com/is-fullwidth-code-point/download/is-fullwidth-code-point-3.0.0.tgz"
  "version" "3.0.0"

"is-glob@^4.0.0", "is-glob@^4.0.1", "is-glob@^4.0.3", "is-glob@~4.0.1":
  "integrity" "sha1-ZPYeQsu7LuwgcanawLKLoeZdUIQ="
  "resolved" "https://registry.npmmirror.com/is-glob/download/is-glob-4.0.3.tgz?cache=0&sync_timestamp=1632934573225&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2Fis-glob%2Fdownload%2Fis-glob-4.0.3.tgz"
  "version" "4.0.3"
  dependencies:
    "is-extglob" "^2.1.1"

"is-interactive@^1.0.0":
  "integrity" "sha1-zqbmrlyHCnsKAAQHC3tYfgJSkS4="
  "resolved" "https://registry.npmmirror.com/is-interactive/download/is-interactive-1.0.0.tgz"
  "version" "1.0.0"

"is-number@^3.0.0":
  "integrity" "sha1-JP1iAaR4LPUFYcgQJ2r8fRLXEZU="
  "resolved" "https://registry.npmmirror.com/is-number/download/is-number-3.0.0.tgz"
  "version" "3.0.0"
  dependencies:
    "kind-of" "^3.0.2"

"is-number@^7.0.0":
  "integrity" "sha1-dTU0W4lnNNX4DE0GxQlVUnoU8Ss="
  "resolved" "https://registry.npmmirror.com/is-number/download/is-number-7.0.0.tgz"
  "version" "7.0.0"

"is-path-cwd@^2.2.0":
  "integrity" "sha1-Z9Q7gmZKe1GR/ZEZEn6zAASKn9s="
  "resolved" "https://registry.npmmirror.com/is-path-cwd/download/is-path-cwd-2.2.0.tgz"
  "version" "2.2.0"

"is-path-inside@^3.0.2":
  "integrity" "sha1-0jE2LlOgf/Kw4Op/7QSRYf/RYoM="
  "resolved" "https://registry.npmmirror.com/is-path-inside/download/is-path-inside-3.0.3.tgz"
  "version" "3.0.3"

"is-plain-obj@^1.1":
  "integrity" "sha1-caUMhCnfync8kqOQpKA7OfzVHT4="
  "resolved" "https://registry.npmmirror.com/is-plain-obj/download/is-plain-obj-1.1.0.tgz"
  "version" "1.1.0"

"is-plain-obj@^3.0.0":
  "integrity" "sha1-r28uoUrFpkYYOlu9tbqrvBVq2dc="
  "resolved" "https://registry.npmmirror.com/is-plain-obj/download/is-plain-obj-3.0.0.tgz"
  "version" "3.0.0"

"is-plain-object@^2.0.3", "is-plain-object@^2.0.4":
  "integrity" "sha1-LBY7P6+xtgbZ0Xko8FwqHDjgdnc="
  "resolved" "https://registry.npmmirror.com/is-plain-object/download/is-plain-object-2.0.4.tgz"
  "version" "2.0.4"
  dependencies:
    "isobject" "^3.0.1"

"is-regex@^1.0.4":
  "integrity" "sha1-7vVmPNWfpMCuM5UFMj32hUuxWVg="
  "resolved" "https://registry.npmmirror.com/is-regex/download/is-regex-1.1.4.tgz"
  "version" "1.1.4"
  dependencies:
    "call-bind" "^1.0.2"
    "has-tostringtag" "^1.0.0"

"is-resolvable@^1.1.0":
  "integrity" "sha1-+xj4fOH+uSUWnJpAfBkxijIG7Yg="
  "resolved" "https://registry.npmmirror.com/is-resolvable/download/is-resolvable-1.1.0.tgz"
  "version" "1.1.0"

"is-stream@^1.1.0":
  "integrity" "sha1-EtSj3U5o4Lec6428hBc66A2RykQ="
  "resolved" "https://registry.npmmirror.com/is-stream/download/is-stream-1.1.0.tgz"
  "version" "1.1.0"

"is-stream@^2.0.0":
  "integrity" "sha1-+sHj1TuXrVqdCunO8jifWBClwHc="
  "resolved" "https://registry.npmmirror.com/is-stream/download/is-stream-2.0.1.tgz"
  "version" "2.0.1"

"is-unicode-supported@^0.1.0":
  "integrity" "sha1-PybHaoCVk7Ur+i7LVxDtJ3m1Iqc="
  "resolved" "https://registry.npmmirror.com/is-unicode-supported/download/is-unicode-supported-0.1.0.tgz"
  "version" "0.1.0"

"is-windows@^1.0.2":
  "integrity" "sha1-0YUOuXkezRjmGCzhKjDzlmNLsZ0="
  "resolved" "https://registry.npmmirror.com/is-windows/download/is-windows-1.0.2.tgz"
  "version" "1.0.2"

"is-wsl@^2.1.1", "is-wsl@^2.2.0":
  "integrity" "sha1-dKTHbnfKn9P5MvKQwX6jJs0VcnE="
  "resolved" "https://registry.npmmirror.com/is-wsl/download/is-wsl-2.2.0.tgz"
  "version" "2.2.0"
  dependencies:
    "is-docker" "^2.0.0"

"isarray@~1.0.0", "isarray@1.0.0":
  "integrity" "sha1-u5NdSFgsuhaMBoNJV6VKPgcSTxE="
  "resolved" "https://registry.npmmirror.com/isarray/download/isarray-1.0.0.tgz"
  "version" "1.0.0"

"isexe@^2.0.0":
  "integrity" "sha1-6PvzdNxVb/iUehDcsFctYz8s+hA="
  "resolved" "https://registry.npmmirror.com/isexe/download/isexe-2.0.0.tgz"
  "version" "2.0.0"

"isobject@^2.0.0":
  "integrity" "sha1-8GVWEJaj8dou9GJy+BXIQNh+DIk="
  "resolved" "https://registry.npmmirror.com/isobject/download/isobject-2.1.0.tgz"
  "version" "2.1.0"
  dependencies:
    "isarray" "1.0.0"

"isobject@^2.1.0":
  "integrity" "sha1-8GVWEJaj8dou9GJy+BXIQNh+DIk="
  "resolved" "https://registry.npmmirror.com/isobject/download/isobject-2.1.0.tgz"
  "version" "2.1.0"
  dependencies:
    "isarray" "1.0.0"

"isobject@^3.0.0":
  "integrity" "sha1-TkMekrEalzFjaqH5yNHMvP2reN8="
  "resolved" "https://registry.npmmirror.com/isobject/download/isobject-3.0.1.tgz"
  "version" "3.0.1"

"isobject@^3.0.1":
  "integrity" "sha1-TkMekrEalzFjaqH5yNHMvP2reN8="
  "resolved" "https://registry.npmmirror.com/isobject/download/isobject-3.0.1.tgz"
  "version" "3.0.1"

"javascript-stringify@^2.0.1":
  "integrity" "sha1-J8dlOb4U2L0Sghmi1zGwkzeQTnk="
  "resolved" "https://registry.npmmirror.com/javascript-stringify/download/javascript-stringify-2.1.0.tgz"
  "version" "2.1.0"

"jest-worker@^27.0.2", "jest-worker@^27.0.6", "jest-worker@^27.3.1":
  "integrity" "sha512-jfwxYJvfua1b1XkyuyPh01ATmgg4e5fPM/muLmhy9Qc6dmiwacQB0MLHaU6IjEsv/+nAixHGxTn8WllA27Pn0w=="
  "resolved" "https://registry.npmmirror.com/jest-worker/download/jest-worker-27.4.4.tgz"
  "version" "27.4.4"
  dependencies:
    "@types/node" "*"
    "merge-stream" "^2.0.0"
    "supports-color" "^8.0.0"

"jiti@^1.18.2":
  "integrity" "sha512-3TV69ZbrvV6U5DfQimop50jE9Dl6J8O1ja1dvBbMba/sZ3YBEQqJ2VZRoQPVnhlzjNtU1vaXRZVrVjU4qtm8yA=="
  "resolved" "http://castle-npm.cp.hxdi.cn/jiti/-/jiti-1.20.0.tgz"
  "version" "1.20.0"

"joi@^17.4.0":
  "integrity" "sha512-R7hR50COp7StzLnDi4ywOXHrBrgNXuUUfJWIR5lPY5Bm/pOD3jZaTwpluUXVLRWcoWZxkrHBBJ5hLxgnlehbdw=="
  "resolved" "https://registry.npmmirror.com/joi/download/joi-17.5.0.tgz"
  "version" "17.5.0"
  dependencies:
    "@hapi/hoek" "^9.0.0"
    "@hapi/topo" "^5.0.0"
    "@sideway/address" "^4.1.3"
    "@sideway/formula" "^3.0.0"
    "@sideway/pinpoint" "^2.0.0"

"js-base64@^2.1.9":
  "integrity" "sha1-9OaGxd4eofhn28rT1G2WlCjfmMQ="
  "resolved" "https://registry.npmmirror.com/js-base64/download/js-base64-2.6.4.tgz"
  "version" "2.6.4"

"js-message@1.0.7":
  "integrity" "sha1-+93QU8ekcCGHG7iyyVOXzBfCDkc="
  "resolved" "https://registry.npmmirror.com/js-message/download/js-message-1.0.7.tgz"
  "version" "1.0.7"

"js-queue@2.0.2":
  "integrity" "sha1-C+WQM4+QOzbHPTPDGIOoIUEs1II="
  "resolved" "https://registry.npmmirror.com/js-queue/download/js-queue-2.0.2.tgz"
  "version" "2.0.2"
  dependencies:
    "easy-stack" "^1.0.1"

"js-tokens@^4.0.0":
  "integrity" "sha1-GSA/tZmR35jjoocFDUZHzerzJJk="
  "resolved" "https://registry.npmmirror.com/js-tokens/download/js-tokens-4.0.0.tgz"
  "version" "4.0.0"

"js-yaml@^3.13.1":
  "integrity" "sha1-2ugS/bOCX6MGYJqHFzg8UMNqBTc="
  "resolved" "https://registry.npmmirror.com/js-yaml/download/js-yaml-3.14.1.tgz"
  "version" "3.14.1"
  dependencies:
    "argparse" "^1.0.7"
    "esprima" "^4.0.0"

"jsbarcode@^3.11.5":
  "integrity" "sha512-zv3KsH51zD00I/LrFzFSM6dst7rDn0vIMzaiZFL7qusTjPZiPtxg3zxetp0RR7obmjTw4f6NyGgbdkBCgZUIrA=="
  "resolved" "http://castle-npm.cp.hxdi.cn/jsbarcode/-/jsbarcode-3.11.5.tgz"
  "version" "3.11.5"

"jsencrypt@^3.3.2":
  "integrity" "sha512-arQR1R1ESGdAxY7ZheWr12wCaF2yF47v5qpB76TtV64H1pyGudk9Hvw8Y9tb/FiTIaaTRUyaSnm5T/Y53Ghm/A=="
  "resolved" "http://castle-npm.cp.hxdi.cn/jsencrypt/-/jsencrypt-3.3.2.tgz"
  "version" "3.3.2"

"jsesc@^2.5.1":
  "integrity" "sha1-gFZNLkg9rPbo7yCWUKZ98/DCg6Q="
  "resolved" "https://registry.npmmirror.com/jsesc/download/jsesc-2.5.2.tgz"
  "version" "2.5.2"

"jsesc@~0.5.0":
  "integrity" "sha1-597mbjXW/Bb3EP6R1c9p9w8IkR0="
  "resolved" "https://registry.npmmirror.com/jsesc/download/jsesc-0.5.0.tgz"
  "version" "0.5.0"

"json-parse-better-errors@^1.0.2":
  "integrity" "sha1-u4Z8+zRQ5pEHwTHRxRS6s9yLyqk="
  "resolved" "https://registry.npmmirror.com/json-parse-better-errors/download/json-parse-better-errors-1.0.2.tgz"
  "version" "1.0.2"

"json-parse-even-better-errors@^2.3.0":
  "integrity" "sha1-fEeAWpQxmSjgV3dAXcEuH3pO4C0="
  "resolved" "https://registry.npmmirror.com/json-parse-even-better-errors/download/json-parse-even-better-errors-2.3.1.tgz"
  "version" "2.3.1"

"json-schema-traverse@^0.4.1":
  "integrity" "sha1-afaofZUTq4u4/mO9sJecRI5oRmA="
  "resolved" "https://registry.npmmirror.com/json-schema-traverse/download/json-schema-traverse-0.4.1.tgz"
  "version" "0.4.1"

"json-schema-traverse@^1.0.0":
  "integrity" "sha1-rnvLNlard6c7pcSb9lTzjmtoYOI="
  "resolved" "https://registry.npmmirror.com/json-schema-traverse/download/json-schema-traverse-1.0.0.tgz"
  "version" "1.0.0"

"json-stable-stringify-without-jsonify@^1.0.1":
  "integrity" "sha1-nbe1lJatPzz+8wp1FC0tkwrXJlE="
  "resolved" "https://registry.npmmirror.com/json-stable-stringify-without-jsonify/download/json-stable-stringify-without-jsonify-1.0.1.tgz"
  "version" "1.0.1"

"json5@^1.0.1":
  "integrity" "sha1-d5+wAYYE+oVOrL9iUhgNg1Q+Pb4="
  "resolved" "https://registry.npmmirror.com/json5/download/json5-1.0.1.tgz"
  "version" "1.0.1"
  dependencies:
    "minimist" "^1.2.0"

"json5@^2.1.2":
  "integrity" "sha1-Lf7+cgxrpSXZ69kJlQ8FFTFsiaM="
  "resolved" "https://registry.npmmirror.com/json5/download/json5-2.2.0.tgz"
  "version" "2.2.0"
  dependencies:
    "minimist" "^1.2.5"

"jsonfile@^6.0.1":
  "integrity" "sha1-vFWyY0eTxnnsZAMJTrE2mKbsCq4="
  "resolved" "https://registry.npmmirror.com/jsonfile/download/jsonfile-6.1.0.tgz"
  "version" "6.1.0"
  dependencies:
    "universalify" "^2.0.0"
  optionalDependencies:
    "graceful-fs" "^4.1.6"

"kind-of@^3.0.2", "kind-of@^3.0.3":
  "integrity" "sha1-MeohpzS6ubuw8yRm2JOupR5KPGQ="
  "resolved" "https://registry.npmmirror.com/kind-of/download/kind-of-3.2.2.tgz"
  "version" "3.2.2"
  dependencies:
    "is-buffer" "^1.1.5"

"kind-of@^3.2.0":
  "integrity" "sha1-MeohpzS6ubuw8yRm2JOupR5KPGQ="
  "resolved" "https://registry.npmmirror.com/kind-of/download/kind-of-3.2.2.tgz"
  "version" "3.2.2"
  dependencies:
    "is-buffer" "^1.1.5"

"kind-of@^4.0.0":
  "integrity" "sha1-IIE989cSkosgc3hpGkUGb65y3Vc="
  "resolved" "https://registry.npmmirror.com/kind-of/download/kind-of-4.0.0.tgz"
  "version" "4.0.0"
  dependencies:
    "is-buffer" "^1.1.5"

"kind-of@^5.0.0":
  "integrity" "sha1-cpyR4thXt6QZofmqZWhcTDP1hF0="
  "resolved" "https://registry.npmmirror.com/kind-of/download/kind-of-5.1.0.tgz"
  "version" "5.1.0"

"kind-of@^5.0.2":
  "integrity" "sha1-cpyR4thXt6QZofmqZWhcTDP1hF0="
  "resolved" "https://registry.npmmirror.com/kind-of/download/kind-of-5.1.0.tgz"
  "version" "5.1.0"

"kind-of@^6.0.0", "kind-of@^6.0.2":
  "integrity" "sha1-B8BQNKbDSfoG4k+jWqdttFgM5N0="
  "resolved" "https://registry.npmmirror.com/kind-of/download/kind-of-6.0.3.tgz"
  "version" "6.0.3"

"klona@^2.0.4", "klona@^2.0.5":
  "integrity" "sha1-0WZXTZAHY5XZljqnqSj6u412r7w="
  "resolved" "https://registry.npmmirror.com/klona/download/klona-2.0.5.tgz"
  "version" "2.0.5"

"launch-editor-middleware@^2.2.1":
  "integrity" "sha1-4UsH5scVSwpLhqD9NFeE5FgEwVc="
  "resolved" "https://registry.npmmirror.com/launch-editor-middleware/download/launch-editor-middleware-2.2.1.tgz"
  "version" "2.2.1"
  dependencies:
    "launch-editor" "^2.2.1"

"launch-editor@^2.2.1":
  "integrity" "sha1-hxtaPuOdZoD8wm03kwtu7aidsMo="
  "resolved" "https://registry.npmmirror.com/launch-editor/download/launch-editor-2.2.1.tgz"
  "version" "2.2.1"
  dependencies:
    "chalk" "^2.3.0"
    "shell-quote" "^1.6.1"

"levn@^0.4.1":
  "integrity" "sha1-rkViwAdHO5MqYgDUAyaN0v/8at4="
  "resolved" "https://registry.npmmirror.com/levn/download/levn-0.4.1.tgz"
  "version" "0.4.1"
  dependencies:
    "prelude-ls" "^1.2.1"
    "type-check" "~0.4.0"

"lilconfig@^2.0.3", "lilconfig@^2.0.5", "lilconfig@^2.1.0":
  "integrity" "sha512-utWOt/GHzuUxnLKxB6dk81RoOeoNeHgbrXiuGk4yyF5qlRz+iIVWu56E2fqGHFrXz0QNUhLB/8nKqvRH66JKGQ=="
  "resolved" "http://castle-npm.cp.hxdi.cn/lilconfig/-/lilconfig-2.1.0.tgz"
  "version" "2.1.0"

"lines-and-columns@^1.1.6":
  "integrity" "sha512-7ylylesZQ/PV29jhEDl3Ufjo6ZX7gCqJr5F7PKrqc93v7fzSymt1BpwEU8nAUXs8qzzvqhbjhK5QZg6Mt/HkBg=="
  "resolved" "https://registry.npmmirror.com/lines-and-columns/download/lines-and-columns-1.2.4.tgz"
  "version" "1.2.4"

"loader-runner@^4.1.0", "loader-runner@^4.2.0":
  "integrity" "sha1-1wIjgNZtFMX7HUlriYZOvP1Hg4Q="
  "resolved" "https://registry.npmmirror.com/loader-runner/download/loader-runner-4.2.0.tgz"
  "version" "4.2.0"

"loader-utils@^1.0.2", "loader-utils@^1.1.0", "loader-utils@^1.2.3", "loader-utils@^1.4.0":
  "integrity" "sha1-xXm140yzSxp07cbB+za/o3HVphM="
  "resolved" "https://registry.npmmirror.com/loader-utils/download/loader-utils-1.4.0.tgz?cache=0&sync_timestamp=1636687921573&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2Floader-utils%2Fdownload%2Floader-utils-1.4.0.tgz"
  "version" "1.4.0"
  dependencies:
    "big.js" "^5.2.2"
    "emojis-list" "^3.0.0"
    "json5" "^1.0.1"

"loader-utils@^2.0.0":
  "integrity" "sha1-1uO0+4GHByGuTghoqxHdY4NowSk="
  "resolved" "https://registry.npmmirror.com/loader-utils/download/loader-utils-2.0.2.tgz?cache=0&sync_timestamp=1636687921573&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2Floader-utils%2Fdownload%2Floader-utils-2.0.2.tgz"
  "version" "2.0.2"
  dependencies:
    "big.js" "^5.2.2"
    "emojis-list" "^3.0.0"
    "json5" "^2.1.2"

"local-pkg@^0.4.3":
  "integrity" "sha512-SFppqq5p42fe2qcZQqqEOiVRXl+WCP1MdT6k7BDEW1j++sp5fIY+/fdRQitvKgB5BrBcmrs5m/L0v2FrU5MY1g=="
  "resolved" "http://castle-npm.cp.hxdi.cn/local-pkg/-/local-pkg-0.4.3.tgz"
  "version" "0.4.3"

"locate-path@^5.0.0":
  "integrity" "sha1-Gvujlq/WdqbUJQTQpno6frn2KqA="
  "resolved" "https://registry.npmmirror.com/locate-path/download/locate-path-5.0.0.tgz"
  "version" "5.0.0"
  dependencies:
    "p-locate" "^4.1.0"

"lodash-es@^4.17.21":
  "integrity" "sha1-Q+YmxG5lkbd1C+srUBFzkMYJ4+4="
  "resolved" "https://registry.npmmirror.com/lodash-es/download/lodash-es-4.17.21.tgz"
  "version" "4.17.21"

"lodash.debounce@^4.0.8":
  "integrity" "sha1-gteb/zCmfEAF/9XiUVMArZyk168="
  "resolved" "https://registry.npmmirror.com/lodash.debounce/download/lodash.debounce-4.0.8.tgz"
  "version" "4.0.8"

"lodash.defaultsdeep@^4.6.1":
  "integrity" "sha1-US6b1yHSctlOPTpjZT+hdRZ0HKY="
  "resolved" "https://registry.npmmirror.com/lodash.defaultsdeep/download/lodash.defaultsdeep-4.6.1.tgz"
  "version" "4.6.1"

"lodash.kebabcase@^4.1.1":
  "integrity" "sha1-hImxyw0p/4gZXM7KRI/21swpXDY="
  "resolved" "https://registry.npmmirror.com/lodash.kebabcase/download/lodash.kebabcase-4.1.1.tgz"
  "version" "4.1.1"

"lodash.mapvalues@^4.6.0":
  "integrity" "sha1-G6+lAF3p3W9PJmaMMMo3IwzJaJw="
  "resolved" "https://registry.npmmirror.com/lodash.mapvalues/download/lodash.mapvalues-4.6.0.tgz"
  "version" "4.6.0"

"lodash.memoize@^4.1.2":
  "integrity" "sha1-vMbEmkKihA7Zl/Mj6tpezRguC/4="
  "resolved" "https://registry.npmmirror.com/lodash.memoize/download/lodash.memoize-4.1.2.tgz"
  "version" "4.1.2"

"lodash.merge@^4.6.2":
  "integrity" "sha1-VYqlO0O2YeGSWgr9+japoQhf5Xo="
  "resolved" "https://registry.npmmirror.com/lodash.merge/download/lodash.merge-4.6.2.tgz"
  "version" "4.6.2"

"lodash.truncate@^4.4.2":
  "integrity" "sha1-WjUNoLERO4N+z//VgSy+WNbq4ZM="
  "resolved" "https://registry.npmmirror.com/lodash.truncate/download/lodash.truncate-4.4.2.tgz"
  "version" "4.4.2"

"lodash.uniq@^4.5.0":
  "integrity" "sha1-0CJTc662Uq3BvILklFM5qEJ1R3M="
  "resolved" "https://registry.npmmirror.com/lodash.uniq/download/lodash.uniq-4.5.0.tgz"
  "version" "4.5.0"

"lodash@^4.17.14", "lodash@^4.17.20", "lodash@^4.17.21":
  "integrity" "sha1-Z5WRxWTDv/quhFTPCz3zcMPWkRw="
  "resolved" "https://registry.npmmirror.com/lodash/download/lodash-4.17.21.tgz"
  "version" "4.17.21"

"log-symbols@^4.1.0":
  "integrity" "sha1-P727lbRoOsn8eFER55LlWNSr1QM="
  "resolved" "https://registry.npmmirror.com/log-symbols/download/log-symbols-4.1.0.tgz"
  "version" "4.1.0"
  dependencies:
    "chalk" "^4.1.0"
    "is-unicode-supported" "^0.1.0"

"log-update@^2.1.0":
  "integrity" "sha1-iDKP19HOeTiykoN0bwsbwSayRwg="
  "resolved" "https://registry.npmmirror.com/log-update/download/log-update-2.3.0.tgz?cache=0&sync_timestamp=1634542359448&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2Flog-update%2Fdownload%2Flog-update-2.3.0.tgz"
  "version" "2.3.0"
  dependencies:
    "ansi-escapes" "^3.0.0"
    "cli-cursor" "^2.0.0"
    "wrap-ansi" "^3.0.1"

"lower-case@^2.0.2":
  "integrity" "sha1-b6I3xj29xKgsoP2ILkci3F5jTig="
  "resolved" "https://registry.npmmirror.com/lower-case/download/lower-case-2.0.2.tgz"
  "version" "2.0.2"
  dependencies:
    "tslib" "^2.0.3"

"lru-cache@^4.0.1":
  "integrity" "sha1-i75Q6oW+1ZvJ4z3KuCNe6bz0Q80="
  "resolved" "https://registry.npmmirror.com/lru-cache/download/lru-cache-4.1.5.tgz"
  "version" "4.1.5"
  dependencies:
    "pseudomap" "^1.0.2"
    "yallist" "^2.1.2"

"lru-cache@^4.1.2":
  "integrity" "sha1-i75Q6oW+1ZvJ4z3KuCNe6bz0Q80="
  "resolved" "https://registry.npmmirror.com/lru-cache/download/lru-cache-4.1.5.tgz"
  "version" "4.1.5"
  dependencies:
    "pseudomap" "^1.0.2"
    "yallist" "^2.1.2"

"lru-cache@^6.0.0":
  "integrity" "sha1-bW/mVw69lqr5D8rR2vo7JWbbOpQ="
  "resolved" "https://registry.npmmirror.com/lru-cache/download/lru-cache-6.0.0.tgz"
  "version" "6.0.0"
  dependencies:
    "yallist" "^4.0.0"

"magic-string@^0.25.7":
  "integrity" "sha1-P0l9b9NMZpxnmNy4IfLvMfVEUFE="
  "resolved" "https://registry.npmmirror.com/magic-string/download/magic-string-0.25.7.tgz"
  "version" "0.25.7"
  dependencies:
    "sourcemap-codec" "^1.4.4"

"magic-string@^0.30.1":
  "integrity" "sha512-B7xGbll2fG/VjP+SWg4sX3JynwIU0mjoTc6MPpKNuIvftk6u6vqhDnk1R80b8C2GBR6ywqy+1DcKBrevBg+bmw=="
  "resolved" "http://castle-npm.cp.hxdi.cn/magic-string/-/magic-string-0.30.3.tgz"
  "version" "0.30.3"
  dependencies:
    "@jridgewell/sourcemap-codec" "^1.4.15"

"make-dir@^3.0.2", "make-dir@^3.1.0":
  "integrity" "sha1-QV6WcEazp/HRhSd9hKpYIDcmoT8="
  "resolved" "https://registry.npmmirror.com/make-dir/download/make-dir-3.1.0.tgz"
  "version" "3.1.0"
  dependencies:
    "semver" "^6.0.0"

"map-cache@^0.2.2":
  "integrity" "sha1-wyq9C9ZSXZsFFkW7TyasXcmKDb8="
  "resolved" "https://registry.npmmirror.com/map-cache/download/map-cache-0.2.2.tgz"
  "version" "0.2.2"

"map-visit@^1.0.0":
  "integrity" "sha1-7Nyo8TFE5mDxtb1B8S80edmN+48="
  "resolved" "https://registry.npmmirror.com/map-visit/download/map-visit-1.0.0.tgz"
  "version" "1.0.0"
  dependencies:
    "object-visit" "^1.0.0"

"mdn-data@2.0.14":
  "integrity" "sha1-cRP8QoGRfWPOKbQ0RvcB5owlulA="
  "resolved" "https://registry.npmmirror.com/mdn-data/download/mdn-data-2.0.14.tgz"
  "version" "2.0.14"

"media-typer@0.3.0":
  "integrity" "sha1-hxDXrwqmJvj/+hzgAWhUUmMlV0g="
  "resolved" "https://registry.npmmirror.com/media-typer/download/media-typer-0.3.0.tgz"
  "version" "0.3.0"

"memfs@^3.2.2":
  "integrity" "sha512-o/RfP0J1d03YwsAxyHxAYs2kyJp55AFkMazlFAZFR2I2IXkxiUTXRabJ6RmNNCQ83LAD2jy52Khj0m3OffpNdA=="
  "resolved" "https://registry.npmmirror.com/memfs/download/memfs-3.4.0.tgz"
  "version" "3.4.0"
  dependencies:
    "fs-monkey" "1.0.3"

"merge-descriptors@1.0.1":
  "integrity" "sha1-sAqqVW3YtEVoFQ7J0blT8/kMu2E="
  "resolved" "https://registry.npmmirror.com/merge-descriptors/download/merge-descriptors-1.0.1.tgz"
  "version" "1.0.1"

"merge-options@1.0.1":
  "integrity" "sha1-KmSyRFe+zU5NxggoMkfpTOWJqjI="
  "resolved" "https://registry.npmmirror.com/merge-options/download/merge-options-1.0.1.tgz"
  "version" "1.0.1"
  dependencies:
    "is-plain-obj" "^1.1"

"merge-source-map@^1.1.0":
  "integrity" "sha1-L93n5gIJOfcJBqaPLXrmheTIxkY="
  "resolved" "https://registry.npmmirror.com/merge-source-map/download/merge-source-map-1.1.0.tgz"
  "version" "1.1.0"
  dependencies:
    "source-map" "^0.6.1"

"merge-stream@^2.0.0":
  "integrity" "sha1-UoI2KaFN0AyXcPtq1H3GMQ8sH2A="
  "resolved" "https://registry.npmmirror.com/merge-stream/download/merge-stream-2.0.0.tgz"
  "version" "2.0.0"

"merge2@^1.3.0":
  "integrity" "sha1-Q2iJL4hekHRVpv19xVwMnUBJkK4="
  "resolved" "https://registry.npmmirror.com/merge2/download/merge2-1.4.1.tgz"
  "version" "1.4.1"

"methods@~1.1.2":
  "integrity" "sha1-VSmk1nZUE07cxSZmVoNbD4Ua/O4="
  "resolved" "https://registry.npmmirror.com/methods/download/methods-1.1.2.tgz"
  "version" "1.1.2"

"micromatch@^4.0.2", "micromatch@^4.0.4", "micromatch@^4.0.5":
  "integrity" "sha512-DMy+ERcEW2q8Z2Po+WNXuw3c5YaUSFjAO5GsJqfEl7UjvtIuFKO6ZrKvcItdy98dwFI2N1tg3zNIdKaQT+aNdA=="
  "resolved" "http://castle-npm.cp.hxdi.cn/micromatch/-/micromatch-4.0.5.tgz"
  "version" "4.0.5"
  dependencies:
    "braces" "^3.0.2"
    "picomatch" "^2.3.1"

"micromatch@3.1.0":
  "integrity" "sha1-UQLU6vILaZfWAI46z+HESj+oFeI="
  "resolved" "https://registry.npmmirror.com/micromatch/download/micromatch-3.1.0.tgz"
  "version" "3.1.0"
  dependencies:
    "arr-diff" "^4.0.0"
    "array-unique" "^0.3.2"
    "braces" "^2.2.2"
    "define-property" "^1.0.0"
    "extend-shallow" "^2.0.1"
    "extglob" "^2.0.2"
    "fragment-cache" "^0.2.1"
    "kind-of" "^5.0.2"
    "nanomatch" "^1.2.1"
    "object.pick" "^1.3.0"
    "regex-not" "^1.0.0"
    "snapdragon" "^0.8.1"
    "to-regex" "^3.0.1"

"mime-db@>= 1.43.0 < 2", "mime-db@1.51.0":
  "integrity" "sha512-5y8A56jg7XVQx2mbv1lu49NR4dokRnhZYTtL+KGfaa27uq4pSTXkwQkFJl4pkRMyNFz/EtYDSkiiEHx3F7UN6g=="
  "resolved" "https://registry.npmmirror.com/mime-db/download/mime-db-1.51.0.tgz?cache=0&sync_timestamp=1636426011941&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2Fmime-db%2Fdownload%2Fmime-db-1.51.0.tgz"
  "version" "1.51.0"

"mime-types@^2.1.27", "mime-types@^2.1.31", "mime-types@~2.1.17", "mime-types@~2.1.24":
  "integrity" "sha512-6cP692WwGIs9XXdOO4++N+7qjqv0rqxxVvJ3VHPh/Sc9mVZcQP+ZGhkKiTvWMQRr2tbHkJP/Yn7Y0npb3ZBs4A=="
  "resolved" "https://registry.npmmirror.com/mime-types/download/mime-types-2.1.34.tgz?cache=0&sync_timestamp=1636432243934&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2Fmime-types%2Fdownload%2Fmime-types-2.1.34.tgz"
  "version" "2.1.34"
  dependencies:
    "mime-db" "1.51.0"

"mime@1.6.0":
  "integrity" "sha1-Ms2eXGRVO9WNGaVor0Uqz/BJgbE="
  "resolved" "https://registry.npmmirror.com/mime/download/mime-1.6.0.tgz"
  "version" "1.6.0"

"mimic-fn@^1.0.0":
  "integrity" "sha1-ggyGo5M0ZA6ZUWkovQP8qIBX0CI="
  "resolved" "https://registry.npmmirror.com/mimic-fn/download/mimic-fn-1.2.0.tgz"
  "version" "1.2.0"

"mimic-fn@^2.1.0":
  "integrity" "sha1-ftLCzMyvhNP/y3pptXcR/CCDQBs="
  "resolved" "https://registry.npmmirror.com/mimic-fn/download/mimic-fn-2.1.0.tgz"
  "version" "2.1.0"

"mini-css-extract-plugin@^2.4.3":
  "integrity" "sha512-oEIhRucyn1JbT/1tU2BhnwO6ft1jjH1iCX9Gc59WFMg0n5773rQU0oyQ0zzeYFFuBfONaRbQJyGoPtuNseMxjA=="
  "resolved" "https://registry.npmmirror.com/mini-css-extract-plugin/download/mini-css-extract-plugin-2.4.5.tgz"
  "version" "2.4.5"
  dependencies:
    "schema-utils" "^4.0.0"

"minimalistic-assert@^1.0.0":
  "integrity" "sha1-LhlN4ERibUoQ5/f7wAznPoPk1cc="
  "resolved" "https://registry.npmmirror.com/minimalistic-assert/download/minimalistic-assert-1.0.1.tgz"
  "version" "1.0.1"

"minimatch@^3.0.4":
  "integrity" "sha1-UWbihkV/AzBgZL5Ul+jbsMPTIIM="
  "resolved" "https://registry.npmmirror.com/minimatch/download/minimatch-3.0.4.tgz"
  "version" "3.0.4"
  dependencies:
    "brace-expansion" "^1.1.7"

"minimatch@^9.0.3":
  "integrity" "sha512-RHiac9mvaRw0x3AYRgDC1CxAP7HTcNrrECeA8YYJeWnpo+2Q5CegtZjaotWTWxDG3UeGA1coE05iH1mPjT/2mg=="
  "resolved" "http://castle-npm.cp.hxdi.cn/minimatch/-/minimatch-9.0.3.tgz"
  "version" "9.0.3"
  dependencies:
    "brace-expansion" "^2.0.1"

"minimist@^1.2.0", "minimist@^1.2.5":
  "integrity" "sha1-Z9ZgFLZqaoqqDAg8X9WN9OTpdgI="
  "resolved" "https://registry.npmmirror.com/minimist/download/minimist-1.2.5.tgz"
  "version" "1.2.5"

"minipass@^3.1.1":
  "integrity" "sha512-rty5kpw9/z8SX9dmxblFA6edItUmwJgMeYDZRrwlIVN27i8gysGbznJwUggw2V/FVqFSDdWy040ZPS811DYAqQ=="
  "resolved" "https://registry.npmmirror.com/minipass/download/minipass-3.1.6.tgz"
  "version" "3.1.6"
  dependencies:
    "yallist" "^4.0.0"

"mitt@1.1.2":
  "integrity" "sha1-OA5hSA1qYVtmDwertg1R4KTkvtY="
  "resolved" "https://registry.npmmirror.com/mitt/download/mitt-1.1.2.tgz"
  "version" "1.1.2"

"mixin-deep@^1.2.0":
  "integrity" "sha1-ESC0PcNZp4Xc5ltVuC4lfM9HlWY="
  "resolved" "https://registry.npmmirror.com/mixin-deep/download/mixin-deep-1.3.2.tgz"
  "version" "1.3.2"
  dependencies:
    "for-in" "^1.0.2"
    "is-extendable" "^1.0.1"

"mkdirp@^0.5.1", "mkdirp@^0.5.5":
  "integrity" "sha1-2Rzv1i0UNsoPQWIOJRKI1CAJne8="
  "resolved" "https://registry.npmmirror.com/mkdirp/download/mkdirp-0.5.5.tgz?cache=0&sync_timestamp=1636300883420&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2Fmkdirp%2Fdownload%2Fmkdirp-0.5.5.tgz"
  "version" "0.5.5"
  dependencies:
    "minimist" "^1.2.5"

"module-alias@^2.2.2":
  "integrity" "sha1-FRzc7MJOJXOf8KpuUeHFcWl0wOA="
  "resolved" "https://registry.npmmirror.com/module-alias/download/module-alias-2.2.2.tgz"
  "version" "2.2.2"

"mrmime@^1.0.0":
  "integrity" "sha1-FNOH8FhaUjPSkbq6M5sGN1KiOYs="
  "resolved" "https://registry.npmmirror.com/mrmime/download/mrmime-1.0.0.tgz"
  "version" "1.0.0"

"ms@^2.1.1", "ms@2.1.2":
  "integrity" "sha1-0J0fNXtEP0kzgqjrPM0YOHKuYAk="
  "resolved" "https://registry.npmmirror.com/ms/download/ms-2.1.2.tgz?cache=0&sync_timestamp=1632788458271&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2Fms%2Fdownload%2Fms-2.1.2.tgz"
  "version" "2.1.2"

"ms@2.0.0":
  "integrity" "sha1-VgiurfwAvmwpAd9fmGF4jeDVl8g="
  "resolved" "https://registry.npmmirror.com/ms/download/ms-2.0.0.tgz?cache=0&sync_timestamp=1632788458271&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2Fms%2Fdownload%2Fms-2.0.0.tgz"
  "version" "2.0.0"

"ms@2.1.1":
  "integrity" "sha1-MKWGTrPrsKZvLr5tcnrwagnYbgo="
  "resolved" "https://registry.npmmirror.com/ms/download/ms-2.1.1.tgz?cache=0&sync_timestamp=1632788458271&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2Fms%2Fdownload%2Fms-2.1.1.tgz"
  "version" "2.1.1"

"msedge-tts-browserify@^1.4.1":
  "integrity" "sha512-T0ilajpYKRz6Eh9WzNvP6AJ9LuWOnGYUZGrJECSQtTVeVZ1jh4rRky3k0xCZVfbNBGSe7PtZAyLTNE82ycdemA=="
  "resolved" "https://registry.npmmirror.com/msedge-tts-browserify/-/msedge-tts-browserify-1.4.1.tgz"
  "version" "1.4.1"
  dependencies:
    "buffer" "^6.0.3"

"multicast-dns-service-types@^1.1.0":
  "integrity" "sha1-iZ8R2WhuXgXLkbNdXw5jt3PPyQE="
  "resolved" "https://registry.npmmirror.com/multicast-dns-service-types/download/multicast-dns-service-types-1.1.0.tgz"
  "version" "1.1.0"

"multicast-dns@^6.0.1":
  "integrity" "sha1-oOx72QVcQoL3kMPIL04o2zsxsik="
  "resolved" "https://registry.npmmirror.com/multicast-dns/download/multicast-dns-6.2.3.tgz?cache=0&sync_timestamp=1633355335858&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2Fmulticast-dns%2Fdownload%2Fmulticast-dns-6.2.3.tgz"
  "version" "6.2.3"
  dependencies:
    "dns-packet" "^1.3.1"
    "thunky" "^1.0.2"

"mutation-observer@^1.0.3":
  "integrity" "sha512-M/O/4rF2h776hV7qGMZUH3utZLO/jK7p8rnNgGkjKUw8zCGjRQPxB8z6+5l8+VjRUQ3dNYu4vjqXYLr+U8ZVNA=="
  "resolved" "http://castle-npm.cp.hxdi.cn/mutation-observer/-/mutation-observer-1.0.3.tgz"
  "version" "1.0.3"

"mute-stream@0.0.8":
  "integrity" "sha512-nnbWWOkoWyUsTjKrhgD0dcz22mdkSnpYqbEjIm2nhwhuxlSkpywJmBo8h0ZqJdkp73mb90SssHkN4rsRaBAfAA=="
  "resolved" "http://castle-npm.cp.hxdi.cn/mute-stream/-/mute-stream-0.0.8.tgz"
  "version" "0.0.8"

"mz@^2.4.0", "mz@^2.7.0":
  "integrity" "sha1-lQCAV6Vsr63CvGPd5/n/aVWUjjI="
  "resolved" "https://registry.npmmirror.com/mz/download/mz-2.7.0.tgz"
  "version" "2.7.0"
  dependencies:
    "any-promise" "^1.0.0"
    "object-assign" "^4.0.1"
    "thenify-all" "^1.0.0"

"nanoid@^3.3.6":
  "integrity" "sha512-BGcqMMJuToF7i1rt+2PWSNVnWIkGCU78jBG3RxO/bZlnZPK2Cmi2QaffxGO/2RvWi9sL+FAiRiXMgsyxQ1DIDA=="
  "resolved" "http://castle-npm.cp.hxdi.cn/nanoid/-/nanoid-3.3.6.tgz"
  "version" "3.3.6"

"nanomatch@^1.2.1":
  "integrity" "sha1-uHqKpPwN6P5r6IiVs4mD/yZb0Rk="
  "resolved" "https://registry.npmmirror.com/nanomatch/download/nanomatch-1.2.13.tgz"
  "version" "1.2.13"
  dependencies:
    "arr-diff" "^4.0.0"
    "array-unique" "^0.3.2"
    "define-property" "^2.0.2"
    "extend-shallow" "^3.0.2"
    "fragment-cache" "^0.2.1"
    "is-windows" "^1.0.2"
    "kind-of" "^6.0.2"
    "object.pick" "^1.3.0"
    "regex-not" "^1.0.0"
    "snapdragon" "^0.8.1"
    "to-regex" "^3.0.1"

"natural-compare@^1.4.0":
  "integrity" "sha1-Sr6/7tdUHywnrPspvbvRXI1bpPc="
  "resolved" "https://registry.npmmirror.com/natural-compare/download/natural-compare-1.4.0.tgz"
  "version" "1.4.0"

"negotiator@0.6.2":
  "integrity" "sha1-/qz3zPUlp3rpY0Q2pkiD/+yjRvs="
  "resolved" "https://registry.npmmirror.com/negotiator/download/negotiator-0.6.2.tgz"
  "version" "0.6.2"

"neo-async@^2.6.1", "neo-async@^2.6.2":
  "integrity" "sha1-tKr7k+OustgXTKU88WOrfXMIMF8="
  "resolved" "https://registry.npmmirror.com/neo-async/download/neo-async-2.6.2.tgz"
  "version" "2.6.2"

"nice-try@^1.0.4":
  "integrity" "sha1-ozeKdpbOfSI+iPybdkvX7xCJ42Y="
  "resolved" "https://registry.npmmirror.com/nice-try/download/nice-try-1.0.5.tgz"
  "version" "1.0.5"

"no-case@^3.0.4":
  "integrity" "sha1-02H9XJgA9VhVGoNp/A3NRmK2Ek0="
  "resolved" "https://registry.npmmirror.com/no-case/download/no-case-3.0.4.tgz"
  "version" "3.0.4"
  dependencies:
    "lower-case" "^2.0.2"
    "tslib" "^2.0.3"

"node-fetch@^2.6.1":
  "integrity" "sha1-F1GnwBg06OFpd1hzLp77burfr4k="
  "resolved" "https://registry.npmmirror.com/node-fetch/download/node-fetch-2.6.6.tgz"
  "version" "2.6.6"
  dependencies:
    "whatwg-url" "^5.0.0"

"node-forge@^0.10.0":
  "integrity" "sha1-Mt6ir7Ppkm8C7lzoeUkCaRpna/M="
  "resolved" "https://registry.npmmirror.com/node-forge/download/node-forge-0.10.0.tgz"
  "version" "0.10.0"

"node-ipc@^9.1.1":
  "integrity" "sha1-sy9mEV+dbOhB3E7CAJ1qcz+Yu2s="
  "resolved" "https://registry.npmmirror.com/node-ipc/download/node-ipc-9.2.1.tgz"
  "version" "9.2.1"
  dependencies:
    "event-pubsub" "4.3.0"
    "js-message" "1.0.7"
    "js-queue" "2.0.2"

"node-releases@^2.0.13":
  "integrity" "sha512-uYr7J37ae/ORWdZeQ1xxMJe3NtdmqMC/JZK+geofDrkLUApKRHPd18/TxtBOJ4A0/+uUIliorNrfYV6s1b02eQ=="
  "resolved" "http://castle-npm.cp.hxdi.cn/node-releases/-/node-releases-2.0.13.tgz"
  "version" "2.0.13"

"normalize-package-data@^2.5.0":
  "integrity" "sha1-5m2xg4sgDB38IzIl0SyzZSDiNKg="
  "resolved" "https://registry.npmmirror.com/normalize-package-data/download/normalize-package-data-2.5.0.tgz"
  "version" "2.5.0"
  dependencies:
    "hosted-git-info" "^2.1.4"
    "resolve" "^1.10.0"
    "semver" "2 || 3 || 4 || 5"
    "validate-npm-package-license" "^3.0.1"

"normalize-path@^1.0.0":
  "integrity" "sha1-MtDkcvkf80VwHBWoMRAY07CpA3k="
  "resolved" "https://registry.npmmirror.com/normalize-path/download/normalize-path-1.0.0.tgz"
  "version" "1.0.0"

"normalize-path@^3.0.0", "normalize-path@~3.0.0":
  "integrity" "sha1-Dc1p/yOhybEf0JeDFmRKA4ghamU="
  "resolved" "https://registry.npmmirror.com/normalize-path/download/normalize-path-3.0.0.tgz"
  "version" "3.0.0"

"normalize-range@^0.1.2":
  "integrity" "sha1-LRDAa9/TEuqXd2laTShDlFa3WUI="
  "resolved" "https://registry.npmmirror.com/normalize-range/download/normalize-range-0.1.2.tgz"
  "version" "0.1.2"

"normalize-url@^6.0.1":
  "integrity" "sha1-QNCIW1Nd7/4/MUe+yHfQX+TFZoo="
  "resolved" "https://registry.npmmirror.com/normalize-url/download/normalize-url-6.1.0.tgz"
  "version" "6.1.0"

"npm-run-path@^2.0.0":
  "integrity" "sha1-NakjLfo11wZ7TLLd8jV7GHFTbF8="
  "resolved" "https://registry.npmmirror.com/npm-run-path/download/npm-run-path-2.0.2.tgz?cache=0&sync_timestamp=1633420701906&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2Fnpm-run-path%2Fdownload%2Fnpm-run-path-2.0.2.tgz"
  "version" "2.0.2"
  dependencies:
    "path-key" "^2.0.0"

"npm-run-path@^4.0.1":
  "integrity" "sha1-t+zR5e1T2o43pV4cImnguX7XSOo="
  "resolved" "https://registry.npmmirror.com/npm-run-path/download/npm-run-path-4.0.1.tgz?cache=0&sync_timestamp=1633420701906&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2Fnpm-run-path%2Fdownload%2Fnpm-run-path-4.0.1.tgz"
  "version" "4.0.1"
  dependencies:
    "path-key" "^3.0.0"

"nth-check@^2.0.0":
  "integrity" "sha1-Lv4WL1w9oGoolZ+9PbddvuqfD8I="
  "resolved" "https://registry.npmmirror.com/nth-check/download/nth-check-2.0.1.tgz"
  "version" "2.0.1"
  dependencies:
    "boolbase" "^1.0.0"

"object-assign@^4.0.1", "object-assign@^4.1.0":
  "integrity" "sha1-IQmtx5ZYh8/AXLvUQsrIv7s2CGM="
  "resolved" "https://registry.npmmirror.com/object-assign/download/object-assign-4.1.1.tgz"
  "version" "4.1.1"

"object-copy@^0.1.0":
  "integrity" "sha1-fn2Fi3gb18mRpBupde04EnVOmYw="
  "resolved" "https://registry.npmmirror.com/object-copy/download/object-copy-0.1.0.tgz"
  "version" "0.1.0"
  dependencies:
    "copy-descriptor" "^0.1.0"
    "define-property" "^0.2.5"
    "kind-of" "^3.0.3"

"object-hash@^3.0.0":
  "integrity" "sha512-RSn9F68PjH9HqtltsSnqYC1XXoWe9Bju5+213R98cNGttag9q9yAOTzdbsqvIa7aNm5WffBZFpWYr2aWrklWAw=="
  "resolved" "http://castle-npm.cp.hxdi.cn/object-hash/-/object-hash-3.0.0.tgz"
  "version" "3.0.0"

"object-inspect@^1.9.0":
  "integrity" "sha512-If7BjFlpkzzBeV1cqgT3OSWT3azyoxDGajR+iGnFBfVV2EWyDyWaZZW2ERDjUaY2QM8i5jI3Sj7mhsM4DDAqWA=="
  "resolved" "https://registry.npmmirror.com/object-inspect/download/object-inspect-1.11.1.tgz"
  "version" "1.11.1"

"object-is@^1.0.1":
  "integrity" "sha1-ud7qpfx/GEag+uzc7sE45XePU6w="
  "resolved" "https://registry.npmmirror.com/object-is/download/object-is-1.1.5.tgz"
  "version" "1.1.5"
  dependencies:
    "call-bind" "^1.0.2"
    "define-properties" "^1.1.3"

"object-keys@^1.0.12", "object-keys@^1.1.1":
  "integrity" "sha1-HEfyct8nfzsdrwYWd9nILiMixg4="
  "resolved" "https://registry.npmmirror.com/object-keys/download/object-keys-1.1.1.tgz"
  "version" "1.1.1"

"object-visit@^1.0.0":
  "integrity" "sha1-95xEk68MU3e1n+OdOV5BBC3QRbs="
  "resolved" "https://registry.npmmirror.com/object-visit/download/object-visit-1.0.1.tgz"
  "version" "1.0.1"
  dependencies:
    "isobject" "^3.0.0"

"object.assign@^4.1.0":
  "integrity" "sha1-DtVKNC7Os3s4/3brgxoOeIy2OUA="
  "resolved" "https://registry.npmmirror.com/object.assign/download/object.assign-4.1.2.tgz"
  "version" "4.1.2"
  dependencies:
    "call-bind" "^1.0.0"
    "define-properties" "^1.1.3"
    "has-symbols" "^1.0.1"
    "object-keys" "^1.1.1"

"object.pick@^1.3.0":
  "integrity" "sha1-h6EKxMFpS9Lhy/U1kaZhQftd10c="
  "resolved" "https://registry.npmmirror.com/object.pick/download/object.pick-1.3.0.tgz"
  "version" "1.3.0"
  dependencies:
    "isobject" "^3.0.1"

"obuf@^1.0.0", "obuf@^1.1.2":
  "integrity" "sha1-Cb6jND1BhZ69RGKS0RydTbYZCE4="
  "resolved" "https://registry.npmmirror.com/obuf/download/obuf-1.1.2.tgz"
  "version" "1.1.2"

"on-finished@~2.3.0":
  "integrity" "sha1-IPEzZIGwg811M3mSoWlxqi2QaUc="
  "resolved" "https://registry.npmmirror.com/on-finished/download/on-finished-2.3.0.tgz"
  "version" "2.3.0"
  dependencies:
    "ee-first" "1.1.1"

"on-headers@~1.0.2":
  "integrity" "sha1-dysK5qqlJcOZ5Imt+tkMQD6zwo8="
  "resolved" "https://registry.npmmirror.com/on-headers/download/on-headers-1.0.2.tgz"
  "version" "1.0.2"

"once@^1.3.0", "once@^1.3.1", "once@^1.4.0":
  "integrity" "sha1-WDsap3WWHUsROsF9nFC6753Xa9E="
  "resolved" "https://registry.npmmirror.com/once/download/once-1.4.0.tgz"
  "version" "1.4.0"
  dependencies:
    "wrappy" "1"

"onetime@^2.0.0":
  "integrity" "sha1-BnQoIw/WdEOyeUsiu6UotoZ5YtQ="
  "resolved" "https://registry.npmmirror.com/onetime/download/onetime-2.0.1.tgz"
  "version" "2.0.1"
  dependencies:
    "mimic-fn" "^1.0.0"

"onetime@^5.1.0", "onetime@^5.1.2":
  "integrity" "sha1-0Oluu1awdHbfHdnEgG5SN5hcpF4="
  "resolved" "https://registry.npmmirror.com/onetime/download/onetime-5.1.2.tgz"
  "version" "5.1.2"
  dependencies:
    "mimic-fn" "^2.1.0"

"open@^8.0.2", "open@^8.0.9":
  "integrity" "sha1-NFMhrhj4E4+CVlqRD9xrOejCRPg="
  "resolved" "https://registry.npmmirror.com/open/download/open-8.4.0.tgz?cache=0&sync_timestamp=1635049260934&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2Fopen%2Fdownload%2Fopen-8.4.0.tgz"
  "version" "8.4.0"
  dependencies:
    "define-lazy-prop" "^2.0.0"
    "is-docker" "^2.1.1"
    "is-wsl" "^2.2.0"

"opener@^1.5.2":
  "integrity" "sha1-XTfh81B3udysQwE3InGv3rKhNZg="
  "resolved" "https://registry.npmmirror.com/opener/download/opener-1.5.2.tgz"
  "version" "1.5.2"

"optionator@^0.9.1":
  "integrity" "sha1-TyNqY3Pa4FZqbUPhMmZ09QwpFJk="
  "resolved" "https://registry.npmmirror.com/optionator/download/optionator-0.9.1.tgz"
  "version" "0.9.1"
  dependencies:
    "deep-is" "^0.1.3"
    "fast-levenshtein" "^2.0.6"
    "levn" "^0.4.1"
    "prelude-ls" "^1.2.1"
    "type-check" "^0.4.0"
    "word-wrap" "^1.2.3"

"ora@^5.3.0":
  "integrity" "sha1-GyZ4Qmr0rEpQkAjl5KyemVnbnhg="
  "resolved" "https://registry.npmmirror.com/ora/download/ora-5.4.1.tgz"
  "version" "5.4.1"
  dependencies:
    "bl" "^4.1.0"
    "chalk" "^4.1.0"
    "cli-cursor" "^3.1.0"
    "cli-spinners" "^2.5.0"
    "is-interactive" "^1.0.0"
    "is-unicode-supported" "^0.1.0"
    "log-symbols" "^4.1.0"
    "strip-ansi" "^6.0.0"
    "wcwidth" "^1.0.1"

"os-tmpdir@~1.0.2":
  "integrity" "sha1-u+Z0BseaqFxc/sdm/lc0VV36EnQ="
  "resolved" "http://castle-npm.cp.hxdi.cn/os-tmpdir/-/os-tmpdir-1.0.2.tgz"
  "version" "1.0.2"

"p-finally@^1.0.0":
  "integrity" "sha1-P7z7FbiZpEEjs0ttzBi3JDNqLK4="
  "resolved" "https://registry.npmmirror.com/p-finally/download/p-finally-1.0.0.tgz"
  "version" "1.0.0"

"p-limit@^2.2.0":
  "integrity" "sha1-PdM8ZHohT9//2DWTPrCG2g3CHbE="
  "resolved" "https://registry.npmmirror.com/p-limit/download/p-limit-2.3.0.tgz"
  "version" "2.3.0"
  dependencies:
    "p-try" "^2.0.0"

"p-locate@^4.1.0":
  "integrity" "sha1-o0KLtwiLOmApL2aRkni3wpetTwc="
  "resolved" "https://registry.npmmirror.com/p-locate/download/p-locate-4.1.0.tgz"
  "version" "4.1.0"
  dependencies:
    "p-limit" "^2.2.0"

"p-map@^4.0.0":
  "integrity" "sha1-uy+Vpe2i7BaOySdOBqdHw+KQTSs="
  "resolved" "https://registry.npmmirror.com/p-map/download/p-map-4.0.0.tgz"
  "version" "4.0.0"
  dependencies:
    "aggregate-error" "^3.0.0"

"p-retry@^4.5.0":
  "integrity" "sha1-j83dXN96Z6CRGpzy7w5d9/YCMWw="
  "resolved" "https://registry.npmmirror.com/p-retry/download/p-retry-4.6.1.tgz"
  "version" "4.6.1"
  dependencies:
    "@types/retry" "^0.12.0"
    "retry" "^0.13.1"

"p-try@^2.0.0":
  "integrity" "sha1-yyhoVA4xPWHeWPr741zpAE1VQOY="
  "resolved" "https://registry.npmmirror.com/p-try/download/p-try-2.2.0.tgz"
  "version" "2.2.0"

"param-case@^3.0.4":
  "integrity" "sha1-fRf+SqEr3jTUp32RrPtiGcqtAcU="
  "resolved" "https://registry.npmmirror.com/param-case/download/param-case-3.0.4.tgz"
  "version" "3.0.4"
  dependencies:
    "dot-case" "^3.0.4"
    "tslib" "^2.0.3"

"parent-module@^1.0.0":
  "integrity" "sha1-aR0nCeeMefrjoVZiJFLQB2LKqqI="
  "resolved" "https://registry.npmmirror.com/parent-module/download/parent-module-1.0.1.tgz"
  "version" "1.0.1"
  dependencies:
    "callsites" "^3.0.0"

"parse-json@^5.0.0":
  "integrity" "sha1-x2/Gbe5UIxyWKyK8yKcs8vmXU80="
  "resolved" "https://registry.npmmirror.com/parse-json/download/parse-json-5.2.0.tgz?cache=0&sync_timestamp=1637475877442&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2Fparse-json%2Fdownload%2Fparse-json-5.2.0.tgz"
  "version" "5.2.0"
  dependencies:
    "@babel/code-frame" "^7.0.0"
    "error-ex" "^1.3.1"
    "json-parse-even-better-errors" "^2.3.0"
    "lines-and-columns" "^1.1.6"

"parse5-htmlparser2-tree-adapter@^6.0.0":
  "integrity" "sha1-LN+a2CMyEUA3DU2/XT6Sx8jdxuY="
  "resolved" "https://registry.npmmirror.com/parse5-htmlparser2-tree-adapter/download/parse5-htmlparser2-tree-adapter-6.0.1.tgz"
  "version" "6.0.1"
  dependencies:
    "parse5" "^6.0.1"

"parse5@^5.1.1":
  "integrity" "sha1-9o5OW6GFKsLK3AD0VV//bCq7YXg="
  "resolved" "https://registry.npmmirror.com/parse5/download/parse5-5.1.1.tgz"
  "version" "5.1.1"

"parse5@^6.0.1":
  "integrity" "sha1-4aHAhcVps9wIMhGE8Zo5zCf3wws="
  "resolved" "https://registry.npmmirror.com/parse5/download/parse5-6.0.1.tgz"
  "version" "6.0.1"

"parseurl@~1.3.2", "parseurl@~1.3.3":
  "integrity" "sha1-naGee+6NEt/wUT7Vt2lXeTvC6NQ="
  "resolved" "https://registry.npmmirror.com/parseurl/download/parseurl-1.3.3.tgz"
  "version" "1.3.3"

"pascal-case@^3.1.2":
  "integrity" "sha1-tI4O8rmOIF58Ha50fQsVCCN2YOs="
  "resolved" "https://registry.npmmirror.com/pascal-case/download/pascal-case-3.1.2.tgz"
  "version" "3.1.2"
  dependencies:
    "no-case" "^3.0.4"
    "tslib" "^2.0.3"

"pascalcase@^0.1.1":
  "integrity" "sha1-s2PlXoAGym/iF4TS2yK9FdeRfxQ="
  "resolved" "https://registry.npmmirror.com/pascalcase/download/pascalcase-0.1.1.tgz"
  "version" "0.1.1"

"path-browserify@^1.0.1":
  "integrity" "sha1-2YRUqcN1PVeQhg8W9ohnueRr4f0="
  "resolved" "https://registry.npmmirror.com/path-browserify/download/path-browserify-1.0.1.tgz"
  "version" "1.0.1"

"path-exists@^4.0.0":
  "integrity" "sha1-UTvb4tO5XXdi6METfvoZXGxhtbM="
  "resolved" "https://registry.npmmirror.com/path-exists/download/path-exists-4.0.0.tgz"
  "version" "4.0.0"

"path-is-absolute@^1.0.0":
  "integrity" "sha1-F0uSaHNVNP+8es5r9TpanhtcX18="
  "resolved" "https://registry.npmmirror.com/path-is-absolute/download/path-is-absolute-1.0.1.tgz"
  "version" "1.0.1"

"path-key@^2.0.0":
  "integrity" "sha1-QRyttXTFoUDTpLGRDUDYDMn0C0A="
  "resolved" "https://registry.npmmirror.com/path-key/download/path-key-2.0.1.tgz"
  "version" "2.0.1"

"path-key@^2.0.1":
  "integrity" "sha1-QRyttXTFoUDTpLGRDUDYDMn0C0A="
  "resolved" "https://registry.npmmirror.com/path-key/download/path-key-2.0.1.tgz"
  "version" "2.0.1"

"path-key@^3.0.0", "path-key@^3.1.0":
  "integrity" "sha1-WB9q3mWMu6ZaDTOA3ndTKVBU83U="
  "resolved" "https://registry.npmmirror.com/path-key/download/path-key-3.1.1.tgz"
  "version" "3.1.1"

"path-parse@^1.0.7":
  "integrity" "sha1-+8EUtgykKzDZ2vWFjkvWi77bZzU="
  "resolved" "https://registry.npmmirror.com/path-parse/download/path-parse-1.0.7.tgz"
  "version" "1.0.7"

"path-to-regexp@0.1.7":
  "integrity" "sha1-32BBeABfUi8V60SQ5yR6G/qmf4w="
  "resolved" "https://registry.npmmirror.com/path-to-regexp/download/path-to-regexp-0.1.7.tgz"
  "version" "0.1.7"

"path-type@^4.0.0":
  "integrity" "sha1-hO0BwKe6OAr+CdkKjBgNzZ0DBDs="
  "resolved" "https://registry.npmmirror.com/path-type/download/path-type-4.0.0.tgz"
  "version" "4.0.0"

"picocolors@^0.2.1":
  "integrity" "sha1-VwZw95NkaFHRuhNZlpYqutWHhZ8="
  "resolved" "https://registry.npmmirror.com/picocolors/download/picocolors-0.2.1.tgz?cache=0&sync_timestamp=1634093437726&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2Fpicocolors%2Fdownload%2Fpicocolors-0.2.1.tgz"
  "version" "0.2.1"

"picocolors@^1.0.0":
  "integrity" "sha1-y1vcdP8/UYkiNur3nWi8RFZKuBw="
  "resolved" "https://registry.npmmirror.com/picocolors/download/picocolors-1.0.0.tgz?cache=0&sync_timestamp=1634093437726&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2Fpicocolors%2Fdownload%2Fpicocolors-1.0.0.tgz"
  "version" "1.0.0"

"picomatch@^2.0.4", "picomatch@^2.2.1", "picomatch@^2.3.1":
  "integrity" "sha512-JU3teHTNjmE2VCGFzuY8EXzCDVwEqB2a8fsIvwaStHhAWJEeVd1o1QD80CU6+ZdEXXSLbSsuLwJjkCBWqRQUVA=="
  "resolved" "http://castle-npm.cp.hxdi.cn/picomatch/-/picomatch-2.3.1.tgz"
  "version" "2.3.1"

"pify@^2.3.0":
  "integrity" "sha512-udgsAY+fTnvv7kI7aaxbqwWNb0AHiB0qBO89PZKPkoTmGOgdbrHDKD+0B2X4uTfJ/FT1R09r9gTsjUjNJotuog=="
  "resolved" "http://castle-npm.cp.hxdi.cn/pify/-/pify-2.3.0.tgz"
  "version" "2.3.0"

"pirates@^4.0.1":
  "integrity" "sha512-saLsH7WeYYPiD25LDuLRRY/i+6HaPYr6G1OUlN39otzkSTxKnubR9RTxS3/Kk50s1g2JTgFwWQDQyplC5/SHZg=="
  "resolved" "http://castle-npm.cp.hxdi.cn/pirates/-/pirates-4.0.6.tgz"
  "version" "4.0.6"

"pkg-dir@^4.1.0":
  "integrity" "sha1-8JkTPfft5CLoHR2ESCcO6z5CYfM="
  "resolved" "https://registry.npmmirror.com/pkg-dir/download/pkg-dir-4.2.0.tgz?cache=0&sync_timestamp=1633498184785&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2Fpkg-dir%2Fdownload%2Fpkg-dir-4.2.0.tgz"
  "version" "4.2.0"
  dependencies:
    "find-up" "^4.0.0"

"pngjs@^5.0.0":
  "integrity" "sha512-40QW5YalBNfQo5yRYmiw7Yz6TKKVr3h6970B2YE+3fQpsWcrbj1PzJgxeJ19DRQjhMbKPIuMY8rFaXc8moolVw=="
  "resolved" "http://castle-npm.cp.hxdi.cn/pngjs/-/pngjs-5.0.0.tgz"
  "version" "5.0.0"

"pnpm@^7.11.0":
  "integrity" "sha512-cxh4TfCE8L4ZbLBN72kTGKTYP7X08nrIzoQ2rQCKihAcIHAdNIgsk4bEJyE1xHjE+bNJt9skwr7aJv3LpvUawQ=="
  "resolved" "http://castle-npm.cp.hxdi.cn/pnpm/-/pnpm-7.11.0.tgz"
  "version" "7.11.0"

"portfinder@^1.0.26", "portfinder@^1.0.28":
  "integrity" "sha1-Z8RiKFK9U3TdHdkA93n1NGL6x3g="
  "resolved" "https://registry.npmmirror.com/portfinder/download/portfinder-1.0.28.tgz"
  "version" "1.0.28"
  dependencies:
    "async" "^2.6.2"
    "debug" "^3.1.1"
    "mkdirp" "^0.5.5"

"posix-character-classes@^0.1.0":
  "integrity" "sha1-AerA/jta9xoqbAL+q7jB/vfgDqs="
  "resolved" "https://registry.npmmirror.com/posix-character-classes/download/posix-character-classes-0.1.1.tgz"
  "version" "0.1.1"

"postcss-calc@^8.0.0":
  "integrity" "sha1-oFuHqs0TJ0Cl2wlGKjYSRT5d+Qo="
  "resolved" "https://registry.npmmirror.com/postcss-calc/download/postcss-calc-8.0.0.tgz"
  "version" "8.0.0"
  dependencies:
    "postcss-selector-parser" "^6.0.2"
    "postcss-value-parser" "^4.0.2"

"postcss-colormin@^5.2.1":
  "integrity" "sha1-bkRKgG/TxXiCfbrQInYt8ZM0QU0="
  "resolved" "https://registry.npmmirror.com/postcss-colormin/download/postcss-colormin-5.2.1.tgz"
  "version" "5.2.1"
  dependencies:
    "browserslist" "^4.16.6"
    "caniuse-api" "^3.0.0"
    "colord" "^2.9.1"
    "postcss-value-parser" "^4.1.0"

"postcss-convert-values@^5.0.2":
  "integrity" "sha1-h5uEncNnfH1ryUtqLBo/CAh5gFk="
  "resolved" "https://registry.npmmirror.com/postcss-convert-values/download/postcss-convert-values-5.0.2.tgz?cache=0&sync_timestamp=1635857655996&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2Fpostcss-convert-values%2Fdownload%2Fpostcss-convert-values-5.0.2.tgz"
  "version" "5.0.2"
  dependencies:
    "postcss-value-parser" "^4.1.0"

"postcss-discard-comments@^5.0.1":
  "integrity" "sha1-nq5LdHz3YNMfJEfCfwYZ1XGJAf4="
  "resolved" "https://registry.npmmirror.com/postcss-discard-comments/download/postcss-discard-comments-5.0.1.tgz"
  "version" "5.0.1"

"postcss-discard-duplicates@^5.0.1":
  "integrity" "sha1-aPfMZFj+a6suRsn1WuUoafaA5m0="
  "resolved" "https://registry.npmmirror.com/postcss-discard-duplicates/download/postcss-discard-duplicates-5.0.1.tgz"
  "version" "5.0.1"

"postcss-discard-empty@^5.0.1":
  "integrity" "sha1-7hNsOeJ9XS7U2g7l7QK8ip+L9tg="
  "resolved" "https://registry.npmmirror.com/postcss-discard-empty/download/postcss-discard-empty-5.0.1.tgz"
  "version" "5.0.1"

"postcss-discard-overridden@^5.0.1":
  "integrity" "sha1-RUtB9wcwC5gQmnUAXKSrD/J0OsY="
  "resolved" "https://registry.npmmirror.com/postcss-discard-overridden/download/postcss-discard-overridden-5.0.1.tgz"
  "version" "5.0.1"

"postcss-import@^15.1.0":
  "integrity" "sha512-hpr+J05B2FVYUAXHeK1YyI267J/dDDhMU6B6civm8hSY1jYJnBXxzKDKDswzJmtLHryrjhnDjqqp/49t8FALew=="
  "resolved" "http://castle-npm.cp.hxdi.cn/postcss-import/-/postcss-import-15.1.0.tgz"
  "version" "15.1.0"
  dependencies:
    "postcss-value-parser" "^4.0.0"
    "read-cache" "^1.0.0"
    "resolve" "^1.1.7"

"postcss-js@^4.0.1":
  "integrity" "sha512-dDLF8pEO191hJMtlHFPRa8xsizHaM82MLfNkUHdUtVEV3tgTp5oj+8qbEqYM57SLfc74KSbw//4SeJma2LRVIw=="
  "resolved" "http://castle-npm.cp.hxdi.cn/postcss-js/-/postcss-js-4.0.1.tgz"
  "version" "4.0.1"
  dependencies:
    "camelcase-css" "^2.0.1"

"postcss-load-config@^4.0.1":
  "integrity" "sha512-vEJIc8RdiBRu3oRAI0ymerOn+7rPuMvRXslTvZUKZonDHFIczxztIyJ1urxM1x9JXEikvpWWTUUqal5j/8QgvA=="
  "resolved" "http://castle-npm.cp.hxdi.cn/postcss-load-config/-/postcss-load-config-4.0.1.tgz"
  "version" "4.0.1"
  dependencies:
    "lilconfig" "^2.0.5"
    "yaml" "^2.1.1"

"postcss-loader@^6.1.1":
  "integrity" "sha512-WbbYpmAaKcux/P66bZ40bpWsBucjx/TTgVVzRZ9yUO8yQfVBlameJ0ZGVaPfH64hNSBh63a+ICP5nqOpBA0w+Q=="
  "resolved" "https://registry.npmmirror.com/postcss-loader/download/postcss-loader-6.2.1.tgz"
  "version" "6.2.1"
  dependencies:
    "cosmiconfig" "^7.0.0"
    "klona" "^2.0.5"
    "semver" "^7.3.5"

"postcss-merge-longhand@^5.0.4":
  "integrity" "sha512-2lZrOVD+d81aoYkZDpWu6+3dTAAGkCKbV5DoRhnIR7KOULVrI/R7bcMjhrH9KTRy6iiHKqmtG+n/MMj1WmqHFw=="
  "resolved" "https://registry.npmmirror.com/postcss-merge-longhand/download/postcss-merge-longhand-5.0.4.tgz?cache=0&sync_timestamp=1637084815262&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2Fpostcss-merge-longhand%2Fdownload%2Fpostcss-merge-longhand-5.0.4.tgz"
  "version" "5.0.4"
  dependencies:
    "postcss-value-parser" "^4.1.0"
    "stylehacks" "^5.0.1"

"postcss-merge-rules@^5.0.3":
  "integrity" "sha512-cEKTMEbWazVa5NXd8deLdCnXl+6cYG7m2am+1HzqH0EnTdy8fRysatkaXb2dEnR+fdaDxTvuZ5zoBdv6efF6hg=="
  "resolved" "https://registry.npmmirror.com/postcss-merge-rules/download/postcss-merge-rules-5.0.3.tgz?cache=0&sync_timestamp=1637085416849&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2Fpostcss-merge-rules%2Fdownload%2Fpostcss-merge-rules-5.0.3.tgz"
  "version" "5.0.3"
  dependencies:
    "browserslist" "^4.16.6"
    "caniuse-api" "^3.0.0"
    "cssnano-utils" "^2.0.1"
    "postcss-selector-parser" "^6.0.5"

"postcss-minify-font-values@^5.0.1":
  "integrity" "sha1-qQzvv9qgdb09uqGzNYi7TcJord8="
  "resolved" "https://registry.npmmirror.com/postcss-minify-font-values/download/postcss-minify-font-values-5.0.1.tgz"
  "version" "5.0.1"
  dependencies:
    "postcss-value-parser" "^4.1.0"

"postcss-minify-gradients@^5.0.3":
  "integrity" "sha1-+XChHMceCOkJXnjsOms0uRwZVQ4="
  "resolved" "https://registry.npmmirror.com/postcss-minify-gradients/download/postcss-minify-gradients-5.0.3.tgz?cache=0&sync_timestamp=1635856917388&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2Fpostcss-minify-gradients%2Fdownload%2Fpostcss-minify-gradients-5.0.3.tgz"
  "version" "5.0.3"
  dependencies:
    "colord" "^2.9.1"
    "cssnano-utils" "^2.0.1"
    "postcss-value-parser" "^4.1.0"

"postcss-minify-params@^5.0.2":
  "integrity" "sha512-qJAPuBzxO1yhLad7h2Dzk/F7n1vPyfHfCCh5grjGfjhi1ttCnq4ZXGIW77GSrEbh9Hus9Lc/e/+tB4vh3/GpDg=="
  "resolved" "https://registry.npmmirror.com/postcss-minify-params/download/postcss-minify-params-5.0.2.tgz?cache=0&sync_timestamp=1637084815479&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2Fpostcss-minify-params%2Fdownload%2Fpostcss-minify-params-5.0.2.tgz"
  "version" "5.0.2"
  dependencies:
    "alphanum-sort" "^1.0.2"
    "browserslist" "^4.16.6"
    "cssnano-utils" "^2.0.1"
    "postcss-value-parser" "^4.1.0"

"postcss-minify-selectors@^5.1.0":
  "integrity" "sha1-Q4XIRdOXn/FgKRd0Uj/6VOr9WlQ="
  "resolved" "https://registry.npmmirror.com/postcss-minify-selectors/download/postcss-minify-selectors-5.1.0.tgz"
  "version" "5.1.0"
  dependencies:
    "alphanum-sort" "^1.0.2"
    "postcss-selector-parser" "^6.0.5"

"postcss-modules-extract-imports@^3.0.0":
  "integrity" "sha1-zaHwR8CugMl9vijD52pDuIAldB0="
  "resolved" "https://registry.npmmirror.com/postcss-modules-extract-imports/download/postcss-modules-extract-imports-3.0.0.tgz"
  "version" "3.0.0"

"postcss-modules-local-by-default@^4.0.0":
  "integrity" "sha1-67tU+uFZjuz99pGgKz/zs5ClpRw="
  "resolved" "https://registry.npmmirror.com/postcss-modules-local-by-default/download/postcss-modules-local-by-default-4.0.0.tgz"
  "version" "4.0.0"
  dependencies:
    "icss-utils" "^5.0.0"
    "postcss-selector-parser" "^6.0.2"
    "postcss-value-parser" "^4.1.0"

"postcss-modules-scope@^3.0.0":
  "integrity" "sha1-nvMVFFbTu/oSDKRImN/Kby+gHwY="
  "resolved" "https://registry.npmmirror.com/postcss-modules-scope/download/postcss-modules-scope-3.0.0.tgz"
  "version" "3.0.0"
  dependencies:
    "postcss-selector-parser" "^6.0.4"

"postcss-modules-values@^4.0.0":
  "integrity" "sha1-18Xn5ow7s8myfL9Iyguz/7RgLJw="
  "resolved" "https://registry.npmmirror.com/postcss-modules-values/download/postcss-modules-values-4.0.0.tgz"
  "version" "4.0.0"
  dependencies:
    "icss-utils" "^5.0.0"

"postcss-nested@^6.0.1":
  "integrity" "sha512-mEp4xPMi5bSWiMbsgoPfcP74lsWLHkQbZc3sY+jWYd65CUwXrUaTp0fmNpa01ZcETKlIgUdFN/MpS2xZtqL9dQ=="
  "resolved" "http://castle-npm.cp.hxdi.cn/postcss-nested/-/postcss-nested-6.0.1.tgz"
  "version" "6.0.1"
  dependencies:
    "postcss-selector-parser" "^6.0.11"

"postcss-normalize-charset@^5.0.1":
  "integrity" "sha1-EhVZ0b68VayNJK839nvU2p79kdA="
  "resolved" "https://registry.npmmirror.com/postcss-normalize-charset/download/postcss-normalize-charset-5.0.1.tgz"
  "version" "5.0.1"

"postcss-normalize-display-values@^5.0.1":
  "integrity" "sha1-YmULllmBqVXf/ugzY0U9uC9q0f0="
  "resolved" "https://registry.npmmirror.com/postcss-normalize-display-values/download/postcss-normalize-display-values-5.0.1.tgz"
  "version" "5.0.1"
  dependencies:
    "cssnano-utils" "^2.0.1"
    "postcss-value-parser" "^4.1.0"

"postcss-normalize-positions@^5.0.1":
  "integrity" "sha1-ho9q8Xlf36hvu+lg3OtH5flJL+U="
  "resolved" "https://registry.npmmirror.com/postcss-normalize-positions/download/postcss-normalize-positions-5.0.1.tgz"
  "version" "5.0.1"
  dependencies:
    "postcss-value-parser" "^4.1.0"

"postcss-normalize-repeat-style@^5.0.1":
  "integrity" "sha1-y8DeE4O1f1u2Hd1qhGU7XoZlsrU="
  "resolved" "https://registry.npmmirror.com/postcss-normalize-repeat-style/download/postcss-normalize-repeat-style-5.0.1.tgz"
  "version" "5.0.1"
  dependencies:
    "cssnano-utils" "^2.0.1"
    "postcss-value-parser" "^4.1.0"

"postcss-normalize-string@^5.0.1":
  "integrity" "sha1-2er6pN94x6O5c640bvDkfFVJhbA="
  "resolved" "https://registry.npmmirror.com/postcss-normalize-string/download/postcss-normalize-string-5.0.1.tgz"
  "version" "5.0.1"
  dependencies:
    "postcss-value-parser" "^4.1.0"

"postcss-normalize-timing-functions@^5.0.1":
  "integrity" "sha1-juQRA7kTBCnGy7pzaTK3XF4ssIw="
  "resolved" "https://registry.npmmirror.com/postcss-normalize-timing-functions/download/postcss-normalize-timing-functions-5.0.1.tgz"
  "version" "5.0.1"
  dependencies:
    "cssnano-utils" "^2.0.1"
    "postcss-value-parser" "^4.1.0"

"postcss-normalize-unicode@^5.0.1":
  "integrity" "sha1-gtZy1kikEYFKpb865WU3nM2fXjc="
  "resolved" "https://registry.npmmirror.com/postcss-normalize-unicode/download/postcss-normalize-unicode-5.0.1.tgz"
  "version" "5.0.1"
  dependencies:
    "browserslist" "^4.16.0"
    "postcss-value-parser" "^4.1.0"

"postcss-normalize-url@^5.0.3":
  "integrity" "sha512-qWiUMbvkRx3kc1Dp5opzUwc7MBWZcSDK2yofCmdvFBCpx+zFPkxBC1FASQ59Pt+flYfj/nTZSkmF56+XG5elSg=="
  "resolved" "https://registry.npmmirror.com/postcss-normalize-url/download/postcss-normalize-url-5.0.3.tgz?cache=0&sync_timestamp=1637084815840&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2Fpostcss-normalize-url%2Fdownload%2Fpostcss-normalize-url-5.0.3.tgz"
  "version" "5.0.3"
  dependencies:
    "is-absolute-url" "^3.0.3"
    "normalize-url" "^6.0.1"
    "postcss-value-parser" "^4.1.0"

"postcss-normalize-whitespace@^5.0.1":
  "integrity" "sha1-sLQLW8rINYX/B+rS2vLc++7vjpo="
  "resolved" "https://registry.npmmirror.com/postcss-normalize-whitespace/download/postcss-normalize-whitespace-5.0.1.tgz"
  "version" "5.0.1"
  dependencies:
    "postcss-value-parser" "^4.1.0"

"postcss-ordered-values@^5.0.2":
  "integrity" "sha1-HzUUJpd74A4PdlsxZK11PayO0EQ="
  "resolved" "https://registry.npmmirror.com/postcss-ordered-values/download/postcss-ordered-values-5.0.2.tgz"
  "version" "5.0.2"
  dependencies:
    "cssnano-utils" "^2.0.1"
    "postcss-value-parser" "^4.1.0"

"postcss-prefix-selector@^1.6.0":
  "integrity" "sha512-8d5fiBQZWMtGWH/7ewEeo6RnBNyT2kLD5wTIfV2oHYqH4hjiofg/rP5X3SUwnqOINzE4mM/K/UOAiNrIaKzd4w=="
  "resolved" "https://registry.npmmirror.com/postcss-prefix-selector/download/postcss-prefix-selector-1.14.0.tgz"
  "version" "1.14.0"

"postcss-reduce-initial@^5.0.2":
  "integrity" "sha512-v/kbAAQ+S1V5v9TJvbGkV98V2ERPdU6XvMcKMjqAlYiJ2NtsHGlKYLPjWWcXlaTKNxooId7BGxeraK8qXvzKtw=="
  "resolved" "https://registry.npmmirror.com/postcss-reduce-initial/download/postcss-reduce-initial-5.0.2.tgz"
  "version" "5.0.2"
  dependencies:
    "browserslist" "^4.16.6"
    "caniuse-api" "^3.0.0"

"postcss-reduce-transforms@^5.0.1":
  "integrity" "sha1-k8EvahWUdKpxHVJpkj4jg87c9kA="
  "resolved" "https://registry.npmmirror.com/postcss-reduce-transforms/download/postcss-reduce-transforms-5.0.1.tgz"
  "version" "5.0.1"
  dependencies:
    "cssnano-utils" "^2.0.1"
    "postcss-value-parser" "^4.1.0"

"postcss-selector-parser@^6.0.11", "postcss-selector-parser@^6.0.2", "postcss-selector-parser@^6.0.4", "postcss-selector-parser@^6.0.5":
  "integrity" "sha512-EaV1Gl4mUEV4ddhDnv/xtj7sxwrwxdetHdWUGnT4VJQf+4d05v6lHYZr8N573k5Z0BViss7BDhfWtKS3+sfAqQ=="
  "resolved" "http://castle-npm.cp.hxdi.cn/postcss-selector-parser/-/postcss-selector-parser-6.0.13.tgz"
  "version" "6.0.13"
  dependencies:
    "cssesc" "^3.0.0"
    "util-deprecate" "^1.0.2"

"postcss-svgo@^5.0.3":
  "integrity" "sha1-2UUYV1bl36rgf57bDTyuf/efmzA="
  "resolved" "https://registry.npmmirror.com/postcss-svgo/download/postcss-svgo-5.0.3.tgz"
  "version" "5.0.3"
  dependencies:
    "postcss-value-parser" "^4.1.0"
    "svgo" "^2.7.0"

"postcss-unique-selectors@^5.0.2":
  "integrity" "sha512-w3zBVlrtZm7loQWRPVC0yjUwwpty7OM6DnEHkxcSQXO1bMS3RJ+JUS5LFMSDZHJcvGsRwhZinCWVqn8Kej4EDA=="
  "resolved" "https://registry.npmmirror.com/postcss-unique-selectors/download/postcss-unique-selectors-5.0.2.tgz?cache=0&sync_timestamp=1637084816359&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2Fpostcss-unique-selectors%2Fdownload%2Fpostcss-unique-selectors-5.0.2.tgz"
  "version" "5.0.2"
  dependencies:
    "alphanum-sort" "^1.0.2"
    "postcss-selector-parser" "^6.0.5"

"postcss-value-parser@^4.0.0", "postcss-value-parser@^4.0.2", "postcss-value-parser@^4.1.0", "postcss-value-parser@^4.2.0":
  "integrity" "sha512-1NNCs6uurfkVbeXG4S8JFT9t19m45ICnif8zWLd5oPSZ50QnwMfK+H3jv408d4jw/7Bttv5axS5IiHoLaVNHeQ=="
  "resolved" "https://registry.npmmirror.com/postcss-value-parser/download/postcss-value-parser-4.2.0.tgz"
  "version" "4.2.0"

"postcss@^5.2.17":
  "integrity" "sha1-ut+hSX1GJE9jkPWLMZgw2RB4U8U="
  "resolved" "https://registry.npmmirror.com/postcss/download/postcss-5.2.18.tgz"
  "version" "5.2.18"
  dependencies:
    "chalk" "^1.1.3"
    "js-base64" "^2.1.9"
    "source-map" "^0.5.6"
    "supports-color" "^3.2.3"

"postcss@^7.0.0 || ^8.0.1", "postcss@^8.0.0", "postcss@^8.0.9", "postcss@^8.1.0", "postcss@^8.1.10", "postcss@^8.2.14", "postcss@^8.2.15", "postcss@^8.2.2", "postcss@^8.2.6", "postcss@^8.3.5", "postcss@^8.4.21", "postcss@^8.4.23", "postcss@^8.4.29", "postcss@>=8.0.9", "postcss@7.x || 8.x":
  "integrity" "sha512-cbI+jaqIeu/VGqXEarWkRCCffhjgXc0qjBtXpqJhTBohMUjUQnbBr0xqX3vEKudc4iviTewcJo5ajcec5+wdJw=="
  "resolved" "http://castle-npm.cp.hxdi.cn/postcss/-/postcss-8.4.29.tgz"
  "version" "8.4.29"
  dependencies:
    "nanoid" "^3.3.6"
    "picocolors" "^1.0.0"
    "source-map-js" "^1.0.2"

"postcss@^7.0.36":
  "integrity" "sha1-liQ3XZZWMOLh8sAqk1yCpZy0gwk="
  "resolved" "https://registry.npmmirror.com/postcss/download/postcss-7.0.39.tgz"
  "version" "7.0.39"
  dependencies:
    "picocolors" "^0.2.1"
    "source-map" "^0.6.1"

"postcss@5 - 7":
  "integrity" "sha1-liQ3XZZWMOLh8sAqk1yCpZy0gwk="
  "resolved" "https://registry.npmmirror.com/postcss/download/postcss-7.0.39.tgz"
  "version" "7.0.39"
  dependencies:
    "picocolors" "^0.2.1"
    "source-map" "^0.6.1"

"posthtml-parser@^0.2.0", "posthtml-parser@^0.2.1":
  "integrity" "sha1-NdUw3jhnQMK6JP8usvrznM3ycd0="
  "resolved" "https://registry.npmmirror.com/posthtml-parser/download/posthtml-parser-0.2.1.tgz"
  "version" "0.2.1"
  dependencies:
    "htmlparser2" "^3.8.3"
    "isobject" "^2.1.0"

"posthtml-rename-id@^1.0":
  "integrity" "sha1-z39us3FGvxr6wx5o8YxswZrmFDM="
  "resolved" "https://registry.npmmirror.com/posthtml-rename-id/download/posthtml-rename-id-1.0.12.tgz"
  "version" "1.0.12"
  dependencies:
    "escape-string-regexp" "1.0.5"

"posthtml-render@^1.0.5", "posthtml-render@^1.0.6":
  "integrity" "sha1-QBFAcMRYgcrLkzR9rj7/U6+8/xM="
  "resolved" "https://registry.npmmirror.com/posthtml-render/download/posthtml-render-1.4.0.tgz"
  "version" "1.4.0"

"posthtml-svg-mode@^1.0.3":
  "integrity" "sha1-q9VU+s6BIjyrDLNn4Y5O/SpOdLA="
  "resolved" "https://registry.npmmirror.com/posthtml-svg-mode/download/posthtml-svg-mode-1.0.3.tgz"
  "version" "1.0.3"
  dependencies:
    "merge-options" "1.0.1"
    "posthtml" "^0.9.2"
    "posthtml-parser" "^0.2.1"
    "posthtml-render" "^1.0.6"

"posthtml@^0.9.2":
  "integrity" "sha1-9MBtufZ7Yf0XxOJW5+PZUVv3Jv0="
  "resolved" "https://registry.npmmirror.com/posthtml/download/posthtml-0.9.2.tgz"
  "version" "0.9.2"
  dependencies:
    "posthtml-parser" "^0.2.0"
    "posthtml-render" "^1.0.5"

"prelude-ls@^1.2.1":
  "integrity" "sha1-3rxkidem5rDnYRiIzsiAM30xY5Y="
  "resolved" "https://registry.npmmirror.com/prelude-ls/download/prelude-ls-1.2.1.tgz"
  "version" "1.2.1"

"prettier-linter-helpers@^1.0.0":
  "integrity" "sha1-0j1B/hN1ZG3i0BBNNFSjAIgCz3s="
  "resolved" "https://registry.npmmirror.com/prettier-linter-helpers/download/prettier-linter-helpers-1.0.0.tgz"
  "version" "1.0.0"
  dependencies:
    "fast-diff" "^1.1.2"

"prettier@^1.18.2 || ^2.0.0", "prettier@^2.2.1", "prettier@>= 1.13.0", "prettier@>=1.13.0":
  "integrity" "sha512-vBZcPRUR5MZJwoyi3ZoyQlc1rXeEck8KgeC9AwwOn+exuxLxq5toTRDTSaVrXHxelDMHy9zlicw8u66yxoSUFg=="
  "resolved" "https://registry.npmmirror.com/prettier/download/prettier-2.5.1.tgz"
  "version" "2.5.1"

"pretty-error@^4.0.0":
  "integrity" "sha1-kKcD9G3XI0rbRtD4SCPp0cuPENY="
  "resolved" "https://registry.npmmirror.com/pretty-error/download/pretty-error-4.0.0.tgz"
  "version" "4.0.0"
  dependencies:
    "lodash" "^4.17.20"
    "renderkid" "^3.0.0"

"process-nextick-args@~2.0.0":
  "integrity" "sha1-eCDZsWEgzFXKmud5JoCufbptf+I="
  "resolved" "https://registry.npmmirror.com/process-nextick-args/download/process-nextick-args-2.0.1.tgz"
  "version" "2.0.1"

"progress-webpack-plugin@^1.0.12":
  "integrity" "sha1-A08ovlJDBikEoC3fpTxECXD7vIE="
  "resolved" "https://registry.npmmirror.com/progress-webpack-plugin/download/progress-webpack-plugin-1.0.12.tgz"
  "version" "1.0.12"
  dependencies:
    "chalk" "^2.1.0"
    "figures" "^2.0.0"
    "log-update" "^2.1.0"

"progress@^2.0.0":
  "integrity" "sha1-foz42PW48jnBvGi+tOt4Vn1XLvg="
  "resolved" "https://registry.npmmirror.com/progress/download/progress-2.0.3.tgz?cache=0&sync_timestamp=1632753471690&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2Fprogress%2Fdownload%2Fprogress-2.0.3.tgz"
  "version" "2.0.3"

"proxy-addr@~2.0.5":
  "integrity" "sha1-8Z/mnOqzEe65S0LnDowgcPm6ECU="
  "resolved" "https://registry.npmmirror.com/proxy-addr/download/proxy-addr-2.0.7.tgz"
  "version" "2.0.7"
  dependencies:
    "forwarded" "0.2.0"
    "ipaddr.js" "1.9.1"

"pseudomap@^1.0.2":
  "integrity" "sha1-8FKijacOYYkX7wqKw0wa5aaChrM="
  "resolved" "https://registry.npmmirror.com/pseudomap/download/pseudomap-1.0.2.tgz"
  "version" "1.0.2"

"pump@^3.0.0":
  "integrity" "sha1-tKIRaBW94vTh6mAjVOjHVWUQemQ="
  "resolved" "https://registry.npmmirror.com/pump/download/pump-3.0.0.tgz"
  "version" "3.0.0"
  dependencies:
    "end-of-stream" "^1.1.0"
    "once" "^1.3.1"

"punycode@^2.1.0":
  "integrity" "sha1-tYsBCsQMIsVldhbI0sLALHv0eew="
  "resolved" "https://registry.npmmirror.com/punycode/download/punycode-2.1.1.tgz"
  "version" "2.1.1"

"punycode@1.3.2":
  "integrity" "sha1-llOgNvt8HuQjQvIyXM7v6jkmxI0="
  "resolved" "https://registry.npmmirror.com/punycode/download/punycode-1.3.2.tgz"
  "version" "1.3.2"

"qrcode@^1.5.1":
  "integrity" "sha512-nS8NJ1Z3md8uTjKtP+SGGhfqmTCs5flU/xR623oI0JX+Wepz9R8UrRVCTBTJm3qGw3rH6jJ6MUHjkDx15cxSSg=="
  "resolved" "http://castle-npm.cp.hxdi.cn/qrcode/-/qrcode-1.5.1.tgz"
  "version" "1.5.1"
  dependencies:
    "dijkstrajs" "^1.0.1"
    "encode-utf8" "^1.0.3"
    "pngjs" "^5.0.0"
    "yargs" "^15.3.1"

"qs@^6.10.1":
  "integrity" "sha512-mSIdjzqznWgfd4pMii7sHtaYF8rx8861hBO80SraY5GT0XQibWZWJSid0avzHGkDIZLImux2S5mXO0Hfct2QCw=="
  "resolved" "https://registry.npmmirror.com/qs/download/qs-6.10.2.tgz"
  "version" "6.10.2"
  dependencies:
    "side-channel" "^1.0.4"

"qs@6.7.0":
  "integrity" "sha1-QdwaAV49WB8WIXdr4xr7KHapsbw="
  "resolved" "https://registry.npmmirror.com/qs/download/qs-6.7.0.tgz"
  "version" "6.7.0"

"query-string@^4.3.2":
  "integrity" "sha1-u7aTucqRXCMlFbIosaArYJBD2+s="
  "resolved" "https://registry.npmmirror.com/query-string/download/query-string-4.3.4.tgz"
  "version" "4.3.4"
  dependencies:
    "object-assign" "^4.1.0"
    "strict-uri-encode" "^1.0.0"

"querystring@0.2.0":
  "integrity" "sha1-sgmEkgO7Jd+CDadW50cAWHhSFiA="
  "resolved" "https://registry.npmmirror.com/querystring/download/querystring-0.2.0.tgz"
  "version" "0.2.0"

"queue-microtask@^1.2.2":
  "integrity" "sha1-SSkii7xyTfrEPg77BYyve2z7YkM="
  "resolved" "https://registry.npmmirror.com/queue-microtask/download/queue-microtask-1.2.3.tgz"
  "version" "1.2.3"

"randombytes@^2.1.0":
  "integrity" "sha1-32+ENy8CcNxlzfYpE0mrekc9Tyo="
  "resolved" "https://registry.npmmirror.com/randombytes/download/randombytes-2.1.0.tgz"
  "version" "2.1.0"
  dependencies:
    "safe-buffer" "^5.1.0"

"range-parser@^1.2.1", "range-parser@~1.2.1":
  "integrity" "sha1-PPNwI9GZ4cJNGlW4SADC8+ZGgDE="
  "resolved" "https://registry.npmmirror.com/range-parser/download/range-parser-1.2.1.tgz"
  "version" "1.2.1"

"raw-body@2.4.0":
  "integrity" "sha1-oc5vucm8NWylLoklarWQWeE9AzI="
  "resolved" "https://registry.npmmirror.com/raw-body/download/raw-body-2.4.0.tgz?cache=0&sync_timestamp=1637116816390&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2Fraw-body%2Fdownload%2Fraw-body-2.4.0.tgz"
  "version" "2.4.0"
  dependencies:
    "bytes" "3.1.0"
    "http-errors" "1.7.2"
    "iconv-lite" "0.4.24"
    "unpipe" "1.0.0"

"read-cache@^1.0.0":
  "integrity" "sha1-5mTvMRYRZsl1HNvo28+GtftY93Q="
  "resolved" "http://castle-npm.cp.hxdi.cn/read-cache/-/read-cache-1.0.0.tgz"
  "version" "1.0.0"
  dependencies:
    "pify" "^2.3.0"

"read-pkg-up@^7.0.1":
  "integrity" "sha1-86YTV1hFlzOuK5VjgFbhhU5+9Qc="
  "resolved" "https://registry.npmmirror.com/read-pkg-up/download/read-pkg-up-7.0.1.tgz"
  "version" "7.0.1"
  dependencies:
    "find-up" "^4.1.0"
    "read-pkg" "^5.2.0"
    "type-fest" "^0.8.1"

"read-pkg@^5.1.1", "read-pkg@^5.2.0":
  "integrity" "sha1-e/KVQ4yloz5WzTDgU7NO5yUMk8w="
  "resolved" "https://registry.npmmirror.com/read-pkg/download/read-pkg-5.2.0.tgz"
  "version" "5.2.0"
  dependencies:
    "@types/normalize-package-data" "^2.4.0"
    "normalize-package-data" "^2.5.0"
    "parse-json" "^5.0.0"
    "type-fest" "^0.6.0"

"readable-stream@^2.0.1":
  "integrity" "sha1-Hsoc9xGu+BTAT2IlKjamL2yyO1c="
  "resolved" "https://registry.npmmirror.com/readable-stream/download/readable-stream-2.3.7.tgz"
  "version" "2.3.7"
  dependencies:
    "core-util-is" "~1.0.0"
    "inherits" "~2.0.3"
    "isarray" "~1.0.0"
    "process-nextick-args" "~2.0.0"
    "safe-buffer" "~5.1.1"
    "string_decoder" "~1.1.1"
    "util-deprecate" "~1.0.1"

"readable-stream@^3.0.6", "readable-stream@^3.1.1", "readable-stream@^3.4.0":
  "integrity" "sha1-M3u9o63AcGvT4CRCaihtS0sskZg="
  "resolved" "https://registry.npmmirror.com/readable-stream/download/readable-stream-3.6.0.tgz"
  "version" "3.6.0"
  dependencies:
    "inherits" "^2.0.3"
    "string_decoder" "^1.1.1"
    "util-deprecate" "^1.0.1"

"readdirp@~3.6.0":
  "integrity" "sha1-dKNwvYVxFuJFspzJc0DNQxoCpsc="
  "resolved" "https://registry.npmmirror.com/readdirp/download/readdirp-3.6.0.tgz"
  "version" "3.6.0"
  dependencies:
    "picomatch" "^2.2.1"

"regenerate-unicode-properties@^9.0.0":
  "integrity" "sha1-VNCccRXh9T3CMUqXSzLBw0Tv4yY="
  "resolved" "https://registry.npmmirror.com/regenerate-unicode-properties/download/regenerate-unicode-properties-9.0.0.tgz"
  "version" "9.0.0"
  dependencies:
    "regenerate" "^1.4.2"

"regenerate@^1.4.2":
  "integrity" "sha1-uTRtiCfo9aMve6KWN9OYtpAUhIo="
  "resolved" "https://registry.npmmirror.com/regenerate/download/regenerate-1.4.2.tgz"
  "version" "1.4.2"

"regenerator-runtime@^0.13.11":
  "integrity" "sha512-kY1AZVr2Ra+t+piVaJ4gxaFaReZVH40AKNo7UCX6W+dEwBo/2oZJzqfuN1qLq1oL45o56cPaTXELwrTh8Fpggg=="
  "resolved" "http://castle-npm.cp.hxdi.cn/regenerator-runtime/-/regenerator-runtime-0.13.11.tgz"
  "version" "0.13.11"

"regenerator-transform@^0.14.2":
  "integrity" "sha1-yY2hVGg2ccnE3LFuznNlF+G3/rQ="
  "resolved" "https://registry.npmmirror.com/regenerator-transform/download/regenerator-transform-0.14.5.tgz"
  "version" "0.14.5"
  dependencies:
    "@babel/runtime" "^7.8.4"

"regex-not@^1.0.0", "regex-not@^1.0.2":
  "integrity" "sha1-H07OJ+ALC2XgJHpoEOaoXYOldSw="
  "resolved" "https://registry.npmmirror.com/regex-not/download/regex-not-1.0.2.tgz"
  "version" "1.0.2"
  dependencies:
    "extend-shallow" "^3.0.2"
    "safe-regex" "^1.1.0"

"regexp.prototype.flags@^1.2.0":
  "integrity" "sha1-fvNSro0VnnWMDq3Kb4/LTu8HviY="
  "resolved" "https://registry.npmmirror.com/regexp.prototype.flags/download/regexp.prototype.flags-1.3.1.tgz"
  "version" "1.3.1"
  dependencies:
    "call-bind" "^1.0.2"
    "define-properties" "^1.1.3"

"regexpp@^3.1.0":
  "integrity" "sha1-BCWido2PI7rXDKS5BGH6LxIT4bI="
  "resolved" "https://registry.npmmirror.com/regexpp/download/regexpp-3.2.0.tgz"
  "version" "3.2.0"

"regexpu-core@^4.7.1":
  "integrity" "sha1-5WBbo2G2excYR4UBMnUC9EeamPA="
  "resolved" "https://registry.npmmirror.com/regexpu-core/download/regexpu-core-4.8.0.tgz"
  "version" "4.8.0"
  dependencies:
    "regenerate" "^1.4.2"
    "regenerate-unicode-properties" "^9.0.0"
    "regjsgen" "^0.5.2"
    "regjsparser" "^0.7.0"
    "unicode-match-property-ecmascript" "^2.0.0"
    "unicode-match-property-value-ecmascript" "^2.0.0"

"regjsgen@^0.5.2":
  "integrity" "sha1-kv8pX7He7L9uzaslQ9IH6RqjNzM="
  "resolved" "https://registry.npmmirror.com/regjsgen/download/regjsgen-0.5.2.tgz?cache=0&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2Fregjsgen%2Fdownload%2Fregjsgen-0.5.2.tgz"
  "version" "0.5.2"

"regjsparser@^0.7.0":
  "integrity" "sha1-prZntUyIXhi1JVTLSWDvcRh+mWg="
  "resolved" "https://registry.npmmirror.com/regjsparser/download/regjsparser-0.7.0.tgz"
  "version" "0.7.0"
  dependencies:
    "jsesc" "~0.5.0"

"relateurl@^0.2.7":
  "integrity" "sha1-VNvzd+UUQKypCkzSdGANP/LYiKk="
  "resolved" "https://registry.npmmirror.com/relateurl/download/relateurl-0.2.7.tgz"
  "version" "0.2.7"

"renderkid@^3.0.0":
  "integrity" "sha1-X9gj5NaVHTc1jsyaWLHwaDa2Joo="
  "resolved" "https://registry.npmmirror.com/renderkid/download/renderkid-3.0.0.tgz"
  "version" "3.0.0"
  dependencies:
    "css-select" "^4.1.3"
    "dom-converter" "^0.2.0"
    "htmlparser2" "^6.1.0"
    "lodash" "^4.17.21"
    "strip-ansi" "^6.0.1"

"repeat-element@^1.1.2":
  "integrity" "sha1-vmgVIIR6tYx1aKx1+/rSjtQtOek="
  "resolved" "https://registry.npmmirror.com/repeat-element/download/repeat-element-1.1.4.tgz"
  "version" "1.1.4"

"repeat-string@^1.6.1":
  "integrity" "sha1-jcrkcOHIirwtYA//Sndihtp15jc="
  "resolved" "https://registry.npmmirror.com/repeat-string/download/repeat-string-1.6.1.tgz"
  "version" "1.6.1"

"require-directory@^2.1.1":
  "integrity" "sha1-jGStX9MNqxyXbiNE/+f3kqam30I="
  "resolved" "https://registry.npmmirror.com/require-directory/download/require-directory-2.1.1.tgz"
  "version" "2.1.1"

"require-from-string@^2.0.2":
  "integrity" "sha1-iaf92TgmEmcxjq/hT5wy5ZjDaQk="
  "resolved" "https://registry.npmmirror.com/require-from-string/download/require-from-string-2.0.2.tgz"
  "version" "2.0.2"

"require-main-filename@^2.0.0":
  "integrity" "sha512-NKN5kMDylKuldxYLSUfrbo5Tuzh4hd+2E8NPPX02mZtn1VuREQToYe/ZdlJy+J3uCpfaiGF05e7B8W0iXbQHmg=="
  "resolved" "http://castle-npm.cp.hxdi.cn/require-main-filename/-/require-main-filename-2.0.0.tgz"
  "version" "2.0.0"

"requires-port@^1.0.0":
  "integrity" "sha1-kl0mAdOaxIXgkc8NpcbmlNw9yv8="
  "resolved" "https://registry.npmmirror.com/requires-port/download/requires-port-1.0.0.tgz"
  "version" "1.0.0"

"resize-detector@^0.3.0":
  "integrity" "sha1-/klREuGEaVUAqPUeA4nxV3TLHPw="
  "resolved" "https://registry.npmmirror.com/resize-detector/download/resize-detector-0.3.0.tgz"
  "version" "0.3.0"

"resolve-from@^4.0.0":
  "integrity" "sha1-SrzYUq0y3Xuqv+m0DgCjbbXzkuY="
  "resolved" "https://registry.npmmirror.com/resolve-from/download/resolve-from-4.0.0.tgz"
  "version" "4.0.0"

"resolve-url@^0.2.1":
  "integrity" "sha1-LGN/53yJOv0qZj/iGqkIAGjiBSo="
  "resolved" "https://registry.npmmirror.com/resolve-url/download/resolve-url-0.2.1.tgz"
  "version" "0.2.1"

"resolve@^1.1.7", "resolve@^1.10.0", "resolve@^1.14.2", "resolve@^1.22.2":
  "integrity" "sha512-PXNdCiPqDqeUou+w1C2eTQbNfxKSuMxqTCuvlmmMsk1NWHL5fRrhY6Pl0qEYYc6+QqGClco1Qj8XnjPego4wfg=="
  "resolved" "http://castle-npm.cp.hxdi.cn/resolve/-/resolve-1.22.4.tgz"
  "version" "1.22.4"
  dependencies:
    "is-core-module" "^2.13.0"
    "path-parse" "^1.0.7"
    "supports-preserve-symlinks-flag" "^1.0.0"

"restore-cursor@^2.0.0":
  "integrity" "sha1-n37ih/gv0ybU/RYpI9YhKe7g368="
  "resolved" "https://registry.npmmirror.com/restore-cursor/download/restore-cursor-2.0.0.tgz"
  "version" "2.0.0"
  dependencies:
    "onetime" "^2.0.0"
    "signal-exit" "^3.0.2"

"restore-cursor@^3.1.0":
  "integrity" "sha1-OfZ8VLOnpYzqUjbZXPADQjljH34="
  "resolved" "https://registry.npmmirror.com/restore-cursor/download/restore-cursor-3.1.0.tgz"
  "version" "3.1.0"
  dependencies:
    "onetime" "^5.1.0"
    "signal-exit" "^3.0.2"

"ret@~0.1.10":
  "integrity" "sha1-uKSCXVvbH8P29Twrwz+BOIaBx7w="
  "resolved" "https://registry.npmmirror.com/ret/download/ret-0.1.15.tgz"
  "version" "0.1.15"

"retry@^0.13.1":
  "integrity" "sha1-GFsVh6z2eRnWOzVzSeA1N7JIRlg="
  "resolved" "https://registry.npmmirror.com/retry/download/retry-0.13.1.tgz"
  "version" "0.13.1"

"reusify@^1.0.4":
  "integrity" "sha1-kNo4Kx4SbvwCFG6QhFqI2xKSXXY="
  "resolved" "https://registry.npmmirror.com/reusify/download/reusify-1.0.4.tgz"
  "version" "1.0.4"

"rimraf@^3.0.2":
  "integrity" "sha1-8aVAK6YiCtUswSgrrBrjqkn9Bho="
  "resolved" "https://registry.npmmirror.com/rimraf/download/rimraf-3.0.2.tgz"
  "version" "3.0.2"
  dependencies:
    "glob" "^7.1.3"

"run-async@^2.4.0":
  "integrity" "sha512-tvVnVv01b8c1RrA6Ep7JkStj85Guv/YrMcwqYQnwjsAS2cTmmPGBBjAjpCW7RrSodNSoE2/qg9O4bceNvUuDgQ=="
  "resolved" "http://castle-npm.cp.hxdi.cn/run-async/-/run-async-2.4.1.tgz"
  "version" "2.4.1"

"run-parallel@^1.1.9":
  "integrity" "sha1-ZtE2jae9+SHrnZW9GpIp5/IaQ+4="
  "resolved" "https://registry.npmmirror.com/run-parallel/download/run-parallel-1.2.0.tgz"
  "version" "1.2.0"
  dependencies:
    "queue-microtask" "^1.2.2"

"rxjs@^6.6.6":
  "integrity" "sha512-hTdwr+7yYNIT5n4AMYp85KA6yw2Va0FLa3Rguvbpa4W3I5xynaBZo41cM3XM+4Q6fRMj3sBYIR1VAmZMXYJvRQ=="
  "resolved" "http://castle-npm.cp.hxdi.cn/rxjs/-/rxjs-6.6.7.tgz"
  "version" "6.6.7"
  dependencies:
    "tslib" "^1.9.0"

"safe-buffer@^5.0.1", "safe-buffer@^5.1.0", "safe-buffer@>=5.1.0", "safe-buffer@~5.1.0", "safe-buffer@~5.1.1", "safe-buffer@5.1.2":
  "integrity" "sha1-mR7GnSluAxN0fVm9/St0XDX4go0="
  "resolved" "https://registry.npmmirror.com/safe-buffer/download/safe-buffer-5.1.2.tgz"
  "version" "5.1.2"

"safe-buffer@~5.2.0":
  "integrity" "sha1-Hq+fqb2x/dTsdfWPnNtOa3gn7sY="
  "resolved" "https://registry.npmmirror.com/safe-buffer/download/safe-buffer-5.2.1.tgz"
  "version" "5.2.1"

"safe-regex@^1.1.0":
  "integrity" "sha1-QKNmnzsHfR6UPURinhV91IAjvy4="
  "resolved" "https://registry.npmmirror.com/safe-regex/download/safe-regex-1.1.0.tgz"
  "version" "1.1.0"
  dependencies:
    "ret" "~0.1.10"

"safer-buffer@>= 2.1.2 < 3":
  "integrity" "sha1-RPoWGwGHuVSd2Eu5GAL5vYOFzWo="
  "resolved" "https://registry.npmmirror.com/safer-buffer/download/safer-buffer-2.1.2.tgz"
  "version" "2.1.2"

"sass-loader@^12.0.0":
  "integrity" "sha512-7xN+8khDIzym1oL9XyS6zP6Ges+Bo2B2xbPrjdMHEYyV3AQYhd/wXeru++3ODHF0zMjYmVadblSKrPrjEkL8mg=="
  "resolved" "https://registry.npmmirror.com/sass-loader/download/sass-loader-12.4.0.tgz"
  "version" "12.4.0"
  dependencies:
    "klona" "^2.0.4"
    "neo-async" "^2.6.2"

"sass@^1.3.0", "sass@^1.32.7":
  "integrity" "sha512-ONy5bjppoohtNkFJRqdz1gscXamMzN3wQy1YH9qO2FiNpgjLhpz/IPRGg0PpCjyz/pWfCOaNEaiEGCcjOFAjqw=="
  "resolved" "https://registry.npmmirror.com/sass/download/sass-1.45.0.tgz"
  "version" "1.45.0"
  dependencies:
    "chokidar" ">=3.0.0 <4.0.0"
    "immutable" "^4.0.0"
    "source-map-js" ">=0.6.2 <2.0.0"

"schema-utils@^2.0.0", "schema-utils@^2.6.5":
  "integrity" "sha1-HKTzLRskxZDCA7jnpQvw6kzTlNc="
  "resolved" "https://registry.npmmirror.com/schema-utils/download/schema-utils-2.7.1.tgz?cache=0&sync_timestamp=1637075979719&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2Fschema-utils%2Fdownload%2Fschema-utils-2.7.1.tgz"
  "version" "2.7.1"
  dependencies:
    "@types/json-schema" "^7.0.5"
    "ajv" "^6.12.4"
    "ajv-keywords" "^3.5.2"

"schema-utils@^3.0.0":
  "integrity" "sha1-vHTEtraZXB2I92qLd76nIZ4MgoE="
  "resolved" "https://registry.npmmirror.com/schema-utils/download/schema-utils-3.1.1.tgz?cache=0&sync_timestamp=1637075979719&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2Fschema-utils%2Fdownload%2Fschema-utils-3.1.1.tgz"
  "version" "3.1.1"
  dependencies:
    "@types/json-schema" "^7.0.8"
    "ajv" "^6.12.5"
    "ajv-keywords" "^3.5.2"

"schema-utils@^3.1.0":
  "integrity" "sha1-vHTEtraZXB2I92qLd76nIZ4MgoE="
  "resolved" "https://registry.npmmirror.com/schema-utils/download/schema-utils-3.1.1.tgz?cache=0&sync_timestamp=1637075979719&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2Fschema-utils%2Fdownload%2Fschema-utils-3.1.1.tgz"
  "version" "3.1.1"
  dependencies:
    "@types/json-schema" "^7.0.8"
    "ajv" "^6.12.5"
    "ajv-keywords" "^3.5.2"

"schema-utils@^3.1.1":
  "integrity" "sha1-vHTEtraZXB2I92qLd76nIZ4MgoE="
  "resolved" "https://registry.npmmirror.com/schema-utils/download/schema-utils-3.1.1.tgz?cache=0&sync_timestamp=1637075979719&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2Fschema-utils%2Fdownload%2Fschema-utils-3.1.1.tgz"
  "version" "3.1.1"
  dependencies:
    "@types/json-schema" "^7.0.8"
    "ajv" "^6.12.5"
    "ajv-keywords" "^3.5.2"

"schema-utils@^4.0.0":
  "integrity" "sha512-1edyXKgh6XnJsJSQ8mKWXnN/BVaIbFMLpouRUrXgVq7WYne5kw3MW7UPhO44uRXQSIpTSXoJbmrR2X0w9kUTyg=="
  "resolved" "https://registry.npmmirror.com/schema-utils/download/schema-utils-4.0.0.tgz?cache=0&sync_timestamp=1637075979719&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2Fschema-utils%2Fdownload%2Fschema-utils-4.0.0.tgz"
  "version" "4.0.0"
  dependencies:
    "@types/json-schema" "^7.0.9"
    "ajv" "^8.8.0"
    "ajv-formats" "^2.1.1"
    "ajv-keywords" "^5.0.0"

"select-hose@^2.0.0":
  "integrity" "sha1-Yl2GWPhlr0Psliv8N2o3NZpJlMo="
  "resolved" "https://registry.npmmirror.com/select-hose/download/select-hose-2.0.0.tgz"
  "version" "2.0.0"

"selfsigned@^1.10.11":
  "integrity" "sha1-JJKc2Qb+D0S20B+yOZmnOVN6y+k="
  "resolved" "https://registry.npmmirror.com/selfsigned/download/selfsigned-1.10.11.tgz"
  "version" "1.10.11"
  dependencies:
    "node-forge" "^0.10.0"

"semver@^5.5.0":
  "integrity" "sha1-qVT5Ma66UI0we78Gnv8MAclhFvc="
  "resolved" "https://registry.npmmirror.com/semver/download/semver-5.7.1.tgz"
  "version" "5.7.1"

"semver@^6.0.0", "semver@^6.1.1", "semver@^6.1.2", "semver@^6.3.0":
  "integrity" "sha1-7gpkyK9ejO6mdoexM3YeG+y9HT0="
  "resolved" "https://registry.npmmirror.com/semver/download/semver-6.3.0.tgz"
  "version" "6.3.0"

"semver@^7.2.1":
  "integrity" "sha1-C2Ich5NI2JmOSw5L6Us/EuYBjvc="
  "resolved" "https://registry.npmmirror.com/semver/download/semver-7.3.5.tgz"
  "version" "7.3.5"
  dependencies:
    "lru-cache" "^6.0.0"

"semver@^7.3.4":
  "integrity" "sha1-C2Ich5NI2JmOSw5L6Us/EuYBjvc="
  "resolved" "https://registry.npmmirror.com/semver/download/semver-7.3.5.tgz"
  "version" "7.3.5"
  dependencies:
    "lru-cache" "^6.0.0"

"semver@^7.3.5":
  "integrity" "sha1-C2Ich5NI2JmOSw5L6Us/EuYBjvc="
  "resolved" "https://registry.npmmirror.com/semver/download/semver-7.3.5.tgz"
  "version" "7.3.5"
  dependencies:
    "lru-cache" "^6.0.0"

"semver@2 || 3 || 4 || 5":
  "integrity" "sha1-qVT5Ma66UI0we78Gnv8MAclhFvc="
  "resolved" "https://registry.npmmirror.com/semver/download/semver-5.7.1.tgz"
  "version" "5.7.1"

"semver@7.0.0":
  "integrity" "sha1-XzyjV2HkfgWyBsba/yz4FPAxa44="
  "resolved" "https://registry.npmmirror.com/semver/download/semver-7.0.0.tgz"
  "version" "7.0.0"

"send@0.17.1":
  "integrity" "sha1-wdiwWfeQD3Rm3Uk4vcROEd2zdsg="
  "resolved" "https://registry.npmmirror.com/send/download/send-0.17.1.tgz"
  "version" "0.17.1"
  dependencies:
    "debug" "2.6.9"
    "depd" "~1.1.2"
    "destroy" "~1.0.4"
    "encodeurl" "~1.0.2"
    "escape-html" "~1.0.3"
    "etag" "~1.8.1"
    "fresh" "0.5.2"
    "http-errors" "~1.7.2"
    "mime" "1.6.0"
    "ms" "2.1.1"
    "on-finished" "~2.3.0"
    "range-parser" "~1.2.1"
    "statuses" "~1.5.0"

"serialize-javascript@^6.0.0":
  "integrity" "sha1-765diPRdeSQUHai1w6en5mP+/rg="
  "resolved" "https://registry.npmmirror.com/serialize-javascript/download/serialize-javascript-6.0.0.tgz"
  "version" "6.0.0"
  dependencies:
    "randombytes" "^2.1.0"

"serve-index@^1.9.1":
  "integrity" "sha1-03aNabHn2C5c4FD/9bRTvqEqkjk="
  "resolved" "https://registry.npmmirror.com/serve-index/download/serve-index-1.9.1.tgz"
  "version" "1.9.1"
  dependencies:
    "accepts" "~1.3.4"
    "batch" "0.6.1"
    "debug" "2.6.9"
    "escape-html" "~1.0.3"
    "http-errors" "~1.6.2"
    "mime-types" "~2.1.17"
    "parseurl" "~1.3.2"

"serve-static@1.14.1":
  "integrity" "sha1-Zm5jbcTwEPfvKZcKiKZ0MgiYsvk="
  "resolved" "https://registry.npmmirror.com/serve-static/download/serve-static-1.14.1.tgz"
  "version" "1.14.1"
  dependencies:
    "encodeurl" "~1.0.2"
    "escape-html" "~1.0.3"
    "parseurl" "~1.3.3"
    "send" "0.17.1"

"set-blocking@^2.0.0":
  "integrity" "sha1-BF+XgtARrppoA93TgrJDkrPYkPc="
  "resolved" "http://castle-npm.cp.hxdi.cn/set-blocking/-/set-blocking-2.0.0.tgz"
  "version" "2.0.0"

"set-value@^2.0.0", "set-value@^2.0.1":
  "integrity" "sha1-oY1AUw5vB95CKMfe/kInr4ytAFs="
  "resolved" "https://registry.npmmirror.com/set-value/download/set-value-2.0.1.tgz"
  "version" "2.0.1"
  dependencies:
    "extend-shallow" "^2.0.1"
    "is-extendable" "^0.1.1"
    "is-plain-object" "^2.0.3"
    "split-string" "^3.0.1"

"setprototypeof@1.1.0":
  "integrity" "sha1-0L2FU2iHtv58DYGMuWLZ2RxU5lY="
  "resolved" "https://registry.npmmirror.com/setprototypeof/download/setprototypeof-1.1.0.tgz"
  "version" "1.1.0"

"setprototypeof@1.1.1":
  "integrity" "sha1-fpWsskqpL1iF4KvvW6ExMw1K5oM="
  "resolved" "https://registry.npmmirror.com/setprototypeof/download/setprototypeof-1.1.1.tgz"
  "version" "1.1.1"

"shallow-clone@^3.0.0":
  "integrity" "sha1-jymBrZJTH1UDWwH7IwdppA4C76M="
  "resolved" "https://registry.npmmirror.com/shallow-clone/download/shallow-clone-3.0.1.tgz"
  "version" "3.0.1"
  dependencies:
    "kind-of" "^6.0.2"

"shebang-command@^1.2.0":
  "integrity" "sha1-RKrGW2lbAzmJaMOfNj/uXer98eo="
  "resolved" "https://registry.npmmirror.com/shebang-command/download/shebang-command-1.2.0.tgz"
  "version" "1.2.0"
  dependencies:
    "shebang-regex" "^1.0.0"

"shebang-command@^2.0.0":
  "integrity" "sha1-zNCvT4g1+9wmW4JGGq8MNmY/NOo="
  "resolved" "https://registry.npmmirror.com/shebang-command/download/shebang-command-2.0.0.tgz"
  "version" "2.0.0"
  dependencies:
    "shebang-regex" "^3.0.0"

"shebang-regex@^1.0.0":
  "integrity" "sha1-2kL0l0DAtC2yypcoVxyxkMmO/qM="
  "resolved" "https://registry.npmmirror.com/shebang-regex/download/shebang-regex-1.0.0.tgz?cache=0&sync_timestamp=1632753420312&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2Fshebang-regex%2Fdownload%2Fshebang-regex-1.0.0.tgz"
  "version" "1.0.0"

"shebang-regex@^3.0.0":
  "integrity" "sha1-rhbxZE2HPsrYQ7AwexQzYtTEIXI="
  "resolved" "https://registry.npmmirror.com/shebang-regex/download/shebang-regex-3.0.0.tgz?cache=0&sync_timestamp=1632753420312&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2Fshebang-regex%2Fdownload%2Fshebang-regex-3.0.0.tgz"
  "version" "3.0.0"

"shell-quote@^1.6.1":
  "integrity" "sha1-qkDtrBcERbmkMeF7tiwLiBucQSM="
  "resolved" "https://registry.npmmirror.com/shell-quote/download/shell-quote-1.7.3.tgz"
  "version" "1.7.3"

"side-channel@^1.0.4":
  "integrity" "sha1-785cj9wQTudRslxY1CkAEfpeos8="
  "resolved" "https://registry.npmmirror.com/side-channel/download/side-channel-1.0.4.tgz"
  "version" "1.0.4"
  dependencies:
    "call-bind" "^1.0.0"
    "get-intrinsic" "^1.0.2"
    "object-inspect" "^1.9.0"

"signal-exit@^3.0.0", "signal-exit@^3.0.2", "signal-exit@^3.0.3":
  "integrity" "sha512-sDl4qMFpijcGw22U5w63KmD3cZJfBuFlVNbVMKje2keoKML7X2UzWbc4XrmEbDwg0NXJc3yv4/ox7b+JWb57kQ=="
  "resolved" "https://registry.npmmirror.com/signal-exit/download/signal-exit-3.0.6.tgz"
  "version" "3.0.6"

"sirv@^1.0.7":
  "integrity" "sha512-JuLThK3TnZG1TAKDwNIqNq6QA2afLOCcm+iE8D1Kj3GA40pSPsxQjjJl0J8X3tsR7T+CP1GavpzLwYkgVLWrZQ=="
  "resolved" "https://registry.npmmirror.com/sirv/download/sirv-1.0.19.tgz"
  "version" "1.0.19"
  dependencies:
    "@polka/url" "^1.0.0-next.20"
    "mrmime" "^1.0.0"
    "totalist" "^1.0.0"

"slash@^3.0.0":
  "integrity" "sha1-ZTm+hwwWWtvVJAIg2+Nh8bxNRjQ="
  "resolved" "https://registry.npmmirror.com/slash/download/slash-3.0.0.tgz?cache=0&sync_timestamp=1632753426618&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2Fslash%2Fdownload%2Fslash-3.0.0.tgz"
  "version" "3.0.0"

"slice-ansi@^4.0.0":
  "integrity" "sha1-UA6N0P1VsFgVCGJVsxla3ypF/ms="
  "resolved" "https://registry.npmmirror.com/slice-ansi/download/slice-ansi-4.0.0.tgz?cache=0&sync_timestamp=1632753426896&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2Fslice-ansi%2Fdownload%2Fslice-ansi-4.0.0.tgz"
  "version" "4.0.0"
  dependencies:
    "ansi-styles" "^4.0.0"
    "astral-regex" "^2.0.0"
    "is-fullwidth-code-point" "^3.0.0"

"snapdragon-node@^2.0.1":
  "integrity" "sha1-bBdfhv8UvbByRWPo88GwIaKGhTs="
  "resolved" "https://registry.npmmirror.com/snapdragon-node/download/snapdragon-node-2.1.1.tgz"
  "version" "2.1.1"
  dependencies:
    "define-property" "^1.0.0"
    "isobject" "^3.0.0"
    "snapdragon-util" "^3.0.1"

"snapdragon-util@^3.0.1":
  "integrity" "sha1-+VZHlIbyrNeXAGk/b3uAXkWrVuI="
  "resolved" "https://registry.npmmirror.com/snapdragon-util/download/snapdragon-util-3.0.1.tgz"
  "version" "3.0.1"
  dependencies:
    "kind-of" "^3.2.0"

"snapdragon@^0.8.1":
  "integrity" "sha1-ZJIufFZbDhQgS6GqfWlkJ40lGC0="
  "resolved" "https://registry.npmmirror.com/snapdragon/download/snapdragon-0.8.2.tgz"
  "version" "0.8.2"
  dependencies:
    "base" "^0.11.1"
    "debug" "^2.2.0"
    "define-property" "^0.2.5"
    "extend-shallow" "^2.0.1"
    "map-cache" "^0.2.2"
    "source-map" "^0.5.6"
    "source-map-resolve" "^0.5.0"
    "use" "^3.1.0"

"sockjs@^0.3.21":
  "integrity" "sha512-GJgLTZ7vYb/JtPSSZ10hsOYIvEYsjbNU+zPdIHcUaWVNUEPivzxku31865sSSud0Da0W4lEeOPlmw93zLQchuQ=="
  "resolved" "https://registry.npmmirror.com/sockjs/download/sockjs-0.3.24.tgz"
  "version" "0.3.24"
  dependencies:
    "faye-websocket" "^0.11.3"
    "uuid" "^8.3.2"
    "websocket-driver" "^0.7.4"

"source-map-js@^1.0.2", "source-map-js@>=0.6.2 <2.0.0":
  "integrity" "sha512-R0XvVJ9WusLiqTCEiGCmICCMplcCkIwwR11mOSD9CR5u+IXYdiseeEuXCVAjS54zqwkLcPNnmU4OeJ6tUrWhDw=="
  "resolved" "http://castle-npm.cp.hxdi.cn/source-map-js/-/source-map-js-1.0.2.tgz"
  "version" "1.0.2"

"source-map-resolve@^0.5.0":
  "integrity" "sha1-GQhmvs51U+H48mei7oLGBrVQmho="
  "resolved" "https://registry.npmmirror.com/source-map-resolve/download/source-map-resolve-0.5.3.tgz"
  "version" "0.5.3"
  dependencies:
    "atob" "^2.1.2"
    "decode-uri-component" "^0.2.0"
    "resolve-url" "^0.2.1"
    "source-map-url" "^0.4.0"
    "urix" "^0.1.0"

"source-map-support@~0.5.20":
  "integrity" "sha512-uBHU3L3czsIyYXKX88fdrGovxdSCoTGDRZ6SYXtSRxLZUzHg5P/66Ht6uoUlHu9EZod+inXhKo3qQgwXUT/y1w=="
  "resolved" "https://registry.npmmirror.com/source-map-support/download/source-map-support-0.5.21.tgz?cache=0&sync_timestamp=1637320266739&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2Fsource-map-support%2Fdownload%2Fsource-map-support-0.5.21.tgz"
  "version" "0.5.21"
  dependencies:
    "buffer-from" "^1.0.0"
    "source-map" "^0.6.0"

"source-map-url@^0.4.0":
  "integrity" "sha1-CvZmBadFpaL5HPG7+KevvCg97FY="
  "resolved" "https://registry.npmmirror.com/source-map-url/download/source-map-url-0.4.1.tgz"
  "version" "0.4.1"

"source-map@^0.5.0", "source-map@^0.5.6":
  "integrity" "sha1-igOdLRAh0i0eoUyA2OpGi6LvP8w="
  "resolved" "https://registry.npmmirror.com/source-map/download/source-map-0.5.7.tgz"
  "version" "0.5.7"

"source-map@^0.6.0":
  "integrity" "sha1-dHIq8y6WFOnCh6jQu95IteLxomM="
  "resolved" "https://registry.npmmirror.com/source-map/download/source-map-0.6.1.tgz"
  "version" "0.6.1"

"source-map@^0.6.1", "source-map@~0.6.1":
  "integrity" "sha1-dHIq8y6WFOnCh6jQu95IteLxomM="
  "resolved" "https://registry.npmmirror.com/source-map/download/source-map-0.6.1.tgz"
  "version" "0.6.1"

"source-map@~0.6.0":
  "integrity" "sha1-dHIq8y6WFOnCh6jQu95IteLxomM="
  "resolved" "https://registry.npmmirror.com/source-map/download/source-map-0.6.1.tgz"
  "version" "0.6.1"

"source-map@~0.7.2":
  "integrity" "sha1-UwL4FpAxc1ImVECS5kmB91F1A4M="
  "resolved" "https://registry.npmmirror.com/source-map/download/source-map-0.7.3.tgz"
  "version" "0.7.3"

"sourcemap-codec@^1.4.4":
  "integrity" "sha1-6oBL2UhXQC5pktBaOO8a41qatMQ="
  "resolved" "https://registry.npmmirror.com/sourcemap-codec/download/sourcemap-codec-1.4.8.tgz"
  "version" "1.4.8"

"spdx-correct@^3.0.0":
  "integrity" "sha1-3s6BrJweZxPl99G28X1Gj6U9iak="
  "resolved" "https://registry.npmmirror.com/spdx-correct/download/spdx-correct-3.1.1.tgz"
  "version" "3.1.1"
  dependencies:
    "spdx-expression-parse" "^3.0.0"
    "spdx-license-ids" "^3.0.0"

"spdx-exceptions@^2.1.0":
  "integrity" "sha1-PyjOGnegA3JoPq3kpDMYNSeiFj0="
  "resolved" "https://registry.npmmirror.com/spdx-exceptions/download/spdx-exceptions-2.3.0.tgz"
  "version" "2.3.0"

"spdx-expression-parse@^3.0.0":
  "integrity" "sha1-z3D1BILu/cmOPOCmgz5KU87rpnk="
  "resolved" "https://registry.npmmirror.com/spdx-expression-parse/download/spdx-expression-parse-3.0.1.tgz"
  "version" "3.0.1"
  dependencies:
    "spdx-exceptions" "^2.1.0"
    "spdx-license-ids" "^3.0.0"

"spdx-license-ids@^3.0.0":
  "integrity" "sha512-Ctl2BrFiM0X3MANYgj3CkygxhRmr9mi6xhejbdO960nF6EDJApTYpn0BQnDKlnNBULKiCN1n3w9EBkHK8ZWg+g=="
  "resolved" "https://registry.npmmirror.com/spdx-license-ids/download/spdx-license-ids-3.0.11.tgz?cache=0&sync_timestamp=1636978448866&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2Fspdx-license-ids%2Fdownload%2Fspdx-license-ids-3.0.11.tgz"
  "version" "3.0.11"

"spdy-transport@^3.0.0":
  "integrity" "sha1-ANSGOmQArXXfkzYaFghgXl3NzzE="
  "resolved" "https://registry.npmmirror.com/spdy-transport/download/spdy-transport-3.0.0.tgz"
  "version" "3.0.0"
  dependencies:
    "debug" "^4.1.0"
    "detect-node" "^2.0.4"
    "hpack.js" "^2.1.6"
    "obuf" "^1.1.2"
    "readable-stream" "^3.0.6"
    "wbuf" "^1.7.3"

"spdy@^4.0.2":
  "integrity" "sha1-t09GYgOj7aRSwCSSuR+56EonZ3s="
  "resolved" "https://registry.npmmirror.com/spdy/download/spdy-4.0.2.tgz"
  "version" "4.0.2"
  dependencies:
    "debug" "^4.1.0"
    "handle-thing" "^2.0.0"
    "http-deceiver" "^1.2.7"
    "select-hose" "^2.0.0"
    "spdy-transport" "^3.0.0"

"split-string@^3.0.1", "split-string@^3.0.2":
  "integrity" "sha1-fLCd2jqGWFcFxks5pkZgOGguj+I="
  "resolved" "https://registry.npmmirror.com/split-string/download/split-string-3.1.0.tgz"
  "version" "3.1.0"
  dependencies:
    "extend-shallow" "^3.0.0"

"sprintf-js@~1.0.2":
  "integrity" "sha1-BOaSb2YolTVPPdAVIDYzuFcpfiw="
  "resolved" "https://registry.npmmirror.com/sprintf-js/download/sprintf-js-1.0.3.tgz"
  "version" "1.0.3"

"ssri@^8.0.1":
  "integrity" "sha1-Y45OQ54v+9LNKJd21cpFfE9Roq8="
  "resolved" "https://registry.npmmirror.com/ssri/download/ssri-8.0.1.tgz"
  "version" "8.0.1"
  dependencies:
    "minipass" "^3.1.1"

"stable@^0.1.8":
  "integrity" "sha1-g26zyDgv4pNv6vVEYxAXzn1Ho88="
  "resolved" "https://registry.npmmirror.com/stable/download/stable-0.1.8.tgz"
  "version" "0.1.8"

"stackframe@^1.1.1":
  "integrity" "sha1-UkKUktY8YuuYmATBFVLj0i53kwM="
  "resolved" "https://registry.npmmirror.com/stackframe/download/stackframe-1.2.0.tgz"
  "version" "1.2.0"

"static-extend@^0.1.1":
  "integrity" "sha1-YICcOcv/VTNyJv1eC1IPNB8ftcY="
  "resolved" "https://registry.npmmirror.com/static-extend/download/static-extend-0.1.2.tgz"
  "version" "0.1.2"
  dependencies:
    "define-property" "^0.2.5"
    "object-copy" "^0.1.0"

"statuses@>= 1.4.0 < 2", "statuses@>= 1.5.0 < 2", "statuses@~1.5.0":
  "integrity" "sha1-Fhx9rBd2Wf2YEfQ3cfqZOBR4Yow="
  "resolved" "https://registry.npmmirror.com/statuses/download/statuses-1.5.0.tgz"
  "version" "1.5.0"

"strict-uri-encode@^1.0.0":
  "integrity" "sha1-J5siXfHVgrH1TmWt3UNS4Y+qBxM="
  "resolved" "https://registry.npmmirror.com/strict-uri-encode/download/strict-uri-encode-1.1.0.tgz"
  "version" "1.1.0"

"string_decoder@^1.1.1":
  "integrity" "sha1-QvEUWUpGzxqOMLCoT1bHjD7awh4="
  "resolved" "https://registry.npmmirror.com/string_decoder/download/string_decoder-1.3.0.tgz"
  "version" "1.3.0"
  dependencies:
    "safe-buffer" "~5.2.0"

"string_decoder@~1.1.1":
  "integrity" "sha1-nPFhG6YmhdcDCunkujQUnDrwP8g="
  "resolved" "https://registry.npmmirror.com/string_decoder/download/string_decoder-1.1.1.tgz"
  "version" "1.1.1"
  dependencies:
    "safe-buffer" "~5.1.0"

"string-width@^2.1.1":
  "integrity" "sha1-q5Pyeo3BPSjKyBXEYhQ6bZASrp4="
  "resolved" "https://registry.npmmirror.com/string-width/download/string-width-2.1.1.tgz"
  "version" "2.1.1"
  dependencies:
    "is-fullwidth-code-point" "^2.0.0"
    "strip-ansi" "^4.0.0"

"string-width@^4.1.0", "string-width@^4.2.0", "string-width@^4.2.3":
  "integrity" "sha1-JpxxF9J7Ba0uU2gwqOyJXvnG0BA="
  "resolved" "https://registry.npmmirror.com/string-width/download/string-width-4.2.3.tgz"
  "version" "4.2.3"
  dependencies:
    "emoji-regex" "^8.0.0"
    "is-fullwidth-code-point" "^3.0.0"
    "strip-ansi" "^6.0.1"

"strip-ansi@^3.0.0":
  "integrity" "sha1-ajhfuIU9lS1f8F0Oiq+UJ43GPc8="
  "resolved" "https://registry.npmmirror.com/strip-ansi/download/strip-ansi-3.0.1.tgz"
  "version" "3.0.1"
  dependencies:
    "ansi-regex" "^2.0.0"

"strip-ansi@^4.0.0":
  "integrity" "sha1-qEeQIusaw2iocTibY1JixQXuNo8="
  "resolved" "https://registry.npmmirror.com/strip-ansi/download/strip-ansi-4.0.0.tgz"
  "version" "4.0.0"
  dependencies:
    "ansi-regex" "^3.0.0"

"strip-ansi@^6.0.0", "strip-ansi@^6.0.1":
  "integrity" "sha1-nibGPTD1NEPpSJSVshBdN7Z6hdk="
  "resolved" "https://registry.npmmirror.com/strip-ansi/download/strip-ansi-6.0.1.tgz"
  "version" "6.0.1"
  dependencies:
    "ansi-regex" "^5.0.1"

"strip-ansi@^7.0.0":
  "integrity" "sha1-YXQKCM42th5Q5lZT8HBg0ACXX7I="
  "resolved" "https://registry.npmmirror.com/strip-ansi/download/strip-ansi-7.0.1.tgz"
  "version" "7.0.1"
  dependencies:
    "ansi-regex" "^6.0.1"

"strip-eof@^1.0.0":
  "integrity" "sha1-u0P/VZim6wXYm1n80SnJgzE2Br8="
  "resolved" "https://registry.npmmirror.com/strip-eof/download/strip-eof-1.0.0.tgz"
  "version" "1.0.0"

"strip-final-newline@^2.0.0":
  "integrity" "sha1-ibhS+y/L6Tb29LMYevsKEsGrWK0="
  "resolved" "https://registry.npmmirror.com/strip-final-newline/download/strip-final-newline-2.0.0.tgz"
  "version" "2.0.0"

"strip-indent@^2.0.0":
  "integrity" "sha1-XvjbKV0B5u1sv3qrlpmNeCJSe2g="
  "resolved" "https://registry.npmmirror.com/strip-indent/download/strip-indent-2.0.0.tgz"
  "version" "2.0.0"

"strip-json-comments@^3.1.0", "strip-json-comments@^3.1.1":
  "integrity" "sha1-MfEoGzgyYwQ0gxwxDAHMzajL4AY="
  "resolved" "https://registry.npmmirror.com/strip-json-comments/download/strip-json-comments-3.1.1.tgz"
  "version" "3.1.1"

"stylehacks@^5.0.1":
  "integrity" "sha1-Mj7FVBmFIJhoBjiMf9rrw40sBvs="
  "resolved" "https://registry.npmmirror.com/stylehacks/download/stylehacks-5.0.1.tgz"
  "version" "5.0.1"
  dependencies:
    "browserslist" "^4.16.0"
    "postcss-selector-parser" "^6.0.4"

"sucrase@^3.32.0":
  "integrity" "sha512-70/LQEZ07TEcxiU2dz51FKaE6hCTWC6vr7FOk3Gr0U60C3shtAN+H+BFr9XlYe5xqf3RA8nrc+VIwzCfnxuXJw=="
  "resolved" "http://castle-npm.cp.hxdi.cn/sucrase/-/sucrase-3.34.0.tgz"
  "version" "3.34.0"
  dependencies:
    "@jridgewell/gen-mapping" "^0.3.2"
    "commander" "^4.0.0"
    "glob" "7.1.6"
    "lines-and-columns" "^1.1.6"
    "mz" "^2.7.0"
    "pirates" "^4.0.1"
    "ts-interface-checker" "^0.1.9"

"supports-color@^2.0.0":
  "integrity" "sha1-U10EXOa2Nj+kARcIRimZXp3zJMc="
  "resolved" "https://registry.npmmirror.com/supports-color/download/supports-color-2.0.0.tgz"
  "version" "2.0.0"

"supports-color@^3.2.3":
  "integrity" "sha1-ZawFBLOVQXHYpklGsq48u4pfVPY="
  "resolved" "https://registry.npmmirror.com/supports-color/download/supports-color-3.2.3.tgz"
  "version" "3.2.3"
  dependencies:
    "has-flag" "^1.0.0"

"supports-color@^5.3.0":
  "integrity" "sha1-4uaaRKyHcveKHsCzW2id9lMO/I8="
  "resolved" "https://registry.npmmirror.com/supports-color/download/supports-color-5.5.0.tgz"
  "version" "5.5.0"
  dependencies:
    "has-flag" "^3.0.0"

"supports-color@^7.1.0":
  "integrity" "sha1-G33NyzK4E4gBs+R4umpRyqiWSNo="
  "resolved" "https://registry.npmmirror.com/supports-color/download/supports-color-7.2.0.tgz"
  "version" "7.2.0"
  dependencies:
    "has-flag" "^4.0.0"

"supports-color@^8.0.0":
  "integrity" "sha1-zW/BfihQDP9WwbhsCn/UpUpzAFw="
  "resolved" "https://registry.npmmirror.com/supports-color/download/supports-color-8.1.1.tgz"
  "version" "8.1.1"
  dependencies:
    "has-flag" "^4.0.0"

"supports-preserve-symlinks-flag@^1.0.0":
  "integrity" "sha512-ot0WnXS9fgdkgIcePe6RHNk1WA8+muPa6cSjeR3V8K27q9BB1rTE3R1p7Hv0z1ZyAc8s6Vvv8DIyWf681MAt0w=="
  "resolved" "http://castle-npm.cp.hxdi.cn/supports-preserve-symlinks-flag/-/supports-preserve-symlinks-flag-1.0.0.tgz"
  "version" "1.0.0"

"svg-baker-runtime@^1.4.7":
  "integrity" "sha1-9HIGN/W2IC7vY3jYHx/q0IFfik4="
  "resolved" "https://registry.npmmirror.com/svg-baker-runtime/download/svg-baker-runtime-1.4.7.tgz"
  "version" "1.4.7"
  dependencies:
    "deepmerge" "1.3.2"
    "mitt" "1.1.2"
    "svg-baker" "^1.7.0"

"svg-baker@^1.5.0", "svg-baker@^1.7.0":
  "integrity" "sha1-g2f3jYdVUMUv5HVvcwPVxdfC6ac="
  "resolved" "https://registry.npmmirror.com/svg-baker/download/svg-baker-1.7.0.tgz"
  "version" "1.7.0"
  dependencies:
    "bluebird" "^3.5.0"
    "clone" "^2.1.1"
    "he" "^1.1.1"
    "image-size" "^0.5.1"
    "loader-utils" "^1.1.0"
    "merge-options" "1.0.1"
    "micromatch" "3.1.0"
    "postcss" "^5.2.17"
    "postcss-prefix-selector" "^1.6.0"
    "posthtml-rename-id" "^1.0"
    "posthtml-svg-mode" "^1.0.3"
    "query-string" "^4.3.2"
    "traverse" "^0.6.6"

"svg-sprite-loader@^6.0.10":
  "integrity" "sha1-pNYM7j10IyosF9Mcc6IAgpX2EiA="
  "resolved" "https://registry.npmmirror.com/svg-sprite-loader/download/svg-sprite-loader-6.0.11.tgz"
  "version" "6.0.11"
  dependencies:
    "bluebird" "^3.5.0"
    "deepmerge" "1.3.2"
    "domready" "1.0.8"
    "escape-string-regexp" "1.0.5"
    "loader-utils" "^1.1.0"
    "svg-baker" "^1.5.0"
    "svg-baker-runtime" "^1.4.7"
    "url-slug" "2.0.0"

"svg-tags@^1.0.0":
  "integrity" "sha1-WPcc7jvVGbWdSyqEO2x95krAR2Q="
  "resolved" "https://registry.npmmirror.com/svg-tags/download/svg-tags-1.0.0.tgz"
  "version" "1.0.0"

"svgo@^2.7.0":
  "integrity" "sha1-T/gMzmcQ3CeV8MfHQQHmdkz8zSQ="
  "resolved" "https://registry.npmmirror.com/svgo/download/svgo-2.8.0.tgz"
  "version" "2.8.0"
  dependencies:
    "@trysound/sax" "0.2.0"
    "commander" "^7.2.0"
    "css-select" "^4.1.3"
    "css-tree" "^1.1.3"
    "csso" "^4.2.0"
    "picocolors" "^1.0.0"
    "stable" "^0.1.8"

"table@^6.0.9":
  "integrity" "sha512-LFNeryOqiQHqCVKzhkymKwt6ozeRhlm8IL1mE8rNUurkir4heF6PzMyRgaTa4tlyPTGGgXuvVOF/OLWiH09Lqw=="
  "resolved" "https://registry.npmmirror.com/table/download/table-6.7.5.tgz"
  "version" "6.7.5"
  dependencies:
    "ajv" "^8.0.1"
    "lodash.truncate" "^4.4.2"
    "slice-ansi" "^4.0.0"
    "string-width" "^4.2.3"
    "strip-ansi" "^6.0.1"

"tailwindcss@^3.3.3":
  "integrity" "sha512-A0KgSkef7eE4Mf+nKJ83i75TMyq8HqY3qmFIJSWy8bNt0v1lG7jUcpGpoTFxAwYcWOphcTBLPPJg+bDfhDf52w=="
  "resolved" "http://castle-npm.cp.hxdi.cn/tailwindcss/-/tailwindcss-3.3.3.tgz"
  "version" "3.3.3"
  dependencies:
    "@alloc/quick-lru" "^5.2.0"
    "arg" "^5.0.2"
    "chokidar" "^3.5.3"
    "didyoumean" "^1.2.2"
    "dlv" "^1.1.3"
    "fast-glob" "^3.2.12"
    "glob-parent" "^6.0.2"
    "is-glob" "^4.0.3"
    "jiti" "^1.18.2"
    "lilconfig" "^2.1.0"
    "micromatch" "^4.0.5"
    "normalize-path" "^3.0.0"
    "object-hash" "^3.0.0"
    "picocolors" "^1.0.0"
    "postcss" "^8.4.23"
    "postcss-import" "^15.1.0"
    "postcss-js" "^4.0.1"
    "postcss-load-config" "^4.0.1"
    "postcss-nested" "^6.0.1"
    "postcss-selector-parser" "^6.0.11"
    "resolve" "^1.22.2"
    "sucrase" "^3.32.0"

"tapable@^2.0.0", "tapable@^2.1.1", "tapable@^2.2.0":
  "integrity" "sha1-GWenPvQGCoLxKrlq+G1S/bdu7KA="
  "resolved" "https://registry.npmmirror.com/tapable/download/tapable-2.2.1.tgz"
  "version" "2.2.1"

"terser-webpack-plugin@^5.1.1", "terser-webpack-plugin@^5.1.3":
  "integrity" "sha512-3luOVHku5l0QBeYS8r4CdHYWEGMmIj3H1U64jgkdZzECcSOJAyJ9TjuqcQZvw1Y+4AOBN9SeYJPJmFn2cM4/2g=="
  "resolved" "https://registry.npmmirror.com/terser-webpack-plugin/download/terser-webpack-plugin-5.2.5.tgz?cache=0&sync_timestamp=1636385947065&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2Fterser-webpack-plugin%2Fdownload%2Fterser-webpack-plugin-5.2.5.tgz"
  "version" "5.2.5"
  dependencies:
    "jest-worker" "^27.0.6"
    "schema-utils" "^3.1.1"
    "serialize-javascript" "^6.0.0"
    "source-map" "^0.6.1"
    "terser" "^5.7.2"

"terser@^5.10.0", "terser@^5.7.2":
  "integrity" "sha512-AMmF99DMfEDiRJfxfY5jj5wNH/bYO09cniSqhfoyxc8sFoYIgkJy86G04UoZU5VjlpnplVu0K6Tx6E9b5+DlHA=="
  "resolved" "https://registry.npmmirror.com/terser/download/terser-5.10.0.tgz?cache=0&sync_timestamp=1636988219940&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2Fterser%2Fdownload%2Fterser-5.10.0.tgz"
  "version" "5.10.0"
  dependencies:
    "commander" "^2.20.0"
    "source-map" "~0.7.2"
    "source-map-support" "~0.5.20"

"text-table@^0.2.0":
  "integrity" "sha1-f17oI66AUgfACvLfSoTsP8+lcLQ="
  "resolved" "https://registry.npmmirror.com/text-table/download/text-table-0.2.0.tgz"
  "version" "0.2.0"

"thenify-all@^1.0.0":
  "integrity" "sha1-GhkY1ALY/D+Y+/I02wvMjMEOlyY="
  "resolved" "https://registry.npmmirror.com/thenify-all/download/thenify-all-1.6.0.tgz"
  "version" "1.6.0"
  dependencies:
    "thenify" ">= 3.1.0 < 4"

"thenify@>= 3.1.0 < 4":
  "integrity" "sha1-iTLmhqQGYDigFt2eLKRq3Zg4qV8="
  "resolved" "https://registry.npmmirror.com/thenify/download/thenify-3.3.1.tgz"
  "version" "3.3.1"
  dependencies:
    "any-promise" "^1.0.0"

"thread-loader@^3.0.0":
  "integrity" "sha1-w5LkwCQfvIBDDraA5IhoGbUEoxs="
  "resolved" "https://registry.npmmirror.com/thread-loader/download/thread-loader-3.0.4.tgz"
  "version" "3.0.4"
  dependencies:
    "json-parse-better-errors" "^1.0.2"
    "loader-runner" "^4.1.0"
    "loader-utils" "^2.0.0"
    "neo-async" "^2.6.2"
    "schema-utils" "^3.0.0"

"through@^2.3.6":
  "integrity" "sha512-w89qg7PI8wAdvX60bMDP+bFoD5Dvhm9oLheFp5O4a2QF0cSBGsBX4qZmadPMvVqlLJBBci+WqGGOAPvcDeNSVg=="
  "resolved" "http://castle-npm.cp.hxdi.cn/through/-/through-2.3.8.tgz"
  "version" "2.3.8"

"thunky@^1.0.2":
  "integrity" "sha1-Wrr3FKlAXbBQRzK7zNLO3Z75U30="
  "resolved" "https://registry.npmmirror.com/thunky/download/thunky-1.1.0.tgz"
  "version" "1.1.0"

"timsort@^0.3.0":
  "integrity" "sha1-QFQRqOfmM5/mTbmiNN4R3DHgK9Q="
  "resolved" "https://registry.npmmirror.com/timsort/download/timsort-0.3.0.tgz"
  "version" "0.3.0"

"tmp@^0.0.33":
  "integrity" "sha512-jRCJlojKnZ3addtTOjdIqoRuPEKBvNXcGYqzO6zWZX8KfKEpnGY5jfggJQ3EjKuu8D4bJRr0y+cYJFmYbImXGw=="
  "resolved" "http://castle-npm.cp.hxdi.cn/tmp/-/tmp-0.0.33.tgz"
  "version" "0.0.33"
  dependencies:
    "os-tmpdir" "~1.0.2"

"to-fast-properties@^2.0.0":
  "integrity" "sha1-3F5pjL0HkmW8c+A3doGk5Og/YW4="
  "resolved" "https://registry.npmmirror.com/to-fast-properties/download/to-fast-properties-2.0.0.tgz"
  "version" "2.0.0"

"to-object-path@^0.3.0":
  "integrity" "sha1-KXWIt7Dn4KwI4E5nL4XB9JmeF68="
  "resolved" "https://registry.npmmirror.com/to-object-path/download/to-object-path-0.3.0.tgz"
  "version" "0.3.0"
  dependencies:
    "kind-of" "^3.0.2"

"to-regex-range@^2.1.0":
  "integrity" "sha1-fIDBe53+vlmeJzZ+DU3VWQFB2zg="
  "resolved" "https://registry.npmmirror.com/to-regex-range/download/to-regex-range-2.1.1.tgz"
  "version" "2.1.1"
  dependencies:
    "is-number" "^3.0.0"
    "repeat-string" "^1.6.1"

"to-regex-range@^5.0.1":
  "integrity" "sha1-FkjESq58jZiKMmAY7XL1tN0DkuQ="
  "resolved" "https://registry.npmmirror.com/to-regex-range/download/to-regex-range-5.0.1.tgz"
  "version" "5.0.1"
  dependencies:
    "is-number" "^7.0.0"

"to-regex@^3.0.1":
  "integrity" "sha1-E8/dmzNlUvMLUfM6iuG0Knp1mc4="
  "resolved" "https://registry.npmmirror.com/to-regex/download/to-regex-3.0.2.tgz"
  "version" "3.0.2"
  dependencies:
    "define-property" "^2.0.2"
    "extend-shallow" "^3.0.2"
    "regex-not" "^1.0.2"
    "safe-regex" "^1.1.0"

"toidentifier@1.0.0":
  "integrity" "sha1-fhvjRw8ed5SLxD2Uo8j013UrpVM="
  "resolved" "https://registry.npmmirror.com/toidentifier/download/toidentifier-1.0.0.tgz?cache=0&sync_timestamp=1636938425151&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2Ftoidentifier%2Fdownload%2Ftoidentifier-1.0.0.tgz"
  "version" "1.0.0"

"totalist@^1.0.0":
  "integrity" "sha1-pNZaPlRlF3AePlw3pHpwrJf+Vt8="
  "resolved" "https://registry.npmmirror.com/totalist/download/totalist-1.1.0.tgz"
  "version" "1.1.0"

"tr46@~0.0.3":
  "integrity" "sha1-gYT9NH2snNwYWZLzpmIuFLnZq2o="
  "resolved" "https://registry.npmmirror.com/tr46/download/tr46-0.0.3.tgz"
  "version" "0.0.3"

"traverse@^0.6.6":
  "integrity" "sha1-y99WD9e5r2MlAv7UD5GMFX6pcTc="
  "resolved" "https://registry.npmmirror.com/traverse/download/traverse-0.6.6.tgz"
  "version" "0.6.6"

"ts-interface-checker@^0.1.9":
  "integrity" "sha512-Y/arvbn+rrz3JCKl9C4kVNfTfSm2/mEp5FSz5EsZSANGPSlQrpRI5M4PKF+mJnE52jOO90PnPSc3Ur3bTQw0gA=="
  "resolved" "http://castle-npm.cp.hxdi.cn/ts-interface-checker/-/ts-interface-checker-0.1.13.tgz"
  "version" "0.1.13"

"tslib@^1.9.0":
  "integrity" "sha512-Xni35NKzjgMrwevysHTCArtLDpPvye8zV/0E4EyYn43P7/7qvQwPh9BGkHewbMulVntbigmcT7rdX3BNo9wRJg=="
  "resolved" "http://castle-npm.cp.hxdi.cn/tslib/-/tslib-1.14.1.tgz"
  "version" "1.14.1"

"tslib@^2.0.3", "tslib@2.3.0":
  "integrity" "sha1-gDuM2rPhK6WBpMpByIObuw2ssJ4="
  "resolved" "https://registry.npmmirror.com/tslib/download/tslib-2.3.0.tgz?cache=0&sync_timestamp=1632753476207&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2Ftslib%2Fdownload%2Ftslib-2.3.0.tgz"
  "version" "2.3.0"

"type-check@^0.4.0", "type-check@~0.4.0":
  "integrity" "sha1-B7ggO/pwVsBlcFDjzNLDdzC6uPE="
  "resolved" "https://registry.npmmirror.com/type-check/download/type-check-0.4.0.tgz"
  "version" "0.4.0"
  dependencies:
    "prelude-ls" "^1.2.1"

"type-fest@^0.20.2":
  "integrity" "sha1-G/IH9LKPkVg2ZstfvTJ4hzAc1fQ="
  "resolved" "https://registry.npmmirror.com/type-fest/download/type-fest-0.20.2.tgz"
  "version" "0.20.2"

"type-fest@^0.21.3":
  "integrity" "sha512-t0rzBq87m3fVcduHDUFhKmyyX+9eo6WQjZvf51Ea/M0Q7+T374Jp1aUiyUl0GKxp8M/OETVHSDvmkyPgvX+X2w=="
  "resolved" "http://castle-npm.cp.hxdi.cn/type-fest/-/type-fest-0.21.3.tgz"
  "version" "0.21.3"

"type-fest@^0.6.0":
  "integrity" "sha1-jSojcNPfiG61yQraHFv2GIrPg4s="
  "resolved" "https://registry.npmmirror.com/type-fest/download/type-fest-0.6.0.tgz"
  "version" "0.6.0"

"type-fest@^0.8.1":
  "integrity" "sha1-CeJJ696FHTseSNJ8EFREZn8XuD0="
  "resolved" "https://registry.npmmirror.com/type-fest/download/type-fest-0.8.1.tgz"
  "version" "0.8.1"

"type-is@~1.6.17", "type-is@~1.6.18":
  "integrity" "sha1-TlUs0F3wlGfcvE73Od6J8s83wTE="
  "resolved" "https://registry.npmmirror.com/type-is/download/type-is-1.6.18.tgz"
  "version" "1.6.18"
  dependencies:
    "media-typer" "0.3.0"
    "mime-types" "~2.1.24"

"unicode-canonical-property-names-ecmascript@^2.0.0":
  "integrity" "sha1-MBrNxSVjFnDTn2FG4Od/9rvevdw="
  "resolved" "https://registry.npmmirror.com/unicode-canonical-property-names-ecmascript/download/unicode-canonical-property-names-ecmascript-2.0.0.tgz"
  "version" "2.0.0"

"unicode-match-property-ecmascript@^2.0.0":
  "integrity" "sha1-VP0W4OyxZ88Ezx91a9zJLrp5dsM="
  "resolved" "https://registry.npmmirror.com/unicode-match-property-ecmascript/download/unicode-match-property-ecmascript-2.0.0.tgz"
  "version" "2.0.0"
  dependencies:
    "unicode-canonical-property-names-ecmascript" "^2.0.0"
    "unicode-property-aliases-ecmascript" "^2.0.0"

"unicode-match-property-value-ecmascript@^2.0.0":
  "integrity" "sha1-GgGqVyR8FMVouJd1pUk4eIGJpxQ="
  "resolved" "https://registry.npmmirror.com/unicode-match-property-value-ecmascript/download/unicode-match-property-value-ecmascript-2.0.0.tgz"
  "version" "2.0.0"

"unicode-property-aliases-ecmascript@^2.0.0":
  "integrity" "sha1-CjbLmlhcT2q9Ua0d7dsoXBZSl8g="
  "resolved" "https://registry.npmmirror.com/unicode-property-aliases-ecmascript/download/unicode-property-aliases-ecmascript-2.0.0.tgz"
  "version" "2.0.0"

"unidecode@0.1.8":
  "integrity" "sha1-77swFTi8RSRqmsjFWdcvAVMFBT4="
  "resolved" "https://registry.npmmirror.com/unidecode/download/unidecode-0.1.8.tgz"
  "version" "0.1.8"

"union-value@^1.0.0":
  "integrity" "sha1-C2/nuDWuzaYcbqTU8CwUIh4QmEc="
  "resolved" "https://registry.npmmirror.com/union-value/download/union-value-1.0.1.tgz"
  "version" "1.0.1"
  dependencies:
    "arr-union" "^3.1.0"
    "get-value" "^2.0.6"
    "is-extendable" "^0.1.1"
    "set-value" "^2.0.1"

"universalify@^2.0.0":
  "integrity" "sha1-daSYTv7cSwiXXFrrc/Uw0C3yVxc="
  "resolved" "https://registry.npmmirror.com/universalify/download/universalify-2.0.0.tgz"
  "version" "2.0.0"

"unpipe@~1.0.0", "unpipe@1.0.0":
  "integrity" "sha1-sr9O6FFKrmFltIF4KdIbLvSZBOw="
  "resolved" "https://registry.npmmirror.com/unpipe/download/unpipe-1.0.0.tgz"
  "version" "1.0.0"

"unplugin-vue-components@^0.25.2":
  "integrity" "sha512-OVmLFqILH6w+eM8fyt/d/eoJT9A6WO51NZLf1vC5c1FZ4rmq2bbGxTy8WP2Jm7xwFdukaIdv819+UI7RClPyCA=="
  "resolved" "http://castle-npm.cp.hxdi.cn/unplugin-vue-components/-/unplugin-vue-components-0.25.2.tgz"
  "version" "0.25.2"
  dependencies:
    "@antfu/utils" "^0.7.5"
    "@rollup/pluginutils" "^5.0.2"
    "chokidar" "^3.5.3"
    "debug" "^4.3.4"
    "fast-glob" "^3.3.0"
    "local-pkg" "^0.4.3"
    "magic-string" "^0.30.1"
    "minimatch" "^9.0.3"
    "resolve" "^1.22.2"
    "unplugin" "^1.4.0"

"unplugin@^1.4.0":
  "integrity" "sha512-5x4eIEL6WgbzqGtF9UV8VEC/ehKptPXDS6L2b0mv4FRMkJxRtjaJfOWDd6a8+kYbqsjklix7yWP0N3SUepjXcg=="
  "resolved" "http://castle-npm.cp.hxdi.cn/unplugin/-/unplugin-1.4.0.tgz"
  "version" "1.4.0"
  dependencies:
    "acorn" "^8.9.0"
    "chokidar" "^3.5.3"
    "webpack-sources" "^3.2.3"
    "webpack-virtual-modules" "^0.5.0"

"unset-value@^1.0.0":
  "integrity" "sha1-g3aHP30jNRef+x5vw6jtDfyKtVk="
  "resolved" "https://registry.npmmirror.com/unset-value/download/unset-value-1.0.0.tgz"
  "version" "1.0.0"
  dependencies:
    "has-value" "^0.3.1"
    "isobject" "^3.0.0"

"update-browserslist-db@^1.0.11":
  "integrity" "sha512-dCwEFf0/oT85M1fHBg4F0jtLwJrutGoHSQXCh7u4o2t1drG+c0a9Flnqww6XUKSfQMPpJBRjU8d4RXB09qtvaA=="
  "resolved" "http://castle-npm.cp.hxdi.cn/update-browserslist-db/-/update-browserslist-db-1.0.11.tgz"
  "version" "1.0.11"
  dependencies:
    "escalade" "^3.1.1"
    "picocolors" "^1.0.0"

"uri-js@^4.2.2":
  "integrity" "sha1-mxpSWVIlhZ5V9mnZKPiMbFfyp34="
  "resolved" "https://registry.npmmirror.com/uri-js/download/uri-js-4.4.1.tgz"
  "version" "4.4.1"
  dependencies:
    "punycode" "^2.1.0"

"urix@^0.1.0":
  "integrity" "sha1-2pN/emLiH+wf0Y1Js1wpNQZ6bHI="
  "resolved" "https://registry.npmmirror.com/urix/download/urix-0.1.0.tgz"
  "version" "0.1.0"

"url-slug@2.0.0":
  "integrity" "sha1-p4nVrtSZXA2VrzM3etHVxo1NcCc="
  "resolved" "https://registry.npmmirror.com/url-slug/download/url-slug-2.0.0.tgz"
  "version" "2.0.0"
  dependencies:
    "unidecode" "0.1.8"

"url@^0.11.0":
  "integrity" "sha1-ODjpfPxgUh63PFJajlW/3Z4uKPE="
  "resolved" "https://registry.npmmirror.com/url/download/url-0.11.0.tgz"
  "version" "0.11.0"
  dependencies:
    "punycode" "1.3.2"
    "querystring" "0.2.0"

"use@^3.1.0":
  "integrity" "sha1-1QyMrHmhn7wg8pEfVuuXP04QBw8="
  "resolved" "https://registry.npmmirror.com/use/download/use-3.1.1.tgz"
  "version" "3.1.1"

"util-deprecate@^1.0.1", "util-deprecate@^1.0.2", "util-deprecate@~1.0.1":
  "integrity" "sha1-RQ1Nyfpw3nMnYvvS1KKJgUGaDM8="
  "resolved" "https://registry.npmmirror.com/util-deprecate/download/util-deprecate-1.0.2.tgz"
  "version" "1.0.2"

"utila@~0.4":
  "integrity" "sha1-ihagXURWV6Oupe7MWxKk+lN5dyw="
  "resolved" "https://registry.npmmirror.com/utila/download/utila-0.4.0.tgz"
  "version" "0.4.0"

"utils-merge@1.0.1":
  "integrity" "sha1-n5VxD1CiZ5R7LMwSR0HBAoQn5xM="
  "resolved" "https://registry.npmmirror.com/utils-merge/download/utils-merge-1.0.1.tgz"
  "version" "1.0.1"

"uuid@^8.3.2":
  "integrity" "sha1-gNW1ztJxu5r2xEXyGhoExgbO++I="
  "resolved" "https://registry.npmmirror.com/uuid/download/uuid-8.3.2.tgz"
  "version" "8.3.2"

"v8-compile-cache@^2.0.3":
  "integrity" "sha1-LeGWGMZtwkfc+2+ZM4A12CRaLO4="
  "resolved" "https://registry.npmmirror.com/v8-compile-cache/download/v8-compile-cache-2.3.0.tgz"
  "version" "2.3.0"

"validate-npm-package-license@^3.0.1":
  "integrity" "sha1-/JH2uce6FchX9MssXe/uw51PQQo="
  "resolved" "https://registry.npmmirror.com/validate-npm-package-license/download/validate-npm-package-license-3.0.4.tgz"
  "version" "3.0.4"
  dependencies:
    "spdx-correct" "^3.0.0"
    "spdx-expression-parse" "^3.0.0"

"vant@^3.2.5":
  "integrity" "sha512-xDNqdBvBAELwkkC4QqzXvcqQJMCP6l0faYDH0aU1dfkIm7Muc+7gqyHMVhPKPFcDGBP33ilZd0Vg2+JoIQWitw=="
  "resolved" "https://registry.npmmirror.com/vant/download/vant-3.3.7.tgz"
  "version" "3.3.7"
  dependencies:
    "@vant/icons" "^1.7.1"
    "@vant/popperjs" "^1.1.0"
    "@vant/use" "^1.3.4"

"vary@~1.1.2":
  "integrity" "sha1-IpnwLG3tMNSllhsLn3RSShj2NPw="
  "resolved" "https://registry.npmmirror.com/vary/download/vary-1.1.2.tgz"
  "version" "1.1.2"

"vconsole@^3.15.0":
  "integrity" "sha512-8hq7wabPcRucSWQyN7/1tthMawP9JPvM95zgtMHpPknMMMCKj+abpoK7P7oKK4B0qw58C24Mdvo9+raUdpHyVQ=="
  "resolved" "http://castle-npm.cp.hxdi.cn/vconsole/-/vconsole-3.15.0.tgz"
  "version" "3.15.0"
  dependencies:
    "@babel/runtime" "^7.17.2"
    "copy-text-to-clipboard" "^3.0.1"
    "core-js" "^3.11.0"
    "mutation-observer" "^1.0.3"

"vue-eslint-parser@^7.10.0":
  "integrity" "sha1-IUtd6pYQB/z/su5luJEjB2KNDa8="
  "resolved" "https://registry.npmmirror.com/vue-eslint-parser/download/vue-eslint-parser-7.11.0.tgz"
  "version" "7.11.0"
  dependencies:
    "debug" "^4.1.1"
    "eslint-scope" "^5.1.1"
    "eslint-visitor-keys" "^1.1.0"
    "espree" "^6.2.1"
    "esquery" "^1.4.0"
    "lodash" "^4.17.21"
    "semver" "^6.3.0"

"vue-hot-reload-api@^2.3.0":
  "integrity" "sha512-BXq3jwIagosjgNVae6tkHzzIk6a8MHFtzAdwhnV5VlvPTFxDCvIttgSiHWjdGoTJvXtmRu5HacExfdarRcFhog=="
  "resolved" "https://registry.npmmirror.com/vue-hot-reload-api/-/vue-hot-reload-api-2.3.4.tgz"
  "version" "2.3.4"

"vue-loader@^16.8.2":
  "integrity" "sha1-1D5nXe9bqTRdbH8FkUwT2GGZcIc="
  "resolved" "https://registry.npmmirror.com/vue-loader/download/vue-loader-16.8.3.tgz"
  "version" "16.8.3"
  dependencies:
    "chalk" "^4.1.0"
    "hash-sum" "^2.0.0"
    "loader-utils" "^2.0.0"

"vue-router@^4.0.3":
  "integrity" "sha1-jceSzd9bsavMOQj5BkE23n4TxGA="
  "resolved" "https://registry.npmmirror.com/vue-router/download/vue-router-4.0.12.tgz"
  "version" "4.0.12"
  dependencies:
    "@vue/devtools-api" "^6.0.0-beta.18"

"vue-style-loader@^4.1.0", "vue-style-loader@^4.1.3":
  "integrity" "sha1-bVWGOlH6dXqyTonZNxRlByqnvDU="
  "resolved" "https://registry.npmmirror.com/vue-style-loader/download/vue-style-loader-4.1.3.tgz"
  "version" "4.1.3"
  dependencies:
    "hash-sum" "^1.0.2"
    "loader-utils" "^1.0.2"

"vue-template-es2015-compiler@^1.9.0":
  "integrity" "sha1-HuO8mhbsv1EYvjNLsV+cRvgvWCU="
  "resolved" "https://registry.npmmirror.com/vue-template-es2015-compiler/download/vue-template-es2015-compiler-1.9.1.tgz"
  "version" "1.9.1"

"vue@^2 || ^3.2.13", "vue@^3.0.0", "vue@^3.0.2", "vue@^3.2.6", "vue@2 || 3", "vue@3.2.26":
  "integrity" "sha512-KD4lULmskL5cCsEkfhERVRIOEDrfEL9CwAsLYpzptOGjaGFNWo3BQ9g8MAb7RaIO71rmVOziZ/uEN/rHwcUIhg=="
  "resolved" "https://registry.npmmirror.com/vue/download/vue-3.2.26.tgz"
  "version" "3.2.26"
  dependencies:
    "@vue/compiler-dom" "3.2.26"
    "@vue/compiler-sfc" "3.2.26"
    "@vue/runtime-dom" "3.2.26"
    "@vue/server-renderer" "3.2.26"
    "@vue/shared" "3.2.26"

"vuex@^4.0.0":
  "integrity" "sha1-+Jbb1b8qDpY/AMZ+m2EN50nMrMk="
  "resolved" "https://registry.npmmirror.com/vuex/download/vuex-4.0.2.tgz"
  "version" "4.0.2"
  dependencies:
    "@vue/devtools-api" "^6.0.0-beta.11"

"watchpack@^2.3.1":
  "integrity" "sha512-x0t0JuydIo8qCNctdDrn1OzH/qDzk2+rdCOC3YzumZ42fiMqmQ7T3xQurykYMhYfHaPHTp4ZxAx2NfUo1K6QaA=="
  "resolved" "https://registry.npmmirror.com/watchpack/download/watchpack-2.3.1.tgz"
  "version" "2.3.1"
  dependencies:
    "glob-to-regexp" "^0.4.1"
    "graceful-fs" "^4.1.2"

"wbuf@^1.1.0", "wbuf@^1.7.3":
  "integrity" "sha1-wdjRSTFtPqhShIiVy2oL/oh7h98="
  "resolved" "https://registry.npmmirror.com/wbuf/download/wbuf-1.7.3.tgz"
  "version" "1.7.3"
  dependencies:
    "minimalistic-assert" "^1.0.0"

"wcwidth@^1.0.1":
  "integrity" "sha1-8LDc+RW8X/FSivrbLA4XtTLaL+g="
  "resolved" "https://registry.npmmirror.com/wcwidth/download/wcwidth-1.0.1.tgz"
  "version" "1.0.1"
  dependencies:
    "defaults" "^1.0.3"

"webidl-conversions@^3.0.0":
  "integrity" "sha1-JFNCdeKnvGvnvIZhHMFq4KVlSHE="
  "resolved" "https://registry.npmmirror.com/webidl-conversions/download/webidl-conversions-3.0.1.tgz"
  "version" "3.0.1"

"webpack-bundle-analyzer@^4.4.0":
  "integrity" "sha1-Gw7qKUfnNSh1Sm+a8+kbK24PedU="
  "resolved" "https://registry.npmmirror.com/webpack-bundle-analyzer/download/webpack-bundle-analyzer-4.5.0.tgz?cache=0&sync_timestamp=1634019948189&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2Fwebpack-bundle-analyzer%2Fdownload%2Fwebpack-bundle-analyzer-4.5.0.tgz"
  "version" "4.5.0"
  dependencies:
    "acorn" "^8.0.4"
    "acorn-walk" "^8.0.0"
    "chalk" "^4.1.0"
    "commander" "^7.2.0"
    "gzip-size" "^6.0.0"
    "lodash" "^4.17.20"
    "opener" "^1.5.2"
    "sirv" "^1.0.7"
    "ws" "^7.3.1"

"webpack-chain@^6.5.1":
  "integrity" "sha1-TycoTLu2N+PI+970Pu9YjU2GEgY="
  "resolved" "https://registry.npmmirror.com/webpack-chain/download/webpack-chain-6.5.1.tgz"
  "version" "6.5.1"
  dependencies:
    "deepmerge" "^1.5.2"
    "javascript-stringify" "^2.0.1"

"webpack-dev-middleware@^5.2.1":
  "integrity" "sha512-DjZyYrsHhkikAFNvSNKrpnziXukU1EChFAh9j4LAm6ndPLPW8cN0KhM7T+RAiOqsQ6ABfQ8hoKIs9IWMTjov+w=="
  "resolved" "https://registry.npmmirror.com/webpack-dev-middleware/download/webpack-dev-middleware-5.2.2.tgz"
  "version" "5.2.2"
  dependencies:
    "colorette" "^2.0.10"
    "memfs" "^3.2.2"
    "mime-types" "^2.1.31"
    "range-parser" "^1.2.1"
    "schema-utils" "^4.0.0"

"webpack-dev-server@^4.1.0":
  "integrity" "sha512-oojcBIKvx3Ya7qs1/AVWHDgmP1Xml8rGsEBnSobxU/UJSX1xP1GPM3MwsAnDzvqcVmVki8tV7lbcsjEjk0PtYg=="
  "resolved" "https://registry.npmmirror.com/webpack-dev-server/download/webpack-dev-server-4.6.0.tgz"
  "version" "4.6.0"
  dependencies:
    "ansi-html-community" "^0.0.8"
    "bonjour" "^3.5.0"
    "chokidar" "^3.5.2"
    "colorette" "^2.0.10"
    "compression" "^1.7.4"
    "connect-history-api-fallback" "^1.6.0"
    "default-gateway" "^6.0.3"
    "del" "^6.0.0"
    "express" "^4.17.1"
    "graceful-fs" "^4.2.6"
    "html-entities" "^2.3.2"
    "http-proxy-middleware" "^2.0.0"
    "ipaddr.js" "^2.0.1"
    "open" "^8.0.9"
    "p-retry" "^4.5.0"
    "portfinder" "^1.0.28"
    "schema-utils" "^4.0.0"
    "selfsigned" "^1.10.11"
    "serve-index" "^1.9.1"
    "sockjs" "^0.3.21"
    "spdy" "^4.0.2"
    "strip-ansi" "^7.0.0"
    "url" "^0.11.0"
    "webpack-dev-middleware" "^5.2.1"
    "ws" "^8.1.0"

"webpack-merge@^5.7.3":
  "integrity" "sha1-Kznb8ir4d3atdEw5AiNzHTCmj2E="
  "resolved" "https://registry.npmmirror.com/webpack-merge/download/webpack-merge-5.8.0.tgz"
  "version" "5.8.0"
  dependencies:
    "clone-deep" "^4.0.1"
    "wildcard" "^2.0.0"

"webpack-sources@*", "webpack-sources@^3.2.2", "webpack-sources@^3.2.3":
  "integrity" "sha512-/DyMEOrDgLKKIG0fmvtz+4dUX/3Ghozwgm6iPp8KRhvn+eQf9+Q7GWxVNMk3+uCPWfdXYC4ExGBckIXdFEfH1w=="
  "resolved" "http://castle-npm.cp.hxdi.cn/webpack-sources/-/webpack-sources-3.2.3.tgz"
  "version" "3.2.3"

"webpack-virtual-modules@^0.4.2":
  "integrity" "sha1-zVl8bVHVpey0c+6hmDpY+ooX3tk="
  "resolved" "https://registry.npmmirror.com/webpack-virtual-modules/download/webpack-virtual-modules-0.4.3.tgz"
  "version" "0.4.3"

"webpack-virtual-modules@^0.5.0":
  "integrity" "sha512-kyDivFZ7ZM0BVOUteVbDFhlRt7Ah/CSPwJdi8hBpkK7QLumUqdLtVfm/PX/hkcnrvr0i77fO5+TjZ94Pe+C9iw=="
  "resolved" "http://castle-npm.cp.hxdi.cn/webpack-virtual-modules/-/webpack-virtual-modules-0.5.0.tgz"
  "version" "0.5.0"

"webpack@*", "webpack@^2.0.0 || ^3.0.0 || ^4.0.0 || ^5.0.0", "webpack@^4.0.0", "webpack@^4.0.0 || ^5.0.0", "webpack@^4.1.0 || ^5.0.0-0", "webpack@^4.27.0 || ^5.0.0", "webpack@^4.37.0 || ^5.0.0", "webpack@^5.0.0", "webpack@^5.1.0", "webpack@^5.20.0", "webpack@^5.38.1", "webpack@^5.54.0", "webpack@>=2":
  "integrity" "sha512-Q5or2o6EKs7+oKmJo7LaqZaMOlDWQse9Tm5l1WAfU/ujLGN5Pb0SqGeVkN/4bpPmEqEP5RnVhiqsOtWtUVwGRw=="
  "resolved" "https://registry.npmmirror.com/webpack/download/webpack-5.65.0.tgz"
  "version" "5.65.0"
  dependencies:
    "@types/eslint-scope" "^3.7.0"
    "@types/estree" "^0.0.50"
    "@webassemblyjs/ast" "1.11.1"
    "@webassemblyjs/wasm-edit" "1.11.1"
    "@webassemblyjs/wasm-parser" "1.11.1"
    "acorn" "^8.4.1"
    "acorn-import-assertions" "^1.7.6"
    "browserslist" "^4.14.5"
    "chrome-trace-event" "^1.0.2"
    "enhanced-resolve" "^5.8.3"
    "es-module-lexer" "^0.9.0"
    "eslint-scope" "5.1.1"
    "events" "^3.2.0"
    "glob-to-regexp" "^0.4.1"
    "graceful-fs" "^4.2.4"
    "json-parse-better-errors" "^1.0.2"
    "loader-runner" "^4.2.0"
    "mime-types" "^2.1.27"
    "neo-async" "^2.6.2"
    "schema-utils" "^3.1.0"
    "tapable" "^2.1.1"
    "terser-webpack-plugin" "^5.1.3"
    "watchpack" "^2.3.1"
    "webpack-sources" "^3.2.2"

"websocket-driver@^0.7.4", "websocket-driver@>=0.5.1":
  "integrity" "sha1-ia1Slbv2S0gKvLox5JU6ynBvV2A="
  "resolved" "https://registry.npmmirror.com/websocket-driver/download/websocket-driver-0.7.4.tgz?cache=0&sync_timestamp=1632753509394&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2Fwebsocket-driver%2Fdownload%2Fwebsocket-driver-0.7.4.tgz"
  "version" "0.7.4"
  dependencies:
    "http-parser-js" ">=0.5.1"
    "safe-buffer" ">=5.1.0"
    "websocket-extensions" ">=0.1.1"

"websocket-extensions@>=0.1.1":
  "integrity" "sha1-f4RzvIOd/YdgituV1+sHUhFXikI="
  "resolved" "https://registry.npmmirror.com/websocket-extensions/download/websocket-extensions-0.1.4.tgz"
  "version" "0.1.4"

"whatwg-fetch@^3.6.2":
  "integrity" "sha1-3O0k838mJO0CgXJdUdDi4/5nf4w="
  "resolved" "https://registry.npmmirror.com/whatwg-fetch/download/whatwg-fetch-3.6.2.tgz"
  "version" "3.6.2"

"whatwg-url@^5.0.0":
  "integrity" "sha1-lmRU6HZUYuN2RNNib2dCzotwll0="
  "resolved" "https://registry.npmmirror.com/whatwg-url/download/whatwg-url-5.0.0.tgz"
  "version" "5.0.0"
  dependencies:
    "tr46" "~0.0.3"
    "webidl-conversions" "^3.0.0"

"which-module@^2.0.0":
  "integrity" "sha1-2e8H3Od7mQK4o6j6SzHD4/fm6Ho="
  "resolved" "http://castle-npm.cp.hxdi.cn/which-module/-/which-module-2.0.0.tgz"
  "version" "2.0.0"

"which@^1.2.9":
  "integrity" "sha1-pFBD1U9YBTFtqNYvn1CRjT2nCwo="
  "resolved" "https://registry.npmmirror.com/which/download/which-1.3.1.tgz?cache=0&sync_timestamp=1632753511486&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2Fwhich%2Fdownload%2Fwhich-1.3.1.tgz"
  "version" "1.3.1"
  dependencies:
    "isexe" "^2.0.0"

"which@^2.0.1":
  "integrity" "sha1-fGqN0KY2oDJ+ELWckobu6T8/UbE="
  "resolved" "https://registry.npmmirror.com/which/download/which-2.0.2.tgz?cache=0&sync_timestamp=1632753511486&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2Fwhich%2Fdownload%2Fwhich-2.0.2.tgz"
  "version" "2.0.2"
  dependencies:
    "isexe" "^2.0.0"

"wildcard@^2.0.0":
  "integrity" "sha1-p30g5SAMb6qsl55LOq3Hs91/j+w="
  "resolved" "https://registry.npmmirror.com/wildcard/download/wildcard-2.0.0.tgz"
  "version" "2.0.0"

"word-wrap@^1.2.3":
  "integrity" "sha1-YQY29rH3A4kb00dxzLF/uTtHB5w="
  "resolved" "https://registry.npmmirror.com/word-wrap/download/word-wrap-1.2.3.tgz"
  "version" "1.2.3"

"wrap-ansi@^3.0.1":
  "integrity" "sha1-KIoE2H7aXChuBg3+jxNc6NAH+Lo="
  "resolved" "https://registry.npmmirror.com/wrap-ansi/download/wrap-ansi-3.0.1.tgz"
  "version" "3.0.1"
  dependencies:
    "string-width" "^2.1.1"
    "strip-ansi" "^4.0.0"

"wrap-ansi@^6.2.0":
  "integrity" "sha512-r6lPcBGxZXlIcymEu7InxDMhdW0KDxpLgoFLcguasxCaJ/SOIZwINatK9KY/tf+ZrlywOKU0UDj3ATXUBfxJXA=="
  "resolved" "http://castle-npm.cp.hxdi.cn/wrap-ansi/-/wrap-ansi-6.2.0.tgz"
  "version" "6.2.0"
  dependencies:
    "ansi-styles" "^4.0.0"
    "string-width" "^4.1.0"
    "strip-ansi" "^6.0.0"

"wrap-ansi@^7.0.0":
  "integrity" "sha1-Z+FFz/UQpqaYS98RUpEdadLrnkM="
  "resolved" "https://registry.npmmirror.com/wrap-ansi/download/wrap-ansi-7.0.0.tgz"
  "version" "7.0.0"
  dependencies:
    "ansi-styles" "^4.0.0"
    "string-width" "^4.1.0"
    "strip-ansi" "^6.0.0"

"wrappy@1":
  "integrity" "sha1-tSQ9jz7BqjXxNkYFvA0QNuMKtp8="
  "resolved" "https://registry.npmmirror.com/wrappy/download/wrappy-1.0.2.tgz"
  "version" "1.0.2"

"ws@^7.3.1":
  "integrity" "sha512-6GLgCqo2cy2A2rjCNFlxQS6ZljG/coZfZXclldI8FB/1G3CCI36Zd8xy2HrFVACi8tfk5XrgLQEk+P0Tnz9UcA=="
  "resolved" "https://registry.npmmirror.com/ws/download/ws-7.5.6.tgz"
  "version" "7.5.6"

"ws@^8.1.0":
  "integrity" "sha512-Gs5EZtpqZzLvmIM59w4igITU57lrtYVFneaa434VROv4thzJyV6UjIL3D42lslWlI+D4KzLYnxSwtfuiO79sNw=="
  "resolved" "https://registry.npmmirror.com/ws/download/ws-8.3.0.tgz"
  "version" "8.3.0"

"xml-escape@^1.1.0":
  "integrity" "sha512-B/T4sDK8Z6aUh/qNr7mjKAwwncIljFuUP+DO/D5hloYFj+90O88z8Wf7oSucZTHxBAsC1/CTP4rtx/x1Uf72Mg=="
  "resolved" "https://registry.npmmirror.com/xml-escape/-/xml-escape-1.1.0.tgz"
  "version" "1.1.0"

"y18n@^4.0.0":
  "integrity" "sha512-JKhqTOwSrqNA1NY5lSztJ1GrBiUodLMmIZuLiDaMRJ+itFd+ABVE8XBjOvIWL+rSqNDC74LCSFmlb/U4UZ4hJQ=="
  "resolved" "http://castle-npm.cp.hxdi.cn/y18n/-/y18n-4.0.3.tgz"
  "version" "4.0.3"

"y18n@^5.0.5":
  "integrity" "sha1-f0k00PfKjFb5UxSTndzS3ZHOHVU="
  "resolved" "https://registry.npmmirror.com/y18n/download/y18n-5.0.8.tgz"
  "version" "5.0.8"

"yallist@^2.1.2":
  "integrity" "sha1-HBH5IY8HYImkfdUS+TxmmaaoHVI="
  "resolved" "https://registry.npmmirror.com/yallist/download/yallist-2.1.2.tgz"
  "version" "2.1.2"

"yallist@^4.0.0":
  "integrity" "sha1-m7knkNnA7/7GO+c1GeEaNQGaOnI="
  "resolved" "https://registry.npmmirror.com/yallist/download/yallist-4.0.0.tgz"
  "version" "4.0.0"

"yaml@^1.10.0", "yaml@^1.10.2":
  "integrity" "sha1-IwHF/78StGfejaIzOkWeKeeSDks="
  "resolved" "https://registry.npmmirror.com/yaml/download/yaml-1.10.2.tgz?cache=0&sync_timestamp=1636797314110&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2Fyaml%2Fdownload%2Fyaml-1.10.2.tgz"
  "version" "1.10.2"

"yaml@^2.1.1":
  "integrity" "sha512-N/lyzTPaJasoDmfV7YTrYCI0G/3ivm/9wdG0aHuheKowWQwGTsK0Eoiw6utmzAnI6pkJa0DUVygvp3spqqEKXg=="
  "resolved" "http://castle-npm.cp.hxdi.cn/yaml/-/yaml-2.3.2.tgz"
  "version" "2.3.2"

"yargs-parser@^18.1.2":
  "integrity" "sha512-o50j0JeToy/4K6OZcaQmW6lyXXKhq7csREXcDwk2omFPJEwUNOVtJKvmDr9EI1fAJZUyZcRF7kxGBWmRXudrCQ=="
  "resolved" "http://castle-npm.cp.hxdi.cn/yargs-parser/-/yargs-parser-18.1.3.tgz"
  "version" "18.1.3"
  dependencies:
    "camelcase" "^5.0.0"
    "decamelize" "^1.2.0"

"yargs-parser@^20.2.2":
  "integrity" "sha1-LrfcOwKJcY/ClfNidThFxBoMlO4="
  "resolved" "https://registry.npmmirror.com/yargs-parser/download/yargs-parser-20.2.9.tgz?cache=0&sync_timestamp=1637031026741&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2Fyargs-parser%2Fdownload%2Fyargs-parser-20.2.9.tgz"
  "version" "20.2.9"

"yargs@^15.3.1":
  "integrity" "sha512-aePbxDmcYW++PaqBsJ+HYUFwCdv4LVvdnhBy78E57PIor8/OVvhMrADFFEDh8DHDFRv/O9i3lPhsENjO7QX0+A=="
  "resolved" "http://castle-npm.cp.hxdi.cn/yargs/-/yargs-15.4.1.tgz"
  "version" "15.4.1"
  dependencies:
    "cliui" "^6.0.0"
    "decamelize" "^1.2.0"
    "find-up" "^4.1.0"
    "get-caller-file" "^2.0.1"
    "require-directory" "^2.1.1"
    "require-main-filename" "^2.0.0"
    "set-blocking" "^2.0.0"
    "string-width" "^4.2.0"
    "which-module" "^2.0.0"
    "y18n" "^4.0.0"
    "yargs-parser" "^18.1.2"

"yargs@^16.0.0":
  "integrity" "sha1-HIK/D2tqZur85+8w43b0mhJHf2Y="
  "resolved" "https://registry.npmmirror.com/yargs/download/yargs-16.2.0.tgz"
  "version" "16.2.0"
  dependencies:
    "cliui" "^7.0.2"
    "escalade" "^3.1.1"
    "get-caller-file" "^2.0.5"
    "require-directory" "^2.1.1"
    "string-width" "^4.2.0"
    "y18n" "^5.0.5"
    "yargs-parser" "^20.2.2"

"yorkie@^2.0.0":
  "integrity" "sha1-kkEZEtQ1IU4SxRwq4Qk+VLa7g9k="
  "resolved" "https://registry.npmmirror.com/yorkie/download/yorkie-2.0.0.tgz"
  "version" "2.0.0"
  dependencies:
    "execa" "^0.8.0"
    "is-ci" "^1.0.10"
    "normalize-path" "^1.0.0"
    "strip-indent" "^2.0.0"

"zrender@5.2.1":
  "integrity" "sha1-X0u9qRW6bUErCxncJDG+qtBUF7s="
  "resolved" "https://registry.npmmirror.com/zrender/download/zrender-5.2.1.tgz"
  "version" "5.2.1"
  dependencies:
    "tslib" "2.3.0"
