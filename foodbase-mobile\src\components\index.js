import HEchart from '@/components/HEchart/HEchart';
import HFixedNumber from '@/components/HFixedNumber/HFixedNumber';
import HCard from '@/components/HCard/HCard';
import HPicker from '@/components/HPicker/HPicker';
import HQrCode from '@/components/HQrCode/HQrCode';
import HList from '@/components/HList/HList';
import HMultipleSelect from '@/components/HMultipleSelect/HMultipleSelect';

export { HEchart, HFixedNumber, HCard, HPicker, HQrCode };

export default {
  install(app) {
    app.component(HEchart.name, HEchart);
    app.component(HFixedNumber.name, HFixedNumber);
    app.component(HList.name, HList);
    app.component(HMultipleSelect.name, HMultipleSelect);
    app.component('HCard', HCard);
    app.component('HPicker', HPicker);
    app.component('HQrCode', HQrCode);
  },
};
