<template>
  <div class="login">
    <template v-if="useAutoLogin">
      <p v-if="loginFailed">登录失败</p>
      <p v-else>自动登录中...</p>
    </template>
    <template v-else>
      <h1 @click="switchEnv" class="my-8 text-3xl font-bold">登录</h1>
      <Form @submit="onSubmit" class="login-form">
        <CellGroup inset>
          <Field
            size="large"
            v-model="formData.username"
            name="username"
            label="手机号"
            placeholder="手机号"
            :rules="[{ required: true, message: '请输入手机号' }]"
          />
          <Field
            size="large"
            v-model="formData.password"
            type="password"
            name="password"
            label="密码"
            placeholder="密码"
            :rules="[{ required: true, message: '请输入密码' }]"
          />
          <Field size="large" v-model="formData.code" label="验证码" placeholder="请输入验证码">
            <template #extra>
              <img class="code" :src="codeSrc" alt="code" @click="loadAuthCode" />
            </template>
          </Field>
        </CellGroup>
        <div style="margin: 16px">
          <Button round block type="primary" native-type="submit" :loading="loading">
            登 录
          </Button>
        </div>
      </Form>
      <div class="bottom-issue" v-if="isSichuan">
        <div>主办单位：四川省粮食和物资储备局</div>
        <div style="margin-top: 8px">运维电话：19357192836</div>
      </div>
      <div class="bottom-issue" v-if="isZhejiang">
        <div>主办单位：浙江省粮食和物资储备局</div>
        <div style="margin-top: 8px">
          投诉电话：<span style="color: dodgerblue"><u>0571-86856101</u></span>
        </div>
      </div>
    </template>
  </div>
</template>

<script setup>
import { onMounted, reactive, ref, computed } from 'vue';
import { useRoute, useRouter } from 'vue-router';
import { useStore } from 'vuex';
import { Form, CellGroup, Field, Button, Dialog } from 'vant';
import { getAuthCode } from '@/api/auth';
import { removeToken, removeReserverToken, removeHxdiframeworkToken } from '@/utils/auth';
import VConsole from 'vconsole';

const HOME_NAME = process.env.VUE_APP_HOME_NAME || 'Home';

const router = useRouter();
const route = useRoute();
const store = useStore();

const useAutoLogin = ref(true);
const loginFailed = ref(false);

const codeSrc = ref('');

const formData = reactive({
  username: '',
  password: '',
  code: '',
  uuid: '',
});

const isSichuan = computed(() => {
  return process.env.VUE_APP_MODE === 'sichuan';
});
const isZhejiang = computed(() => {
  return process.env.VUE_APP_MODE === 'zhejiang';
});
const loadAuthCode = async () => {
  const data = await getAuthCode();
  const { uuid, img } = data;
  codeSrc.value = img;
  formData.uuid = uuid;
  formData.code = '';
};

let devFlagNum = ref(0);
const switchEnv = () => {
  devFlagNum.value += 1;
  if (devFlagNum.value >= 7) {
    Dialog.alert({
      title: '提示',
      message: '您已经切换到测试环境',
    }).then(() => {
      window.__isProd = 1;
      loadAuthCode();
      // eslint-disable-next-line no-unused-vars
      const vConsole = new VConsole({ theme: 'dark' });
      store.commit('user/setInfo', null);
      window.checkAppVersion(false);
    });
  }
};

const ehbLogin = () => {
  return new Promise((resolve) => {
    if (process.env.VUE_APP_THEME_MODE === 'hubei-ehb') {
      window.ehbAppJssdk.user.getLoginStatus({
        jumpToLogin: '0',
        success: function (res) {
          // console.log(res);

          if (!JSON.parse(res)?.oginState === '1') {
            Dialog.alert({
              title: '提示',
              message: '您还未登陆鄂汇办',
              beforeClose: () => false,
            });
            resolve({});
          }

          window.ehbAppJssdk.user.getUserInfo({
            success: function (res) {
              // console.log(res);
              resolve(res);
            },
          });
        },
      });
    } else {
      resolve({});
    }
  });
};

onMounted(async () => {
  let { phone, accountId, employeeCode, tenantId, redirect } = route.query;

  // 鄂汇办自动登陆
  const ehbUseInfo = await ehbLogin();
  if (ehbUseInfo?.mobilePhone) {
    phone = ehbUseInfo.mobilePhone;
  }

  if (employeeCode) {
    // 自动登录
    try {
      await store.dispatch('user/ZZDLogin', {
        accountId: accountId ? accountId : '',
        employeeCode,
        tenantId: tenantId ? tenantId : '',
      });

      if (redirect) {
        await router.replace({ path: redirect });
      } else {
        await router.replace({ name: HOME_NAME });
      }
    } catch (e) {
      console.warn('login failed');
      loginFailed.value = true;
    }
  } else if (phone) {
    // 自动登录
    try {
      await store.dispatch('user/phoneLogin', {
        username: phone,
        password: 'any',
      });

      if (redirect) {
        await router.replace({ path: redirect });
      } else {
        await router.replace({ name: HOME_NAME });
      }
    } catch (e) {
      console.warn('login failed');
      loginFailed.value = true;
    }
  } else {
    // 显示登录页面，手动登录
    useAutoLogin.value = false;
    loadAuthCode();
  }
});

const loading = ref(false);

const onSubmit = async () => {
  if (loading.value) {
    return;
  }
  try {
    loading.value = true;
    removeToken();
    removeReserverToken();
    removeHxdiframeworkToken();
    // await store.dispatch('user/login', formData);
    await store.dispatch('user/reserverLogin', formData);
    const { redirect } = route.query;
    if (redirect) {
      await router.replace({ path: redirect });
    } else {
      await router.replace({ name: HOME_NAME });
    }
  } catch (e) {
    console.warn('login failed', e);
    // if (e.message) {
    //   Toast.fail(e.message);
    // }
    loadAuthCode();
  } finally {
    loading.value = false;
  }
};
</script>

<style scoped lang="scss">
.login {
  display: flex;
  flex-direction: column;
  align-items: center;
  min-height: 100vh;
}

.login-form {
  flex: 1;
  --van-cell-line-height: 32px;
  --van-cell-large-vertical-padding: 10px;
  --van-cell-font-size: 16px;

  .code {
    display: inline-block;
    height: 32px;
  }
}
.bottom-issue {
  font-family: 'Franklin Gothic Medium', 'Arial Narrow', Arial, sans-serif;
  height: 80px;
  font-size: 12px;
  display: flex;
  flex-direction: column;
  justify-content: center;
  text-align: center;
}
</style>
