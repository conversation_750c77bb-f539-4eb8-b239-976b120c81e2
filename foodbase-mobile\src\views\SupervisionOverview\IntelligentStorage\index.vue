<template>
  <div class="intelligent-storage">
    <div v-for="(item, i) in models" :key="i" class="model-list">
      <div
        v-if="checkPermission(item.permission)"
        class="model-item"
        @click="handleItem(item)"
      >
        <img :src="item.image" :style="{'background-color': item.bgColor}" alt />
        {{ item.label }}
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref } from 'vue'
import { useRouter } from 'vue-router'
import { checkPermission } from '@/utils/permission';

const models = ref([
  {
    label: '控温记录',
    route: { name: 'TemperatureRecord' },
    image: require("@/assets/icon-temperature.png"),
    bgColor: '#F8BA68',
    permission: ['app-temperature-record'],
  },
  {
    label: '通风记录',
    route: { name: 'VentilationRecord' },
    image: require("@/assets/icon-wind.png"),
    bgColor: '#85E1E1',
    permission: ['app-ventilation-record'],
  },
  {
    label: '气调日志',
    route: { name: 'AirconditioningLog' },
    image: require("@/assets/icon-log.png"),
    bgColor: '#D1EEA5',
    permission: ['app-airconditioning-log'],
  },
  {
    label: '作业申请',
    route: { name: 'HomeWorkApply' },
    image: require("@/assets/icon-apply.png"),
    bgColor: '#F7B9BD',
    permission: ['app-home-work-apply'],
  }
])

const router = useRouter();

const handleItem = (item) => {
  const { route } = item;
  router.push(route);
}
</script>

<style lang="scss" scoped>
.intelligent-storage {
  display: flex;
  flex-wrap: wrap;
  .model-list {
    width: 50%;
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 12px 12px 0px 12px;
    .model-item {
      width: 100%;
      height: 80px;
      display: flex;
      align-items: center;
      justify-content: center;
      background-color: white;
      cursor: pointer;
      img {
        width: 50px;
        height: 50px;
        border-radius: 25px;
        padding: 10px 10px;
        margin-right: 12px;
      }
    }
  }
  .model-list:nth-child(2n) {
    padding-left: 0px;
  }
}
</style>
