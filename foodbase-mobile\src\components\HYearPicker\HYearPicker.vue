<template>
  <HPicker class="h-year-picker" :value="value" @update:value="onUpdate" :options="options" />
</template>

<script setup>
import HPicker from '@/components/HPicker/HPicker';
import { computed } from 'vue';

const props = defineProps({
  value: String,
});

const emits = defineEmits(['update:value']);

const currentYear = new Date().getFullYear();
const options = Array.from(Array(5)).map((_, i) => {
  const year = String(currentYear - i);
  return {
    text: year,
    value: year,
  };
});

const value = computed(() => {
  return props.value;
});

const onUpdate = (newValue) => {
  emits('update:value', newValue);
};
</script>

<style lang="scss"></style>
