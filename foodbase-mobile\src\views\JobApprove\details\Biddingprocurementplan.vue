<template>
  <div class="formats">
    <Row>
      <Col :span="24">
        <Field
          v-model="queryForm.year"
          label="年份"
          is-link
          readonly
          placeholder="请选择"
          @click="showYearPicker = true"
        />
      </Col>
    </Row>
    <PullRefresh v-model="refreshing" @refresh="onRefresh" style="margin-top: 15px">
      <List
        ref="listRef"
        v-model:loading="loading"
        :finished="finished"
        finished-text="没有更多了"
        @load="getList"
      >
        <HCard class="detail-card" v-for="item in list" :key="item.id" @click="goToDetail(item)">
          <div class="type">{{ item.name }}</div>
          <div class="card-content">
            <div class="row">
              <span class="label">竞价时间：</span>
              <span class="value">{{ item.biddingTime }}</span>
            </div>
            <div class="row">
              <span class="label">轮换开始时间：</span>
              <span class="value">{{ item.startTime }}</span>
            </div>
            <div class="row">
              <span class="label">轮换结束时间：</span>
              <span class="value">{{ item.endTime }}</span>
            </div>
            <div class="row">
              <span class="label">拆标人：</span>
              <span class="value">{{ item.signAgent }}</span>
            </div>
            <div class="row">
              <span class="label">标的总量：</span>
              <span class="value">{{ item.totalNumber }}吨</span>
            </div>
          </div>
        </HCard>
      </List>
    </PullRefresh>
    <ActionSheet v-model:show="showYearPicker">
      <van-picker
        :columns="[2020, 2021, 2022, 2023, 2024, 2025]"
        @cancel="showYearPicker = false"
        @confirm="handleConfirmYear"
      ></van-picker>
    </ActionSheet>
  </div>
</template>

<script setup>
import { ref, reactive, watch, onMounted } from 'vue';
import { List, PullRefresh, ActionSheet, Field, Row, Col } from 'vant';
import { useRouter, useRoute } from 'vue-router';
import { getBiddingProcurementPlanList } from '@/api/job-approve';
import { useStore } from 'vuex';

const router = useRouter();
const route = useRoute();
const store = useStore();
const userOrgLevel = ref(store.state.user?.reserverIds?.userOrgLevel);

const showYearPicker = ref(false);

const pagination = reactive({
  page: 0,
  size: 10,
  total: 0,
});

const queryForm = reactive({
  year: new Date().getFullYear(),
});

const listRef = ref();
const list = ref([]);
const loading = ref(false);
const finished = ref(true);
const refreshing = ref(false);

const onRefresh = () => {
  if (refreshing.value) {
    pagination.page = 0;
    pagination.total = 0;
    list.value = [];
    refreshing.value = false;
  }
  finished.value = false;
  loading.value = true;
  getList();
};

const onSearch = () => {
  finished.value = false;
  list.value = [];
  pagination.page = 0;
  pagination.total = 0;
  listRef.value?.check();
};

const handleConfirmYear = (val) => {
  queryForm.year = val;
  showYearPicker.value = false;
  onSearch();
};

const goToDetail = (item) => {
  router.push({
    name: 'TenderDetails',
    query: {
      id: item.id,
      mode: item.mode,
      typer: item.typer,
      name: item.name,
    },
  });
};

const getList = async () => {
  const { items, page, total } = await getBiddingProcurementPlanList({
    page: pagination.page + 1,
    size: pagination.size,
    year: queryForm.year,
    rotationType: '招标采购',
    mode: 0,
    typer: 0,
    foodReserveId: userOrgLevel.value,
  });
  list.value.push(...items);
  pagination.page = page;
  pagination.total = total;
  // 加载状态结束
  loading.value = false;

  // 数据全部加载完成
  if (list.value.length >= total) {
    finished.value = true;
  }
};
onMounted(() => {
  onSearch();
});

watch(
  () => route.name,
  () => {
    onSearch();
  },
);
</script>

<style lang="scss" scoped>
.detail-card {
  padding: 16px;
  margin-bottom: 10px;
  .type {
    font-size: 16px;
    font-weight: bold;
    color: #232323;
  }
}

.total {
  padding: 10px 0 10px 16px;
  color: #232323;
  font-size: 14px;
  span {
    color: #1492ff;
  }
}
.card-head {
  display: flex;
  align-items: center;
  justify-content: space-between;
  font-size: 18px;
  font-weight: bold;
  color: #1f3359;
  .next {
    color: rgba(109, 116, 141, 0.6);
  }
}
.schedule-num {
  flex-grow: 1;
  margin-left: 12px;
}
.card-content {
  margin-top: 8px;
  line-height: 30px;
  // border-bottom: 1px solid #e8e9ec;
  .row {
    display: flex;
    .label {
      font-size: 14px;
      font-weight: 500;
      color: #686b73;
      flex-basis: 8em;
    }
    .value {
      font-size: 14px;
      font-weight: 400;
      color: #232323;
      flex-grow: 1;
    }

    .carNum {
      padding: 3px 6px;
      background: #f4f4f4;
      border-radius: 2px;
      border: 1px solid #ebedf0;
      margin-right: 5px;
    }
  }
}
</style>
