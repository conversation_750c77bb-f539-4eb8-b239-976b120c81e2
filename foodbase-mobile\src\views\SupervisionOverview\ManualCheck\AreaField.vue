<template>
  <Field
    :model-value="currentLabel"
    is-link
    readonly
    input-align="right"
    v-bind="$attrs"
    @click="onClick"
  />
  <Popup v-model:show="showPicker" round position="bottom" teleport="body">
    <Cascader
      :value="cascaderValue"
      title="请选择地区"
      :options="options"
      active-color="#1989fa"
      @close="showPicker = false"
      @finish="onFinish"
    />
  </Popup>
</template>

<script>
export default {
  name: 'AreaField',
  inheritAttrs: false,
};
</script>

<script setup>
import { Field, Popup, Cascader } from 'vant';
import { computed, ref } from 'vue';
import { useStore } from 'vuex';
import { getNode } from '@/utils/collection';

const props = defineProps({
  modelValue: [String, Number],
  allAreaSelect: Boolean,
  topAreaCode: [String, Number],
});

const emits = defineEmits(['update:modelValue']);

const store = useStore();

const showPicker = ref(false);

const options = computed(() => {
  const areaStore = store.state.area;
  let areaTree = props.allAreaSelect ? areaStore.areaTreeWidthAllArea : areaStore.areaTree;
  if (props.topAreaCode) {
    const userAreaNode = getNode(areaTree, (it) => it.value === props.topAreaCode);
    areaTree = userAreaNode ? [userAreaNode] : [];
  }
  return areaTree;
});

const areaMap = computed(() => {
  return store.state.area.areaMap;
});

const currentLabel = computed(() => {
  const { modelValue } = props;
  const currentNode = areaMap.value.get(modelValue);
  return currentNode?.text;
});

const cascaderValue = computed(() => {
  return props.modelValue;
});

const onClick = () => {
  showPicker.value = true;
};

const onFinish = ({ value }) => {
  showPicker.value = false;
  emits('update:modelValue', value);
};
</script>
