import { h, KeepAlive } from 'vue';
import { RouterView } from 'vue-router';

export function CreateKeepAliveRouterView(keepAliveComponentName) {
  return {
    render() {
      return h(RouterView, null, {
        default: (props) => {
          return h(
            KeepAlive,
            { include: keepAliveComponentName },
            {
              default: () => props.Component,
            },
          );
        },
      });
    },
  };
}
