<template>
  <HCard title="库存红线（单位：吨）">
    <div class="product-grain-scale">
      <div class="image-container">
        <img class="granary-image" src="../assets/product-grain.png" alt="粮仓" />

        <div class="current-line" :class="{ reverse: reverseCurrent }" :style="currentLineStyle">
          <div class="current-line-pointer"></div>
          <div class="current-line-text">
            <div class="current-line-number">
              <h-fixed-number :fraction-digits="0">{{ current }}</h-fixed-number>
            </div>
            <div class="current-line-name">实际库存</div>
          </div>
        </div>

        <div v-if="!$store.getters['user/isStoreUser']" class="red-arc" :style="redArcStyle"></div>
        <div
          v-if="!$store.getters['user/isStoreUser']"
          class="red-line"
          :class="{ reverse: reverseRed }"
          :style="redLineStyle"
        >
          <div class="red-line-pointer"></div>
          <div class="red-line-text">
            <div class="red-line-number">
              <h-fixed-number :fraction-digits="0">{{ redLine }}</h-fixed-number>
            </div>
            <div class="red-line-name">
              <span>储备红线(储备规模*</span>
              <HFixedNumber :ratio="100" :fraction-digits="0">
                {{ redLineRatio }}
              </HFixedNumber>
              <span>%)</span>
            </div>
          </div>
        </div>
      </div>
    </div>
  </HCard>
</template>

<script>
import { HCard, HFixedNumber } from '@/components';

export default {
  name: 'ProductGrainScale',
  components: { HCard, HFixedNumber },
  props: {
    max: Number,
    current: Number,
    redLineRatio: Number,
  },
  data() {
    return {
      height: 188 - 54,
    };
  },
  computed: {
    reverseCurrent() {
      if (this.max == 0) {
        return false;
      }
      if (this.current / this.max < 0.2) {
        return false;
      }
      return this.current < this.redLine;
    },
    reverseRed() {
      if (this.max == 0) {
        return true;
      }
      return this.current < this.redLine;
    },
    redLine() {
      return Math.round(this.max * this.redLineRatio);
    },
    currentLineStyle() {
      const offset = this.reverseCurrent ? 0 : -56;
      let topSize = 54 + offset + this.height * (1 - this.current / this.max);
      if (topSize < 0 || this.max === 0) {
        topSize = 0;
      }
      return {
        top: `${topSize}px`,
      };
    },
    redLineStyle() {
      const offset = this.reverseRed ? -56 : 0;
      if (this.max == 0) {
        return {
          top: '132px',
        };
      }
      return {
        top: `${54 + offset + this.height * (1 - this.redLine / this.max)}px`,
      };
    },
    redArcStyle() {
      if (this.max == 0) {
        return {
          top: '188px',
        };
      }
      return {
        top: `${54 + this.height * (1 - this.redLine / this.max)}px`,
      };
    },
  },
};
</script>

<style lang="scss" scoped>
.product-grain-scale {
  transform: scale(0.85);

  .image-container {
    width: 121px;
    height: 188px;
    vertical-align: middle;
    position: relative;

    .granary-image {
      width: 100%;
      height: 100%;
    }

    .current-line {
      position: absolute;
      left: 50%;
      top: -1000px;
      white-space: nowrap;
      display: flex;
    }

    .current-line-pointer {
      width: 100px;
      background-image: url('../assets/currentline-pointer.svg');
      background-repeat: no-repeat;
      background-position-y: bottom;
    }

    .reverse {
      .red-line-pointer,
      .current-line-pointer {
        transform: rotateX(180deg);
      }
    }

    .current-line-number {
      font-size: 24px;
      color: #f4b345;
    }

    .current-line-text {
      font-size: 18px;
    }

    .red-arc {
      width: 110px;
      height: 8px;
      background-image: url('../assets/redline.svg');
      background-repeat: no-repeat;
      background-size: 100%;
      background-position-y: top;
      position: absolute;
      left: 4%;
      top: -1000px;
    }

    .red-line {
      position: absolute;
      left: 50%;
      top: -1000px;
      white-space: nowrap;
      display: flex;
    }

    .red-line-pointer {
      width: 100px;
      background-image: url('../assets/redline-pointer.svg');
      background-repeat: no-repeat;
      background-position-y: top;
    }

    .red-line-number {
      font-size: 24px;
      color: #ff2702;
    }

    .red-line-text {
      font-size: 18px;
    }
  }
}
</style>
