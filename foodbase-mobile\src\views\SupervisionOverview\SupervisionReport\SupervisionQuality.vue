<template>
  <div class="supervision-quality">
    <div class="circle-charts">
      <div class="chart">
        <HEchart :options="dbChartOptions" />
        <div class="chart-name">质量达标率</div>
      </div>
      <div class="chart">
        <HEchart :options="ycChartOptions" />
        <div class="chart-name">品质宜存率</div>
      </div>
      <div class="chart">
        <HEchart :options="hgChartOptions" />
        <div class="chart-name">食安合格率</div>
      </div>
    </div>
    <div class="detail-values" v-if="isAbnormal">
      <div class="value-item">
        质量不达标数量:
        <span class="value">{{ props.data.unqualifiedSum }}</span>
        吨
      </div>
      <div class="value-item">
        轻度不宜存数量:
        <span class="value">{{ props.data.littleUnsuitableStorageSum }}</span>
        吨
      </div>
      <div class="value-item">
        重度不宜存数量:
        <span class="value">{{ props.data.heavyUnsuitableStorageSum }}</span>
        吨
      </div>
      <div class="value-item">
        食安不合格数量:
        <span class="value">{{ props.data.foodUnsafeSum }}</span>
        吨
      </div>
    </div>
  </div>
</template>

<script setup>
import HEchart from '@/components/HEchart/HEchart';
import { computed } from 'vue';

const props = defineProps({
  data: {
    type: Object,
    default: () => ({}),
  },
});

const isAbnormal = computed(() => {
  const { data } = props;
  return (
    data.unqualifiedSum ||
    data.littleUnsuitableStorageSum ||
    data.heavyUnsuitableStorageSum ||
    data.foodUnsafeSum
  );
});

const createCircleBarOptions = (percent = 0, color) => {
  return {
    title: [
      {
        text: `${percent}%`,
        x: 'center',
        y: 'center',
        textStyle: {
          color: color,
          fontSize: 16,
        },
      },
    ],
    polar: {
      radius: ['60%', '80%'],
      center: ['50%', '50%'],
    },
    angleAxis: {
      max: 100,
      show: false,
    },
    radiusAxis: {
      type: 'category',
      show: true,
      axisLabel: {
        show: false,
      },
      axisLine: {
        show: false,
      },
      axisTick: {
        show: false,
      },
    },
    series: [
      {
        type: 'bar',
        barWidth: '100%',
        showBackground: true,
        backgroundStyle: {
          color: '#E0E4E8',
        },
        data: [percent],
        coordinateSystem: 'polar',

        itemStyle: {
          color: color,
        },
      },
    ],
  };
};

// 达标率
const dbChartOptions = computed(() => {
  return createCircleBarOptions(props.data.qualityRate, '#7CC863');
});

// 宜存率
const ycChartOptions = computed(() => {
  return createCircleBarOptions(props.data.suitableStorageRate, '#7CC863');
});

// 合格率
const hgChartOptions = computed(() => {
  return createCircleBarOptions(props.data.foodSafeRate, '#7CC863');
});
</script>

<style scoped lang="scss">
.supervision-quality {
  padding: 10px 15px 10px 10px;
}

.item {
  display: flex;
}

.item + .item {
  margin-top: 20px;
}

.unit-name {
  text-align: left;
  padding: 0;
}

.circle-charts {
  display: flex;
  justify-content: space-between;
}

.chart {
  display: flex;
  flex-direction: column;
  align-items: center;

  .chart-name {
    font-size: 18px;
    font-weight: 500;
    color: var(--van-gray-8);
    line-height: 25px;
  }
}

.value-item {
  font-size: 16px;
  color: var(--van-gray-8);
  line-height: 22px;
  display: flex;
  flex-wrap: wrap;
  align-items: center;
}

.value-item + .value-item {
  margin-top: 10px;
}

.value {
  font-size: 20px;
  font-weight: 500;
  color: #ffac38;
  margin-left: auto;
  margin-right: 2px;
}

.h-echarts {
  height: 90px;
  width: 90px;
}
</style>
