import { getOriginPlace } from '@/api/in-out-manage';
import { generateTree } from '@/utils/collection';

export default {
  namespaced: true,
  state: {
    tree: [],
    list: [],
  },
  mutations: {
    setTree(state, tree) {
      state.tree = tree;
    },
    setList(state, list) {
      state.list = list;
    },
  },
  actions: {
    async load({ state, commit }, forceReload = false) {
      if (state.tree.length > 0 && !forceReload) {
        return;
      }
      const list = await getOriginPlace();
      commit('setList', list);
      commit(
        'setTree',
        generateTree(
          list.map((it) => {
            return {
              ...it,
              value: it.id,
              label: it.name,
            };
          }),
        ),
      );
    },
  },
};
