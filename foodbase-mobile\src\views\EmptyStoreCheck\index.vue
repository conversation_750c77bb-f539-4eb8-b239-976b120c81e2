<template>
  <div>
    <!-- 库点 -->
    <Field
      v-model="storeName"
      label="库点名称"
      is-link
      readonly
      name="signPicker"
      input-align="right"
      placeholder="请选择"
      @click="showStorePicker = true"
      size="large"
    >
    </Field>
    <Popup v-model:show="showStorePicker" position="bottom">
      <Search v-model="storeSearch" placeholder="请输入库点名称"></Search>
      <Picker
        :columns-field-names="storeFieldNames"
        @confirm="onStoreConfirm"
        @cancel="showStorePicker = false"
        :columns="storeFilter(storeSearch)"
      />
    </Popup>
    <!-- 仓房 -->
    <Field
      v-model="storeHouseName"
      label="仓房"
      is-link
      readonly
      name="signPicker"
      input-align="right"
      placeholder="请选择"
      @click="showStoreHousePicker = true"
      size="large"
    >
    </Field>
    <Popup v-model:show="showStoreHousePicker" position="bottom">
      <Picker
        :columns="storeHouseOptions"
        @confirm="onStoreHouseConfirm"
        @cancel="showStoreHousePicker = false"
      />
    </Popup>
    <PullRefresh @refresh="onRefresh" v-model="refreshLoading">
      <List
        ref="listRef"
        v-model:loading="isLoading"
        :finished="finished"
        finished-text="没有更多了"
        @load="getList"
      >
        <div class="store-list">
          <div class="store-item" v-for="(item, i) in list" :key="i">
            <div class="font-bold text-[#232323]">{{ item.store }}</div>
            <div class="flex justify-between">
              <div class="mt-[10px]">
                <span class="text-[#686B73]">仓房:&nbsp;</span
                ><span class="ml-[16px] text-[#232323]">{{ item.storeHouse }}</span>
              </div>
              <Button
                type="primary"
                size="small"
                class="w-[68px] h-[28px]"
                @click="onShowPopup(item)"
                :disabled="!(item.currentStoreState === '4' && !item.checkFinish)"
                v-p="['app-empty-store-check:check']"
                >空仓验收</Button
              >
            </div>
            <Empty
              class="empty-camera"
              v-if="emptyStoreList && emptyStoreList.length === 0"
              description="暂无数据"
            />
          </div>
        </div>
      </List>
    </PullRefresh>
    <Popup
      v-model:show="showPopup"
      closeable
      position="bottom"
      class="!bg-gray-50 pop-content h-screen overflow-hidden"
    >
      <EmptyStoreCheck :info="info" @close="onClose" @refresh="onSearch" />
    </Popup>
  </div>
</template>

<script setup>
import { onMounted, ref, reactive } from 'vue';
import { Field, Picker, Search, Popup, Empty, Button, List, PullRefresh } from 'vant';
import { getStoreOfCompany, getStoreHouseAndOil } from '@/api/supervision';
import { useStore } from 'vuex';
import EmptyStoreCheck from './EmptyStoreCheck.vue';
import { getEmptyStoreList } from '@/api/empty-store-check';

const store = useStore();
const storeOptions = ref([]);
const showStorePicker = ref(false);
const storeSearch = ref('');
const storeFieldNames = {
  text: 'storeName',
  value: 'storeId',
  children: 'child',
};
//库点
const storeName = ref('');
const storeId = ref('');
//仓房
const storeHouseName = ref('');
const storehouseId = ref('');
const showStoreHousePicker = ref(false);
const storeHouseOptions = ref([]);
const showPopup = ref(false);
const info = ref({});
//分页
const pagination = reactive({
  page: 0,
  size: 10,
  total: 0,
});
//列表
const list = ref([]);
const isLoading = ref(false);
const finished = ref(false);
const refreshLoading = ref(false);
const listRef = ref();
// 库点名称搜索
const storeFilter = (value) => {
  if (!value) {
    return storeOptions.value?.storeList;
  }
  const filterUnitList = storeOptions.value?.storeList.filter(
    (item) => item.storeName.indexOf(value) > -1,
  );
  return filterUnitList;
};
//获取库点下拉框数据
const loadStores = async () => {
  const { unitId } = store.state.user.info;
  storeOptions.value = [];
  storeOptions.value = await getStoreOfCompany({ handleUnitId: unitId });
};
//获取仓房下拉框数据
const loadStoreHouses = async () => {
  storeHouseOptions.value = [];
  const list = await getStoreHouseAndOil({ storeId: storeId.value });
  storeHouseOptions.value = list?.map((it) => {
    return {
      text: it.name,
      value: it.id,
    };
  });

  storeHouseOptions.value.unshift({
    text: '全部',
    value: undefined,
  });
};
const onStoreConfirm = (val) => {
  storeName.value = val.storeName;
  storeId.value = val.storeId;
  // 清空仓房
  storeHouseName.value = '';
  storehouseId.value = '';
  showStorePicker.value = false;
  onSearch();
  loadStoreHouses();
};
//选择仓房
const onStoreHouseConfirm = (val) => {
  storeHouseName.value = val.text;
  storehouseId.value = val.value;
  showStoreHousePicker.value = false;
  onSearch();
};
//弹出空仓验收
const onShowPopup = (item) => {
  info.value = item;
  showPopup.value = true;
};
const onSearch = () => {
  finished.value = false;
  list.value = [];
  pagination.page = 0;
  pagination.total = 0;
  listRef.value?.check();
};
const getList = async () => {
  try {
    const { items, page, total } = await getEmptyStoreList({
      storeId: storeId.value,
      storeHouseId: storehouseId.value,
      page: pagination.page + 1,
      size: pagination.size,
    });
    list.value.push(...items);
    pagination.page = page;
    pagination.total = total;
    // 加载状态结束
    isLoading.value = false;

    // 数据全部加载完成
    if (list.value.length >= total) {
      finished.value = true;
    }
  } catch {
    isLoading.value = false;
    finished.value = true;
  }
};
const onRefresh = () => {
  if (refreshLoading.value) {
    pagination.page = 0;
    pagination.total = 0;
    list.value = [];
    refreshLoading.value = false;
  }
  finished.value = false;
  isLoading.value = true;
  getList();
};
const onClose = (value) => {
  showPopup.value = value;
};
onMounted(() => {
  loadStores();
});
</script>

<style lang="scss" scoped>
.store-item {
  background: #ffffff;
  margin-top: 10px;
  padding: 16px;
}
</style>
