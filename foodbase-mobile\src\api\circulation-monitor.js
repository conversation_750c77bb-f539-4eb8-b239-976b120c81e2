import { reserverPurchase } from '@/utils/request';
import { getArrItemNum } from '@/utils/tools'


export async function getUserWatchInfo(params) {
    return reserverPurchase().get('/point/getByUser', { params });
}
// 批量添加监测点配置
export function handleAddPriceBatch(data) {
    return reserverPurchase().post('/price/batch/add', data)
}

// 批量编辑
export function handleUpdatePriceBatch(data) {
    return reserverPurchase().post('/price/batch/edit', data)
}

// 批量提交
export function handleSubmitPriceBatch(data) {
    return reserverPurchase().post('/price/batch/submit', data)
}

// 获取监测点列表(全部数据)
export function getWatchConfigSelect(params) {
    return reserverPurchase().get('/point/list', { params }).then((res) => {
        const nameList = res.map((i) => {
            return i.name
        })
        const timeArr = getArrItemNum(nameList)
        res = res.map((i) => {
            if (timeArr[i.name] > 1) {
                return {
                    name: `${i.name}(${i.countyName || i.cityName || i.provinceName})`,
                    id: i.id,
                }
            }
            else {
                return { ...i }
            }
        })
        return res
    })
}

// 监测点上报记录-列表（分页查询）
export function getDataMaintainList(params) {
    return reserverPurchase().get('/price/record/page', {
        params
    })
}

// 监测点上报记录-列表 根据创建时间查询（分页查询）
export function getDataMaintainRecord(params) {
    return reserverPurchase().get('/price/app/record/page', {
        params
    })
}


// 监测点上报记录-根据id获取粮食价格采集数据
export function getPriceByMonitorPointId(params) {
    return reserverPurchase().get('/price/list', {
        params
    })
}

// 监测点上报记录-驳回
export function rejectDataMaintain(data) {
    return reserverPurchase().post('/price/reject', data)
}



// 监测点上报记录-发布
export function publishDataMaintain(data) {
    return reserverPurchase().post('/price/publish', data)
}
  