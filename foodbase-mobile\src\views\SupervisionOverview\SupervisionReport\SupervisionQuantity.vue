<template>
  <div class="supervision-quantity">
    <div class="item">
      <SvgIcon name="reserve" />
      <span class="name">储备规模</span>
      <HFixedNumber class="value" :fraction-digits="0">{{ props.data.reserveSum }}</HFixedNumber>
      <span class="unit">吨</span>
    </div>
    <div class="item">
      <SvgIcon name="stock" />
      <span class="name">储备库存</span>
      <HFixedNumber class="value" :fraction-digits="0">
        {{ props.data.expectStockSum }}
      </HFixedNumber>
      <span class="unit">吨</span>
    </div>
    <div class="item">
      <SvgIcon name="stock" />
      <span class="name">今年已轮入</span>
      <HFixedNumber class="value" :fraction-digits="0">
        {{ props.data.rollInSum }}
      </HFixedNumber>
      <span class="unit">吨</span>
    </div>
    <div class="item">
      <SvgIcon name="stock" />
      <span class="name">今年已轮出</span>
      <HFixedNumber class="value" :fraction-digits="0">
        {{ props.data.rollOutSum }}
      </HFixedNumber>
      <span class="unit">吨</span>
    </div>
    <div class="item">
      <SvgIcon name="check-result" />
      <span class="name">清查结果</span>
      <HFixedNumber class="value" :fraction-digits="0">
        {{ props.data.actualStockSum }}
      </HFixedNumber>
      <span class="unit">吨</span>
    </div>
    <div class="result-desc">
      <Icon name="info" />
      <span>
        清查结果/储备规模 {{ props.data.stockReach ? '&gt;' : '&lt;' }} 70%，
        {{ props.data.resultName }}
      </span>
    </div>
  </div>
</template>

<script setup>
import SvgIcon from '@/components/SvgIcon/SvgIcon';
import { Icon } from 'vant';
import HFixedNumber from '@/components/HFixedNumber/HFixedNumber';

const props = defineProps({
  data: {
    type: Object,
    default: () => ({}),
  },
});
</script>

<style scoped lang="scss">
.supervision-quantity {
  padding: 10px 30px;
  .item {
    display: flex;
    line-height: 32px;
    align-items: center;
    margin: 8px 0;

    .svg-icon {
      font-size: 18px;
    }

    .name {
      font-size: 18px;
      font-weight: bold;
      color: var(--van-gray-7);
      margin-left: 4px;
    }

    .value {
      font-size: 24px;
      font-weight: bold;
      color: #1492ff;
      margin-left: auto;
    }

    .unit {
      font-size: 16px;
      font-weight: 500;
      color: var(--van-gray-8);
      margin-left: 6px;
    }
  }

  .result-desc {
    font-size: 16px;
    line-height: 22px;
    display: flex;
    align-items: center;
    .van-icon {
      font-size: 18px;
      color: #ffb532;
      margin-right: 4px;
    }
  }
}
</style>
