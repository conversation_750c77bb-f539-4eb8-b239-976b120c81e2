<template>
  <div class="job-appointment-detail page-warpper">
    <div class="job-appointment-detail-warpper">
      <HCard class="info-card" title="基本信息" bgColor="#F7F8FA">
        <Cell title="预约号" :value="form.name" title-class="bold-text"></Cell>
        <Cell title="预约状态" :value="form.bookingerStatusName" title-class="bold-text"></Cell>
        <Cell title="预约客户/单位" :value="form.bookinger" title-class="bold-text"></Cell>
        <Cell title="预约电话" :value="form.bookingerTel" title-class="bold-text"></Cell>
        <Cell title="预约时间" title-class="bold-text">
          <template #value>
            {{ form.bookingerDate ? dayjs(form.bookingerDate).format('YYYY-MM-DD') : '' }}
          </template>
        </Cell>
        <Cell title="预约业务" :value="form.buzType" title-class="bold-text"></Cell>
        <Cell
          title="粮油品种"
          :value="form.detail?.foodCategoryName"
          title-class="bold-text"
        ></Cell>
        <Cell title="数量（吨）" title-class="bold-text">
          <template #value>
            <HFixedNumber :ratio="1000" :fraction-digits="0">
              {{ form.detail?.counter }}
            </HFixedNumber>
          </template>
        </Cell>
        <Cell title="出入库凭证" :value="form.detail?.voucherId" title-class="bold-text"></Cell>
        <Cell
          title="运输方式"
          :value="form.detail?.typeShippingName"
          title-class="bold-text"
        ></Cell>
        <Cell
          title="运输车辆"
          :value="form.detail?.transportVehicleNo"
          title-class="bold-text"
        ></Cell>
        <Cell
          title="运输司机"
          :value="form.detail?.transportVehicleDriver"
          title-class="bold-text"
        ></Cell>
        <Cell
          title="联系电话"
          :value="form.detail?.transportVehicleTel"
          title-class="bold-text"
        ></Cell>
        <Cell title="身份证号" :value="form.detail?.idcard" title-class="bold-text"></Cell>
      </HCard>
      <HCard class="info-card" title="审核" bgColor="#F7F8FA">
        <Field name="radio" label="审批结果" class="slot-field">
          <template #input>
            <RadioGroup v-model="bookingerAudit" direction="horizontal">
              <Radio :name="2">同意</Radio>
              <Radio :name="3">拒绝</Radio>
            </RadioGroup>
          </template>
        </Field>
        <Field
          v-model="bookingerCause"
          type="textarea"
          autosize
          label="反馈："
          maxlength="200"
          show-word-limit
          border
          class="border-field"
          label-width="3.2em"
        ></Field>
      </HCard>
    </div>
    <div class="bottom-warpper">
      <div class="buttons">
        <Button class="cancel-button" style="width: 100px" @click="onClose"> 取消 </Button>
        <Button class="next-button" style="width: 100px" @click="onApproval"> 确定 </Button>
      </div>
    </div>
  </div>
</template>

<script>
import { Cell, Field, RadioGroup, Radio, Toast } from 'vant';
import { HCard, HFixedNumber } from '@/components';
import { ref, onMounted } from 'vue';
import { useRouter, useRoute } from 'vue-router';
import { approvalAppointment } from '@/api/in-out-manage';
import dayjs from 'dayjs';

export default {
  components: { Cell, Field, RadioGroup, Radio, HCard, HFixedNumber },
  setup() {
    const router = useRouter();
    const route = useRoute();
    const form = ref({});
    const bookingerAudit = ref(2);
    const bookingerCause = ref('');

    onMounted(() => {
      initData();
    });

    const initData = async () => {
      const id = route.params.id;
      console.log(id);
      console.log(form.value);
    };
    const onClose = () => {
      router.push({
        name: 'JobAppointment',
      });
    };
    const onApproval = async () => {
      const id = route.params.id + '';
      try {
        await approvalAppointment({
          idStr: id,
          bookingerAudit: bookingerAudit.value,
          bookingerCause: bookingerCause.value,
        });
        Toast.success('已保存');
        onClose();
      } catch (error) {
        Toast.fail('保存失败');
      }
    };

    return {
      form,
      bookingerAudit,
      bookingerCause,
      onClose,
      onApproval,
      dayjs,
    };
  },
};
</script>

<style lang="scss" scoped>
@import '@/views/InoutManage/style/index';
.job-appointment-detail-warpper {
  margin-bottom: 120px;
}
.info-card {
  ::v-deep(.bold-text) {
    font-weight: bold;
  }
}
</style>
