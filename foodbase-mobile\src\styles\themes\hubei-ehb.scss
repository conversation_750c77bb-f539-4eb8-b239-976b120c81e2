@import '@/styles/themes/hubei.scss';

html, body {
  --van-tabs-bottom-bar-color: #5b81e2;
}

.h-picker.bg-gray {
  background-color: var(--van-gray-1)!important;
  border-radius: 3px!important;
}

.h-picker-trigger, .area-picker-trigger {
  // border-top-width: 0!important;
  // border-left-width: 0!important;
  // border-right-width: 0!important;
  // border-radius: 0!important;
  // border-color: var(--van-gray-3)!important;
  border-width: 0!important;

  .van-icon.van-icon-arrow-down {
    font-size: 14px!important;
    margin-left: 6px;
  }
}

.van-row.row-line {
  .van-col:not(:first-child) {
    position: relative;
    &::before {
      content: '';
      display: block;
      width: 1px;
      height: 14px;
      position: absolute;
      left: -2px;
      background-color: var(--van-gray-3);
      top: 50%;
      transform: translateY(-50%);
    }
  }
  border-bottom: solid 1px var(--van-gray-3);
}

.empty-camera {
  .van-empty__image {
    position: relative;
    img {
      display: none;
    }
    &::before {
      content: '';
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      background-image: url('~@/assets/theme/hubei-ehb/empty.png');
    }
  }
}

.van-search {
  .van-search__content {
    background-color: #fff!important;
    border-bottom: solid 1px var(--van-gray-3)!important;
  }
}