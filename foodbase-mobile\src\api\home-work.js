import { manualRequest } from '@/utils/request';
import { reserveRequest } from '@/utils/request';

export function getStoreInfoByUser(params) {
  return reserveRequest().get('/api/store/getStoreInfoByUser', { params });
}

// 作业申请
export async function startHomeworkAsk(params, config) {
  return reserveRequest().post('/api/jobApply/add', params, config);
}

// 作业申请-审批提交
export async function saveApproveTask(params, config) {
  return reserveRequest().post(`/api/activiti/task/submit?businessId=${params.businessId}&coder=${params.coder}`, params, config);
}

// 作业申请审批
export async function startApprove(params, config) {
  return reserveRequest().post('/api/activiti/task/approve', params, { params, ...config});
}

// 作业申请记录
export async function getHomeworkRecord(params) {
  return reserveRequest().get('/api/jobApply/list', { params })
    .then(res => {
      return {
        data: {
          obj: {
            list: res || [],
            total: res?.length || 0,
          }
        }
      }
    });
}

// 控温记录
export async function getTemperatureRecord(formData) {
  return manualRequest().post('/MANUAL/manualWindJobAction!getTempLogs.action', formData, { noErrorHandler: true });
}

// 通风记录
export async function getVentilationRecord(formData) {
  return manualRequest().post('/MANUAL/manualWindJobAction!getWindLogs.action', formData, { noErrorHandler: true });
}

// 气调日志
export async function getAirConditionRecord(formData) {
  return manualRequest().post('/MANUAL/manualWindJobAction!getGasLogs.action', formData, { noErrorHandler: true });
}