import { getAreaTree } from '@/api/area';

export default {
  namespaced: true,
  state: {
    areaTree: null,
    areaTreeWidthAllArea: null,
    areaMap: new Map(),
  },
  getters: {},
  mutations: {
    setAreaTree(state, value) {
      state.areaTree = value;
    },
    setAreaTreeWidthAllArea(state, value) {
      state.areaTreeWidthAllArea = value;
    },
    setAreaMap(state, areaTree) {
      const stack = [...areaTree];
      const areaMap = new Map();
      while (stack.length) {
        const item = stack.pop();
        areaMap.set(item.value, item);
        if (item.children?.length) {
          stack.push(...item.children);
        }
      }
      state.areaMap = areaMap;
    },
  },
  actions: {
    async load({ commit, rootGetters }) {
      const areaCode = rootGetters['user/userAreaCode'];
      if(!areaCode){
        return
      }
      const list = (await getAreaTree(areaCode)) || [];

      function transformAreaTree(tree, addAllArea = false) {
        return tree.map((it) => {
          const item = {
            value: it.coder,
            text: it.name,
          };
          if (it.children?.length) {
            item.children = transformAreaTree(it.children, addAllArea);
            if (addAllArea) {
              item.children.unshift({
                value: item.value,
                text: '全' + item.text.slice(-1),
              });
            }
          }
          return item;
        });
      }
      const areaTree = transformAreaTree(list);
      const areaTreeWithAllArea = transformAreaTree(list, true);
      commit('setAreaTree', areaTree);
      commit('setAreaTreeWidthAllArea', areaTreeWithAllArea);
      commit('setAreaMap', areaTree);
    },
  },
};
