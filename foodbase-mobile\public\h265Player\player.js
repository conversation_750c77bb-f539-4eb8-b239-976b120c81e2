//Decoder states.
const decoderStateIdle = 0;
const decoderStateInitializing = 1;
const decoderStateReady = 2;
const decoderStateFinished = 3;

//Player states.
const playerStateIdle = 0;
const playerStatePlaying = 1;
const playerStatePausing = 2;

//Seek states.
const SeekStatePause = 0; //��ͣʱseek
const SeekStatePlay = 1; //����ʱseek

//Constant.
const maxBufferTimeLength = 5.0;
const downloadSpeedByteRateCoef = 2.0;
const minWaitHeaderLength = 10 * 1024; //����Ҫ�յ���ֵ�Ŵ򿪽�����
const waitMaxMsec = 400; //������δ���㣬��ȴ��ó�ʱʱ�䣬�ͳ��Դ򿪽�����
const playMaxRetryTimes = 5; //����ʧ�ܵ�������Դ���
String.prototype.startWith = function(str) {
  var reg = new RegExp('^' + str);
  return reg.test(this);
};

function FileInfo(url) {
  this.url = url;
  this.size = 0;
  this.offset = 0;
  this.chunkSize = 524288;
}

function Player() {
  this.fileInfo = null;
  this.pcmPlayer = null;
  this.canvas = null;
  this.canvalWrap = null;
  this.webglPlayer = null;
  this.downloadWorker = null;
  this.decodeWorker = null;
  this.callback = null;
  this.successCallBack = null;
  this.waitHeaderLength = 524288;
  this.duration = 0;
  this.pixFmt = 0;
  this.videoWidth = 0;
  this.videoHeight = 0;
  this.yLength = 0;
  this.uvLength = 0;
  this.beginTimeOffset = 0;
  this.decoderState = decoderStateIdle;
  this.playerState = playerStateIdle;
  this.decoding = false;
  this.decodeInterval = 5;
  //this.videoRendererTimer = null;
  this.downloadTimer = null;
  this.chunkInterval = 200;
  this.downloadSeqNo = 0;
  this.downloading = false;
  this.downloadProto = kProtoHttp;
  this.timeLabel = null;
  this.timeTrack = null;
  this.trackTimer = null;
  this.trackTimerInterval = 40;
  this.displayDuration = '00:00:00';
  this.audioEncoding = '';
  this.audioChannels = 0;
  this.audioSampleRate = 0;
  this.justReplayed = false; //�Ƿ�����²���(��ͣ�󲥷ţ��򲥷�ʱseek�����λ�����¿�ʼ����)
  this.seeking = false; // Flag to preventing multi seek from track.
  this.seekStatus = SeekStatePause; //seek״̬���ñ�����δʹ��
  this.justSeeked = false; // Flag to preventing multi seek from ffmpeg.
  this.urgent = false;
  this.seekWaitLen = 524288; // Default wait for 512K, will be updated in onVideoParam.
  this.seekReceivedLen = 0;
  this.loadingDiv = null;
  this.buffering = false;
  this.frameBuffer = [];
  this.isStream = false;
  this.streamReceivedLen = 0;
  this.firstAudioFrame = true;
  this.fetchController = null;
  this.streamPauseParam = null;
  this.streamDone = 0; //���Ƿ������(flv �ļ�)
  this.logger = new Logger('Player');
  this.initDecoder = 0; //�Ƿ��ѳ�ʼ��������
  this.lastDisplayTimestamp = 0; //��һ����Ƶ֡����ʾʱ���
  this.firstCheckOpenDecoderMsec = 0; //��һ�ο�ʼ�жϴ򿪽������ĺ���ʱ���
  this.videoTimestamp = 0; //ʵʱ��Ƶ��ʾʱʱ���
  this.seekResume = false; //����seekʱ�Ƿ���Resume��
  this.videoStartMsec = 0;
  this.codec_id = 0;
  this.onlyGetCodeID = false; //�Ƿ�ֻ��ȡ����ID(CodeID)
  this.latestDisplayMsec = 0; //���һ����ʾ֡��ʱ���
  this.curWaitMaxMsec = waitMaxMsec; //������δ���㣬��ȴ��ó�ʱʱ�䣬�ͳ��Դ򿪽�����,��ʧ�ܣ��򷭱�
  this.openDecoderTimes = 0; //���������򿪽���������
  this.url = '';
  this.rev_Bs = 0; //ÿ���յ����ֽ���
  this.last_stat_msec = 0; //�ϴ�ͳ�ƹ����ʵĺ���ʱ���ֵ
  this.rev_kbs = 0; //ÿ���յ���ǧ�ֽ���(KB/S)������
  /*this.initDownloadWorker();
    this.initDecodeWorker();*/
}

Player.prototype.initDownloadWorker = function() {
  var self = this;
  this.downloadWorker = new Worker('./h265Player/downloader.js');
  this.downloadWorker.onmessage = function(evt) {
    var objData = evt.data;
    switch (objData.t) {
      case kGetFileInfoRsp:
        self.onGetFileInfo(objData.i);
        break;
      case kFileData:
        self.onFileData(objData.d, objData.s, objData.e, objData.q);
        break;
    }
  };
};

Player.prototype.initDecodeWorker = function() {
  var self = this;
  this.decodeWorker = new Worker('./h265Player/decoder.js');
  this.decodeWorker.onmessage = function(evt) {
    var objData = evt.data;
    switch (objData.t) {
      case kInitDecoderRsp:
        self.onInitDecoder(objData);
        break;
      case kOpenDecoderRsp:
        self.onOpenDecoder(objData);
        break;
      case kVideoFrame:
        self.onVideoFrame(objData);
        break;
      case kAudioFrame:
        self.onAudioFrame(objData);
        break;
      case kDecodeFinishedEvt:
        self.onDecodeFinished(objData);
        break;
      case kRequestDataEvt:
        self.onRequestData(objData.o, objData.a);
        break;
      case kSeekToRsp:
        self.onSeekToRsp(objData.r);
        break;
    }
  };
};

Player.prototype.getCodeID = function(url, waitHeaderLength, isStream) {
  /*this.logger.logInfo("getCodeID " + url + ", isStream: "+isStream);

    var ret = {
        e: 0,
        m: "Success"
    };

    var success = true;
    do {
		this.onlyGetCodeID = true;
		this.seeking = false;
		this.seekStatus = SeekStatePlay;
		this.initDecoder = 0;
	    this.duration = 0;

	    this.firstCheckOpenDecoderMsec = 0;

        if (!url) {
            ret = {
                e: -1,
                m: "Invalid url"
            };
            success = false;
            this.logger.logError("[ER] playVideo error, url empty.");
            break;
        }

        if (!this.decodeWorker) {
			this.initDecodeWorker();
			if (!this.decodeWorker) {
	            ret = {
	                e: -4,
	                m: "Decoder not initialized"
	            };
	            success = false;
	            this.logger.logError("[ER] Decoder not initialized.");
	            break
    		}
        }

        if (url.startWith("ws://") || url.startWith("wss://")) {
            this.downloadProto = kProtoWebsocket;
        } else {
            this.downloadProto = kProtoHttp;
        }
		if(!this.fileInfo){
        	this.fileInfo = new FileInfo(url);
		}
        this.waitHeaderLength = waitHeaderLength || this.waitHeaderLength;
		this.playerState = playerStatePlaying;
		this.isStream = isStream;
		this.streamDone = 0;
        if (!this.isStream) {
			if (!this.downloadWorker) {
				this.initDownloadWorker();
		    		if (!this.downloadWorker) {
		            ret = {
		                e: -3,
		                m: "Downloader not initialized"
		            };
		            success = false;
		            this.logger.logError("[ER] Downloader not initialized.");
		            break
	    		}
        	}
            var req = {
                t: kGetFileInfoReq,
                u: url,
                p: this.downloadProto
            };
            this.downloadWorker.postMessage(req);
        } else {
            this.requestStream(url);
            this.onGetFileInfo({
                sz: -1,
                st: 200
            });
        }

        this.buffering = true;
    } while (false);
    return ret;
*/

  return this.codec_id;
};

Player.prototype.play = function(
  url,
  canvas,
  callback,
  waitHeaderLength,
  isStream,
  successCallBack,
  canvalWrap,
) {
  this.logger.logInfo('Play ' + url + '.');

  var ret = {
    e: 0,
    m: 'Success',
  };

  var success = true;
  do {
    this.seeking = false;
    this.seekStatus = SeekStatePlay;
    this.latestDisplayMsec = 0;
    //this.logger.logInfo("play, playerState: "+this.playerState);
    if (this.playerState == playerStatePausing) {
      //only flv file
      ret = this.resume();
      break;
    }
    if (this.playerState == playerStatePlaying) {
      break;
    }
    this.initDecoder = 0;
    this.duration = 0;
    this.firstCheckOpenDecoderMsec = 0;

    if (!url) {
      ret = {
        e: -1,
        m: 'Invalid url',
      };
      success = false;
      this.logger.logError('[ER] playVideo error, url empty.');
      break;
    }

    if (!canvas) {
      ret = {
        e: -2,
        m: 'Canvas not set',
      };
      success = false;
      this.logger.logError('[ER] playVideo error, canvas empty.');
      break;
    }

    if (!this.decodeWorker) {
      this.initDecodeWorker();
      if (!this.decodeWorker) {
        ret = {
          e: -4,
          m: 'Decoder not initialized',
        };
        success = false;
        this.logger.logError('[ER] Decoder not initialized.');
        break;
      }
    }

    if (url.startWith('ws://') || url.startWith('wss://')) {
      this.downloadProto = kProtoWebsocket;
    } else {
      this.downloadProto = kProtoHttp;
    }
    if (!this.fileInfo) {
      this.fileInfo = new FileInfo(url);
    }
    this.url = url;
    this.canvas = canvas;
    this.callback = callback;
    this.successCallBack = successCallBack;
    this.canvalWrap = canvalWrap;
    this.waitHeaderLength = waitHeaderLength || this.waitHeaderLength;
    //this.logger.logInfo("[test] waitHeaderLength: "+ this.waitHeaderLength+".");
    this.playerState = playerStatePlaying;
    this.isStream = isStream;
    this.streamDone = 0;
    this.videoTimestamp = 0;
    this.startTrackTimer();
    this.displayLoop();

    //var playCanvasContext = playCanvas.getContext("2d"); //If get 2d, webgl will be disabled.
    /*this.webglPlayer = new WebGLPlayer(this.canvas, {
            preserveDrawingBuffer: false
        });*/
    if (!this.webglPlayer) {
      this.webglPlayer = new WebGLPlayer(
        this.canvas,
        {
          alpha: true,
          depth: true,
          stencil: false,
          antialias: false,
          premultipliedAlpha: true,
          preserveDrawingBuffer: false,
          powerPreference: 'default',
          failIfMajorPerformanceCaveat: false,
          majorVersion: 1,
          minorVersion: 0,
          enableExtensionsByDefault: 1,
          explicitSwapControl: 0,
          proxyContextToMainThread: 0,
          renderViaOffscreenBackBuffer: 0,
        },
        this.canvalWrap,
      );
    }

    if (!this.isStream) {
      if (!this.downloadWorker) {
        this.initDownloadWorker();
        if (!this.downloadWorker) {
          ret = {
            e: -3,
            m: 'Downloader not initialized',
          };
          success = false;
          this.logger.logError('[ER] Downloader not initialized.');
          break;
        }
      }
      var req = {
        t: kGetFileInfoReq,
        u: url,
        p: this.downloadProto,
      };
      this.downloadWorker.postMessage(req);
    } else {
      this.requestStream(url);
      this.onGetFileInfo({
        sz: -1,
        st: 200,
      });
    }

    var self = this;
    this.registerVisibilityEvent(function(visible) {
      if (visible) {
        self.seekTo(0);
        self.resume();
      } else {
        self.logger.logInfo('play: invisible, will pause');
        self.pause();
      }
    });

    this.buffering = true;
    this.showLoading();

    //this.logger.logInfo("play end");
  } while (false);

  return ret;
};

Player.prototype.pauseStream = function() {
  if (this.playerState != playerStatePlaying) {
    var ret = {
      e: -1,
      m: 'Not playing',
    };
    return ret;
  }

  this.streamPauseParam = {
    url: this.fileInfo.url,
    canvas: this.canvas,
    callback: this.callback,
    waitHeaderLength: this.waitHeaderLength,
  };

  this.logger.logInfo('Stop in stream pause.');
  this.stop();

  var ret = {
    e: 0,
    m: 'Success',
  };

  return ret;
};

Player.prototype.pause = function(fromSeek) {
  this.onlyGetCodeID = false;
  this.clearCodeRateInfo(); //��ʼ���������ͳ����Ϣ
  if (this.isStream) {
    return this.pauseStream();
  }
  var ret = {
    e: 0,
    m: 'Success',
  };
  if (this.playerState == playerStateIdle) {
    return ret;
  }
  this.logger.logInfo('Pause.');

  if (this.playerState != playerStatePlaying && fromSeek != true) {
    ret = {
      e: -1,
      m: 'Not playing',
    };
    return ret;
  }

  //暂停前先更新一次
  this.updateTrackTime();
  //Stop track timer.
  this.stopTrackTimer();

  //Pause video rendering and audio flushing.
  if (fromSeek != true) {
    this.playerState = playerStatePausing;
    this.seekStatus = SeekStatePause;
    //this.logger.logInfo("Pause, SeekStatePause.");
  }
  //Pause audio context.
  if (this.pcmPlayer) {
    this.pcmPlayer.pause();
  }

  //Pause decoding.
  this.pauseDecoding();

  return ret;
};

Player.prototype.resumeStream = function() {
  if (this.playerState != playerStateIdle || !this.streamPauseParam) {
    var ret = {
      e: -1,
      m: 'Not pausing',
    };
    return ret;
  }

  this.logger.logInfo('Play in stream resume.');
  this.play(
    this.streamPauseParam.url,
    this.streamPauseParam.canvas,
    this.streamPauseParam.callback,
    this.streamPauseParam.waitHeaderLength,
    true,
  );
  this.streamPauseParam = null;

  var ret = {
    e: 0,
    m: 'Success',
  };

  return ret;
};

Player.prototype.resume = function(fromSeek) {
  this.onlyGetCodeID = false;
  if (this.isStream) {
    return this.resumeStream();
  }
  if (!this.fileInfo || this.fileInfo.size == 0 || this.fileInfo.offset == 0) {
    //�����ļ�δ��ȡ��
    var ret = {
      e: -1,
      m: 'fileInfo.size is 0',
    };
    return ret;
  }

  this.logger.logInfo('Resume, fromSeek:' + fromSeek);

  if (this.playerState != playerStatePausing && fromSeek != true) {
    var ret = {
      e: -1,
      m: 'Not pausing',
    };
    return ret;
  }

  if (fromSeek != true) {
    //true or false or ��ֵ�Ļ�������undefined��
    //Resume audio context.
    if (this.pcmPlayer) {
      this.pcmPlayer.resume();
    }
    this.seekStatus = SeekStatePlay;
    this.playerState = playerStatePlaying;
    this.decoderState = decoderStateReady;
    this.urgent = false;
    this.seeking = false;
  } else {
    this.seekResume = true;
  }
  if (this.playerState == playerStatePlaying) {
    this.justReplayed = true;
  } else {
    this.justReplayed = false;
  }
  //this.logger.logInfo("Resume, seekStatus:"+this.seekStatus);

  //If there's a flying video renderer op, interrupt it.
  /*if (this.videoRendererTimer != null) {
        clearTimeout(this.videoRendererTimer);
        this.videoRendererTimer = null;
    }*/

  //Restart video rendering and audio flushing.

  //Restart decoding.
  this.startDecoding();

  var ret = {
    e: 0,
    m: 'Success',
  };
  return ret;
};

//ֹͣ��initPara:�Ƿ��ʼ��play���������ر���
Player.prototype.stop = function(initPara = true) {
  this.logger.logInfo('Stop.');
  this.clearCodeRateInfo(); //��ʼ���������ͳ����Ϣ
  if (this.playerState == playerStateIdle) {
    var ret = {
      e: -1,
      m: 'Not playing',
    };
    return ret;
  }

  /*if (this.videoRendererTimer != null) {
        clearTimeout(this.videoRendererTimer);
        this.videoRendererTimer = null;
        this.logger.logInfo("Video renderer timer stopped.");
    }*/
  if (this.fetchController) {
    this.fetchController.abort();
    this.fetchController = null;
    this.logger.logInfo('fetchController.abort, stop stream.');
  }

  if (this.downloadWorker) {
    this.downloadWorker.terminate();
    this.downloadWorker = null;
  }

  this.stopDownloadTimer();
  this.stopTrackTimer();
  this.hideLoading();

  this.fileInfo = null;
  if (initPara) {
    //��ȫֹͣʱ�����ʼ������
    this.canvas = null;
    this.openDecoderTimes = 0; //�ɹ���Ϊ0
  }

  this.webglPlayer = null;
  this.callback = null;
  this.duration = 0;
  this.pixFmt = 0;
  this.videoWidth = 0;
  this.videoHeight = 0;
  this.yLength = 0;
  this.uvLength = 0;
  this.beginTimeOffset = 0;
  this.decoderState = decoderStateIdle;
  this.playerState = playerStateIdle;
  //this.logger.logInfo("stop, playerState: "+this.playerState);
  this.decoding = false;
  this.frameBuffer = [];
  this.buffering = false;
  this.streamReceivedLen = 0;
  this.firstAudioFrame = true;
  this.urgent = false;
  this.seekReceivedLen = 0;

  if (this.pcmPlayer) {
    this.pcmPlayer.destroy();
    this.pcmPlayer = null;
    this.logger.logInfo('Pcm player released.');
  }

  if (this.timeTrack) {
    this.timeTrack.value = 0;
  }
  if (this.timeLabel) {
    this.timeLabel.innerHTML = this.formatTime(0) + '/' + this.displayDuration;
  }

  /*this.logger.logInfo("Closing decoder.");
    this.decodeWorker.postMessage({
        t: kCloseDecoderReq
    });

	//decodeWorker���߳��ڲ�����ʼ���������߳�
    this.logger.logInfo("Uniniting decoder.");
    this.decodeWorker.postMessage({
        t: kUninitDecoderReq
    });*/

  //ֱ�ӹر��̸߳���
  if (this.decodeWorker) {
    this.decodeWorker.terminate();
    this.decodeWorker = null;
  }

  if (!this.webglPlayer) {
    delete this.webglPlayer;
    this.webglPlayer = null;
  }
  var ret = {
    e: 0,
    m: 'Success',
  };
  return ret;
};

Player.prototype.seekTo = function(ms) {
  if (this.isStream || !this.fileInfo || this.fileInfo.size == 0 || this.fileInfo.offset == 0) {
    //�����ļ�δ��ȡ��
    return;
  }
  this.seeking = true;
  if (this.playerState == playerStatePausing) {
    this.seekStatus = SeekStatePause;
  } else if (this.playerState == playerStatePlaying) {
    this.seekStatus = SeekStatePlay;
  } else if (this.playerState == decoderStateIdle) {
    //this.logger.logInfo("Please play at first.");
    return;
  }
  this.decoderState = decoderStateReady;
  //this.logger.logInfo("seekStatus: "+this.seekStatus);
  // Pause playing.
  //this.logger.logInfo("seekTo: will pause");
  this.pause(true);
  this.seekResume = false;
  // Stop download.
  this.stopDownloadTimer();

  // Clear frame buffer.
  this.frameBuffer.length = 0;

  // Reset begin time offset.
  this.beginTimeOffset = ms / 1000;
  this.logger.logInfo('seekTo beginTimeOffset: ' + this.beginTimeOffset + ', ms: ' + ms);

  this.justSeeked = true;
  this.urgent = true;
  this.seekReceivedLen = 0;
  //this.startBuffering();

  // Request decoder to seek.
  this.decodeWorker.postMessage({
    t: kSeekToReq,
    ms: ms,
  });
};

Player.prototype.fullscreen = function() {
  if (this.webglPlayer) {
    this.webglPlayer.fullscreen();
  }
};

Player.prototype.exitfullscreen = function() {
  if (this.webglPlayer) {
    this.webglPlayer.exitfullscreen();
  }
};

Player.prototype.getState = function() {
  return this.playerState;
};

Player.prototype.setTrack = function(timeTrack, timeLabel) {
  this.timeTrack = timeTrack;
  this.timeLabel = timeLabel;

  if (this.timeTrack) {
    var self = this;
    this.timeTrack.oninput = function() {
      //if (!self.seeking) {
      //self.logger.logInfo("oninput: timeTrack.value: "+self.timeTrack.value+", seeking:"+self.seeking);
      self.seekTo(self.timeTrack.value);
      //}
    };
    this.timeTrack.onchange = function() {
      if (!self.seeking) {
        //self.logger.logInfo("onchange: timeTrack.value: "+self.timeTrack.value+", seeking:"+self.seeking);
        self.seekTo(self.timeTrack.value);
      }
    };
  }
};

Player.prototype.onGetFileInfo = function(info) {
  //this.logger.logInfo("onGetFileInfo, playerState: " + this.playerState);
  if (this.playerState == playerStateIdle) {
    return;
  }

  this.logger.logInfo('Got file size rsp:' + info.st + ' size:' + info.sz + ' byte.');
  if (info.st == 200) {
    this.fileInfo.size = Number(info.sz);
    this.logger.logInfo('Initializing decoder.');
    var req = {
      t: kInitDecoderReq,
      s: this.fileInfo.size,
      c: this.fileInfo.chunkSize,
    };
    this.decodeWorker.postMessage(req);

    if (this.successCallBack !== null) {
      setTimeout(() => {
        this.successCallBack();
      }, 1000);
    }
  } else {
    this.reportPlayError(-1, info.st);
  }
};

Player.prototype.onFileData = function(data, start, end, seq) {
  this.logger.logInfo('onFileData: Got data bytes=' + start + '-' + end + '.');
  this.downloading = false;
  var len = end - start + 1;
  this.updateCodeRateInfo(len); //�����������ͳ����Ϣ
  if (this.playerState == playerStateIdle) {
    return;
  }

  if (seq != this.downloadSeqNo) {
    this.logger.logInfo('onFileData err: seq ' + seq + ' != downloadSeqNo ' + this.downloadSeqNo);
    return; // Old data.
  }
  //this.logger.logInfo("onFileData, playerState: "+this.playerState+", this.seeking: "+this.seeking);
  if (this.seeking) {
    if (!this.seekResume) {
      //δ�ָ��������չ������ݾͻָ�
      this.seekReceivedLen += data.byteLength;
      let left = this.fileInfo.size - this.fileInfo.offset;
      let seekWaitLen = Math.min(left, this.seekWaitLen);
      if (this.seekReceivedLen >= seekWaitLen) {
        this.logger.logInfo('Resume in seek now');
        setTimeout(() => {
          this.resume(true);
        }, 0);
      }
    }
  }

  this.fileInfo.offset += len;
  //������ʾ���ؽ�����,this.duration��ӦtimeTrack.max
  let downloadDiff = (this.fileInfo.offset * this.duration) / this.fileInfo.size; //����ֱ����timeTrack.max��������duration
  //this.logger.logInfo("onFileData: fileInfo.offset=" + this.fileInfo.offset+", downloadDiff="+downloadDiff+", decoderState="+this.decoderState+", duration="+this.duration+", fileInfo.size="+this.fileInfo.size);

  var objData = {
    t: kFeedDataReq,
    d: data,
  };
  this.decodeWorker.postMessage(objData, [objData.d]);

  switch (this.decoderState) {
    case decoderStateIdle:
      this.onFileDataUnderDecoderIdle();
      break;
    case decoderStateInitializing:
      this.onFileDataUnderDecoderInitializing();
      break;
    case decoderStateReady:
      this.onFileDataUnderDecoderReady();
      break;
  }

  if (this.urgent) {
    setTimeout(() => {
      this.downloadOneChunk();
    }, 0);
  }
};

Player.prototype.onFileDataUnderDecoderIdle = function() {
  if (
    this.fileInfo.offset >= this.waitHeaderLength ||
    (!this.isStream && this.fileInfo.offset == this.fileInfo.size)
  ) {
    this.logger.logInfo('Opening decoder for file.');
    this.decoderState = decoderStateInitializing;
    var req = {
      t: kOpenDecoderReq,
    };
    this.decodeWorker.postMessage(req);
  }

  this.downloadOneChunk();
};

Player.prototype.onFileDataUnderDecoderInitializing = function() {
  this.downloadOneChunk();
};

Player.prototype.onFileDataUnderDecoderReady = function() {
  this.downloadOneChunk();
};

Player.prototype.onInitDecoder = function(objData) {
  this.initDecoder = 1; //�������ѳ�ʼ������
  if (this.playerState == playerStateIdle) {
    return;
  }
  this.logger.logInfo(
    'onInitDecoder, duration: ' + this.duration + ', decoderState: ' + this.decoderState,
  );
  //flv�ļ��Ѵ�����ų�ʼ���ɹ�������
  if (this.decoderState == decoderStateIdle && this.streamDone == 1) {
    this.logger.logInfo('onStreamDataUnderDecoderIdle');
    this.onStreamDataUnderDecoderIdle(0);
  } else {
    //this.logger.logInfo("decoderState: " + this.decoderState + ", this.streamDone: " + this.streamDone);
  }

  //this.logger.logInfo("Init decoder response " + objData.e + ".");
  if (objData.e == 0) {
    if (!this.isStream) {
      this.downloadOneChunk();
    }
  } else {
    this.reportPlayError(objData.e);
  }
};

Player.prototype.onOpenDecoder = function(objData) {
  if (this.playerState == playerStateIdle) {
    return;
  }

  if (objData.e == 0) {
    this.openDecoderTimes = 0; //�ɹ���Ϊ0
    if (objData.v.i > 0) {
      this.decodeInterval = objData.v.i; //���½��������
    }
    if (this.curWaitMaxMsec != waitMaxMsec) {
      //�ɹ���ָ���ֵ
      this.curWaitMaxMsec = waitMaxMsec;
    }
    let str_encodeType = '';
    if (objData.v.c == 28) {
      str_encodeType = 'H.264';
    } else if (objData.v.c == 174) {
      str_encodeType = 'H.265(HEVC)';
    }
    this.logger.logInfo(
      'Open decoder response ' +
        objData.e +
        ', decodeInterval: ' +
        this.decodeInterval +
        ', videoStartMsec: ' +
        objData.v.s +
        ', codec_id: ' +
        objData.v.c +
        '.',
    );
    this.logger.logInfo('url: ' + this.url + ', encode type: ' + str_encodeType);

    this.codec_id = objData.v.c; //28: H.264, 174: H.265
    this.videoStartMsec = objData.v.s; //flv�ļ���ʼʱ��
    this.duration = objData.v.d; //flv�ļ�����ʱ��
    this.onVideoParam(objData.v);
    //this.onAudioParam(objData.a);
    if (this.onlyGetCodeID) {
      //ֻ��ȡ����ID������ͣ
      this.logger.logInfo('getCodeID success: ' + objData.v.c);
      this.stopDownloadTimer();
      this.pause();
      if (!this.isStream) {
        this.stop();
      }
    } else {
      this.decoderState = decoderStateReady;
      this.logger.logInfo('Decoder ready now.');
      this.startDecoding();
    }
  } else {
    this.logger.logInfo('Open decoder response ' + objData.e + '.');
    this.reportPlayError(objData.e);
    if (this.playerState != playerStatePlaying) {
      //����ͣ��ֹͣ���ţ�������������Բ���
      return;
    }
    if (this.isStream) {
      //ʵʱ��
      this.openDecoderTimes++; //���Դ򿪽���������
      if (this.fetchController) {
        this.fetchController.abort();
        this.fetchController = null;
        this.logger.logInfo(
          'fetchController.abort, stop stream, openDecoderTimes ' + this.openDecoderTimes,
        );
      }

      if (this.openDecoderTimes < playMaxRetryTimes) {
        //�޶��������Դ���
        this.stop(false);
        this.play(this.url, this.canvas, this.callback, this.waitHeaderLength, this.isStream);
        this.curWaitMaxMsec *= 2; //ʧ�ܣ���ȴ�ʱ�䷭��
        setTimeout(() => {
          this.onStreamDataUnderDecoderIdle(0);
        }, this.curWaitMaxMsec);
      } else {
        this.stop();
        this.curWaitMaxMsec = waitMaxMsec; //�ָ���ʼ�ȴ�ʱ��
      }
    }
  }
};

Player.prototype.onVideoParam = function(v) {
  if (this.playerState == playerStateIdle) {
    return;
  }

  this.logger.logInfo(
    'Video param duation:' + v.d + ' pixFmt:' + v.p + ' width:' + v.w + ' height:' + v.h + '.',
  );
  this.duration = v.d;
  this.pixFmt = v.p;
  //this.canvas.width = v.w;
  //this.canvas.height = v.h;
  this.videoWidth = v.w;
  this.videoHeight = v.h;
  this.yLength = this.videoWidth * this.videoHeight;
  this.uvLength = (this.videoWidth / 2) * (this.videoHeight / 2);

  /*
    //var playCanvasContext = playCanvas.getContext("2d"); //If get 2d, webgl will be disabled.
    this.webglPlayer = new WebGLPlayer(this.canvas, {
        preserveDrawingBuffer: false
    });
    */

  if (this.timeTrack) {
    this.timeTrack.min = 0;
    this.timeTrack.max = this.duration;
    this.timeTrack.value = 0;
    this.displayDuration = this.formatTime(this.duration / 1000);
  }

  if (!this.isStream) {
    var byteRate = ((this.fileInfo.size + this.duration - 1) / this.duration) * 1000; //ÿ�봫���ֽ��ʣ�stream fileInfo.size is -1
    var targetSpeed = downloadSpeedByteRateCoef * byteRate;
    var chunkPerSecond = (targetSpeed + this.fileInfo.chunkSize - 1) / this.fileInfo.chunkSize;
    this.chunkInterval = 1000 / chunkPerSecond; //���Ľ��ֵ:��ֵ���������ʱ��ʵ�ʼ��Ӧ����10ms��
    this.seekWaitLen = byteRate * maxBufferTimeLength * 2;
    this.logger.logInfo(
      'seekWaitLen: ' +
        this.seekWaitLen +
        ', chunkInterval: ' +
        this.chunkInterval +
        ', byteRate: ' +
        byteRate +
        ', chunkPerSecond: ' +
        chunkPerSecond,
    );

    this.startDownloadTimer();
  }
};

Player.prototype.onAudioParam = function(a) {
  if (this.playerState == playerStateIdle) {
    return;
  }

  this.logger.logInfo(
    'Audio param sampleFmt:' + a.f + ' channels:' + a.c + ' sampleRate:' + a.r + '.',
  );

  var sampleFmt = a.f;
  var channels = a.c;
  var sampleRate = a.r;

  var encoding = '16bitInt';
  switch (sampleFmt) {
    case 0:
      encoding = '8bitInt';
      break;
    case 1:
      encoding = '16bitInt';
      break;
    case 2:
      encoding = '32bitInt';
      break;
    case 3:
      encoding = '32bitFloat';
      break;
    default:
      this.logger.logError('Unsupported audio sampleFmt ' + sampleFmt + '!');
  }
  this.logger.logInfo('Audio encoding ' + encoding + '.');

  this.pcmPlayer = new PCMPlayer({
    encoding: encoding,
    channels: channels,
    sampleRate: sampleRate,
    flushingTime: 5000,
  });

  this.audioEncoding = encoding;
  this.audioChannels = channels;
  this.audioSampleRate = sampleRate;
};

Player.prototype.restartAudio = function() {
  /*if (this.pcmPlayer) {
        this.pcmPlayer.destroy();
        this.pcmPlayer = null;
    }

    this.pcmPlayer = new PCMPlayer({
        encoding: this.audioEncoding,
        channels: this.audioChannels,
        sampleRate: this.audioSampleRate,
        flushingTime: 5000
    });*/
};

Player.prototype.bufferFrame = function(frame) {
  // If not decoding, it may be frame before seeking, should be discarded.
  if (!this.decoding) {
    return;
  }
  this.frameBuffer.push(frame);

  //ʵʱ��
  if (this.fileInfo.size == 0 && this.getBufferTimerLength() >= maxBufferTimeLength) {
    if (this.decoding) {
      this.logger.logInfo(
        'pauseDecoding, TimerLen:' +
          this.getBufferTimerLength() +
          ' decoderState:' +
          this.decoderState,
      );
      //this.logger.logInfo("Frame buffer time length >= " + maxBufferTimeLength + ", pause decoding.");
      this.pauseDecoding();
    }
    if (this.buffering) {
      this.stopBuffering();
    }
  }
};

Player.prototype.displayAudioFrame = function(frame) {
  if (this.pcmPlayer == null) {
    return false;
  }
  if (this.playerState != playerStatePlaying && !this.seeking) {
    return false;
  }

  if (this.seeking) {
    this.restartAudio();
    this.startTrackTimer();
    this.hideLoading();
    this.seeking = false;
    this.urgent = false;
  }

  if (this.isStream && this.firstAudioFrame) {
    this.firstAudioFrame = false;
    this.beginTimeOffset = frame.s;
  }
  this.pcmPlayer.play(new Uint8Array(frame.d));
  return true;
};

Player.prototype.onAudioFrame = function(frame) {
  this.bufferFrame(frame);
};

Player.prototype.onDecodeFinished = function(objData) {
  //this.pauseDecoding();
  this.decoderState = decoderStateFinished;
};

Player.prototype.getBufferTimerLength = function() {
  if (!this.frameBuffer || this.frameBuffer.length == 0) {
    return 0;
  }

  let oldest = this.frameBuffer[0];
  let newest = this.frameBuffer[this.frameBuffer.length - 1];
  return newest.s - oldest.s;
};

Player.prototype.onVideoFrame = function(frame) {
  //this.logger.logInfo("displayVideoFrame, frame.s: "+frame.s);
  this.bufferFrame(frame);
};

Player.prototype.displayVideoFrame = function(frame) {
  //���Ż�seek(���š���ͣ)
  if (this.playerState != playerStatePlaying && !this.seeking) {
    return false;
  }
  //this.logger.logInfo("displayVideoFrame, frame.s: "+frame.s);

  /*var audioCurTs = this.pcmPlayer.getTimestamp();
    var audioTimestamp = audioCurTs + this.beginTimeOffset;
    var delay = frame.s - audioTimestamp;*/

  //this.logger.logInfo("displayVideoFrame delay=" + delay + "=" + " " + frame.s  + " - (" + audioCurTs  + " + " + this.beginTimeOffset + ")" + "->" + audioTimestamp);

  //if (audioTimestamp <= 0 || delay <= 0) {
  //var data = new Uint8Array(frame.d);
  //this.renderVideoFrame(data);
  if (this.duration > 0) {
    //flv�ļ�
    let tmMsec = new Date().getTime(); //��ǰ����ʱ���
    //this.logger.logInfo("displayVideoFrame, Local:"+this.startDisplayLocalTimestamp + "~"+tmMsec+", frame: "+ this.startDisplayTimestamp+"~"+frame.s);
    if (this.startDisplayLocalTimestamp == 0) {
      this.startDisplayLocalTimestamp = tmMsec; //每轮解码开启后刚开始显示的本地时间
      this.startDisplayTimestamp = frame.s; //每轮解码开启后刚开始显示的时间
    } else {
      if (frame.s > this.startDisplayTimestamp && tmMsec > this.startDisplayLocalTimestamp) {
        let frameIntervalMsec = (frame.s - this.startDisplayTimestamp) * 1000;
        let localTMIntervalMsec = tmMsec - this.startDisplayLocalTimestamp;
        //this.logger.logInfo("displayVideoFrame: frameIntervalMsec: "+frameIntervalMsec+", localTMIntervalMsec: "+localTMIntervalMsec);
        //本地间隔时间超过帧率对应间隔时间，且帧间隔时间过长，才认为是丢帧，需延迟
        if (localTMIntervalMsec < frameIntervalMsec) {
          //this.logger.logInfo("displayVideoFrame: later display-----");
          return false; //若帧时间戳间隔过长，考虑可能丢帧，需延迟显示
        }
      }
    }

    //this.logger.logInfo("displayVideoFrame, frame.s: "+frame.s);
  }
  this.videoTimestamp = frame.s;
  //this.logger.logInfo("displayVideoFrame: display now");

  this.renderVideoFrame(frame.d);

  return true;
  //}
  //return false;
};

Player.prototype.onSeekToRsp = function(ret) {
  if (ret != 0) {
    this.justSeeked = false;
    this.seeking = false;
  }
};

Player.prototype.onRequestData = function(offset, available) {
  if (this.justSeeked) {
    this.logger.logInfo('Request data ' + offset + ', available ' + available);
    if (offset == -1) {
      //����������
      // Hit in buffer.
      let left = this.fileInfo.size - this.fileInfo.offset;
      if (available >= left) {
        //�������ļ�������������ȫ
        this.logger.logInfo('No need to wait');
        this.resume(true);
        if (left > 0) {
          //δ�����꣬���������
          this.startDownloadTimer();
        }
      } else {
        //����δ�����꣬���������
        this.startDownloadTimer();
      }
    } else {
      //��δ���صģ�����seekλ�ÿ�ʼ����
      if (offset >= 0 && offset < this.fileInfo.size) {
        this.fileInfo.offset = offset;
      }
      this.startDownloadTimer();
    }

    //this.restartAudio();
    this.justSeeked = false;
  }
};

Player.prototype.displayLoop = function() {
  if (this.playerState != playerStateIdle) {
    requestAnimationFrame(this.displayLoop.bind(this));
  }
  //ʵʱ���������ۼ�֡��400ms��֡������Ҫ�ӳ���ʾ
  /*if((this.duration ==0)&&(this.frameBuffer.length < (400/this.decodeInterval))){
        var curMsec = new Date().getTime(); //��ǰ����ʱ���
        if(this.latestDisplayMsec && ((curMsec - this.latestDisplayMsec)< this.decodeInterval)&&
            (curMsec>this.latestDisplayMsec) ){
            return;//δ����ʾ������˳�
        } else {
            this.latestDisplayMsec = curMsec;
        }
    }*/

  if (this.playerState != playerStatePlaying && !this.seeking) {
    return;
  }
  if (this.frameBuffer.length == 0) {
    return;
  }
  //this.logger.logInfo("displayLoop");
  //if (this.buffering) {
  //    return;
  //}
  var filter_count = 5; //����˵�֡���ж�����
  var video_index = -1; //��Ƶ֡���
  /*if(this.frameBuffer.length>5){
        this.logger.logInfo("displayLoop, frameBuffer len:" + this.frameBuffer.length);
    }*/
  if (this.duration == 0 && this.frameBuffer.length >= filter_count) {
    //flv��������֡
    for (i = 0; i < filter_count; ++i) {
      var frame = this.frameBuffer[i];
      if (frame.t == kAudioFrame) {
        //��Ƶ������
        continue;
      }
      if (video_index >= 0) {
        //���ٺ�������Ƶ֡����ɾ����һ����Ƶ֡
        frame = this.frameBuffer[video_index];
        this.frameBuffer.splice(video_index, 1); //ÿ�ι���һ֡�����⿨�١�
        frame.d = null;
        break;
      }
      video_index = i;
    }
  }

  // requestAnimationFrame may be 60fps, if stream fps too large,
  // we need to render more frames in one loop, otherwise display
  // fps won't catch up with source fps, leads to memory increasing,
  // set to 2 now.
  for (i = 0; i < 5; ++i) {
    var frame = this.frameBuffer[0];

    switch (frame.t) {
      case kAudioFrame:
        if (this.displayAudioFrame(frame)) {
          this.frameBuffer.shift();
          frame.d = null;
        }
        break;
      case kVideoFrame:
        if (this.displayVideoFrame(frame)) {
          this.frameBuffer.shift();
          frame.d = null;
        }
        if (this.seeking) {
          this.restartAudio();
          this.hideLoading();
          this.logger.logInfo(
            'displayLoop for seek start, frame.s: ' +
              frame.s +
              ', beginTimeOffset: ' +
              this.beginTimeOffset +
              ', seeking: ' +
              this.seeking,
          );
          if (frame.s >= this.beginTimeOffset) {
            this.urgent = false; //�������startDecodingǰ
            //��������ͣ�򲥷ŵ�seek��ע:����״̬�ڻָ��󶼻��Ϊ���ŵ�״̬
            if (this.playerState == playerStatePausing) {
              this.updateTrackTime(); //ֱ�Ӹ��¶�Ӧʱ��
              this.pause(true);
              this.stopDownloadTimer();
              this.frameBuffer.splice(0, this.frameBuffer.length); //���seek֮��Ļ���
              //this.logger.logInfo("displayLoop, display seek timestamp: " + frame.s);
            } else if (this.playerState == playerStatePlaying) {
              this.startTrackTimer();
              this.startDecoding(); //��������֡����������
            }
            this.seeking = false; //�������updateTrackTime��
          } /*else {
						this.logger.logInfo("displayLoop, remove frameBuffer");
						this.frameBuffer.shift();
						break;	//seekʱ������ǰ�����I֡��ʼ����,��֮ǰ��֡������ʾ������
					}*/
          this.logger.logInfo('displayLoop for seek end');
        } else {
          if (this.justReplayed && this.playerState == playerStatePlaying) {
            this.justReplayed = false;
            //���Ż򲥷�ʱseek��������ʾ��ǰ�ε�һ֡��ſ�ʼ����tracker��ʱ��
            this.startTrackTimer();
          }
        }
        if (frame.d == null) {
          //当前帧暂未能播放，到点才播放，则退出
          break;
        }

        if (this.fileInfo.size > 0 && this.duration > 0 && frame.s * 1000 >= this.duration) {
          this.logger.logInfo(
            'displayLoop file end, frame.s: ' + frame.s + ', duration: ' + this.duration,
          );
          this.pause();
        }
        break;
      default:
        return;
    }
    //��⵽һ����Ƶ֡���˳�,��������Ƶͬ��
    if (this.frameBuffer.length == 0 || frame.t == kVideoFrame) {
      break;
    }
  }

  /*if (this.getBufferTimerLength() < maxBufferTimeLength / 2) {
        if (!this.decoding) {
            //this.logger.logInfo("Buffer time length < " + maxBufferTimeLength / 2 + ", restart decoding.");
            this.startDecoding();
        }
    }*/

  /*if (this.bufferFrame.length == 0) {//����������0
        if (this.decoderState == decoderStateFinished) {
            this.reportPlayError(1, 0, "Finished");
            this.stop();
        } else {
            this.startBuffering();
        }
    }*/
};

/*Player.prototype.startBuffering = function () {
	this.logger.logInfo("startBuffering");
    this.buffering = true;
    this.showLoading();
    console.log("startBuffering: will pause");
    this.pause();
}*/

Player.prototype.stopBuffering = function() {
  this.logger.logInfo('stopBuffering');
  this.buffering = false;
  this.hideLoading();
  this.resume();
};

Player.prototype.renderVideoFrame = function(data) {
  if (this.webglPlayer) {
    this.webglPlayer.renderFrame(
      data,
      this.videoWidth,
      this.videoHeight,
      this.yLength,
      this.uvLength,
    );
  }
};

Player.prototype.downloadOneChunk = function() {
  this.logger.logInfo('downloadOneChunk, downloadSeqNo: ' + this.downloadSeqNo);
  if (this.downloading || this.isStream) {
    return;
  }

  var start = this.fileInfo.offset;
  if (start >= this.fileInfo.size) {
    this.logger.logError('Reach file end.');
    this.stopDownloadTimer();
    return;
  }

  var end = this.fileInfo.offset + this.fileInfo.chunkSize - 1;
  if (end >= this.fileInfo.size) {
    end = this.fileInfo.size - 1;
  }

  var len = end - start + 1;
  if (len > this.fileInfo.chunkSize) {
    console.log('Error: request len:' + len + ' > chunkSize:' + this.fileInfo.chunkSize);
    return;
  }

  var req = {
    t: kDownloadFileReq,
    u: this.fileInfo.url,
    s: start,
    e: end,
    q: this.downloadSeqNo,
    p: this.downloadProto,
  };
  this.downloadWorker.postMessage(req);
  this.downloading = true;
};

Player.prototype.startDownloadTimer = function() {
  var self = this;
  this.downloadSeqNo++;
  this.logger.logInfo('startDownloadTimer, downloadSeqNo: ' + this.downloadSeqNo);

  if (this.downloadTimer) {
    clearInterval(this.downloadTimer);
  }
  this.downloadTimer = setInterval(function() {
    self.downloadOneChunk();
  }, this.chunkInterval);
};

Player.prototype.stopDownloadTimer = function() {
  //this.logger.logInfo("stopDownloadTimer.");
  if (this.downloadTimer != null) {
    clearInterval(this.downloadTimer);
    this.downloadTimer = null;
  }
  this.downloading = false;
};

Player.prototype.startTrackTimer = function() {
  //this.logger.logInfo("startTrackTimer");
  var self = this;
  if (this.trackTimer) {
    clearInterval(this.trackTimer);
  }
  this.trackTimer = setInterval(function() {
    self.updateTrackTime();
  }, this.trackTimerInterval);
};

Player.prototype.stopTrackTimer = function() {
  //this.logger.logInfo("stopTrackTimer");
  if (this.trackTimer != null) {
    clearInterval(this.trackTimer);
    this.trackTimer = null;
  }
};

Player.prototype.updateTrackTime = function() {
  //this.logger.logInfo("updateTrackTime");
  var currentPlayTime = 0;
  /*if(this.pcmPlayer){	//��ʱû����Ƶ��������Ƶʱ���
		currentPlayTime += this.pcmPlayer.getTimestamp();
	}*/
  if (this.playerState == playerStatePlaying || this.seeking) {
    currentPlayTime = this.videoTimestamp;
    if (this.timeTrack) {
      this.timeTrack.value = 1000 * currentPlayTime;
      //this.logger.logInfo("updateTrackTime, timeTrack.value: "+this.timeTrack.value);
    }
    if (this.timeLabel) {
      this.timeLabel.innerHTML = this.formatTime(currentPlayTime) + '/' + this.displayDuration;
    }
  }
  this.StatCodeRateInfo(); //ͳ�����������Ϣ
};

Player.prototype.startDecoding = function() {
  //var decodeInterval = this.isStream ? 5 : this.decodeInterval;//只有文件才间隔帧间隔
  //第一次开始解码flv回放文件，需配置timeTrack最大值
  if (this.timeTrack && this.timeTrack.max == '' && this.duration && !this.isStream) {
    this.timeTrack.min = 0;
    this.timeTrack.max = this.duration;
    //this.logger.logInfo("timeTrack.max:" + this.timeTrack.max);
    this.timeTrack.value = 0;
    this.displayDuration = this.formatTime(this.duration / 1000);
  } else {
    //this.logger.logInfo("this.timeTrack is NULL");
  }

  this.startDisplayLocalTimestamp = 0;
  var req = {
    t: kStartDecodingReq,
    i: this.urgent ? 0 : this.decodeInterval,
    u: this.urgent,
  };
  this.decodeWorker.postMessage(req);
  this.decoding = true;
};

Player.prototype.pauseDecoding = function() {
  var req = {
    t: kPauseDecodingReq,
  };
  this.decodeWorker.postMessage(req);
  this.decoding = false;
};

Player.prototype.formatTime = function(s) {
  var h = Math.floor(s / 3600) < 10 ? '0' + Math.floor(s / 3600) : Math.floor(s / 3600);
  var m =
    Math.floor((s / 60) % 60) < 10 ? '0' + Math.floor((s / 60) % 60) : Math.floor((s / 60) % 60);
  var s = Math.floor(s % 60) < 10 ? '0' + Math.floor(s % 60) : Math.floor(s % 60);
  return (result = h + ':' + m + ':' + s);
};

Player.prototype.reportPlayError = function(error, status, message) {
  var e = {
    error: error || 0,
    status: status || 0,
    message: message,
  };

  if (this.callback) {
    this.callback(e);
  }
};

Player.prototype.setLoadingDiv = function(loadingDiv) {
  this.loadingDiv = loadingDiv;
};

Player.prototype.hideLoading = function() {
  if (this.loadingDiv != null) {
    loading.style.display = 'none';
  }
};

Player.prototype.showLoading = function() {
  if (this.loadingDiv != null) {
    loading.style.display = 'block';
  }
};

Player.prototype.registerVisibilityEvent = function(cb) {
  var hidden = 'hidden';

  // Standards:
  if (hidden in document) {
    document.addEventListener('visibilitychange', onchange);
  } else if ((hidden = 'mozHidden') in document) {
    document.addEventListener('mozvisibilitychange', onchange);
  } else if ((hidden = 'webkitHidden') in document) {
    document.addEventListener('webkitvisibilitychange', onchange);
  } else if ((hidden = 'msHidden') in document) {
    document.addEventListener('msvisibilitychange', onchange);
  } else if ('onfocusin' in document) {
    // IE 9 and lower.
    document.onfocusin = document.onfocusout = onchange;
  } else {
    // All others.
    window.onpageshow = window.onpagehide = window.onfocus = window.onblur = onchange;
  }

  function onchange(evt) {
    var v = true;
    var h = false;
    var evtMap = {
      focus: v,
      focusin: v,
      pageshow: v,
      blur: h,
      focusout: h,
      pagehide: h,
    };

    evt = evt || window.event;
    var visible = v;
    if (evt.type in evtMap) {
      visible = evtMap[evt.type];
    } else {
      visible = this[hidden] ? h : v;
    }
    cb(visible);
  }

  // set the initial state (but only if browser supports the Page Visibility API)
  if (document[hidden] !== undefined) {
    onchange({ type: document[hidden] ? 'blur' : 'focus' });
  }
};

Player.prototype.onStreamDataUnderDecoderIdle = function(length) {
  //this.logger.logInfo("streamReceivedLen: "+ this.streamReceivedLen+", cur rev len: "+length+
  //	", wait len: " +this.waitHeaderLength );
  this.streamReceivedLen += length;
  if (this.playerState != playerStatePlaying) {
    //������δ��ʼ�������ϲ���ֹͣ����ͣ���ţ����˳�
    //this.logger.logInfo("onStreamDataUnderDecoderIdle exit, initDecoder: "+ this.initDecoder+", playerState: "+this.playerState);
    return;
  }
  if (this.firstCheckOpenDecoderMsec == 0) {
    //��һ�ν��յ����ݿ�ʼ��ʱ
    if (length > 0) {
      this.firstCheckOpenDecoderMsec = new Date().getTime(); //��ǰ����ʱ���
      return;
    } else {
      return;
    }
  } else if (this.initDecoder == 0) {
    return;
  }

  let i_openDecoder = 0; //�Ƿ�򿪽�����
  if (this.streamReceivedLen > minWaitHeaderLength) {
    let tmMsec = new Date().getTime();
    //������С�����ҳ���һ��ʱ�䣬����Ϊ��һ��I֡������ȫ
    //var offset = tmMsec - this.firstCheckOpenDecoderMsec;
    //this.logger.logInfo("tmMsec - this.firstCheckOpenDecoderMsec: "+offset+", streamReceivedLen: "+this.streamReceivedLen);
    if (
      tmMsec > this.firstCheckOpenDecoderMsec &&
      tmMsec - this.firstCheckOpenDecoderMsec >= this.curWaitMaxMsec
    ) {
      this.logger.logInfo(
        'receive enough time and streamReceivedLen(' +
          this.streamReceivedLen +
          '), will open decoder',
      );
      i_openDecoder = 1;
    }
  }

  //ʱ�䵽�˻����յ��㹻������ݣ���򿪽�����
  if (i_openDecoder || this.streamReceivedLen >= this.waitHeaderLength) {
    this.logger.logInfo('Opening decoder for Stream.');
    this.decoderState = decoderStateInitializing;
    var req = {
      t: kOpenDecoderReq,
    };
    this.decodeWorker.postMessage(req);
  }
};

Player.prototype.requestStream = function(url) {
  var self = this;
  this.fetchController = new AbortController();
  //self.logger.logInfo("fetchController: "+this.fetchController);
  const signal = this.fetchController.signal;
  fetch(url, { signal })
    .then(function respond(response) {
      const reader = response.body.getReader();
      reader.read().then(function processData({ done, value }) {
        if (done) {
          self.streamDone = 1; //���������
          self.logger.logInfo('Stream done.');
          if (self.decoderState == decoderStateIdle && self.initDecoder > 0) {
            self.logger.logInfo('onStreamDataUnderDecoderIdle');
            self.onStreamDataUnderDecoderIdle(0);
          } else {
            self.logger.logInfo(
              'decoderState: ' + self.decoderState + ', initDecoder: ' + self.initDecoder,
            );
          }
          return;
        }
        if (self.playerState != playerStatePlaying) {
          return;
        }

        var dataLength = value.byteLength;
        var revLength = dataLength;
        var offset = 0;
        self.updateCodeRateInfo(revLength); //�����������ͳ����Ϣ
        if (dataLength > self.fileInfo.chunkSize) {
          do {
            let len = Math.min(self.fileInfo.chunkSize, dataLength);
            var data = value.buffer.slice(offset, offset + len);
            dataLength -= len;
            offset += len;
            var objData = {
              t: kFeedDataReq,
              d: data,
            };
            self.decodeWorker.postMessage(objData, [objData.d]);
            //self.decodeWorker.postMessage({t:kFeedDataReq, d:data}, [data]);
          } while (dataLength > 0);
        } else {
          var objData = {
            t: kFeedDataReq,
            d: value.buffer,
          };
          self.decodeWorker.postMessage(objData, [objData.d]);
          //self.decodeWorker.postMessage({t:kFeedDataReq, d:value.buffer}, [value.buffer]);
        }
        if (self.decoderState == decoderStateIdle) {
          //self.logger.logInfo("onStreamDataUnderDecoderIdle");
          self.onStreamDataUnderDecoderIdle(revLength);
        } /*else{
				this.logger.logInfo("decoderState: "+self.decoderState+", initDecoder: "+self.initDecoder);
            }*/

        reader.read().then(processData);
      });
    })
    .catch(err => {});
};

//�������������Ϣ
Player.prototype.updateCodeRateInfo = function(len) {
  this.rev_Bs += len;
  if (this.last_stat_msec == 0) {
    this.last_stat_msec = new Date().getTime(); //��ǰ����ʱ���
  }
};

//ͳ�����������Ϣ
Player.prototype.StatCodeRateInfo = function() {
  if (this.last_stat_msec) {
    let tmMsec = new Date().getTime(); //��ǰ����ʱ���
    //this.logger.logInfo("tmMsec: "+tmMsec+", last_stat_msec: "+this.last_stat_msec);
    //������ʵ�⣬Уʱ�˸�ʱ���ֵҲ�ǵ�����.ֻ����־ʱ��δ��ʵʱ����Ϊ��ǰʱ��
    if (tmMsec < this.last_stat_msec) {
      this.clearCodeRateInfo();
    } else {
      //����1�룬�����ʵʱ����
      let offset_msec = tmMsec - this.last_stat_msec;
      if (offset_msec >= 1000) {
        this.rev_kbs = (this.rev_Bs / offset_msec).toFixed(1); //����ʵʱ���ʣ�����һλС��λ
        this.rev_Bs = 0;
        this.last_stat_msec = tmMsec;
        //this.logger.logInfo("real time code rate: "+this.rev_kbs);
      }
    }
  }
};

//��ʼ���������ͳ����Ϣ
Player.prototype.clearCodeRateInfo = function() {
  this.rev_Bs = 0; //ÿ���յ����ֽ���
  this.last_stat_msec = 0; //�ϴ�ͳ�ƹ����ʵĺ���ʱ���ֵ
  this.rev_kbs = 0; //ÿ���յ���ǧ�ֽ���(KB/S)������
};
