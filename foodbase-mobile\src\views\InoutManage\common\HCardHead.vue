<template>
  <div class="h-card-head">
    <div class="card-head">
      <!-- <slot></slot> -->
      <slot name="icon"></slot>
      <div class="title">{{ this.title }}</div>
      <slot name="tail"></slot>
    </div>
  </div>
</template>

<script>
export default {
  name: 'HCardHead',
  props: {
    title: {
      type: String,
      default: () => '',
    },
    value: {
      type: Boolean,
      default: () => false,
    },
    labelWidth: {
      type: String,
      default: '155px',
    },
  },
  data() {
    return {};
  },
};
</script>

<style lang="scss" scoped>
.h-card-head {
  margin-top: 6px;
  .card-head {
    display: flex;
    align-items: center;
    font-size: 18px;
    font-weight: bold;
    color: #1f3359;
    margin-bottom: 10px;
    .title {
      margin: 0px 10px;
    }
  }
}
</style>
