<template>
  <div class="in-out-manage-page">
    <van-tabs
      class="tabs"
      v-model="currentTab"
      @change="changeCurrentTab"
      title-active-color="#428FFC"
      title-inactive-color="#686B73"
      animated
    >
      <van-tab name="in" title="到库登记" />
      <van-tab name="out" title="离库登记" />
    </van-tabs>
    <CheckIn v-show="currentTab == 'in'"></CheckIn>
    <CheckOut v-show="currentTab == 'out'"></CheckOut>
  </div>
</template>

<script>
import { Tab, Tabs } from 'vant';
import CheckIn from './CheckIn.vue';
import CheckOut from './CheckOut.vue';

export default {
  name: 'CheckInOut',
  components: {
    'van-tab': Tab,
    'van-tabs': Tabs,
    CheckIn,
    CheckOut,
  },
  data() {
    return {
      currentTab: 'in',
    };
  },
  methods: {
    changeCurrentTab(val) {
      this.currentTab = val;
    },
  },
};
</script>
<style lang="scss" scoped>
.tabs {
  margin-bottom: 10px;
  border-bottom: 1px solid #dddddd;
  font-size: 16px;
}
</style>
