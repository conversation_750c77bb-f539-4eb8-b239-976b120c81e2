<template>
  <HCard title="收购质检详情">
    <template #header-extra>
      <HPicker
        class="bg-gray"
        :options="purchaseCategory"
        v-model:value="selectedFoodCategory"
      ></HPicker>
    </template>
    <div class="stock-origin-distribution">
      <div class="chart">
        <div class="h-[230px] flex" v-if="pieData.length > 0">
          <HEchart
            ref="pieChart"
            class="flex-1 pl-6"
            :options="structureOptions"
            @e:mousedown="handlePieMouseDown"
            @e:finished="handlePieFinished"
          />
          <div class="flex items-center w-[180px] p-4">
            <div class="">
              <p class="">
                <span
                  class="inline-block w-3 h-3 mr-1 rounded-sm"
                  :style="`background-color: ${colors[0]};`"
                ></span
                >质检合格
              </p>
              <strong class="inline-block ml-4 text-[18px]">
                {{ acquisitionQualityData?.qualified?.toLocaleString() ?? '-' }} /
                {{ ((acquisitionQualityData?.qualifiedRatio || 0) * 100)?.toFixed(0) ?? '-' }}%
              </strong>
              <p class="mt-3">
                <span
                  class="inline-block w-3 h-3 mr-1 rounded-sm"
                  :style="`background-color: ${colors[1]};`"
                ></span
                >质检超标
              </p>
              <strong class="inline-block ml-4 text-[18px]">
                {{ acquisitionQualityData?.overStandard?.toLocaleString() ?? '-' }} /
                {{ ((acquisitionQualityData?.overStandardRatio || 0) * 100)?.toFixed(0) ?? '-' }}%
              </strong>
            </div>
          </div>
        </div>
        <EmptyHolder v-else />
      </div>
    </div>
  </HCard>
</template>

<script setup>
import { ref, computed, onMounted, inject, watch } from 'vue';
import { HCard, HEchart, HPicker } from '@/components';
import EmptyHolder from '@/views/common/EmptyHolder';
import { reserveRequest } from '@/utils/request';
import dayjs from 'dayjs';

const purchaseCategory = inject('purchaseCategory');
const acquisitionQualityData = ref({});

const selectedFoodCategory = ref(purchaseCategory?.value?.[0]?.value);

const getData = async () => {
  acquisitionQualityData.value = await reserveRequest().get(
    '/api/acquisitionQuality/qualityDetail',
    {
      params: {
        year: dayjs().format('YYYY'),
        foodBigCategory: selectedFoodCategory.value,
      },
    },
  );
  return Promise.resolve();
};

watch(() => {
  getData();
});

onMounted(async () => {
  await getData();
});

const pieChart = ref(null);

const handleHighlightPieItem = (name) => {
  if (!name) {
    return;
  }
  pieChart.value.dispatchAction({ type: 'downplay' });
  pieChart.value.dispatchAction({
    type: 'highlight',
    name: name,
  });
};

let currentPieHighlight = false;
const handlePieFinished = (force) => {
  if (!currentPieHighlight || force) {
    const firstValidItem = pieData.value.find((it) => !!it.value);
    if (firstValidItem) {
      handleHighlightPieItem(firstValidItem?.name);
    } else {
      handleHighlightPieItem(pieData.value[0]?.name);
    }
    currentPieHighlight = true;
  }
};

const handlePieMouseDown = (params) => {
  const { seriesType, data } = params;
  if (seriesType === 'pie') {
    const { name } = data;
    handleHighlightPieItem(name);
  }
};

const colors = ['#0092FF', '#0ccc74'];
const pieData = computed(() => {
  return [
    { name: '质检合格', value: acquisitionQualityData.value.qualified },
    { name: '质检超标', value: acquisitionQualityData.value.overStandard },
  ].map((i, index) => ({
    name: i.name,
    value: i.value || 0,
    itemStyle: { color: colors[index] },
  }));
});

const structureOptions = computed(() => {
  return {
    legend: {
      show: false,
    },
    series: [
      {
        name: '库存品种结构',
        type: 'pie',
        radius: ['55%', '80%'],
        data: pieData.value,
        label: {
          show: false,
          position: 'center',
          fontSize: 18,
          fontWeight: 'bold',
          lineHeight: 25,
          color: '#323233',
          textBorderColor: 'transparent',
          // eslint-disable-next-line no-unused-vars
          formatter: ({ name, percent }) => {
            return '';
            // return `${name}\n${percent}%`;
          },
        },
        labelLine: {
          show: false,
        },
        emphasis: {
          label: {
            show: true,
          },
          itemStyle: {
            shadowBlur: 10,
            shadowOffsetX: 0,
            shadowColor: 'rgba(0, 0, 0, 0.5)',
          },
        },
        animationType: 'scale',
        animationEasing: 'elasticOut',
      },
    ],
  };
});
</script>

<style lang="scss" scoped>
.chart {
  height: 230px;
}
.h-echarts {
  width: 100%;
  height: 100%;
}
</style>
