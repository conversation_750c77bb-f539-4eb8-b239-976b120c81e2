<template>
  <van-picker
    :title="title"
    :columns="options"
    :columns-field-names="fieldNames"
    :default-index="defaultIndex"
    @cancel="onCancel"
    @confirm="onConfirm"
  ></van-picker>
</template>

<script>
import { Picker } from 'vant';

export default {
  name: 'BizDictPicker',
  inheritAttrs: false,
  components: {
    'van-picker': Picker,
  },
  emits: ['cancel', 'confirm'],
  props: {
    title: String,
    dict: String,
    hiddenValues: Array,
    disabledValues: Array,
    defaultValue: Number,
  },
  data() {
    return {
      fieldNames: {
        text: 'label',
      },
    };
  },
  computed: {
    options() {
      const dictDetails = this.$store.getters['dict/reserveDictOf'](this.dict);
      if (!dictDetails) {
        console.warn(`不存在的业务字典 ${this.dict}`);
        return [];
      }
      const { hiddenValues, disabledValues } = this;
      if (hiddenValues) {
        return dictDetails.filter((it) => !hiddenValues.includes(it['value']));
      }
      if (disabledValues) {
        return dictDetails.map((it) => {
          if (disabledValues.includes(it['value'])) {
            it.disabled = true;
          }
          return { ...it };
        });
      }
      return dictDetails;
    },
    defaultIndex() {
      let index = this.options?.findIndex((i) => i.value === this.defaultValue);
      return index;
    },
  },
  methods: {
    onCancel() {
      this.$emit('cancel');
    },
    onConfirm(picker, value) {
      console.log(value, 'value----');
      this.$emit('confirm', picker);
    },
  },
};
</script>
