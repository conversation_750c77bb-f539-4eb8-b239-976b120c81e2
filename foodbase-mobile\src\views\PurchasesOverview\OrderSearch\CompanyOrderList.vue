<template>
  <div class="company-order-list">
    <div class="query-form">
      <div class="order-total">共{{ pagination.total }}个订单</div>
      <Row gutter="8">
        <!-- <Col span="8">
          <Field v-model="queryForm.farmerName" placeholder="搜索农户" left-icon="search" />
        </Col>
        <Col span="8">
          <HPicker :options="farmerTypeOptions" v-model:value="queryForm.farmerType"></HPicker>
        </Col> -->
        <Col span="8">
          <HYearPicker v-model:value="queryForm.year" />
        </Col>
        <Col span="16">
          <AllUnitSelect
            v-model:value="queryForm.purchasingStation"
            :isCompanyUser="isCompanyUser"
            :userId="userId"
            @confirm="onConfirm"
            placeholder="收购单位"
          />
        </Col>
      </Row>
    </div>
    <List ref="listRef" v-model:loading="loading" :finished="finished" @load="onLoad">
      <EmptyHolder v-if="list.length === 0" />
      <div class="list" v-else>
        <div v-for="item in list" :key="item.id">
          <div style="font-weight: bold; font-size: 18px">{{ item.purchasingStationName }}</div>
          <HCard>
            <!-- <template #header-title>
              <div style="order-title-name">{{ item.foodCategoryName }}</div>
            </template> -->
            <!-- <template #header-extra>
              <Tag>{{ orderType[item.orderType] }}</Tag>
            </template> -->
            <div class="order-detail">
              <div class="header-title">
                <span style="font-weight: bold">{{ item.foodCategoryName }}</span>
                <Tag :color-type="Number(item.orderType)">{{
                  orderType[Number(item.orderType)]
                }}</Tag>
              </div>
              <Divider />
              <div class="data-detail">
                <div class="data-item">
                  <HFixedNumber :fraction-digits="0" class="value">{{
                    item.allocatedAcreage
                  }}</HFixedNumber>
                  <div class="name">订单面积(亩)</div>
                </div>
                <div class="data-item">
                  <HFixedNumber :fraction-digits="0" class="value">{{
                    item.allocated
                  }}</HFixedNumber>
                  <div class="name">订单数量(吨)</div>
                </div>
                <div class="data-item">
                  <HFixedNumber :fraction-digits="0" class="value">{{
                    item.completedNumber
                  }}</HFixedNumber>
                  <div class="name">已收购数量(吨)</div>
                </div>
                <div class="data-item">
                  <span class="value">
                    <HFixedNumber :fraction-digits="item.acquisitionProgress >= 100 ? 0 : 1">
                      {{ item.acquisitionProgress }}
                    </HFixedNumber>
                    %
                  </span>
                  <div class="name">完成进度</div>
                </div>
              </div>
            </div>
            <div class="actions">
              <Button round plain type="primary" @click="showDetail(item)">查看详情</Button>
            </div>
          </HCard>
        </div>
      </div>
    </List>
  </div>
</template>

<script setup>
import { reactive, ref, watch, computed } from 'vue';
import { List, Row, Col, Button, Divider } from 'vant';
import { HCard, HFixedNumber } from '@/components';
import Tag from '@/views/PurchasesOverview/common/Tag';
// import HPicker from '@/components/HPicker/HPicker';
import EmptyHolder from '@/views/common/EmptyHolder';
import HYearPicker from '@/components/HYearPicker/HYearPicker';
import { getInfoZZD } from '@/api/order-search';
import { useRouter } from 'vue-router';
import { useStore } from 'vuex';
import AllUnitSelect from '@/views/PurchasesOverview/common/AllUnitSelect';
// import { getCompanyOrderList } from '@/api/order-search';

// const farmerTypeOptions = [
//   { value: '', text: '全部' },
//   { value: '1', text: '种粮大户' },
//   { value: '2', text: '一般农户' },
//   { value: '3', text: '合作社/家庭农场' },
// ];
const orderType = {
  1: '省订单',
  2: '市订单',
  3: '县订单',
};
const tagColorMap = {
  种粮大户: 1,
  一般农户: 2,
  '合作社/家庭农场': 3,
};

const props = defineProps({
  foodCategory: Array,
});

const router = useRouter();
const store = useStore();

const queryForm = reactive({
  // farmerName: '',
  // farmerType: '',
  year: String(new Date().getFullYear()),
  purchasingStation: '',
});

const isCompanyUser = computed(() => {
  const userOrgLevel = store.state.user?.reserverIds?.userOrgLevel;
  return [7, 8, 9].includes(userOrgLevel);
});
const userId = computed(() => {
  return store.state.user?.reserverIds?.userId;
});
const deptId = computed(() => {
  return store.state.user?.reserverIds?.deptId;
});
const level = computed(() => {
  return store.state.user?.reserverIds?.level;
});
const pagination = reactive({
  page: 0,
  size: 9999,
  total: 0,
});

const listRef = ref();
const list = ref([]);
const loading = ref(false);
const finished = ref(false);

const onLoad = async () => {
  const { items, page, total } = await getInfoZZD({
    page: pagination.page + 1,
    size: 9999,
    status: 2,
    typeList: [1, 2, 3],
    deptId: deptId.value,
    level: level.value,
    categoryIds: props.foodCategory,
    ...queryForm,
  });

  items.forEach((it) => {
    it.percent = (it.tsNum / it.signNum) * 100 || 0;
    it.tagColor = tagColorMap[it.farmerType];
    list.value.push(it);
  });
  pagination.page = page;
  pagination.total = total;
  finished.value = list.value.length >= total;
  loading.value = false;
};
const onConfirm = (value) => {
  queryForm.purchasingStation = value;
};
const showDetail = (record) => {
  router.push({
    name: 'DetailOrderList',
    query: { ...record, deptIdZZD: deptId.value, levelZZD: level.value, year: queryForm.year },
  });
};
watch(
  queryForm,
  () => {
    pagination.page = 0;
    pagination.total = 0;
    list.value = [];
    finished.value = false;
    listRef.value?.check();
  },
  { deep: true },
);
</script>

<style scoped lang="scss">
.h-card {
  margin-bottom: 16px;
}

.query-form {
  padding: 8px;
}

.order-total {
  font-size: 18px;
  font-weight: 500;
  line-height: 25px;
  margin-bottom: 8px;
}
.order-title-name {
  font-weight: bold;
}
.list {
  margin-top: 5px;
  padding: 0 8px;
  .order-detail {
    margin-top: 5px;
    font-size: 18px;
    line-height: 32px;

    // display: flex;
    // padding: 20px 0;
    .header-title {
      display: flex;
      padding: 8px 8px 0px;
    }
    .data-detail {
      margin: 10px 0;
      display: flex;
      .data-item {
        flex: 1;
        display: flex;
        flex-direction: column;
        align-items: center;
        .value {
          font-size: 18px;
          font-weight: 600;
          line-height: 30px;
          text-align: center;
        }

        .name {
          width: 4.5em;
          font-size: 18px;
          color: #686b73;
          line-height: 25px;
          text-align: center;
        }
      }
    }
  }
}

.actions {
  --van-button-normal-font-size: 18px;
  --van-button-default-height: 36px;
  display: flex;
  padding: 0 16px 16px 16px;

  .van-button {
    margin-left: auto;
  }
}
.van-divider {
  --van-divider-margin: 10px 0px;
}
</style>
