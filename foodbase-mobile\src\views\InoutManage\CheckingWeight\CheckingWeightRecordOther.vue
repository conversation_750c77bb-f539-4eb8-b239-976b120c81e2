<template>
  <div class="checking-weight-other">
    <van-form ref="form" label-width="120px">
      <van-cell-group inset>
        <!-- 包装物扣量(公斤) -->
        <van-field
          v-model="form.packingDeduction"
          v-if="isAllowPackingDeduction"
          label="包装物扣量(公斤)"
          name="signPicker"
          placeholder="请输入"
          required
          :disabled="form.isSettle === '1'"
          :rules="[{ required: true, message: '请输入包装物扣量' }]"
          type="digit"
        >
        </van-field>
        <van-field
          v-model="form.otherDeduction"
          v-if="isAllowOtherDeduction"
          :disabled="form.isSettle === '1'"
          label="其他扣量(公斤)"
          name="signPicker"
          placeholder="请输入"
          required
          :rules="[{ required: true, message: '请输入其他扣量' }]"
          type="digit"
        >
        </van-field>
        <Tabbar>
          <TabbarItem class="cancelTab">
            <Button class="com-btn end" @click="onCancel">取消</Button>
          </TabbarItem>
          <TabbarItem class="saveTab">
            <Button class="com-btn save" type="primary" @click="onSave">确定</Button>
          </TabbarItem>
        </Tabbar>
      </van-cell-group>
    </van-form>
  </div>
</template>
<script>
import { Form, Field, Button, CellGroup, Tabbar, TabbarItem, Toast } from 'vant';
import { getTypeSwitch, saveOtherDeduction } from '@/api/in-out-manage';
export default {
  components: {
    'van-form': Form,
    'van-field': Field,
    'van-cell-group': CellGroup,
    Button,
    Tabbar,
    TabbarItem,
  },
  data() {
    return {
      form: {
        schedulingNo: '',
        otherDeduction: '',
        packingDeduction: '',
      },
      record: null,
      isAllowOtherDeduction: false,
      isAllowPackingDeduction: false,
    };
  },
  mounted() {
    this.record = JSON.parse(this.$route.params.schedulingNo);
    this.form.schedulingNo = this.record.schedulingNo;

    this.form.isSettle = this.record.isSettle; //判断是否已结算  0未结算  1已结算
    this.form.packingDeduction = this.record.packingDeduction * 1000;
    this.form.otherDeduction = this.record.otherDeduction * 1000;
    this.getPermission();
  },
  methods: {
    async onSave() {
      try {
        const data = {
          otherDeduction: parseFloat(this.form.otherDeduction / 1000).toFixed(3),
          packingDeduction: parseFloat(this.form.packingDeduction / 1000).toFixed(3),
          schedulingNo: this.form.schedulingNo,
        };
        await saveOtherDeduction({ ...data });
        Toast.success('保存成功！');
        this.onCancel();
      } catch (error) {
        console.log(error);
      }
    },
    async getPermission() {
      const allowOtherDeduction = await getTypeSwitch({ jobOnoffType: 2 });
      this.isAllowOtherDeduction = allowOtherDeduction === '1';
      const allowPackingDeduction = await getTypeSwitch({ jobOnoffType: 5 });
      this.isAllowPackingDeduction = allowPackingDeduction === '1';
    },
    onCancel() {
      this.$router.back();
    },
  },
};
</script>
<style lang="scss" scoped>
.checking-weight-other {
  background-color: white;
  .cancelTab,
  .saveTab {
    width: 50%;
    padding: 10px 10px;
  }
  .com-btn {
    padding: 0 60px;
    margin-bottom: 15px;
  }
  ::v-deep(.van-field__control) {
    text-align: right;
  }
}
</style>
