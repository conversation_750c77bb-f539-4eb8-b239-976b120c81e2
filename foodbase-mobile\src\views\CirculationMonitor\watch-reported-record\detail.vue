<template>
  <div class="circulation-monitor">
    <div class="basic-info">
      <div class="title mb-5">基础信息</div>
      <div class="basic-item">
        <span class="basic-item-label">监测点：</span>
        <span class="basic-item-value">{{ basicInfo.monitorPointName || '-' }}</span>
      </div>
      <div class="basic-item">
        <span class="basic-item-label">监测点地区：</span>
        <span class="basic-item-value">{{ basicInfo.areaName || '-' }}</span>
      </div>
      <div class="basic-item">
        <span class="basic-item-label">上报日期：</span>
        <span class="basic-item-value">{{ basicInfo.monitorTime || '-' }}</span>
      </div>
      <div class="basic-item">
        <span class="basic-item-label">备注：</span>
        <span class="basic-item-value">{{ basicInfo.remark || '-' }}</span>
      </div>
    </div>
    <div class="price-info" v-for="(item, index) in priceInfoData" :key="index">
      <div class="price-info-title">
        <strong>{{ item.foodCategoryName }}</strong>
        <Tag :color="grainTypeMap[Number(item.foodCategoryType)]?.color" size="medium">{{
          grainTypeMap[Number(item.foodCategoryType)]?.label
        }}</Tag>
      </div>
      <Form :model="item">
        <CellGroup inset style="margin: 0">
          <Field
            v-model="item.levelName"
            required
            name="levelName"
            label="等级名称"
            :rules="[{ required: true, message: '请填写等级名称' }]"
            :disabled="true"
          />
          <Field
            v-if="item.foodCategoryType === '1'"
            required
            v-model="item.purchasePrice"
            name="purchasePrice"
            label="收购价"
            :rules="[{ required: true, message: '请填写收购价' }]"
            :disabled="true"
          >
            <template #right-icon>
              <span>元/吨</span>
            </template>
          </Field>
          <Field
            v-if="item.foodCategoryType === '1'"
            required
            v-model="item.salePrice"
            name="salePrice"
            label="出库价"
            :rules="[{ required: true, message: '请填写出库价' }]"
            :disabled="true"
          >
            <template #right-icon>
              <span>元/吨</span>
            </template>
          </Field>
          <Field
            v-if="item.foodCategoryType !== '1'"
            required
            v-model="item.exitPrice"
            name="exitPrice"
            label="出厂价"
            :rules="[{ required: true, message: '请填写出厂价' }]"
            :disabled="true"
          >
            <template #right-icon>
              <span>{{ item.foodCategoryType === '2' ? '元/公斤' : '元/5L' }}</span>
            </template>
          </Field>
          <Field
            v-if="item.foodCategoryType !== '1'"
            required
            v-model="item.wholesalePrice"
            name="wholesalePrice"
            label="批发价"
            :rules="[{ required: true, message: '请填写批发价' }]"
            :disabled="true"
          >
            <template #right-icon>
              <span>{{ item.foodCategoryType === '2' ? '元/公斤' : '元/5L' }}</span>
            </template>
          </Field>
          <Field
            v-if="item.foodCategoryType !== '1'"
            required
            v-model="item.retailPrice"
            name="retailPrice"
            label="零售价"
            :rules="[{ required: true, message: '请填写零售价' }]"
            :disabled="true"
          >
            <template #right-icon>
              <span>{{ item.foodCategoryType === '2' ? '元/公斤' : '元/5L' }}</span>
            </template>
          </Field>
          <Field
            v-model="item.brand"
            :required="item.foodCategoryType !== '1' ? true : false"
            name="brand"
            label="品牌"
            :rules="[
              item.foodCategoryType !== '1' ? { required: true, message: '请填写品牌' } : {},
            ]"
            :disabled="true"
          />
          <Field
            v-model="item.originName"
            :required="item.foodCategoryType !== '1' ? true : false"
            name="originName"
            label="产地"
            :rules="[
              item.foodCategoryType !== '1' ? { required: true, message: '请填写产地' } : {},
            ]"
            :disabled="true"
          />
          <div class="card-btn" v-if="basicInfo.status !== '2'">
            <Button
              type="primary"
              size="small"
              :disabled="disabled || item.publishStatus === 1"
              @click="handleRejectItem(item.id)"
              v-p="['app-watch-reported-record:check']"
              >驳回</Button
            >
          </div>
        </CellGroup>
      </Form>
    </div>
  </div>
</template>
<script setup>
import { ref, onMounted, computed } from 'vue';
import { useStore } from 'vuex';
import { useRoute } from 'vue-router';
import dayjs from 'dayjs';
import { getPriceByMonitorPointId, rejectDataMaintain } from '@/api/circulation-monitor';
import { Form, Field, Tag, Dialog, Toast, Button, CellGroup } from 'vant';
const store = useStore();
const route = useRoute();

const basicInfo = route.query;

const grainTypeMap = ref({
  1: {
    label: '原粮',
    color: '#317cf4',
  },
  2: {
    label: '成品粮',
    color: '#27bb8a',
  },
  3: {
    label: '食用油及油料',
    color: '#f2826a',
  },
});

const priceInfoData = ref();

const pointUserInfo = ref({});

const getList = async () => {
  let data = await getPriceByMonitorPointId({
    monitorPointId: basicInfo.monitorPointId,
    fillStartTime: basicInfo.fillStartTime,
    fillEndTime: basicInfo.fillEndTime,
    monitorTime: basicInfo.status === '2' ? basicInfo.monitorTime : null,
  });
  pointUserInfo.value = data.id ? data : null;
  priceInfoData.value = data?.map((i) => {
    if (i.foodCategoryType === '1') {
      i.purchasePrice = i.purchasePrice ? i.purchasePrice * 1000 : '';
      i.salePrice = i.salePrice ? i.salePrice * 1000 : '';
    }
    i.foodCategoryType = String(i.foodCategoryType);
    i.levelName = categoryLevels.value?.find((m) => m.value === i.level)?.label;
    return i;
  });
};

const handleRejectItem = async (id) => {
  Dialog.confirm({
    title: '是否确认驳回？',
    cancelText: '取消',
    okText: '确定',
  })
    .then(async () => {
      await rejectDataMaintain({
        id,
      });
      Toast.success('驳回成功');
      getList();
    })
    .catch(() => {});
};

const categoryLevels = computed(() => {
  return store.getters['dict/reserveDictOf']('GRAIN_AND_OIL_QUALITY_INSPECTION_GRADE');
});

const disabled = computed(() => {
  const monitorTime = basicInfo.monitorTime?.split(' ')?.[0] + ' 23:59:59';
  const end = dayjs(new Date(monitorTime)).day(7);
  return end.valueOf() < new Date().getTime();
});

onMounted(async () => {
  getList();
});
</script>

<style lang="scss" scoped>
.basic-info {
  background: #fff;
  padding: 10px;
  margin: 10px 0;

  .title {
    position: relative;
    padding-left: 12px; /* 左侧留出竖线的空间 */
  }

  .title::before {
    content: '';
    position: absolute;
    left: 0;
    top: 50%; /* 竖线垂直居中 */
    transform: translateY(-50%);
    height: 80%; /* 竖线高度与容器高度相同 */
    width: 5px; /* 竖线宽度 */
    background: #23b49c; /* 竖线颜色 */
  }

  .basic-item {
    display: flex;
    margin-bottom: 5px;
    .basic-item-label {
      min-width: 100px !important;
      color: #888;
    }
  }
}

.price-info {
  background: #fff;
  padding: 10px;
  margin-bottom: 10px;
  .price-info-title {
    display: flex;
    justify-content: space-between;
  }
}

.card-btn {
  width: 100%;
  margin-top: 10px;
  padding-right: 5px;
  display: flex;
  justify-content: flex-end;
}
</style>
