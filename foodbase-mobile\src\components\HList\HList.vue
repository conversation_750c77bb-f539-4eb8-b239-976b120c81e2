<template>
  <div class="h-list">
    <van-pull-refresh @refresh="onRefresh" v-model="refreshLoading">
      <van-list
        ref="listRef"
        v-model:loading="isLoading"
        :finished="finished"
        :finished-text="list?.length > 0 ? '没有更多了' : ''"
        @load="getList"
      >
        <slot name="list-wrapper" :list="list"></slot>
      </van-list>
    </van-pull-refresh>
    <van-empty
      class="empty-camera"
      v-if="!list || list.length <= 0"
      description="暂无数据"
    />
  </div>
</template>

<script>
export default {
  name: 'HList',
}
</script>

<script setup>
import { ref } from 'vue'

const props = defineProps({
  getListFn: {
    type: Function,
    default: () => {}
  },
  searchParams: {
    type: Object,
    default: () => {}
  },
})

const refreshLoading = ref(false);

const isLoading = ref(false);

const finished = ref(false);

const listRef = ref(null);

const list = ref([]);

const pagination = ref({
  page: 0,
  size: 10,
  total: 0,
});

const filter = () => {
  finished.value = false;
  list.value = [];
  pagination.value.page = 0;
  pagination.value.total = 0;
  listRef.value?.check();
}

const getList = async () => {
  try {
    pagination.value.page += 1;
    let { data: { obj } } = await props.getListFn({ ...props.searchParams, ...pagination.value });
    list.value.push(...obj.list);
    pagination.value.total = obj.total;
    // 加载状态结束
    isLoading.value = false;

    // 数据全部加载完成
    if (list.value.length >= pagination.value.total) {
      finished.value = true;
    }
  } catch {
    isLoading.value = false;
    finished.value = true;
  }
}

const onRefresh = () => {
  if (refreshLoading.value) {
    pagination.value.page = 0;
    pagination.value.total = 0;
    list.value = [];
    refreshLoading.value = false;
  }
  finished.value = false;
  isLoading.value = true;
  getList();
};

defineExpose({
  filter,
  refreshLoading,
  isLoading,
  finished,
  listRef,
  list,
  pagination,
})
</script>

<style lang="scss" scoped>
.h-list {}
</style>
