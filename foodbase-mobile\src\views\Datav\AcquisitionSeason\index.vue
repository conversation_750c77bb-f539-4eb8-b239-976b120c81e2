<template>
  <div class="pb-6">
    <van-tabs v-model:active="active">
      <van-tab
        v-for="item in foodCategoryList"
        :key="item.id"
        :title="item.foodBigCategory"
      ></van-tab>
    </van-tabs>

    <HCard title="" class="mt-3">
      <div class="p-4">
        <van-row gutter="10">
          <van-col span="12">
            <div class="flex p-3 rounded-md bg-blue-50">
              <img src="../GrainQuality/images/full-warehouse.svg" alt="" srcset="" />
              <div class="ml-3">
                <p>今日收购库点</p>
                <p class="">
                  <span class="text-blue-500 text-[20px] font-bold mr-3">
                    {{ acquisitionData?.[0]?.storeNum ?? '-' }} </span
                  >个
                </p>
              </div>
            </div>
          </van-col>
          <van-col span="12">
            <div class="flex p-3 rounded-md bg-blue-50">
              <img src="../GrainQuality/images/quality-improvement.svg" alt="" srcset="" />
              <div class="ml-3">
                <p>今日已收购数量</p>
                <p class="">
                  <!-- <span class="text-blue-500 text-[20px] font-bold mr-3">
                    {{ acquisitionData?.[0]?.acquisitionData?.toLocaleString() ?? '-' }} </span
                  >个 -->
                  <span class="text-blue-500 text-[20px] font-bold mr-3">
                    {{ acquisitionData?.[0]?.acquiredNum?.toLocaleString() ?? '-' }} </span
                  >个
                </p>
              </div>
            </div>
          </van-col>
        </van-row>
      </div>
    </HCard>

    <HCard title="收购计划" class="mt-3" v-if="!isSichuan">
      <template #header-year>
        <HYearPicker v-model:value="year" />
      </template>
      <template #header-extra>
        <span class="text-gray-400">单位：吨</span>
      </template>
      <div class="p-4 mt-3 acquisitionClass">
        <van-row gutter="10">
          <van-col span="10">
            <div class="flex rounded-md">
              <div class="">
                <p class="text-center">订单计划</p>
                <p class="mb-0 text-center">
                  <span class="text-yellow-500 text-[20px] font-bold">
                    {{ acquisitionPlan?.[0]?.planNumber?.toLocaleString() ?? '-' }}
                  </span>
                </p>
              </div>
            </div>
          </van-col>
          <van-col span="14">
            <div class="flex rounded-md">
              <div class="ml-5">
                <p class="text-center">订单收购进度</p>
                <p class="mb-0 text-center">
                  <span class="text-yellow-500 text-[20px] font-bold">
                    {{ acquisitionPlan?.[0]?.acquisitionProgress ?? '-' }}
                  </span>
                </p>
              </div>
            </div>
          </van-col>
        </van-row>
      </div>
      <div class="p-4 mt-3">
        <van-row gutter="10">
          <van-col span="8">
            <div class="flex rounded-md">
              <div class="ml-1">
                <p class="text-center">已收购数量</p>
                <p class="mb-0 text-center">
                  <span class="text-yellow-500 text-[20px] font-bold">
                    {{ acquisitionPlan?.[0]?.completedNumber?.toLocaleString() ?? '-' }}
                  </span>
                </p>
              </div>
            </div>
          </van-col>
          <van-col span="8">
            <div class="flex rounded-md">
              <div class="ml-1">
                <p class="text-center">订单收购数量</p>
                <p class="mb-0 text-center">
                  <span class="text-yellow-500 text-[20px] font-bold">
                    {{ acquisitionPlan?.[0]?.inOrder?.toLocaleString() ?? '-' }}
                  </span>
                </p>
              </div>
            </div>
          </van-col>
          <van-col span="8">
            <div class="flex rounded-md">
              <div class="">
                <p class="text-center">订单外收购数量</p>
                <p class="mb-0 text-center">
                  <span class="text-yellow-500 text-[20px] font-bold">
                    {{ acquisitionPlan?.[0]?.outOrder?.toLocaleString() ?? '-' }}
                  </span>
                </p>
              </div>
            </div>
          </van-col>
        </van-row>
      </div>
    </HCard>

    <HCard title="订单合同" class="mt-3" v-if="!isSichuan">
      <!-- <template #header-extra>
        <span class="text-gray-400">单位：吨</span>
      </template> -->
      <template #header-year>
        <HYearPicker v-model:value="year_set" />
      </template>
      <div class="p-4 mt-3">
        <van-row gutter="10">
          <van-col span="8">
            <div class="flex rounded-md">
              <div class="ml-3">
                <p class="flex text-center">
                  <img
                    src="@/views/Datav/AcquisitionSeason/images/signed-quantity.svg"
                    class="mr-1"
                    alt=""
                    srcset=""
                  />
                  <span style="white-space: nowrap">签订数量(吨)</span>
                </p>
                <p class="mb-0 text-center">
                  <span class="text-black-500 text-[18px] font-bold mr-1">
                    {{ orderContract?.[0]?.num?.toLocaleString() ?? '-' }}
                  </span>
                </p>
              </div>
            </div>
          </van-col>
          <van-col span="8">
            <div class="flex rounded-md">
              <div class="ml-3">
                <p class="flex text-center">
                  <img
                    src="@/views/Datav/AcquisitionSeason/images/signed-area.svg"
                    class="mr-1"
                    alt=""
                    srcset=""
                  />
                  <span style="white-space: nowrap">签订面积(亩)</span>
                </p>
                <p class="mb-0 text-center">
                  <span class="text-black-500 text-[18px] font-bold mr-1">
                    {{ orderContract?.[0]?.areas?.toLocaleString() ?? '-' }}
                  </span>
                </p>
              </div>
            </div>
          </van-col>
          <van-col span="8">
            <div class="flex rounded-md">
              <div class="ml-3">
                <p class="flex text-center">
                  <img
                    src="@/views/Datav/AcquisitionSeason/images/signing-amount.svg"
                    class="mr-1"
                    alt=""
                    srcset=""
                  />
                  <span style="white-space: nowrap">户数(户)</span>
                </p>
                <p class="mb-0 text-center">
                  <span class="text-black-500 text-[18px] font-bold mr-1">
                    {{ orderContract?.[0]?.contractNum?.toLocaleString() ?? '-' }}
                  </span>
                </p>
              </div>
            </div>
          </van-col>
        </van-row>
      </div>
    </HCard>

    <HCard title="售粮情况" class="mt-3">
      <template #header-extra>
        <span class="text-gray-400">单位：辆</span>
      </template>
      <div class="p-4">
        <van-row gutter="10">
          <van-col span="12">
            <div class="flex p-3 rounded-md bg-blue-50">
              <img
                src="@/views/Datav/AcquisitionSeason/images/waiting-for-sample.svg"
                alt=""
                srcset=""
              />
              <div class="ml-3">
                <p class="">
                  <span class="text-[20px] font-bold mr-3">
                    {{ grainSalest?.find((i) => i.numType === 'to_sampling')?.num ?? '-' }}
                  </span>
                </p>
                <p>待扦样</p>
              </div>
            </div>
          </van-col>
          <van-col span="12">
            <div class="flex p-3 rounded-md bg-blue-50">
              <img
                src="@/views/Datav/AcquisitionSeason/images/waiting-for-quality-inspection.svg"
                alt=""
                srcset=""
              />
              <div class="ml-3">
                <p class="">
                  <span class="text-[20px] font-bold mr-3">
                    {{ grainSalest?.find((i) => i.numType === 'to_testing')?.num ?? '-' }}
                  </span>
                </p>
                <p>待质检</p>
              </div>
            </div>
          </van-col>
          <van-col class="mt-3" span="8">
            <div class="flex p-3 rounded-md bg-blue-50">
              <img
                src="@/views/Datav/AcquisitionSeason/images/to-be-weighed.svg"
                alt=""
                srcset=""
              />
              <div class="ml-3">
                <p class="">
                  <span class="text-[20px] font-bold mr-3">
                    {{ grainSalest?.find((i) => i.numType === 'to_weight')?.num ?? '-' }}
                  </span>
                </p>
                <p>待称重</p>
              </div>
            </div>
          </van-col>
          <van-col class="mt-3" span="8">
            <div class="flex p-3 rounded-md bg-blue-50">
              <img src="@/views/Datav/AcquisitionSeason/images/weighing.svg" alt="" srcset="" />
              <div class="ml-3">
                <p class="">
                  <span class="text-[20px] font-bold mr-3">
                    {{ grainSalest?.find((i) => i.numType === 'weighting')?.num ?? '-' }}
                  </span>
                </p>
                <p>称重中</p>
              </div>
            </div>
          </van-col>
          <van-col class="mt-3" span="8">
            <div class="flex p-3 rounded-md bg-blue-50">
              <img
                src="@/views/Datav/AcquisitionSeason/images/waiting-warehouse.svg"
                alt=""
                srcset=""
              />
              <div class="ml-3">
                <p class="">
                  <span class="text-[20px] font-bold mr-3">
                    {{ grainSalest?.find((i) => i.numType === 'to_house')?.num ?? '-' }}
                  </span>
                </p>
                <p>待值仓</p>
              </div>
            </div>
          </van-col>
        </van-row>
      </div>
    </HCard>

    <HCard title="粮款结算" class="mt-3">
      <template #header-extra>
        <span class="text-gray-400" v-if="isSichuan">单位：吨、元</span>
        <span class="text-gray-400" v-else>单位：万元</span>
      </template>
      <div class="p-4 mt-3">
        <van-row gutter="10">
          <van-col span="12" v-if="isSichuan">
            <div class="flex rounded-md" :class="{ 'justify-center': isSichuan }">
              <div class="ml-3">
                <p class="text-center">未结算数量</p>
                <p class="mb-0 text-center">
                  <span class="text-yellow-500 text-[20px] font-bold">
                    {{
                      (
                        grainSettlement?.find((i) => i.amountType === 'unsettled')?.amount ?? 0
                      )?.toLocaleString() || '-'
                    }}
                  </span>
                </p>
              </div>
            </div>
          </van-col>
          <van-col :span="isSichuan ? 12 : 8">
            <div class="flex rounded-md" :class="{ 'justify-center': isSichuan }">
              <div class="ml-3">
                <p class="text-center">已结算粮款</p>
                <p v-if="isSichuan">
                  <span class="text-yellow-500 text-[20px] font-bold">
                    {{
                      (
                        grainSettlement?.find((i) => i.amountType === 'settle')?.amount ?? 0
                      )?.toLocaleString() || '-'
                    }}
                  </span>
                </p>
                <p class="mb-0 text-center" v-else>
                  <span class="text-yellow-500 text-[20px] font-bold">
                    {{
                      +(
                        (grainSettlement?.find((i) => i.amountType === 'settle')?.amount ?? 0) /
                        10000
                      )
                        ?.toFixed(0)
                        ?.toLocaleString() || '-'
                    }}
                  </span>
                </p>
              </div>
            </div>
          </van-col>
          <van-col span="8" v-if="!isSichuan">
            <div class="flex rounded-md">
              <div class="ml-3">
                <p class="text-center">已付款金额</p>
                <p class="mb-0 text-center">
                  <span class="text-yellow-500 text-[20px] font-bold">
                    {{
                      +((grainSettlement?.find((i) => i.amountType === 'pay')?.amount ?? 0) / 10000)
                        ?.toFixed(0)
                        ?.toLocaleString() || '-'
                    }}
                  </span>
                </p>
              </div>
            </div>
          </van-col>
          <van-col span="8" v-if="!isSichuan">
            <div class="flex rounded-md">
              <div class="ml-3">
                <!-- <p class="text-center">已开票粮款</p> -->
                <p class="text-center">订单奖励合计</p>
                <p class="mb-0 text-center">
                  <span class="text-yellow-500 text-[20px] font-bold">
                    {{
                      +(
                        (grainSettlement?.find((i) => i.amountType === 'reward')?.amount ?? 0) /
                        10000
                      )
                        ?.toFixed(0)
                        ?.toLocaleString() || '-'
                    }}
                  </span>
                </p>
              </div>
            </div>
          </van-col>
        </van-row>
      </div>
    </HCard>
  </div>
</template>

<script setup>
import { ref, onMounted, computed, watch } from 'vue';
import { HCard } from '@/components';
import HYearPicker from '@/components/HYearPicker/HYearPicker';
import { useStore } from 'vuex';
import {
  getAcquisitionData,
  getPurchaseCategoryFn,
  getAcquisitionPlan,
  getOrderContract,
  getGrainSalest,
  getGrainSettlement,
} from './apis.js';

const store = useStore();
const userInfo = store.getters['user/userInfo'];
const active = ref(0);
const foodCategoryList = ref([]);
const foodCategoryId = computed(() => foodCategoryList.value[active.value]?.id);

const acquisitionData = ref([]);
const acquisitionPlan = ref([]);
const orderContract = ref([]);
const grainSalest = ref([]);
const grainSettlement = ref([]);
const year = ref(String(new Date().getFullYear()));
const year_set = ref(String(new Date().getFullYear()));
const isSichuan = computed(() => {
  return process.env.VUE_APP_MODE === 'sichuan';
});

const getData = async () => {
  acquisitionData.value = await getAcquisitionData(foodCategoryId.value, userInfo);
  grainSalest.value = await getGrainSalest(foodCategoryId.value, userInfo);
  grainSettlement.value = await getGrainSettlement(foodCategoryId.value, userInfo);
  if (!isSichuan.value) {
    orderContract.value = await getOrderContract(foodCategoryId.value, userInfo, year_set.value);
    acquisitionPlan.value = await getAcquisitionPlan(foodCategoryId.value, userInfo, year.value);
  }
};

onMounted(async () => {
  foodCategoryList.value = await getPurchaseCategoryFn();
  getData();
});
watch(
  () => year.value,
  async () => {
    acquisitionPlan.value = await getAcquisitionPlan(foodCategoryId.value, userInfo, year.value);
  },
);
watch(
  () => year_set.value,
  async () => {
    orderContract.value = await getOrderContract(foodCategoryId.value, userInfo, year_set.value);
  },
);
watch(
  () => foodCategoryId.value,
  async () => {
    getData();
  },
);
</script>

<style lang="scss" scoped>
.acquisitionClass {
  display: flex;
  flex-direction: row;
  justify-content: center;
}
</style>
