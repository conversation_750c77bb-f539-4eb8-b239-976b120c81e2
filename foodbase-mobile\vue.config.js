const path = require('path');
const { VantResolver } = require('@vant/auto-import-resolver');
const ComponentsPlugin = require('unplugin-vue-components/webpack');

const config = {
  publicPath: './',
  outputDir: process.env.VUE_APP_OUT_DIR || 'dist',
  productionSourceMap: false,
  transpileDependencies: true,
  devServer: {},
  configureWebpack: {
    plugins: [
      ComponentsPlugin({
        resolvers: [VantResolver()],
      }),
    ],
  },
  chainWebpack: (config) => {
    const svgRule = config.module.rule('svg');
    svgRule.exclude.add(path.resolve(__dirname, './src/assets/svg-icons')).end();

    const iconRule = config.module.rule('svg-icon');
    iconRule
      .test(/\.(svg)(\?.*)?$/)
      .include.add(path.resolve(__dirname, './src/assets/svg-icons'))
      .end()
      .use('svg-sprite-loader')
      .loader('svg-sprite-loader')
      .options({
        symbolId: 'icon-[name]',
      })
      .end();

    config.plugin('copy').tap((args) => {
      args[0].patterns[0].globOptions.ignore.push(path.resolve(__dirname, './public/app.html'));
      return args;
    });

    config.plugin('html').tap((args) => {
      args[0].template = path.resolve(__dirname, './public/app.html');
      return args;
    });
  },
  css: {
    loaderOptions: {
      scss: {
        // eslint-disable-next-line prettier/prettier
        additionalData: `
        @import '@/styles/common.scss';
        @import '@/styles/themes/${
          process.env.VUE_APP_THEME_MODE || process.env.VUE_APP_MODE
        }.scss';
        `,
      },
    },
  },
};
config.devServer.proxy = {
      '/hxdiframework': {
        target:'http://10.13.4.50:7003',
        changeOrigin: true,
        ws: true,
      },
      '/MANUAL': {
        target:'http://10.13.4.186:8084',
        changeOrigin: true,
        ws: true,
      },
}
// if (process.env.API_BASE_URL) {
//   config.devServer.proxy = {
//     '/security': {
//       target: process.env.API_BASE_URL,
//       changeOrigin: true,
//       ws: true,
//     },
//     '/atas': {
//       target: process.env.ATLAS_API_BASE_URL,
//       changeOrigin: true,
//       ws: true,
//     },
//     '/remote': {
//       target: process.env.VUE_APP_REMOTE_API_BASE_URL,
//       changeOrigin: true,
//       ws: true,
//     },
//     '/ingest': {
//       target: process.env.VUE_APP_INGEST_API_BASE_URL,
//       changeOrigin: true,
//       ws: true,
//     },
//     '/reserver': {
//       target: process.env.VUE_APP_RESERVER_API_BASE_URL,
//       changeOrigin: true,
//       ws: true,
//     },
//   };
// }

module.exports = config;
