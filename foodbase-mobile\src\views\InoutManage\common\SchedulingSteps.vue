<template>
  <div class="scheduling-steps">
    <div class="steps-warpper">
      <div class="step active">
        <div class="step-info">
          <SvgIcon name="info-record"></SvgIcon>
          <div class="step-label">基本信息填写</div>
        </div>
        <div class="step-line active"></div>
      </div>
      <div class="step" :class="{ active: props.active === 1 }">
        <div class="step-line" :class="{ active: props.active === 1 }"></div>
        <div class="step-info">
          <SvgIcon :name="props.active === 1 ? 'car-record-active' : 'car-record'"></SvgIcon>
          <div class="step-label">安排作业车辆</div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import SvgIcon from '@/components/SvgIcon/SvgIcon';

const props = defineProps({
  active: Number,
});
</script>

<style lang="scss" scoped>
.scheduling-steps {
  background: #ffffff;
  .steps-warpper {
    padding: 10px 30px;
    display: flex;
    .step {
      display: flex;
      align-items: center;
      color: #86909c;
      flex: 1;
      &.active {
        color: #1677ff;
      }
      .step-info {
        display: flex;
        flex-direction: column;
        justify-content: center;
        align-items: center;
        width: 6em;
        .step-label {
          font-size: 14px;
          font-weight: 500;
          line-height: 20px;
        }
      }
      .step-line {
        width: calc(100% - 6em);
        height: 1px;
        background: #86909c;
        &.active {
          background: #1677ff;
        }
      }
    }
  }
}
</style>
