<template>
  <div class="module">
    <Steps direction="vertical" active-color="#1973F1" :active="active">
      <Step class="step-item" v-for="item in aprroval" :key="item">
        <p class="step-item-txt step-item-status">
          <span>{{ item?.username }}</span
          ><span class="status-submit">{{ item?.approvedStr }}</span>
        </p>
        <p class="step-item-txt">{{ item?.deptName }}</p>
        <p class="step-item-txt step-item-time">{{ item?.updateTime }}</p>
      </Step>
    </Steps>
  </div>
</template>

<script setup>
import { Step, Steps } from 'vant';
import { defineProps, onMounted, ref, computed } from 'vue';
import { getApproveRemark } from '@/api/job-approve';

const props = defineProps({
  id: [String, Number],
  coder: String,
});
const aprroval = ref([]);

const active = computed(() => {
  const index = aprroval.value.findIndex((it) => it.approved == 0);
  return index !== -1 ? index - 1 : aprroval.value.length - 1;
});

const getApproval = async () => {
  const { id, coder } = props;
  const data = await getApproveRemark({ id, coder: coder?.split('-')[0] });
  aprroval.value = data.reverse();
};

onMounted(() => {
  getApproval();
});
</script>

<style scoped lang="scss">
.module {
  .step-item-txt {
    color: #232323;
    font-size: 16px;
    line-height: 30px;
  }
  .step-item-status {
    display: flex;
    justify-content: space-between;
    font-weight: bold;
  }
  .status-success {
    color: #14c287;
    font-size: 14px;
  }
  .status-submit {
    color: #14c287;
    font-size: 14px;
  }
  .status-check {
    color: #1973f1;
    font-size: 14px;
  }
  .status-wait {
    color: #ffa40d;
    font-size: 14px;
  }
  .step-item-time {
    font-size: 14px;
    color: #686b73;
  }
}
</style>
