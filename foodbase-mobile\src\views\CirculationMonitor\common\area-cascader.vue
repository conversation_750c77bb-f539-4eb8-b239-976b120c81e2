<template>
  <Cascader
    :value="areaCode"
    title="请选择监测地区"
    :options="areaOptions"
    active-color="#1989fa"
    :field-names="fieldNames"
    @close="onClose"
    @change="onChange"
    @finish="onFinish"
  />
</template>

<script setup>
import { forEach, keyBy } from 'lodash-es';
import { onMounted, ref, watch } from 'vue';
import { getAreaData } from '@/api/area.js';
import { getUserWatchInfo } from '@/api/circulation-monitor';
import { findParentIdsById } from '@/utils/tools';
import { Cascader } from 'vant';
import { useStore } from 'vuex';

/**
 * 地区级联控件
 * @param {Boolean} previewText 回显flag
 * @param {Boolean} changeOnSelect 是否限制只能选中最后节点
 * @param {String} defaultValue 地区最下层id
 * @param {String} isMarket 仅展示到市
 * @param {String} isEditCity 把满洲里市和二连浩特市提升到和呼伦贝尔市同级(修改pcoder为150000)
 **/

const props = defineProps({
  isMarket: {
    type: Boolean,
    default: false,
  },
  defaultValue: {
    type: String,
    default: '',
  },
  previewText: {
    type: Boolean,
    default: false,
  },
  placeholder: {
    type: String,
    default: '请选择地区',
  },
  changeOnSelect: {
    type: Boolean,
    default: false,
  },
  maxTagCount: {
    type: Number,
    default: 2,
  },
  multiple: {
    type: Boolean,
    default: false,
  },
  currentCode: {
    type: String,
    default: '',
  },
  isFilter: {
    type: String,
    default: '1',
  },
  isEditCity: {
    type: Boolean,
    default: false,
  },
  allowClear: {
    type: Boolean,
    default: true,
  },
});

const emit = defineEmits(['onClose']);
const store = useStore();
const userInfo = store.state.user.info;
const watchPointInfo = ref();
const areaCode = ref([]);
const areaOptions = ref([]);

const fieldNames = {
  text: 'label',
  value: 'value',
  children: 'children',
};

// 根据角色权限过滤所属数据
const getFilter = (tree = [], code) => {
  code = code.toString();
  const result = [];

  function findChildren(node) {
    if (node.code === code) {
      result.push(node);
    }

    if (node.children && node.children.length > 0) {
      node.children.forEach((child) => {
        findChildren(child);
      });
    }
  }

  tree.forEach((node) => {
    findChildren(node);
  });
  return result;
};

const generateAreaTree = async (areaList) => {
  const neatList = areaList.map((it) => {
    return {
      code: it.coder,
      value: it.coder,
      pcode: props.isEditCity
        ? it.name === '满洲里市'
          ? '150000'
          : it.name === '二连浩特市'
          ? '150000'
          : it.pcoder
        : it.pcoder,
      name: it.name,
      label: it.name,
      areaSort: it.areaSort,
    };
  });
  const areaMap = keyBy(neatList, 'code');
  const tree = [];
  forEach(areaMap, (area) => {
    const { pcode } = area;
    // delete area.pcode;
    if (pcode !== '0') {
      const parent = areaMap[pcode];
      area.pname = areaMap[pcode].name;
      if (!parent.children) {
        parent.children = [];
      }
      parent.children.push(area);
    } else {
      tree.push(area);
    }
  });
  // tree = areaSort(tree)
  let code = '';
  if (watchPointInfo.value && watchPointInfo.value.id) {
    code =
      watchPointInfo.value.areaCode ||
      watchPointInfo.value.cityCode ||
      watchPointInfo.value.provinceCode;
  } else {
    code = userInfo.areaCode || userInfo.cityCode || userInfo.provinceCode || props.currentCode;
  }
  if (code.toString().endsWith('01')) {
    code = `${code.slice(0, code.length - 2)}00`;
  }
  // 满洲里和二连浩特只展示市
  if (userInfo.cityCode === '152500') {
    code = '152501';
  }
  if (userInfo.cityCode === '150700') {
    code = '150781';
  }
  return getFilter(tree, code);
};

const getAreaList = async () => {
  const data = await getAreaData(props.isFilter);
  areaOptions.value = await generateAreaTree(data);
  // 删除县及下面的
  if (props.isMarket) {
    for (const key in areaOptions.value) {
      // 删除第三层children属性
      for (const i in areaOptions.value[key].children) {
        delete areaOptions.value[key].children[i].children;
      }
    }
  }
  if (props.previewText && props.defaultValue?.length > 0) {
    if (props.multiple) {
      // 多选回显
      const cloneValue = props.defaultValue;
      cloneValue.forEach((res) => {
        const data1 = findParentIdsById(res, areaOptions.value[0]);
        areaCode.value.push(data1);
      });
    } else {
      areaCode.value = findParentIdsById(props.defaultValue, areaOptions.value[0]);
    }
  }
};

const onClose = () => {
  emit('close');
};

const onFinish = (value) => {
  emit('finish', value);
};

// const handleChange = (value, selectedOptions) => {
//   if (props.multiple) {
//     const arr1 = JSON.parse(JSON.stringify(value))
//     const arr = []
//     arr1.forEach((res) => {
//       arr.push(res.pop())
//     })
//     emit('updateValue', { value: arr, selectedOptions }) // 多选 值取id 合成一个数组 例:['1111','222']返回
//   }
//   else {
//     emit('updateValue', { value: value?.length > 0 ? value.slice(-1)[0] : value, selectedOptions })
//   }
// }

onMounted(async () => {
  watchPointInfo.value = await getUserWatchInfo();
  getAreaList();
});

watch(
  () => props.defaultValue,
  () => {
    areaCode.value = findParentIdsById(props.defaultValue, areaOptions.value[0]);
  },
);
</script>
