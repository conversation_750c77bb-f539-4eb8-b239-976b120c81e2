<template>
  <div class="checking-weight-detail">
    <Row>
      <Col :span="12">
        <Search v-model="date" placeholder="选择日期区间" readonly @click="datePicker = true" />
      </Col>
      <Col :span="12">
        <Search v-model="search" placeholder="请输入调度号" show-action @search="onSearch">
          <template #action>
            <div class="scan-action" @click="onScan">
              <SvgIcon name="scan" />
            </div>
          </template>
        </Search>
      </Col>
    </Row>
    <Calendar
      v-model:show="datePicker"
      allow-same-day
      type="range"
      :min-date="minDate"
      :default-date="[new Date(startTime), new Date(endTime)]"
      @confirm="onConfirm"
    />
    <PullRefresh @refresh="onRefresh" v-model="refreshLoading">
      <List
        ref="listRef"
        v-model:loading="isLoading"
        :finished="finished"
        finished-text="没有更多了"
        @load="getList"
      >
        <HCard class="detail-card" v-for="item in list" :key="item">
          <div class="card-content">
            <div class="card-head">
              <img :src="IconHouse" />
              <div class="schedule-num">调度号：{{ item.schedulingNo?.split('_')[0] }}</div>
            </div>
            <div class="row">
              <span class="label">作业车牌：</span>
              <span class="carNum" v-for="(it, index) in item.transportVehicleNo" :key="index">
                {{ it }}
              </span>
            </div>
            <div class="row">
              <span class="label">粮油品种：</span>
              <span class="value">{{ item.foodCategoryName }}</span>
            </div>
            <div class="row">
              <span class="label">业务类型：</span>
              <Tag class="tag-cls" :color-type="Number(item.buzTyper)">
                {{ item.buzTyperName }}
              </Tag>
            </div>
            <div class="row">
              <span class="label">值仓仓房：</span>
              <span class="value">
                {{
                  hasCargoSpace && item.cargoSpaceName
                    ? `${item.storeHouseName}_${item.cargoSpaceName}`
                    : item.storeHouseName
                }}
              </span>
            </div>
            <div class="row">
              <span class="label">毛重：</span>
              <span class="value">
                <HFixedNumber :ratio="1000" :fractionDigits="0">
                  {{ item.countMao }}
                </HFixedNumber>
                公斤
              </span>
            </div>
            <div class="row">
              <span class="label">皮重：</span>
              <span class="value">
                <HFixedNumber :ratio="1000" :fractionDigits="0">
                  {{ item.countSkin }}
                </HFixedNumber>
                公斤
              </span>
            </div>
            <div class="row">
              <span class="label">过磅重量：</span>
              <span class="value">
                <HFixedNumber :ratio="1000" :fractionDigits="0">
                  {{ item.countNet }}
                </HFixedNumber>
                公斤
              </span>
            </div>
            <div class="row">
              <span class="label">客户名称：</span>
              <span class="value">{{ item.customerName }}</span>
            </div>
            <div class="row">
              <span class="label">检斤时间：</span>
              <span class="value">{{ item.createTime }}</span>
            </div>
            <div class="row row-btn">
              <Button class="other-button" @click="goToOther(item)"> 其他扣量 </Button>
              <Button class="view-button" @click="goToDetail(item)"> 查看 </Button>
            </div>
          </div>
        </HCard>
      </List>
    </PullRefresh>
  </div>
</template>

<script setup>
import { Search, List, PullRefresh, Button, Row, Col, Calendar } from 'vant';
import { ref, reactive, watch, computed } from 'vue';
import { useRouter, useRoute } from 'vue-router';
import SvgIcon from '@/components/SvgIcon/SvgIcon';
import { HCard } from '@/components';
import IconHouse from '@/assets/icon-house.png';
import Tag from '@/views/InoutManage/common/InOutTypeTag.vue';
import { getWeightHistory } from '@/api/in-out-manage';
import { HFixedNumber } from '@/components';
import dayjs from 'dayjs';
import { checkPermission } from '@/utils/permission';

const router = useRouter();
const route = useRoute();
const search = ref('');
const listRef = ref();

const list = ref([]);
const pagination = reactive({
  page: 0,
  size: 10,
  total: 0,
});

const isLoading = ref(false);
const finished = ref(false);
const refreshLoading = ref(false);

const startTime = ref(dayjs().format('YYYY-MM-DD'));
const endTime = ref(dayjs().format('YYYY-MM-DD'));
const date = ref(`${startTime.value} - ${endTime.value}`);
const datePicker = ref(false);
const minDate = new Date(dayjs().subtract(10, 'year').format('YYYY-MM-DD'));
const hasCargoSpace = computed(() => checkPermission(['cargo_space']));

const onScan = () => {
  window.cordova?.plugins.barcodeScanner.scan(
    (result) => {
      if (result.text) {
        goToDetail(result.text);
      }
    },
    (error) => {
      alert('扫码失败 ' + error);
    },
    {
      prompt: '请将二维码放在扫码框中',
      resultDisplayDuration: 300,
    },
  );
};
const onRefresh = () => {
  if (refreshLoading.value) {
    pagination.page = 0;
    pagination.total = 0;
    list.value = [];
    refreshLoading.value = false;
  }
  finished.value = false;
  isLoading.value = true;
  getList();
};
const onSearch = () => {
  finished.value = false;
  list.value = [];
  pagination.page = 0;
  pagination.total = 0;
  listRef.value?.check();
};
const getList = async () => {
  const { items, page, total } = await getWeightHistory({
    schedulingNo: search.value ? search.value : undefined,
    page: pagination.page + 1,
    size: pagination.size,
    startTime: dayjs(startTime.value).format('YYYY-MM-DD 00:00:00'),
    endTime: dayjs(endTime.value).format('YYYY-MM-DD 23:59:59'),
  });
  list.value.push(...items);
  pagination.page = page;
  pagination.total = total;

  // 加载状态结束
  isLoading.value = false;

  // 数据全部加载完成
  if (list.value.length >= total) {
    finished.value = true;
  }
};

const onConfirm = (values) => {
  const [start, end] = values;
  startTime.value = dayjs(start).format('YYYY-MM-DD');
  endTime.value = dayjs(end).format('YYYY-MM-DD');
  date.value = `${startTime.value} - ${endTime.value}`;
  datePicker.value = false;
  onSearch();
};

const goToDetail = (item) => {
  router.push({
    name: 'CheckingWeightRecordDetail',
    params: {
      schedulingNo: JSON.stringify(item),
    },
  });
};
const goToOther = (item) => {
  router.push({
    name: 'CheckingWeightRecordOther',
    params: {
      schedulingNo: JSON.stringify(item),
    },
  });
};

watch(
  () => route.name,
  () => {
    search.value = '';
    onSearch();
  },
);
</script>

<style lang="scss" scoped>
.scan-action {
  font-size: 25px;
  height: 34px;
  display: flex;
  align-items: center;
  padding: 0 10px;
}
.detail-card {
  padding: 16px;
  margin-bottom: 10px;
}
.card-head {
  display: flex;
  align-items: center;
  justify-content: space-between;
  font-size: 18px;
  font-weight: bold;
  color: #1f3359;
  .next {
    color: rgba(109, 116, 141, 0.6);
  }
}
.schedule-num {
  flex-grow: 1;
  margin-left: 12px;
}
.card-content {
  margin-top: 8px;
  line-height: 30px;
  .row {
    display: flex;
    .label {
      font-size: 14px;
      font-weight: 400;
      color: #6d748d;
      flex-basis: 6em;
    }
    .value {
      font-size: 14px;
      font-weight: 400;
      color: #0f0f0f;
      flex-grow: 1;
    }
    .carNum {
      padding: 3px 6px;
      background: #f4f4f4;
      border-radius: 2px;
      border: 1px solid #ebedf0;
      margin-right: 5px;
    }
  }
  .row-btn {
    border-top: 1px solid #ccc;
    padding-top: 10px;
    justify-content: space-between;
    .van-button {
      height: 36px;
      text-align: center;
      font-size: 15px;
      font-weight: 400;
      line-height: 36px;
      border: 1px #165dff solid;
    }
    ::v-deep(.other-button) {
      width: 30%;
      color: #165dff;
    }
    ::v-deep(.view-button) {
      width: 25%;
      background-color: #165dff;
      color: #fff;
    }
  }
}
.tag-cls {
  line-height: 25px;
  &.tag {
    margin-left: 0;
  }
}
.next-contain {
  width: 80px;
  text-align: right;
}
.icon-cls {
  vertical-align: text-top;
}
</style>
