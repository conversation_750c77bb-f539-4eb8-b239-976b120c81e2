<template>
  <div class="checking-weight-detail page-warpper">
    <div class="noraml" v-bind:class="{ 'info-form': showConfirmTable }">
      <HCard class="info-card">
        <div class="card-head">
          <img :src="IconHouse" />
          <div class="schedule-num">调度号：{{ form.schedulingNo?.split('_')[0] }}</div>
        </div>
        <div class="card-content">
          <div class="row">
            <span class="label">作业车牌：</span>
            <span class="value">{{ form.transportVehicleNo }}</span>
          </div>
          <div class="row">
            <span class="label">粮食品种：</span>
            <span class="value">{{ form.foodCategoryName }}</span>
          </div>
          <div class="row">
            <span class="label">值仓仓房：</span>
            <span class="value">
              {{
                hasCargoSpace && form.cargoSpaceName
                  ? `${form.storeHouseName}_${form.cargoSpaceName}`
                  : form.storeHouseName
              }}
            </span>
          </div>
        </div>
      </HCard>
      <Field
        v-model="count"
        readonly
        :label="form.isWeight === '2' ? '称毛' : form.isWeight === '3' ? '称皮' : '称重点包'"
        placeholder="请获取数据"
        style="margin-bottom: 10px"
      >
        <template #button>
          <Button size="small" type="primary" @click="getCount" class="round-button">
            读取地磅数据
          </Button>
        </template>
      </Field>
      <div v-if="poundDisabled">无法连接到地磅</div>
      <div v-if="form.isWeight !== '6'">
        <Button
          class="confrim-weight one"
          type="primary"
          :loading="weighingLoading"
          :disabled="poundDisabled || !count || count == '0'"
          @click="onWeighing"
        >
          确定本次称重
        </Button>
        <Button class="confrim-weight two" type="primary" @click="goToDetail">数据填报</Button>
      </div>
      <div v-if="form.isWeight === '6'">
        <Button class="confrim-weight two" type="primary" @click="onExtraClick"
          >增加称重数据</Button
        >
      </div>
      <Button
        class="confrim-weight two"
        type="primary"
        @click="isAllDuty"
        :disabled="weightHistory.length === 0"
        >确定净重</Button
      >
      <CheckingWeightHistory
        :refreshTable="refreshTable"
        :showConfirmTable="showConfirmTable"
        :weighTyper="form.isWeight"
        :weightHistory="weightHistory"
        @changeSelect="
          (val) => {
            selected = val;
          }
        "
        @getNetWeight="
          (weight) => {
            checkNetWeight = weight;
          }
        "
        @reFresh="initData"
      />
    </div>
    <div class="bottom-warpper" v-if="showConfirmTable">
      <div class="buttons">
        <Button class="cancel-button" @click="showConfirmTable = false"> 取消 </Button>
        <Button
          class="next-button"
          @click="onConfirm"
          :loading="saveLoading"
          :disabled="selected.length === 0"
        >
          确定
        </Button>
      </div>
      <div class="bar"></div>
    </div>
  </div>
</template>

<script>
import { Field, Button, Toast } from 'vant';
import { HCard } from '@/components';
import { reactive, toRefs, onMounted, computed } from 'vue';
import IconHouse from '@/assets/icon-house.png';
import {
  getWeighingDetail,
  getTypeSwitch,
  getAllEnableInstruction,
  addWeight,
  isDoStoreHouse,
  addTotalWeight,
  confirmWeight,
  confirmNetWeighSingle,
} from '@/api/in-out-manage';
import { useRouter, useRoute } from 'vue-router';
import dayjs from 'dayjs';
import { sumBy } from 'lodash-es';
import axios from 'axios';
import CheckingWeightHistory from './CheckingWeightHistory';
import { checkPermission } from '@/utils/permission';

export default {
  components: { Field, Button, HCard, CheckingWeightHistory },
  setup() {
    const router = useRouter();
    const route = useRoute();
    // const store = useStore();
    const state = reactive({
      form: {},
      count: null,
      weightHistory: [],
      showConfirmTable: false,
      refreshTable: true,
      selected: [],
      checkNetWeight: null,
      weighingLoading: false,
      saveLoading: false,
      isShipAdjustment: 0,
      config: {},
      userData: null,
      token: null,
      poundDisabled: false,
      limitMaoTime: '',
      limitSkinTime: '',
    });
    const hasCargoSpace = computed(() => checkPermission(['cargo_space']));

    const netWeight = computed(() => {
      const selected = state.selected;
      if (selected.length === 0) {
        return 0;
      } else {
        const grossList = selected.filter((item) => item.weighTyper === 2);
        const tareList = selected.filter((item) => item.weighTyper === 3);
        const gross = sumBy(grossList, 'count');
        const tare = sumBy(tareList, 'count');
        return gross - tare;
      }
    });

    const totalWeight = computed(() => {
      var total = 0;
      if (state.weightHistory) {
        state.weightHistory.forEach(function (item) {
          total = total + parseFloat(item.count) * 1000;
        });
        return total;
      } else {
        return 0;
      }
    });
    const averageWeight = computed(() => {
      var average;
      if (state.weightHistory) {
        let totalWeight = 0;
        state.weightHistory.forEach(function (item) {
          totalWeight = totalWeight + parseFloat(item.count) * 1000;
        });
        average = totalWeight / state.weightHistory.length;
        return average;
      } else {
        return 0;
      }
    });

    onMounted(async () => {
      getSwitchByTypeTwo();
      getInstruction();
      initData();
    });

    const goToDetail = () => {
      router.push({
        name: 'CheckingWeightConfirm',
        params: {
          weighTyper: state.form.isWeight,
          outForm: JSON.stringify(state.form),
        },
      });
    };
    const onExtraClick = () => {
      router.push({
        name: 'CheckingWeightAddHistory',
        params: {
          weighTyper: state.form.isWeight,
          record: JSON.stringify(state.form),
        },
      });
    };
    const goToAddData = () => {
      router.push({
        name: 'CheckingWeightAddData',
        params: {
          averageWeight: averageWeight.value,
          totalWeight: totalWeight.value,
          record: JSON.stringify(state.form),
          weightHistory: JSON.stringify(state.weightHistory),
        },
      });
    };
    const initData = async () => {
      const schedulingNo = route.params.schedulingNo;
      state.refreshTable = false;
      const data = await getWeighingDetail({ schedulingNo });
      const { pickUpDetailResVOList, ...rest } = data;
      state.form = rest;
      state.weightHistory = pickUpDetailResVOList;
      state.weightHistory = state.weightHistory.sort(
        (a, b) => dayjs(b.createTime).unix() - dayjs(a.createTime).unix(),
      );
      state.refreshTable = true;
    };
    const getSwitchByTypeTwo = async () => {
      var params = {
        jobOnoffType: 3,
      };
      const data = await getTypeSwitch(params);
      state.isShipAdjustment = data;
    };
    const getInstruction = async () => {
      const instructionConfig = await getAllEnableInstruction();
      state.config = instructionConfig[0];
      const { username, password, platformType, url, port, prefix } = state.config;
      if (platformType == 1) {
        const { status, data } = await axios.post(
          `http://${url}:${port}/api/permission/auth/login`,
          {
            loginName: username,
            password: decodeURIComponent(atob(password)),
          },
        );
        if (status === 200 && data.code == 0) {
          state.poundDisabled = false;
          state.userData = data.data;
          state.token = data.data.token;
        } else {
          state.poundDisabled = true;
        }
        state.poundDisabled = true;
      } else if (platformType == 2) {
        const { status, data } = await axios.post(
          `http://${url}:${port}/hx-smart-iot-platform/authorize/login`,
          {
            username: username,
            password: decodeURIComponent(atob(password)),
          },
        );
        if (status === 200) {
          state.poundDisabled = false;
          state.userData = data.result;
          state.token = data.result.token;
        } else {
          state.poundDisabled = true;
        }
      } else if (platformType == 3) {
        const { status, data } = await axios.post(
          `http://${url}:${port}/${prefix ? prefix + '/' : ''}authorize/login`,
          {
            username: username,
            password: decodeURIComponent(atob(password)),
          },
        );
        if (status === 200) {
          state.poundDisabled = false;
          state.userData = data.result;
          state.token = data.result.token;
        } else {
          state.poundDisabled = true;
        }
        state.poundDisabled = true;
      }
    };

    const getCount = async () => {
      const useWeighbridge = JSON.parse(localStorage.getItem('useWeighbridge'));
      const { url, mqttPort, platformType } = state.config;
      const { devId } = useWeighbridge;
      let data;
      if (platformType == 2) {
        data = await axios.post(
          `http://${url}:${mqttPort}/api/v1/device/${devId}/log/_query`,
          {
            deviceId: devId,
            expires: 0,
          },
          {
            headers: {
              'X-Access-Token': state.token,
            },
          },
        );
      }
      const list = data?.data?.result?.data;
      const latest = list[0];
      if (latest) {
        const content = JSON.parse(latest.content);
        if (content?.properties?.weightInfo) {
          const weightInfo = JSON.parse(content.properties.weightInfo);
          state.count = Number(weightInfo.netWeight);
        }
      }
    };
    const onWeighing = async () => {
      if (!state.count || state.count == '0') {
        return;
      }
      state.weighingLoading = true;
      const count = parseFloat(state.count) / 1000; // 公斤 数量转成吨
      try {
        // // 发送数据给LED
        // this.sendMsgToLED();
        // // 获取图片
        // await this.takeImage();

        const data = {
          ...state.form,
          weighTyper: state.form.isWeight,
          count,
        };

        await addWeight({
          weighDetailDTOList: [data],
          weighingDataType: 1,
          isWeighRepeatedly: state.form.isWeighRepeatedly,
        });
        await initData();
      } catch (err) {
        console.log(err);
      } finally {
        state.weighingLoading = false;
      }
    };
    const isAllDuty = async () => {
      const schedulingNo = route.params.schedulingNo;
      if (schedulingNo) {
        const data = await isDoStoreHouse({
          schedulingNo: schedulingNo,
        });
        if (data === '0') {
          Toast.fail('请先进行值仓，再确认净重');
          return;
        }
      }
      if (state.form.isWeight !== '6') {
        state.showConfirmTable = true;
      } else {
        goToAddData();
      }
    };
    const onConfirm = async () => {
      console.log(state.checkNetWeight);
      if (state.saveLoading) {
        return;
      }
      state.saveLoading = true;
      let countGross = 0;
      let countTare = 0;
      let selectedList = [];
      state.selected.forEach((item) => {
        selectedList.push(state.weightHistory[item]);
      });
      if (selectedList.length === 0) {
        Toast.fail('请选择有效称重数据');
        state.saveLoading = false;
        return;
      }
      selectedList.forEach((item) => {
        if (item.weighTyper == 2) {
          countGross++;
        } else if (item.weighTyper == 3) {
          countTare++;
        }
        //出库业务：获取最早称皮时间 + 最晚称毛时间
        //入库业务：获取最早称毛时间 + 最晚称皮时间
        if (['4', '6', '7'].includes(state.form.buzTyper)) {
          if (item.weighTyper == 2) {
            if (
              !state.limitMaoTime ||
              (state.limitMaoTime &&
                dayjs(item.createTime).valueOf() > dayjs(state.limitMaoTime).valueOf())
            ) {
              state.limitMaoTime = item.createTime;
            }
          } else if (item.weighTyper == 3) {
            if (
              !state.limitSkinTime ||
              (state.limitSkinTime &&
                dayjs(item.createTime).valueOf() < dayjs(state.limitSkinTime).valueOf())
            ) {
              state.limitSkinTime = item.createTime;
            }
          }
        } else {
          if (item.weighTyper == 2) {
            if (
              !state.limitMaoTime ||
              (state.limitMaoTime &&
                dayjs(item.createTime).valueOf() < dayjs(state.limitMaoTime).valueOf())
            ) {
              state.limitMaoTime = item.createTime;
            }
          } else if (item.weighTyper == 3) {
            if (
              !state.limitSkinTime ||
              (state.limitSkinTime &&
                dayjs(item.createTime).valueOf() > dayjs(state.limitSkinTime).valueOf())
            ) {
              state.limitSkinTime = item.createTime;
            }
          }
        }
      });
      if (countGross != countTare) {
        Toast.fail('称毛次数和称皮次数不一致');
        state.saveLoading = false;
        return;
      }
      if (state.form.isWeighRepeatedly == '0') {
        if (countGross != 1 || countTare != 1) {
          Toast.fail('单车单次称重时请选择一条称毛数据和一条称皮数据');
          state.saveLoading = false;
          return;
        }
      }
      if (state.checkNetWeight < 0) {
        Toast.fail('净重非正数,请重新选择称重记录!');
        state.saveLoading = false;
        return;
      }
      if (['4', '6', '7'].includes(state.form.buzTyper)) {
        if (dayjs(state.limitMaoTime).valueOf() < dayjs(state.limitSkinTime).valueOf()) {
          Toast.fail('最早称皮时间不能晚于最后称毛时间，请确认');
          state.saveLoading = false;
          return;
        }
      } else {
        if (dayjs(state.limitMaoTime).valueOf() > dayjs(state.limitSkinTime).valueOf()) {
          Toast.fail('最早称毛时间不能晚于最后称皮时间，请确认');
          state.saveLoading = false;
          return;
        }
      }
      const params = {
        weighDetailDTOList: selectedList,
        weighingDataType: 1,
      };
      if (state.form.isWeighRepeatedly == '1') {
        try {
          await addTotalWeight(params);
        } catch (error) {
          console.log(error);
        } finally {
          onConfirmWeight(selectedList[0]);
        }
      } else {
        try {
          await confirmNetWeighSingle(params);
        } catch (error) {
          console.log(error);
        } finally {
          onConfirmWeight(selectedList[0]);
        }
      }
    };
    const onConfirmWeight = async (record) => {
      const { schedulingNo } = record;
      if (schedulingNo) {
        try {
          await confirmWeight(schedulingNo);
          Toast.success('确认净重成功');
          state.saveLoading = false;
          router.push({
            name: 'CheckingWeight',
          });
        } catch (e) {
          Toast.fail('确认净重失败');
        }
      }
    };

    return {
      ...toRefs(state),
      IconHouse,
      netWeight,
      getCount,
      onWeighing,
      isAllDuty,
      onConfirm,
      goToDetail,
      onExtraClick,
      goToAddData,
      initData,
      hasCargoSpace,
    };
  },
};
</script>

<style lang="scss" scoped>
@import '@/views/InoutManage/style/index';
.noraml {
  margin-bottom: 10px;
}
.info-form {
  margin-bottom: 120px;
}
.info-card {
  padding: 16px;
  margin-bottom: 10px;
}
.card-head {
  display: flex;
  align-items: center;
  justify-content: space-between;
  font-size: 18px;
  font-weight: bold;
  color: #1f3359;
  .next {
    color: rgba(109, 116, 141, 0.6);
  }
}
.schedule-num {
  flex-grow: 1;
  margin-left: 12px;
}
.card-content {
  margin-top: 8px;
  line-height: 30px;
  .row {
    display: flex;
    .label {
      font-size: 14px;
      font-weight: 400;
      color: #6d748d;
      flex-basis: 6em;
    }
    .value {
      font-size: 14px;
      font-weight: 400;
      color: #0f0f0f;
      flex-grow: 1;
    }
    .carNum {
      padding: 3px 6px;
      background: #f4f4f4;
      border-radius: 2px;
      border: 1px solid #ebedf0;
      margin-right: 5px;
    }
  }
}
.round-button {
  background: #165dff;
  border-radius: 20px;
  font-size: 14px;
  font-weight: 400;
  color: #ffffff;
  line-height: 20px;
}
.confrim-weight {
  width: calc(100% - 24px);
  height: 49px;
  margin: 5px 12px;
  border-radius: 4px;
  font-size: 18px;
  font-weight: 400;
  color: #ffffff;
  line-height: 25px;
  text-align: center;
  &.one {
    background: #165dff;
  }
  &.two {
    background: #1f3359;
  }
}
::v-deep(.van-field__label) {
  line-height: 33px;
}
</style>
