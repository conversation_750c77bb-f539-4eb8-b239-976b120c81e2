import { reserveRequest } from '@/utils/request';

export function getBizDict() {
  return reserveRequest().get('/api/dictInfo/all');
}

export function getBizDictDetail(names) {
  return reserveRequest().post(
    '/api/dictInfoDetail/map',
    { dictInfoName: names },
    {
      params: { size: 999 },
    },
  );
}

// 获取浙江储备字典
export function getBizDictFromReserve() {
  return reserveRequest().get('/api/dictInfo/all');
}

export function getBizDictDetailFromReserve(names) {
  return reserveRequest().post(
    '/api/dictInfoDetail/map',
    { dictInfoName: names },
    {
      params: { size: 999 },
    },
  );
}
