import {
  // getUserInfo,
  gerUserInfoByReserve,
  login,
  logout,
  loginWithPhone,
  zzdLogin,
  // getHxdiframeworkToken,
} from '@/api/auth';
import { getUserIdAndDeptId } from '@/api/order-public';
import { setToken, setReserverToken, setHxdiframeworkToken } from '@/utils/auth';
import { reserverLogin } from '@/api/in-out-manage';
import { JSEncrypt } from 'jsencrypt/lib/index.js';

const encrypt = function (txt, publicKey) {
  if (!publicKey) return '';
  const encryptor = new JSEncrypt();
  encryptor.setPublicKey(publicKey);
  return encryptor.encrypt(txt);
};

const PUBLIC_KEY = `MIGfMA0GCSqGSIb3DQEBAQUAA4GNADCBiQKBgQDPIMkop28HzTSbMgtmB0WTOi6y0UsN0Aw9TpakSZEM/yqr7VV0gecDBu7d9eK+8lWRulMe2mgP47U51XUZJIRRzzVldIO1Wi52kUik+AiNaxtrx5n/6GkvU4cLeC1YYJzRd0uSbfGLW9Ss/bJzE5em8k1i/SeUCm5TPQVPgcVCQQIDAQAB`;

const encryptedLoginForm = (loginFormData) => ({
  ...loginFormData, // 所有的登陆参数
  username: loginFormData?.username ? encrypt(loginFormData?.username.trim(), PUBLIC_KEY) : null, // 加密用户名
  password: encrypt(`${loginFormData?.code}HXPTI${loginFormData?.password}`, PUBLIC_KEY), // 加密密码（格式为“验证码HXPTI密码”，通过HXPTI分割）
});

export default {
  namespaced: true,
  state: {
    info: null,
    reserverInfo: null,
    token: null,
    permissions: new Set(),
    reserverToken: null,
    reserverIds: null,
    roles: new Set(),
    hxdiframeworkToken: null,
  },
  getters: {
    isCompanyUser(state) {
      const { userOrgLevel } = state.info;
      return [7, 8, 9].includes(userOrgLevel);
    },
    isBureauUser(state) {
      const { userOrgLevel } = state.info;
      return [1, 2, 3].includes(userOrgLevel);
    },
    isStoreUser(state) {
      const { userOrgLevel } = state.info;
      return String(userOrgLevel) === '5';
    },
    userAreaCode(state) {
      const { areaCode, cityCode, provinceCode } = state.info || {};
      return areaCode || cityCode || provinceCode;
    },
    userInfo(state) {
      return state.info;
    },
  },
  mutations: {
    setInfo(state, info) {
      state.info = info;
    },
    setReserverInfo(state, info) {
      state.reserverInfo = info;
    },
    setToken(state, token) {
      state.token = token;
      setToken(token);
    },
    setPermissions(state, permissions) {
      state.permissions = new Set(permissions);
    },
    setReserverToken(state, token) {
      state.reserverToken = token;
      setReserverToken(token);
    },
    setUserIdAndDeptId(state, info) {
      state.reserverIds = info;
    },
    setRoles(state, roles) {
      state.roles = new Set(roles);
    },
    setHxdiframeworkToken(state, token) {
      state.hxdiframeworkToken = token;
      setHxdiframeworkToken(token);
    },
  },
  actions: {
    phoneLogin: async ({ commit }, credentials) => {
      const loginForm = {
        ...credentials,
        username: credentials?.username ? encrypt(credentials?.username.trim(), PUBLIC_KEY) : null,
        password: encrypt(`${credentials?.code}HXPTI${credentials?.password}`, PUBLIC_KEY),
      };
      let data = await loginWithPhone(loginForm, { noErrorHandler: false });
      // let { data, status } = response;
      // if (status !== 200) {
      //   throw new Error(data?.message || '登录失败');
      // }
      const { token, user } = data;
      const { roles, ...restInfo } = user;
      if (token) {
        commit('setToken', token);
        commit('setInfo', restInfo);
        commit('setPermissions', roles);
        commit('setReserverToken', token);
        commit('setReserverInfo', user);
      }
    },
    async ZZDLogin({ commit, dispatch }, credentials) {
      credentials.accountId = encrypt(credentials.accountId, PUBLIC_KEY);
      credentials.employeeCode = encrypt(credentials.employeeCode, PUBLIC_KEY);
      credentials.tenantId = encrypt(credentials.tenantId, PUBLIC_KEY);
      let response = await zzdLogin(credentials, { noErrorHandler: true });
      let { data, status } = response;
      if (status !== 200) {
        throw new Error(data?.message || '登录失败');
      }
      const { token, user } = data;
      const { roles, ...restInfo } = user;
      const params = {
        username: user.username,
        password: 'Hxpti123',
        code: '',
      };
      await dispatch('reserverLogin', params);
      if (token) {
        commit('setToken', token);
        commit('setInfo', restInfo);
        commit('setPermissions', roles);
      }

      // dispatch('getRoles');
    },
    async login({ commit }, credentials) {
      const response = await login(encryptedLoginForm(credentials), { noErrorHandler: true });
      const { data, status } = response;
      if (status !== 200) {
        throw new Error(data?.message || '登录失败');
      }
      const { token, user } = data;
      const { roles, ...restInfo } = user;
      if (token) {
        window.sensors_sw.login(user.nickName + user.id);
        commit('setToken', token);
        commit('setInfo', restInfo);
        commit('setPermissions', roles);
      }
      // dispatch('getRoles');
    },
    async logout({ commit }) {
      try {
        await logout();
      } catch (e) {
        console.error(e);
      }
      window.sensors_sw.logout();
      commit('setToken', null);
      commit('setReserverToken', null);
      commit('setInfo', null);
      commit('setReserverInfo', null);
      commit('setPermissions', new Set());
      commit('setRoles', new Set());
    },
    async getInfo({ commit }) {
      // const data = await getUserInfo();
      const data = await gerUserInfoByReserve();
      if (data) {
        const { roles, ...restInfo } = data;
        commit('setInfo', restInfo);
        // commit('setPermissions', roles);
        commit('setRoles', roles);
      }
      // dispatch('getRoles');
    },
    // async getRoles({ commit }) {
    //   const { roles } = await gerUserInfoByReserve();
    //   commit('setRoles', roles);
    // },
    async reserverLogin({ commit }, credentials) {
      const loginForm = {
        ...credentials,
        username: credentials?.username ? encrypt(credentials?.username.trim(), PUBLIC_KEY) : null,
        password: encrypt(`${credentials?.code}HXPTI${credentials?.password}`, PUBLIC_KEY),
      };
      const response = await reserverLogin(loginForm, { noErrorHandler: true });
      if (response) {
        const { token, user } = response;
        if (token) {
          commit('setReserverToken', token);
        }
        commit('setReserverInfo', user);
        window.sensors_sw.login(user.nickName + user.id);
      }
    },
    async getReserverUserIdAndDeptId({ commit, state }) {
      const { username } = state.info;
      const result = await getUserIdAndDeptId({ username });
      commit('setUserIdAndDeptId', result);
    },
    // async getHxdiframeworkToken({ commit }) {
    //   const result = await getHxdiframeworkToken();
    //   commit('setHxdiframeworkToken', result?.token);
    // },
  },
};
