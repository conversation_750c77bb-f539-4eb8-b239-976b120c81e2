<template>
  <div class="supervision-overview">
    <HCard class="summary">
      <template #header-title>
        <div class="store-title" v-if="isStoreReport">
          <SvgIcon name="location" />
          {{ storeName }}清查报告
        </div>
        <div class="title" v-else>
          <SvgIcon name="location" />
          {{ areaName }}天天大清查报告
        </div>
      </template>
      <SupervisionSummary :data="summary" />
    </HCard>
    <HCard class="sub-content" title="【1】数量清查">
      <template #header-extra>
        <Tag color-type="1" v-if="quantity.result === 1">{{ quantity.resultName }}</Tag>
        <Tag color-type="2" v-else-if="quantity.result === 2">{{ quantity.resultName }}</Tag>
        <Tag color-type="0" v-else>{{ quantity.resultName }}</Tag>
      </template>
      <StoreSupervisionQuantity :data="quantity" v-if="isStoreReport" />
      <SupervisionQuantity :data="quantity" v-else />
    </HCard>
    <HCard class="sub-content" title="【2】质量清查">
      <template #header-extra>
        <Tag color-type="1" v-if="quality.result === 1">{{ quality.resultName }}</Tag>
        <Tag color-type="2" v-else-if="quality.result === 2">{{ quality.resultName }}</Tag>
        <Tag color-type="0" v-else>{{ quality.resultName }}</Tag>
      </template>
      <SupervisionQuality :data="quality" />
    </HCard>
    <HCard class="sub-content" title="【3】安全清查">
      <template #header-extra>
        <Tag color-type="1" v-if="security.result === 1">{{ security.resultName }}</Tag>
        <Tag color-type="2" v-else-if="security.result === 2">{{ security.resultName }}</Tag>
        <Tag color-type="0" v-else>{{ security.resultName }}</Tag>
      </template>
      <StoreSupervisionSecurity :data="security" v-if="isStoreReport" />
      <SupervisionSecurity :data="security" v-else />
    </HCard>
    <HCard class="sub-content" title="【4】设施清查">
      <SupervisionInfrastructure :data="infrastructure" />
    </HCard>
    <HCard>
      <SupervisionStorehouse :data="storehouses" v-if="isStoreReport" />
      <SupervisionSubArea :data="subArea" v-else />
    </HCard>
  </div>
</template>

<script setup>
import { computed, effect } from 'vue';
import { useRoute } from 'vue-router';
import { useStore } from 'vuex';
import HCard from '@/components/HCard/HCard';
import SvgIcon from '@/components/SvgIcon/SvgIcon';
import SupervisionSummary from './SupervisionSummary';
import SupervisionQuantity from './SupervisionQuantity';
import SupervisionQuality from './SupervisionQuality';
import SupervisionSecurity from './SupervisionSecurity';
import SupervisionInfrastructure from './SupervisionInfrastructure';
import SupervisionSubArea from './SupervisionSubArea';
import Tag from '@/views/PurchasesOverview/common/Tag';
import StoreSupervisionQuantity from '@/views/SupervisionOverview/SupervisionReport/StoreSupervisionQuantity';
import StoreSupervisionSecurity from '@/views/SupervisionOverview/SupervisionReport/StoreSupervisionSecurity';
import SupervisionStorehouse from '@/views/SupervisionOverview/SupervisionReport/SupervisionStorehouse';

const route = useRoute();
const store = useStore();

const isStoreReport = computed(() => {
  const { name: routeName } = route;
  return routeName === 'SupervisionStoreReport';
});

const currentReport = computed(() => {
  if (isStoreReport.value) {
    return store.state['supervision-overview'].storeReport;
  }
  return store.state['supervision-overview'].areaReport;
});

const storeName = computed(() => {
  return currentReport.value?.storeName;
});

const areaName = computed(() => {
  return currentReport.value?.areaName;
});

const summary = computed(() => {
  return currentReport.value?.summary || {};
});
const quantity = computed(() => {
  return currentReport.value?.quantity || {};
});
const quality = computed(() => {
  return currentReport.value?.quality || {};
});
const security = computed(() => {
  return currentReport.value?.security || {};
});
const infrastructure = computed(() => {
  return currentReport.value?.infrastructure || {};
});
const subArea = computed(() => {
  return currentReport.value?.subArea || [];
});
const storehouses = computed(() => {
  return currentReport.value?.storehouses || [];
});

effect(() => {
  const { areaCode, storeId } = route.params;
  if (isStoreReport.value && storeId) {
    store.dispatch('supervision-overview/fetchStoreReport', storeId);
  } else if (areaCode) {
    store.dispatch('supervision-overview/fetchAreaReport', areaCode);
  }
});
</script>

<style scoped lang="scss">
.supervision-overview {
  ::v-deep(.h-card) {
    .detail-values {
      display: flex;
      flex-direction: column;
      background: #f2f4f8;
      border-radius: 4px;
      padding: 15px;
      margin-top: 10px;
    }
  }
}

.summary {
  ::v-deep(.h-card-header) {
    justify-content: center;
  }

  .store-title,
  .title {
    display: flex;
    align-items: center;

    .svg-icon {
      font-size: 1.5em;
      margin-right: 10px;
    }
  }

  .title {
    font-size: 18px;
  }

  .store-title {
    font-size: 16px;
  }
}

.h-card {
  margin-bottom: 16px;
}

.sub-content {
  margin-left: 16px;
  margin-right: 16px;
  border-radius: 4px;

  ::v-deep(.h-card-header) {
    padding: 0 4px;
    line-height: inherit;

    .h-card-header-title {
      font-size: 18px;
      line-height: 40px;
    }
  }

  .tag {
    font-size: 18px;
    line-height: 25px;
    padding: 2px 8px;
    margin-right: 8px;
  }
}
</style>
