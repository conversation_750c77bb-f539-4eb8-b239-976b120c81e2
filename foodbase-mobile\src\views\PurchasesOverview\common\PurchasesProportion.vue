<template>
  <div class="purchases-proportion">
    <div class="title">订单收购进度</div>
    <div class="charts">
      <div class="chart">
        <HEchart :options="xmOptions" />
        <div class="name">小麦</div>
      </div>
      <div class="chart">
        <HEchart :options="zdOptions" />
        <div class="name">早稻</div>
      </div>
      <div class="chart">
        <HEchart :options="wdOptions" />
        <div class="name">晚稻</div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { computed, effect, reactive } from 'vue';
import { HEchart } from '@/components';
import { getPurchasesProgress } from '@/api/purchases-progress';
import { useStore } from 'vuex';
const store = useStore();

const createCircleBarOptions = (percent, color) => {
  return {
    title: [
      {
        text: `${percent >= 100 ? percent : percent.toFixed(1)}%`,
        x: 'center',
        y: 'center',
        textStyle: {
          color: color,
          fontSize: 16,
        },
      },
    ],
    polar: {
      radius: ['60%', '80%'],
      center: ['50%', '50%'],
    },
    angleAxis: {
      max: 100,
      show: false,
    },
    radiusAxis: {
      type: 'category',
      show: true,
      axisLabel: {
        show: false,
      },
      axisLine: {
        show: false,
      },
      axisTick: {
        show: false,
      },
    },
    series: [
      {
        type: 'bar',
        barWidth: '100%',
        showBackground: true,
        backgroundStyle: {
          color: '#E0E4E8',
        },
        data: [percent],
        coordinateSystem: 'polar',

        itemStyle: {
          color: color,
        },
      },
    ],
  };
};

const categoryProgress = reactive({
  zd: 0, // 早稻
  wd: 0, // 晚稻
  xm: 0, // 小麦
});

const zdOptions = computed(() => {
  return createCircleBarOptions(categoryProgress.zd, '#7CC863');
});

const wdOptions = computed(() => {
  return createCircleBarOptions(categoryProgress.wd, '#17BB89');
});

const xmOptions = computed(() => {
  return createCircleBarOptions(categoryProgress.xm, '#FF9F40');
});
const deptId = computed(() => {
  return store.state.user?.reserverIds?.deptId;
});
const level = computed(() => {
  return store.state.user?.reserverIds?.level;
});

effect(async () => {
  categoryProgress.zd =
    (
      (await getPurchasesProgress({
        level: level.value,
        deptId: deptId.value,
        typeList: [1, 2, 3],
        categoryIds: 110103001001000,
      })) || {}
    ).acquisitionProgress.split('%')[0] * 1;
  categoryProgress.wd =
    (
      (await getPurchasesProgress({
        level: level.value,
        deptId: deptId.value,
        typeList: [1, 2, 3],
        categoryIds: [110103001002000, 110103002000000],
      })) || {}
    ).acquisitionProgress.split('%')[0] * 1;
  categoryProgress.xm =
    (
      (await getPurchasesProgress({
        level: level.value,
        deptId: deptId.value,
        typeList: [1, 2, 3],
        categoryIds: 110101005000000,
      })) || {}
    ).acquisitionProgress?.split('%')[0] * 1;
});
</script>

<style scoped lang="scss">
.purchases-proportion {
  padding: 10px 0 20px 0;
}
.title {
  font-size: 16px;
  text-align: center;
  margin-bottom: 5px;
}
.charts {
  display: flex;
}
.chart {
  flex: 1;

  .name {
    font-size: 18px;
    line-height: 25px;
    text-align: center;
  }
}

.h-echarts {
  width: 100%;
  height: 100px;
}
</style>
