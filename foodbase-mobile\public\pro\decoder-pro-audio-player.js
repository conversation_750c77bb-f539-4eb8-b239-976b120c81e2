!function(e,r){"object"==typeof exports&&"undefined"!=typeof module?r(require("path"),require("fs"),require("crypto")):"function"==typeof define&&define.amd?define(["path","fs","crypto"],r):r((e="undefined"!=typeof globalThis?globalThis:e||self).path,e.fs,e.crypto$1)}(this,function(e,r,t){"use strict";function n(e){return e&&"object"==typeof e&&"default"in e?e:{default:e}}var Sr=n(e),Pr=n(r),Ar=n(t);function o(e,r){return e(r={exports:{}},r.exports),r.exports}var i=o(function(x){var y;(y=void 0!==(y=void 0!==y?y:{})?y:{}).locateFile=function(e){return"decoder-pro-audio.wasm"==e&&"undefined"!=typeof JESSIBUCA_PRO_AUDIO_WASM_URL&&""!=JESSIBUCA_PRO_AUDIO_WASM_URL?JESSIBUCA_PRO_AUDIO_WASM_URL:e};var r,m,U,B,W,z,i,I=Object.assign({},y),L="./this.program",N="object"==typeof window,c="function"==typeof importScripts,H="object"==typeof process&&"object"==typeof process.versions&&"string"==typeof process.versions.node,e="",q=(H?(e=c?Sr.default.dirname(e)+"/":__dirname+"/",z=()=>{W||(B=Pr.default,W=Sr.default)},r=function(e,r){return z(),e=W.normalize(e),B.readFileSync(e,r?void 0:"utf8")},U=e=>{e=r(e,!0);return e=e.buffer?e:new Uint8Array(e)},m=(e,t,n)=>{z(),e=W.normalize(e),B.readFile(e,function(e,r){e?n(e):t(r.buffer)})},1<process.argv.length&&(L=process.argv[1].replace(/\\/g,"/")),process.argv.slice(2),x.exports=y,process.on("uncaughtException",function(e){if(!(e instanceof function(e){this.name="ExitStatus",this.message="Program terminated with exit("+e+")",this.status=e}))throw e}),process.on("unhandledRejection",function(e){throw e}),y.inspect=function(){return"[Emscripten Module object]"}):(N||c)&&(c?e=self.location.href:"undefined"!=typeof document&&document.currentScript&&(e=document.currentScript.src),e=0!==e.indexOf("blob:")?e.substr(0,e.replace(/[?#].*/,"").lastIndexOf("/")+1):"",r=e=>{var r=new XMLHttpRequest;return r.open("GET",e,!1),r.send(null),r.responseText},c&&(U=e=>{var r=new XMLHttpRequest;return r.open("GET",e,!1),r.responseType="arraybuffer",r.send(null),new Uint8Array(r.response)}),m=(e,r,t)=>{var n=new XMLHttpRequest;n.open("GET",e,!0),n.responseType="arraybuffer",n.onload=()=>{200==n.status||0==n.status&&n.response?r(n.response):t()},n.onerror=t,n.send(null)}),y.print||console.log.bind(console)),a=y.printErr||console.warn.bind(console),V=(Object.assign(y,I),y.arguments&&y.arguments,y.thisProgram&&(L=y.thisProgram),y.quit&&y.quit,y.wasmBinary&&(i=y.wasmBinary),y.noExitRuntime,"object"!=typeof WebAssembly&&v("no native wasm support detected"),!1);function G(e,r){e||v(r)}var X,d,f,u,J,l,p,K,Z,Q,Y="undefined"!=typeof TextDecoder?new TextDecoder("utf8"):void 0;function s(e,r,t){for(var n=r+t,o=r;e[o]&&!(n<=o);)++o;if(16<o-r&&e.buffer&&Y)return Y.decode(e.subarray(r,o));for(var i="";r<o;){var s,a,u=e[r++];128&u?(s=63&e[r++],192!=(224&u)?(a=63&e[r++],(u=224==(240&u)?(15&u)<<12|s<<6|a:(7&u)<<18|s<<12|a<<6|63&e[r++])<65536?i+=String.fromCharCode(u):(a=u-65536,i+=String.fromCharCode(55296|a>>10,56320|1023&a))):i+=String.fromCharCode((31&u)<<6|s)):i+=String.fromCharCode(u)}return i}function ee(e,r){return e?s(f,e,r):""}function re(e,r,t,n){if(!(0<n))return 0;for(var o=t,i=t+n-1,s=0;s<e.length;++s){var a=e.charCodeAt(s);if((a=55296<=a&&a<=57343?65536+((1023&a)<<10)|1023&e.charCodeAt(++s):a)<=127){if(i<=t)break;r[t++]=a}else if(a<=2047){if(i<=t+1)break;r[t++]=192|a>>6,r[t++]=128|63&a}else if(a<=65535){if(i<=t+2)break;r[t++]=224|a>>12,r[t++]=128|a>>6&63,r[t++]=128|63&a}else{if(i<=t+3)break;r[t++]=240|a>>18,r[t++]=128|a>>12&63,r[t++]=128|a>>6&63,r[t++]=128|63&a}}return r[t]=0,t-o}function te(e){for(var r=0,t=0;t<e.length;++t){var n=e.charCodeAt(t);n<=127?r++:n<=2047?r+=2:55296<=n&&n<=57343?(r+=4,++t):r+=3}return r}y.INITIAL_MEMORY;var h,g,w,ne=[],oe=[],ie=[],t=0,n=null;function se(){t++,y.monitorRunDependencies&&y.monitorRunDependencies(t)}function ae(){var e;t--,y.monitorRunDependencies&&y.monitorRunDependencies(t),0==t&&n&&(e=n,n=null,e())}function v(e){throw y.onAbort&&y.onAbort(e),a(e="Aborted("+e+")"),V=!0,e+=". Build with -sASSERTIONS for more info.",new WebAssembly.RuntimeError(e)}function ue(e){return e.startsWith("data:application/octet-stream;base64,")}function le(e){return e.startsWith("file://")}function ce(e){try{if(e==h&&i)return new Uint8Array(i);if(U)return U(e);throw"both async and sync fetching of the wasm failed"}catch(e){v(e)}}function de(e){for(;0<e.length;)e.shift()(y)}function fe(e){this.excPtr=e,this.ptr=e-24,this.set_type=function(e){p[this.ptr+4>>2]=e},this.get_type=function(){return p[this.ptr+4>>2]},this.set_destructor=function(e){p[this.ptr+8>>2]=e},this.get_destructor=function(){return p[this.ptr+8>>2]},this.set_refcount=function(e){l[this.ptr>>2]=e},this.set_caught=function(e){d[this.ptr+12>>0]=e=e?1:0},this.get_caught=function(){return 0!=d[this.ptr+12>>0]},this.set_rethrown=function(e){d[this.ptr+13>>0]=e=e?1:0},this.get_rethrown=function(){return 0!=d[this.ptr+13>>0]},this.init=function(e,r){this.set_adjusted_ptr(0),this.set_type(e),this.set_destructor(r),this.set_refcount(0),this.set_caught(!1),this.set_rethrown(!1)},this.add_ref=function(){var e=l[this.ptr>>2];l[this.ptr>>2]=e+1},this.release_ref=function(){var e=l[this.ptr>>2];return l[this.ptr>>2]=e-1,1===e},this.set_adjusted_ptr=function(e){p[this.ptr+16>>2]=e},this.get_adjusted_ptr=function(){return p[this.ptr+16>>2]},this.get_exception_ptr=function(){if(_r(this.get_type()))return p[this.excPtr>>2];var e=this.get_adjusted_ptr();return 0!==e?e:this.excPtr}}ue(h="decoder-pro-audio.wasm")||(I=h,h=y.locateFile?y.locateFile(I,e):e+I);var b={isAbs:e=>"/"===e.charAt(0),splitPath:e=>/^(\/?|)([\s\S]*?)((?:\.{1,2}|[^\/]+?|)(\.[^.\/]*|))(?:[\/]*)$/.exec(e).slice(1),normalizeArray:(e,r)=>{for(var t=0,n=e.length-1;0<=n;n--){var o=e[n];"."===o?e.splice(n,1):".."===o?(e.splice(n,1),t++):t&&(e.splice(n,1),t--)}if(r)for(;t;t--)e.unshift("..");return e},normalize:e=>{var r=b.isAbs(e),t="/"===e.substr(-1);return(e=(e=b.normalizeArray(e.split("/").filter(e=>!!e),!r).join("/"))||r?e:".")&&t&&(e+="/"),(r?"/":"")+e},dirname:e=>{var e=b.splitPath(e),r=e[0],e=e[1];return r||e?r+(e=e&&e.substr(0,e.length-1)):"."},basename:e=>{if("/"===e)return"/";var r=(e=(e=b.normalize(e)).replace(/\/$/,"")).lastIndexOf("/");return-1===r?e:e.substr(r+1)},join:function(){var e=Array.prototype.slice.call(arguments,0);return b.normalize(e.join("/"))},join2:(e,r)=>b.normalize(e+"/"+r)},E={resolve:function(){for(var e="",r=!1,t=arguments.length-1;-1<=t&&!r;t--){var n=0<=t?arguments[t]:k.cwd();if("string"!=typeof n)throw new TypeError("Arguments to path.resolve must be strings");if(!n)return"";e=n+"/"+e,r=b.isAbs(n)}return(r?"/":"")+b.normalizeArray(e.split("/").filter(e=>!!e),!r).join("/")||"."},relative:(e,r)=>{function t(e){for(var r=0;r<e.length&&""===e[r];r++);for(var t=e.length-1;0<=t&&""===e[t];t--);return t<r?[]:e.slice(r,t-r+1)}e=E.resolve(e).substr(1),r=E.resolve(r).substr(1);for(var n=t(e.split("/")),o=t(r.split("/")),i=Math.min(n.length,o.length),s=i,a=0;a<i;a++)if(n[a]!==o[a]){s=a;break}for(var u=[],a=s;a<n.length;a++)u.push("..");return(u=u.concat(o.slice(s))).join("/")}};function pe(e,r,t){t=0<t?t:te(e)+1,t=new Array(t),e=re(e,t,0,t.length);return r&&(t.length=e),t}var o={ttys:[],init:function(){},shutdown:function(){},register:function(e,r){o.ttys[e]={input:[],output:[],ops:r},k.registerDevice(e,o.stream_ops)},stream_ops:{open:function(e){var r=o.ttys[e.node.rdev];if(!r)throw new k.ErrnoError(43);e.tty=r,e.seekable=!1},close:function(e){e.tty.ops.flush(e.tty)},flush:function(e){e.tty.ops.flush(e.tty)},read:function(e,r,t,n,o){if(!e.tty||!e.tty.ops.get_char)throw new k.ErrnoError(60);for(var i,s=0,a=0;a<n;a++){try{i=e.tty.ops.get_char(e.tty)}catch(e){throw new k.ErrnoError(29)}if(void 0===i&&0===s)throw new k.ErrnoError(6);if(null==i)break;s++,r[t+a]=i}return s&&(e.node.timestamp=Date.now()),s},write:function(e,r,t,n,o){if(!e.tty||!e.tty.ops.put_char)throw new k.ErrnoError(60);try{for(var i=0;i<n;i++)e.tty.ops.put_char(e.tty,r[t+i])}catch(e){throw new k.ErrnoError(29)}return n&&(e.node.timestamp=Date.now()),i}},default_tty_ops:{get_char:function(e){if(!e.input.length){var r=null;if(H){var t=Buffer.alloc(256),n=0;try{n=B.readSync(process.stdin.fd,t,0,256,-1)}catch(e){if(!e.toString().includes("EOF"))throw e;n=0}r=0<n?t.slice(0,n).toString("utf-8"):null}else"undefined"!=typeof window&&"function"==typeof window.prompt?null!==(r=window.prompt("Input: "))&&(r+="\n"):"function"==typeof readline&&null!==(r=readline())&&(r+="\n");if(!r)return null;e.input=pe(r,!0)}return e.input.shift()},put_char:function(e,r){null===r||10===r?(q(s(e.output,0)),e.output=[]):0!=r&&e.output.push(r)},flush:function(e){e.output&&0<e.output.length&&(q(s(e.output,0)),e.output=[])}},default_tty1_ops:{put_char:function(e,r){null===r||10===r?(a(s(e.output,0)),e.output=[]):0!=r&&e.output.push(r)},flush:function(e){e.output&&0<e.output.length&&(a(s(e.output,0)),e.output=[])}}};function he(e){r=e,e=65536*Math.ceil(r/65536);var r=Er(65536,e);return r?(e=e,f.fill(0,r,r+e),r):0}var _={ops_table:null,mount:function(e){return _.createNode(null,"/",16895,0)},createNode:function(e,r,t,n){if(k.isBlkdev(t)||k.isFIFO(t))throw new k.ErrnoError(63);_.ops_table||(_.ops_table={dir:{node:{getattr:_.node_ops.getattr,setattr:_.node_ops.setattr,lookup:_.node_ops.lookup,mknod:_.node_ops.mknod,rename:_.node_ops.rename,unlink:_.node_ops.unlink,rmdir:_.node_ops.rmdir,readdir:_.node_ops.readdir,symlink:_.node_ops.symlink},stream:{llseek:_.stream_ops.llseek}},file:{node:{getattr:_.node_ops.getattr,setattr:_.node_ops.setattr},stream:{llseek:_.stream_ops.llseek,read:_.stream_ops.read,write:_.stream_ops.write,allocate:_.stream_ops.allocate,mmap:_.stream_ops.mmap,msync:_.stream_ops.msync}},link:{node:{getattr:_.node_ops.getattr,setattr:_.node_ops.setattr,readlink:_.node_ops.readlink},stream:{}},chrdev:{node:{getattr:_.node_ops.getattr,setattr:_.node_ops.setattr},stream:k.chrdev_stream_ops}});t=k.createNode(e,r,t,n);return k.isDir(t.mode)?(t.node_ops=_.ops_table.dir.node,t.stream_ops=_.ops_table.dir.stream,t.contents={}):k.isFile(t.mode)?(t.node_ops=_.ops_table.file.node,t.stream_ops=_.ops_table.file.stream,t.usedBytes=0,t.contents=null):k.isLink(t.mode)?(t.node_ops=_.ops_table.link.node,t.stream_ops=_.ops_table.link.stream):k.isChrdev(t.mode)&&(t.node_ops=_.ops_table.chrdev.node,t.stream_ops=_.ops_table.chrdev.stream),t.timestamp=Date.now(),e&&(e.contents[r]=t,e.timestamp=t.timestamp),t},getFileDataAsTypedArray:function(e){return e.contents?e.contents.subarray?e.contents.subarray(0,e.usedBytes):new Uint8Array(e.contents):new Uint8Array(0)},expandFileStorage:function(e,r){var t=e.contents?e.contents.length:0;r<=t||(r=Math.max(r,t*(t<1048576?2:1.125)>>>0),0!=t&&(r=Math.max(r,256)),t=e.contents,e.contents=new Uint8Array(r),0<e.usedBytes&&e.contents.set(t.subarray(0,e.usedBytes),0))},resizeFileStorage:function(e,r){var t;e.usedBytes!=r&&(0==r?(e.contents=null,e.usedBytes=0):(t=e.contents,e.contents=new Uint8Array(r),t&&e.contents.set(t.subarray(0,Math.min(r,e.usedBytes))),e.usedBytes=r))},node_ops:{getattr:function(e){var r={};return r.dev=k.isChrdev(e.mode)?e.id:1,r.ino=e.id,r.mode=e.mode,r.nlink=1,r.uid=0,r.gid=0,r.rdev=e.rdev,k.isDir(e.mode)?r.size=4096:k.isFile(e.mode)?r.size=e.usedBytes:k.isLink(e.mode)?r.size=e.link.length:r.size=0,r.atime=new Date(e.timestamp),r.mtime=new Date(e.timestamp),r.ctime=new Date(e.timestamp),r.blksize=4096,r.blocks=Math.ceil(r.size/r.blksize),r},setattr:function(e,r){void 0!==r.mode&&(e.mode=r.mode),void 0!==r.timestamp&&(e.timestamp=r.timestamp),void 0!==r.size&&_.resizeFileStorage(e,r.size)},lookup:function(e,r){throw k.genericErrors[44]},mknod:function(e,r,t,n){return _.createNode(e,r,t,n)},rename:function(e,r,t){if(k.isDir(e.mode)){var n;try{n=k.lookupNode(r,t)}catch(e){}if(n)for(var o in n.contents)throw new k.ErrnoError(55)}delete e.parent.contents[e.name],e.parent.timestamp=Date.now(),e.name=t,r.contents[t]=e,r.timestamp=e.parent.timestamp,e.parent=r},unlink:function(e,r){delete e.contents[r],e.timestamp=Date.now()},rmdir:function(e,r){for(var t in k.lookupNode(e,r).contents)throw new k.ErrnoError(55);delete e.contents[r],e.timestamp=Date.now()},readdir:function(e){var r,t=[".",".."];for(r in e.contents)e.contents.hasOwnProperty(r)&&t.push(r);return t},symlink:function(e,r,t){e=_.createNode(e,r,41471,0);return e.link=t,e},readlink:function(e){if(k.isLink(e.mode))return e.link;throw new k.ErrnoError(28)}},stream_ops:{read:function(e,r,t,n,o){var i=e.node.contents;if(o>=e.node.usedBytes)return 0;var s=Math.min(e.node.usedBytes-o,n);if(8<s&&i.subarray)r.set(i.subarray(o,o+s),t);else for(var a=0;a<s;a++)r[t+a]=i[o+a];return s},write:function(e,r,t,n,o,i){if(!n)return 0;var s=e.node;if(s.timestamp=Date.now(),r.subarray&&(!s.contents||s.contents.subarray)){if(i)return s.contents=r.subarray(t,t+n),s.usedBytes=n;if(0===s.usedBytes&&0===o)return s.contents=r.slice(t,t+n),s.usedBytes=n;if(o+n<=s.usedBytes)return s.contents.set(r.subarray(t,t+n),o),n}if(_.expandFileStorage(s,o+n),s.contents.subarray&&r.subarray)s.contents.set(r.subarray(t,t+n),o);else for(var a=0;a<n;a++)s.contents[o+a]=r[t+a];return s.usedBytes=Math.max(s.usedBytes,o+n),n},llseek:function(e,r,t){if(1===t?r+=e.position:2===t&&k.isFile(e.node.mode)&&(r+=e.node.usedBytes),r<0)throw new k.ErrnoError(28);return r},allocate:function(e,r,t){_.expandFileStorage(e.node,r+t),e.node.usedBytes=Math.max(e.node.usedBytes,r+t)},mmap:function(e,r,t,n,o){if(!k.isFile(e.node.mode))throw new k.ErrnoError(43);var i,s,e=e.node.contents;if(2&o||e.buffer!==X){if((0<t||t+r<e.length)&&(e=e.subarray?e.subarray(t,t+r):Array.prototype.slice.call(e,t,t+r)),s=!0,!(i=he(r)))throw new k.ErrnoError(48);d.set(e,i)}else s=!1,i=e.byteOffset;return{ptr:i,allocated:s}},msync:function(e,r,t,n,o){if(k.isFile(e.node.mode))return 2&o||_.stream_ops.write(e,r,0,n,t,!1),0;throw new k.ErrnoError(43)}}},k={root:null,mounts:[],devices:{},streams:[],nextInode:1,nameTable:null,currentPath:"/",initialized:!1,ignorePermissions:!0,ErrnoError:null,genericErrors:{},filesystems:null,syncFSRequests:0,lookupPath:function(e){var r=1<arguments.length&&void 0!==arguments[1]?arguments[1]:{};if(!(e=E.resolve(k.cwd(),e)))return{path:"",node:null};if(8<(r=Object.assign({follow_mount:!0,recurse_count:0},r)).recurse_count)throw new k.ErrnoError(32);for(var t=b.normalizeArray(e.split("/").filter(e=>!!e),!1),n=k.root,o="/",i=0;i<t.length;i++){var s=i===t.length-1;if(s&&r.parent)break;if(n=k.lookupNode(n,t[i]),o=b.join2(o,t[i]),!k.isMountpoint(n)||s&&!r.follow_mount||(n=n.mounted.root),!s||r.follow)for(var a=0;k.isLink(n.mode);){var u=k.readlink(o),o=E.resolve(b.dirname(o),u),n=k.lookupPath(o,{recurse_count:r.recurse_count+1}).node;if(40<a++)throw new k.ErrnoError(32)}}return{path:o,node:n}},getPath:e=>{for(var r,t;;){if(k.isRoot(e))return t=e.mount.mountpoint,r?"/"!==t[t.length-1]?t+"/"+r:t+r:t;r=r?e.name+"/"+r:e.name,e=e.parent}},hashName:(e,r)=>{for(var t=0,n=0;n<r.length;n++)t=(t<<5)-t+r.charCodeAt(n)|0;return(e+t>>>0)%k.nameTable.length},hashAddNode:e=>{var r=k.hashName(e.parent.id,e.name);e.name_next=k.nameTable[r],k.nameTable[r]=e},hashRemoveNode:e=>{var r=k.hashName(e.parent.id,e.name);if(k.nameTable[r]===e)k.nameTable[r]=e.name_next;else for(var t=k.nameTable[r];t;){if(t.name_next===e){t.name_next=e.name_next;break}t=t.name_next}},lookupNode:(e,r)=>{var t=k.mayLookup(e);if(t)throw new k.ErrnoError(t,e);for(var t=k.hashName(e.id,r),n=k.nameTable[t];n;n=n.name_next){var o=n.name;if(n.parent.id===e.id&&o===r)return n}return k.lookup(e,r)},createNode:(e,r,t,n)=>{e=new k.FSNode(e,r,t,n);return k.hashAddNode(e),e},destroyNode:e=>{k.hashRemoveNode(e)},isRoot:e=>e===e.parent,isMountpoint:e=>!!e.mounted,isFile:e=>32768==(61440&e),isDir:e=>16384==(61440&e),isLink:e=>40960==(61440&e),isChrdev:e=>8192==(61440&e),isBlkdev:e=>24576==(61440&e),isFIFO:e=>4096==(61440&e),isSocket:e=>49152==(49152&e),flagModes:{r:0,"r+":2,w:577,"w+":578,a:1089,"a+":1090},modeStringToFlags:e=>{var r=k.flagModes[e];if(void 0===r)throw new Error("Unknown file open mode: "+e);return r},flagsToPermissionString:e=>{var r=["r","w","rw"][3&e];return 512&e&&(r+="w"),r},nodePermissions:(e,r)=>k.ignorePermissions||(!r.includes("r")||292&e.mode)&&(!r.includes("w")||146&e.mode)&&(!r.includes("x")||73&e.mode)?0:2,mayLookup:e=>{return k.nodePermissions(e,"x")||(e.node_ops.lookup?0:2)},mayCreate:(e,r)=>{try{return k.lookupNode(e,r),20}catch(e){}return k.nodePermissions(e,"wx")},mayDelete:(e,r,t)=>{var n;try{n=k.lookupNode(e,r)}catch(e){return e.errno}r=k.nodePermissions(e,"wx");if(r)return r;if(t){if(!k.isDir(n.mode))return 54;if(k.isRoot(n)||k.getPath(n)===k.cwd())return 10}else if(k.isDir(n.mode))return 31;return 0},mayOpen:(e,r)=>e?k.isLink(e.mode)?32:k.isDir(e.mode)&&("r"!==k.flagsToPermissionString(r)||512&r)?31:k.nodePermissions(e,k.flagsToPermissionString(r)):44,MAX_OPEN_FDS:4096,nextfd:function(){for(var e=0<arguments.length&&void 0!==arguments[0]?arguments[0]:0,r=1<arguments.length&&void 0!==arguments[1]?arguments[1]:k.MAX_OPEN_FDS,t=e;t<=r;t++)if(!k.streams[t])return t;throw new k.ErrnoError(33)},getStream:e=>k.streams[e],createStream:(e,r,t)=>{k.FSStream||(k.FSStream=function(){this.shared={}},k.FSStream.prototype={},Object.defineProperties(k.FSStream.prototype,{object:{get:function(){return this.node},set:function(e){this.node=e}},isRead:{get:function(){return 1!=(2097155&this.flags)}},isWrite:{get:function(){return 0!=(2097155&this.flags)}},isAppend:{get:function(){return 1024&this.flags}},flags:{get:function(){return this.shared.flags},set:function(e){this.shared.flags=e}},position:{get:function(){return this.shared.position},set:function(e){this.shared.position=e}}})),e=Object.assign(new k.FSStream,e);r=k.nextfd(r,t);return e.fd=r,k.streams[r]=e},closeStream:e=>{k.streams[e]=null},chrdev_stream_ops:{open:e=>{var r=k.getDevice(e.node.rdev);e.stream_ops=r.stream_ops,e.stream_ops.open&&e.stream_ops.open(e)},llseek:()=>{throw new k.ErrnoError(70)}},major:e=>e>>8,minor:e=>255&e,makedev:(e,r)=>e<<8|r,registerDevice:(e,r)=>{k.devices[e]={stream_ops:r}},getDevice:e=>k.devices[e],getMounts:e=>{for(var r=[],t=[e];t.length;){var n=t.pop();r.push(n),t.push.apply(t,n.mounts)}return r},syncfs:(r,t)=>{"function"==typeof r&&(t=r,r=!1),k.syncFSRequests++,1<k.syncFSRequests&&a("warning: "+k.syncFSRequests+" FS.syncfs operations in flight at once, probably just doing extra work");var n=k.getMounts(k.root.mount),o=0;function i(e){return k.syncFSRequests--,t(e)}function s(e){if(e)return s.errored?void 0:(s.errored=!0,i(e));++o>=n.length&&i(null)}n.forEach(e=>{if(!e.type.syncfs)return s(null);e.type.syncfs(e,r,s)})},mount:(e,r,t)=>{var n,o="/"===t,i=!t;if(o&&k.root)throw new k.ErrnoError(10);if(!o&&!i){i=k.lookupPath(t,{follow_mount:!1});if(t=i.path,n=i.node,k.isMountpoint(n))throw new k.ErrnoError(10);if(!k.isDir(n.mode))throw new k.ErrnoError(54)}i={type:e,opts:r,mountpoint:t,mounts:[]},r=e.mount(i);return(r.mount=i).root=r,o?k.root=r:n&&(n.mounted=i,n.mount&&n.mount.mounts.push(i)),r},unmount:e=>{e=k.lookupPath(e,{follow_mount:!1});if(!k.isMountpoint(e.node))throw new k.ErrnoError(28);var e=e.node,r=e.mounted,n=k.getMounts(r),r=(Object.keys(k.nameTable).forEach(e=>{for(var r=k.nameTable[e];r;){var t=r.name_next;n.includes(r.mount)&&k.destroyNode(r),r=t}}),e.mounted=null,e.mount.mounts.indexOf(r));e.mount.mounts.splice(r,1)},lookup:(e,r)=>e.node_ops.lookup(e,r),mknod:(e,r,t)=>{var n=k.lookupPath(e,{parent:!0}).node,e=b.basename(e);if(!e||"."===e||".."===e)throw new k.ErrnoError(28);var o=k.mayCreate(n,e);if(o)throw new k.ErrnoError(o);if(n.node_ops.mknod)return n.node_ops.mknod(n,e,r,t);throw new k.ErrnoError(63)},create:(e,r)=>k.mknod(e,r=(r=void 0!==r?r:438)&4095|32768,0),mkdir:(e,r)=>k.mknod(e,r=(r=void 0!==r?r:511)&1023|16384,0),mkdirTree:(e,r)=>{for(var t=e.split("/"),n="",o=0;o<t.length;++o)if(t[o]){n+="/"+t[o];try{k.mkdir(n,r)}catch(e){if(20!=e.errno)throw e}}},mkdev:(e,r,t)=>(void 0===t&&(t=r,r=438),k.mknod(e,r|=8192,t)),symlink:(e,r)=>{if(!E.resolve(e))throw new k.ErrnoError(44);var t=k.lookupPath(r,{parent:!0}).node;if(!t)throw new k.ErrnoError(44);var r=b.basename(r),n=k.mayCreate(t,r);if(n)throw new k.ErrnoError(n);if(t.node_ops.symlink)return t.node_ops.symlink(t,r,e);throw new k.ErrnoError(63)},rename:(e,r)=>{var t=b.dirname(e),n=b.dirname(r),o=b.basename(e),i=b.basename(r),s=k.lookupPath(e,{parent:!0}).node,a=k.lookupPath(r,{parent:!0}).node;if(!s||!a)throw new k.ErrnoError(44);if(s.mount!==a.mount)throw new k.ErrnoError(75);var u,l=k.lookupNode(s,o);if("."!==E.relative(e,n).charAt(0))throw new k.ErrnoError(28);if("."!==E.relative(r,t).charAt(0))throw new k.ErrnoError(55);try{u=k.lookupNode(a,i)}catch(e){}if(l!==u){n=k.isDir(l.mode),r=k.mayDelete(s,o,n);if(r)throw new k.ErrnoError(r);if(r=u?k.mayDelete(a,i,n):k.mayCreate(a,i))throw new k.ErrnoError(r);if(!s.node_ops.rename)throw new k.ErrnoError(63);if(k.isMountpoint(l)||u&&k.isMountpoint(u))throw new k.ErrnoError(10);if(a!==s&&(r=k.nodePermissions(s,"w")))throw new k.ErrnoError(r);k.hashRemoveNode(l);try{s.node_ops.rename(l,a,i)}catch(e){throw e}finally{k.hashAddNode(l)}}},rmdir:e=>{var r=k.lookupPath(e,{parent:!0}).node,e=b.basename(e),t=k.lookupNode(r,e),n=k.mayDelete(r,e,!0);if(n)throw new k.ErrnoError(n);if(!r.node_ops.rmdir)throw new k.ErrnoError(63);if(k.isMountpoint(t))throw new k.ErrnoError(10);r.node_ops.rmdir(r,e),k.destroyNode(t)},readdir:e=>{e=k.lookupPath(e,{follow:!0}).node;if(e.node_ops.readdir)return e.node_ops.readdir(e);throw new k.ErrnoError(54)},unlink:e=>{var r=k.lookupPath(e,{parent:!0}).node;if(!r)throw new k.ErrnoError(44);var e=b.basename(e),t=k.lookupNode(r,e),n=k.mayDelete(r,e,!1);if(n)throw new k.ErrnoError(n);if(!r.node_ops.unlink)throw new k.ErrnoError(63);if(k.isMountpoint(t))throw new k.ErrnoError(10);r.node_ops.unlink(r,e),k.destroyNode(t)},readlink:e=>{e=k.lookupPath(e).node;if(!e)throw new k.ErrnoError(44);if(e.node_ops.readlink)return E.resolve(k.getPath(e.parent),e.node_ops.readlink(e));throw new k.ErrnoError(28)},stat:(e,r)=>{e=k.lookupPath(e,{follow:!r}).node;if(!e)throw new k.ErrnoError(44);if(e.node_ops.getattr)return e.node_ops.getattr(e);throw new k.ErrnoError(63)},lstat:e=>k.stat(e,!0),chmod:(e,r,t)=>{t="string"==typeof e?k.lookupPath(e,{follow:!t}).node:e;if(!t.node_ops.setattr)throw new k.ErrnoError(63);t.node_ops.setattr(t,{mode:4095&r|-4096&t.mode,timestamp:Date.now()})},lchmod:(e,r)=>{k.chmod(e,r,!0)},fchmod:(e,r)=>{e=k.getStream(e);if(!e)throw new k.ErrnoError(8);k.chmod(e.node,r)},chown:(e,r,t,n)=>{n="string"==typeof e?k.lookupPath(e,{follow:!n}).node:e;if(!n.node_ops.setattr)throw new k.ErrnoError(63);n.node_ops.setattr(n,{timestamp:Date.now()})},lchown:(e,r,t)=>{k.chown(e,r,t,!0)},fchown:(e,r,t)=>{e=k.getStream(e);if(!e)throw new k.ErrnoError(8);k.chown(e.node,r,t)},truncate:(e,r)=>{if(r<0)throw new k.ErrnoError(28);e="string"==typeof e?k.lookupPath(e,{follow:!0}).node:e;if(!e.node_ops.setattr)throw new k.ErrnoError(63);if(k.isDir(e.mode))throw new k.ErrnoError(31);if(!k.isFile(e.mode))throw new k.ErrnoError(28);var t=k.nodePermissions(e,"w");if(t)throw new k.ErrnoError(t);e.node_ops.setattr(e,{size:r,timestamp:Date.now()})},ftruncate:(e,r)=>{e=k.getStream(e);if(!e)throw new k.ErrnoError(8);if(0==(2097155&e.flags))throw new k.ErrnoError(28);k.truncate(e.node,r)},utime:(e,r,t)=>{e=k.lookupPath(e,{follow:!0}).node;e.node_ops.setattr(e,{timestamp:Math.max(r,t)})},open:(e,r,t)=>{if(""===e)throw new k.ErrnoError(44);var n;if(t=void 0===t?438:t,t=64&(r="string"==typeof r?k.modeStringToFlags(r):r)?4095&t|32768:0,"object"==typeof e)n=e;else{e=b.normalize(e);try{n=k.lookupPath(e,{follow:!(131072&r)}).node}catch(e){}}var o=!1;if(64&r)if(n){if(128&r)throw new k.ErrnoError(20)}else n=k.mknod(e,t,0),o=!0;if(!n)throw new k.ErrnoError(44);if(k.isChrdev(n.mode)&&(r&=-513),65536&r&&!k.isDir(n.mode))throw new k.ErrnoError(54);if(!o){t=k.mayOpen(n,r);if(t)throw new k.ErrnoError(t)}512&r&&!o&&k.truncate(n,0),r&=-131713;t=k.createStream({node:n,path:k.getPath(n),flags:r,seekable:!0,position:0,stream_ops:n.stream_ops,ungotten:[],error:!1});return t.stream_ops.open&&t.stream_ops.open(t),!y.logReadFiles||1&r||(k.readFiles||(k.readFiles={}),e in k.readFiles||(k.readFiles[e]=1)),t},close:e=>{if(k.isClosed(e))throw new k.ErrnoError(8);e.getdents&&(e.getdents=null);try{e.stream_ops.close&&e.stream_ops.close(e)}catch(e){throw e}finally{k.closeStream(e.fd)}e.fd=null},isClosed:e=>null===e.fd,llseek:(e,r,t)=>{if(k.isClosed(e))throw new k.ErrnoError(8);if(!e.seekable||!e.stream_ops.llseek)throw new k.ErrnoError(70);if(0!=t&&1!=t&&2!=t)throw new k.ErrnoError(28);return e.position=e.stream_ops.llseek(e,r,t),e.ungotten=[],e.position},read:(e,r,t,n,o)=>{if(n<0||o<0)throw new k.ErrnoError(28);if(k.isClosed(e))throw new k.ErrnoError(8);if(1==(2097155&e.flags))throw new k.ErrnoError(8);if(k.isDir(e.node.mode))throw new k.ErrnoError(31);if(!e.stream_ops.read)throw new k.ErrnoError(28);var i=void 0!==o;if(i){if(!e.seekable)throw new k.ErrnoError(70)}else o=e.position;r=e.stream_ops.read(e,r,t,n,o);return i||(e.position+=r),r},write:(e,r,t,n,o,i)=>{if(n<0||o<0)throw new k.ErrnoError(28);if(k.isClosed(e))throw new k.ErrnoError(8);if(0==(2097155&e.flags))throw new k.ErrnoError(8);if(k.isDir(e.node.mode))throw new k.ErrnoError(31);if(!e.stream_ops.write)throw new k.ErrnoError(28);e.seekable&&1024&e.flags&&k.llseek(e,0,2);var s=void 0!==o;if(s){if(!e.seekable)throw new k.ErrnoError(70)}else o=e.position;r=e.stream_ops.write(e,r,t,n,o,i);return s||(e.position+=r),r},allocate:(e,r,t)=>{if(k.isClosed(e))throw new k.ErrnoError(8);if(r<0||t<=0)throw new k.ErrnoError(28);if(0==(2097155&e.flags))throw new k.ErrnoError(8);if(!k.isFile(e.node.mode)&&!k.isDir(e.node.mode))throw new k.ErrnoError(43);if(!e.stream_ops.allocate)throw new k.ErrnoError(138);e.stream_ops.allocate(e,r,t)},mmap:(e,r,t,n,o)=>{if(0!=(2&n)&&0==(2&o)&&2!=(2097155&e.flags))throw new k.ErrnoError(2);if(1==(2097155&e.flags))throw new k.ErrnoError(2);if(e.stream_ops.mmap)return e.stream_ops.mmap(e,r,t,n,o);throw new k.ErrnoError(43)},msync:(e,r,t,n,o)=>e&&e.stream_ops.msync?e.stream_ops.msync(e,r,t,n,o):0,munmap:e=>0,ioctl:(e,r,t)=>{if(e.stream_ops.ioctl)return e.stream_ops.ioctl(e,r,t);throw new k.ErrnoError(59)},readFile:function(e){let r=1<arguments.length&&void 0!==arguments[1]?arguments[1]:{};if(r.flags=r.flags||0,r.encoding=r.encoding||"binary","utf8"!==r.encoding&&"binary"!==r.encoding)throw new Error('Invalid encoding type "'+r.encoding+'"');var t,n=k.open(e,r.flags),e=k.stat(e).size,o=new Uint8Array(e);return k.read(n,o,0,e,0),"utf8"===r.encoding?t=s(o,0):"binary"===r.encoding&&(t=o),k.close(n),t},writeFile:function(e,r){let t=2<arguments.length&&void 0!==arguments[2]?arguments[2]:{};t.flags=t.flags||577;e=k.open(e,t.flags,t.mode);if("string"==typeof r){var n=new Uint8Array(te(r)+1),o=re(r,n,0,n.length);k.write(e,n,0,o,void 0,t.canOwn)}else{if(!ArrayBuffer.isView(r))throw new Error("Unsupported data type");k.write(e,r,0,r.byteLength,void 0,t.canOwn)}k.close(e)},cwd:()=>k.currentPath,chdir:e=>{e=k.lookupPath(e,{follow:!0});if(null===e.node)throw new k.ErrnoError(44);if(!k.isDir(e.node.mode))throw new k.ErrnoError(54);var r=k.nodePermissions(e.node,"x");if(r)throw new k.ErrnoError(r);k.currentPath=e.path},createDefaultDirectories:()=>{k.mkdir("/tmp"),k.mkdir("/home"),k.mkdir("/home/<USER>")},createDefaultDevices:()=>{k.mkdir("/dev"),k.registerDevice(k.makedev(1,3),{read:()=>0,write:(e,r,t,n,o)=>n}),k.mkdev("/dev/null",k.makedev(1,3)),o.register(k.makedev(5,0),o.default_tty_ops),o.register(k.makedev(6,0),o.default_tty1_ops),k.mkdev("/dev/tty",k.makedev(5,0)),k.mkdev("/dev/tty1",k.makedev(6,0));var e=function(){var e;if("object"==typeof crypto&&"function"==typeof crypto.getRandomValues)return e=new Uint8Array(1),()=>(crypto.getRandomValues(e),e[0]);if(H)try{var r=Ar.default;return()=>r.randomBytes(1)[0]}catch(e){}return()=>v("randomDevice")}();k.createDevice("/dev","random",e),k.createDevice("/dev","urandom",e),k.mkdir("/dev/shm"),k.mkdir("/dev/shm/tmp")},createSpecialDirectories:()=>{k.mkdir("/proc");var r=k.mkdir("/proc/self");k.mkdir("/proc/self/fd"),k.mount({mount:()=>{var e=k.createNode(r,"fd",16895,73);return e.node_ops={lookup:(e,r)=>{var t=k.getStream(+r);if(!t)throw new k.ErrnoError(8);r={parent:null,mount:{mountpoint:"fake"},node_ops:{readlink:()=>t.path}};return r.parent=r}},e}},{},"/proc/self/fd")},createStandardStreams:()=>{y.stdin?k.createDevice("/dev","stdin",y.stdin):k.symlink("/dev/tty","/dev/stdin"),y.stdout?k.createDevice("/dev","stdout",null,y.stdout):k.symlink("/dev/tty","/dev/stdout"),y.stderr?k.createDevice("/dev","stderr",null,y.stderr):k.symlink("/dev/tty1","/dev/stderr"),k.open("/dev/stdin",0),k.open("/dev/stdout",1),k.open("/dev/stderr",1)},ensureErrnoError:()=>{k.ErrnoError||(k.ErrnoError=function(e,r){this.node=r,this.setErrno=function(e){this.errno=e},this.setErrno(e),this.message="FS error"},k.ErrnoError.prototype=new Error,k.ErrnoError.prototype.constructor=k.ErrnoError,[44].forEach(e=>{k.genericErrors[e]=new k.ErrnoError(e),k.genericErrors[e].stack="<generic error, no stack>"}))},staticInit:()=>{k.ensureErrnoError(),k.nameTable=new Array(4096),k.mount(_,{},"/"),k.createDefaultDirectories(),k.createDefaultDevices(),k.createSpecialDirectories(),k.filesystems={MEMFS:_}},init:(e,r,t)=>{k.init.initialized=!0,k.ensureErrnoError(),y.stdin=e||y.stdin,y.stdout=r||y.stdout,y.stderr=t||y.stderr,k.createStandardStreams()},quit:()=>{k.init.initialized=!1;for(var e=0;e<k.streams.length;e++){var r=k.streams[e];r&&k.close(r)}},getMode:(e,r)=>{var t=0;return e&&(t|=365),r&&(t|=146),t},findObject:(e,r)=>{e=k.analyzePath(e,r);return e.exists?e.object:null},analyzePath:(e,r)=>{try{e=(n=k.lookupPath(e,{follow:!r})).path}catch(e){}var t={isRoot:!1,exists:!1,error:0,name:null,path:null,object:null,parentExists:!1,parentPath:null,parentObject:null};try{var n=k.lookupPath(e,{parent:!0});t.parentExists=!0,t.parentPath=n.path,t.parentObject=n.node,t.name=b.basename(e),n=k.lookupPath(e,{follow:!r}),t.exists=!0,t.path=n.path,t.object=n.node,t.name=n.node.name,t.isRoot="/"===n.path}catch(e){t.error=e.errno}return t},createPath:(e,r,t,n)=>{e="string"==typeof e?e:k.getPath(e);for(var o=r.split("/").reverse();o.length;){var i=o.pop();if(i){var s=b.join2(e,i);try{k.mkdir(s)}catch(e){}e=s}}return s},createFile:(e,r,t,n,o)=>{e=b.join2("string"==typeof e?e:k.getPath(e),r),r=k.getMode(n,o);return k.create(e,r)},createDataFile:(e,r,t,n,o,i)=>{var s=r,r=(e&&(e="string"==typeof e?e:k.getPath(e),s=r?b.join2(e,r):e),k.getMode(n,o)),e=k.create(s,r);if(t){if("string"==typeof t){for(var a=new Array(t.length),u=0,l=t.length;u<l;++u)a[u]=t.charCodeAt(u);t=a}k.chmod(e,146|r);n=k.open(e,577);k.write(n,t,0,t.length,0,i),k.close(n),k.chmod(e,r)}return e},createDevice:(e,r,u,s)=>{var e=b.join2("string"==typeof e?e:k.getPath(e),r),r=k.getMode(!!u,!!s),t=(k.createDevice.major||(k.createDevice.major=64),k.makedev(k.createDevice.major++,0));return k.registerDevice(t,{open:e=>{e.seekable=!1},close:e=>{s&&s.buffer&&s.buffer.length&&s(10)},read:(e,r,t,n,o)=>{for(var i,s=0,a=0;a<n;a++){try{i=u()}catch(e){throw new k.ErrnoError(29)}if(void 0===i&&0===s)throw new k.ErrnoError(6);if(null==i)break;s++,r[t+a]=i}return s&&(e.node.timestamp=Date.now()),s},write:(e,r,t,n,o)=>{for(var i=0;i<n;i++)try{s(r[t+i])}catch(e){throw new k.ErrnoError(29)}return n&&(e.node.timestamp=Date.now()),i}}),k.mkdev(e,r,t)},forceLoadFile:e=>{if(e.isDevice||e.isFolder||e.link||e.contents)return!0;if("undefined"!=typeof XMLHttpRequest)throw new Error("Lazy loading should have been performed (contents set) in createLazyFile, but it was not. Lazy loading only works in web workers. Use --embed-file or --preload-file in emcc on the main thread.");if(!r)throw new Error("Cannot load without read() or XMLHttpRequest.");try{e.contents=pe(r(e.url),!0),e.usedBytes=e.contents.length}catch(e){throw new k.ErrnoError(29)}},createLazyFile:(e,r,s,t,n)=>{function o(){this.lengthKnown=!1,this.chunks=[]}if(o.prototype.get=function(e){var r;if(!(e>this.length-1||e<0))return r=e%this.chunkSize,e=e/this.chunkSize|0,this.getter(e)[r]},o.prototype.setDataGetter=function(e){this.getter=e},o.prototype.cacheLength=function(){var e=new XMLHttpRequest;if(e.open("HEAD",s,!1),e.send(null),!(200<=e.status&&e.status<300||304===e.status))throw new Error("Couldn't load "+s+". Status: "+e.status);var r,n=Number(e.getResponseHeader("Content-length")),t=(r=e.getResponseHeader("Accept-Ranges"))&&"bytes"===r,e=(r=e.getResponseHeader("Content-Encoding"))&&"gzip"===r,o=1048576,i=(t||(o=n),this);i.setDataGetter(e=>{var r=e*o,t=(e+1)*o-1,t=Math.min(t,n-1);if(void 0===i.chunks[e]&&(i.chunks[e]=((e,r)=>{if(r<e)throw new Error("invalid range ("+e+", "+r+") or no bytes requested!");if(n-1<r)throw new Error("only "+n+" bytes available! programmer error!");var t=new XMLHttpRequest;if(t.open("GET",s,!1),n!==o&&t.setRequestHeader("Range","bytes="+e+"-"+r),t.responseType="arraybuffer",t.overrideMimeType&&t.overrideMimeType("text/plain; charset=x-user-defined"),t.send(null),200<=t.status&&t.status<300||304===t.status)return void 0!==t.response?new Uint8Array(t.response||[]):pe(t.responseText||"",!0);throw new Error("Couldn't load "+s+". Status: "+t.status)})(r,t)),void 0===i.chunks[e])throw new Error("doXHR failed!");return i.chunks[e]}),!e&&n||(o=n=1,n=this.getter(0).length,o=n,q("LazyFiles on gzip forces download of the whole file when length is accessed")),this._length=n,this._chunkSize=o,this.lengthKnown=!0},"undefined"!=typeof XMLHttpRequest){if(!c)throw"Cannot do synchronous binary XHRs outside webworkers in modern browsers. Use --embed-file or --preload-file in emcc";var i=new o,i=(Object.defineProperties(i,{length:{get:function(){return this.lengthKnown||this.cacheLength(),this._length}},chunkSize:{get:function(){return this.lengthKnown||this.cacheLength(),this._chunkSize}}}),{isDevice:!1,contents:i})}else i={isDevice:!1,url:s};var a=k.createFile(e,r,i,t,n),u=(i.contents?a.contents=i.contents:i.url&&(a.contents=null,a.url=i.url),Object.defineProperties(a,{usedBytes:{get:function(){return this.contents.length}}}),{});function l(e,r,t,n,o){var i=e.node.contents;if(o>=i.length)return 0;var s=Math.min(i.length-o,n);if(i.slice)for(var a=0;a<s;a++)r[t+a]=i[o+a];else for(a=0;a<s;a++)r[t+a]=i.get(o+a);return s}return Object.keys(a.stream_ops).forEach(e=>{var r=a.stream_ops[e];u[e]=function(){return k.forceLoadFile(a),r.apply(null,arguments)}}),u.read=(e,r,t,n,o)=>(k.forceLoadFile(a),l(e,r,t,n,o)),u.mmap=(e,r,t,n,o)=>{k.forceLoadFile(a);var i=he(r);if(i)return l(e,d,i,r,t),{ptr:i,allocated:!0};throw new k.ErrnoError(48)},a.stream_ops=u,a},createPreloadedFile:(t,n,e,o,i,s,a,u,l,c)=>{var r,d,f,p=n?E.resolve(b.join2(t,n)):t;function h(e){function r(e){c&&c(),u||k.createDataFile(t,n,e,o,i,l),s&&s(),ae()}Browser.handledByPreloadPlugin(e,p,r,()=>{a&&a(),ae()})||r(e)}se(),"string"==typeof e?(d=a,f="al "+(r=e),m(r,e=>{G(e,'Loading data file "'+r+'" failed (no arrayBuffer).'),h(new Uint8Array(e)),f&&ae()},e=>{if(!d)throw'Loading data file "'+r+'" failed.';d()}),f&&se()):h(e)},indexedDB:()=>window.indexedDB||window.mozIndexedDB||window.webkitIndexedDB||window.msIndexedDB,DB_NAME:()=>"EM_FS_"+window.location.pathname,DB_VERSION:20,DB_STORE_NAME:"FILE_DATA",saveFilesToDB:(s,a,u)=>{a=a||(()=>{}),u=u||(()=>{});var e=k.indexedDB();try{var l=e.open(k.DB_NAME(),k.DB_VERSION)}catch(s){return u(s)}l.onupgradeneeded=()=>{q("creating db"),l.result.createObjectStore(k.DB_STORE_NAME)},l.onsuccess=()=>{var e=l.result.transaction([k.DB_STORE_NAME],"readwrite"),r=e.objectStore(k.DB_STORE_NAME),t=0,n=0,o=s.length;function i(){(0==n?a:u)()}s.forEach(e=>{e=r.put(k.analyzePath(e).object.contents,e);e.onsuccess=()=>{++t+n==o&&i()},e.onerror=()=>{t+ ++n==o&&i()}}),e.onerror=u},l.onerror=u},loadFilesFromDB:(a,u,l)=>{u=u||(()=>{}),l=l||(()=>{});var e=k.indexedDB();try{var c=e.open(k.DB_NAME(),k.DB_VERSION)}catch(a){return l(a)}c.onupgradeneeded=l,c.onsuccess=()=>{var e=c.result;try{var r=e.transaction([k.DB_STORE_NAME],"readonly")}catch(e){return void l(e)}var t=r.objectStore(k.DB_STORE_NAME),n=0,o=0,i=a.length;function s(){(0==o?u:l)()}a.forEach(e=>{var r=t.get(e);r.onsuccess=()=>{k.analyzePath(e).exists&&k.unlink(e),k.createDataFile(b.dirname(e),b.basename(e),r.result,!0,!0,!0),++n+o==i&&s()},r.onerror=()=>{n+ ++o==i&&s()}}),r.onerror=l},c.onerror=l}},S={DEFAULT_POLLMASK:5,calculateAt:function(e,r,t){if(b.isAbs(r))return r;var n;if(-100===e)n=k.cwd();else{e=k.getStream(e);if(!e)throw new k.ErrnoError(8);n=e.path}if(0!=r.length)return b.join2(n,r);if(t)return n;throw new k.ErrnoError(44)},doStat:function(e,r,t){try{var n=e(r)}catch(e){if(e&&e.node&&b.normalize(r)!==b.normalize(k.getPath(e.node)))return-54;throw e}return l[t>>2]=n.dev,l[t+4>>2]=0,l[t+8>>2]=n.ino,l[t+12>>2]=n.mode,l[t+16>>2]=n.nlink,l[t+20>>2]=n.uid,l[t+24>>2]=n.gid,l[t+28>>2]=n.rdev,l[t+32>>2]=0,w=[n.size>>>0,(g=n.size,1<=+Math.abs(g)?0<g?(0|Math.min(+Math.floor(g/4294967296),4294967295))>>>0:~~+Math.ceil((g-(~~g>>>0))/4294967296)>>>0:0)],l[t+40>>2]=w[0],l[t+44>>2]=w[1],l[t+48>>2]=4096,l[t+52>>2]=n.blocks,w=[Math.floor(n.atime.getTime()/1e3)>>>0,(g=Math.floor(n.atime.getTime()/1e3),1<=+Math.abs(g)?0<g?(0|Math.min(+Math.floor(g/4294967296),4294967295))>>>0:~~+Math.ceil((g-(~~g>>>0))/4294967296)>>>0:0)],l[t+56>>2]=w[0],l[t+60>>2]=w[1],l[t+64>>2]=0,w=[Math.floor(n.mtime.getTime()/1e3)>>>0,(g=Math.floor(n.mtime.getTime()/1e3),1<=+Math.abs(g)?0<g?(0|Math.min(+Math.floor(g/4294967296),4294967295))>>>0:~~+Math.ceil((g-(~~g>>>0))/4294967296)>>>0:0)],l[t+72>>2]=w[0],l[t+76>>2]=w[1],l[t+80>>2]=0,w=[Math.floor(n.ctime.getTime()/1e3)>>>0,(g=Math.floor(n.ctime.getTime()/1e3),1<=+Math.abs(g)?0<g?(0|Math.min(+Math.floor(g/4294967296),4294967295))>>>0:~~+Math.ceil((g-(~~g>>>0))/4294967296)>>>0:0)],l[t+88>>2]=w[0],l[t+92>>2]=w[1],l[t+96>>2]=0,w=[n.ino>>>0,(g=n.ino,1<=+Math.abs(g)?0<g?(0|Math.min(+Math.floor(g/4294967296),4294967295))>>>0:~~+Math.ceil((g-(~~g>>>0))/4294967296)>>>0:0)],l[t+104>>2]=w[0],l[t+108>>2]=w[1],0},doMsync:function(e,r,t,n,o){e=f.slice(e,e+t);k.msync(r,e,o,t,n)},varargs:void 0,get:function(){return S.varargs+=4,l[S.varargs-4>>2]},getStr:function(e){return ee(e)},getStreamFromFD:function(e){e=k.getStream(e);if(e)return e;throw new k.ErrnoError(8)}};function me(e){switch(e){case 1:return 0;case 2:return 1;case 4:return 2;case 8:return 3;default:throw new TypeError("Unknown type size: "+e)}}var ye=void 0;function P(e){for(var r="",t=e;f[t];)r+=ye[f[t++]];return r}var A={},T={},ge={};function we(e){if(void 0===e)return"_unknown";var r=(e=e.replace(/[^a-zA-Z0-9_]/g,"$")).charCodeAt(0);return 48<=r&&r<=57?"_"+e:e}function ve(e,r){return e=we(e),new Function("body","return function "+e+'() {\n    "use strict";    return body.apply(this, arguments);\n};\n')(r)}function be(e,r){var t=ve(r,function(e){this.name=r,this.message=e;e=new Error(e).stack;void 0!==e&&(this.stack=this.toString()+"\n"+e.replace(/^Error(:[^\n]*)?\n/,""))});return t.prototype=Object.create(e.prototype),(t.prototype.constructor=t).prototype.toString=function(){return void 0===this.message?this.name:this.name+": "+this.message},t}var D=void 0;function F(e){throw new D(e)}var Ee=void 0;function _e(e){throw new Ee(e)}function ke(n,r,o){function t(e){var r=o(e);r.length!==n.length&&_e("Mismatched type converter count");for(var t=0;t<n.length;++t)C(n[t],r[t])}n.forEach(function(e){ge[e]=r});var i=new Array(r.length),s=[],a=0;r.forEach((e,r)=>{T.hasOwnProperty(e)?i[r]=T[e]:(s.push(e),A.hasOwnProperty(e)||(A[e]=[]),A[e].push(()=>{i[r]=T[e],++a===s.length&&t(i)}))}),0===s.length&&t(i)}function C(e,r,t){t=2<arguments.length&&void 0!==t?t:{};if(!("argPackAdvance"in r))throw new TypeError("registerType registeredInstance requires argPackAdvance");var n=r.name;if(e||F('type "'+n+'" must have a positive integer typeid pointer'),T.hasOwnProperty(e)){if(t.ignoreDuplicateRegistrations)return;F("Cannot register type '"+n+"' twice")}T[e]=r,delete ge[e],A.hasOwnProperty(e)&&(t=A[e],delete A[e],t.forEach(e=>e()))}function Se(e){F(e.$$.ptrType.registeredClass.name+" instance already deleted")}var Pe=!1;function Ae(e){}function Te(e){--e.count.value,0===e.count.value&&((e=e).smartPtr?e.smartPtrType.rawDestructor(e.smartPtr):e.ptrType.registeredClass.rawDestructor(e.ptr))}var De={};var Fe=[];function Ce(){for(;Fe.length;){var e=Fe.pop();e.$$.deleteScheduled=!1,e.delete()}}var Me=void 0;var $e={};function Re(e,r){return r.ptrType&&r.ptr||_e("makeClassHandle requires ptr and ptrType"),!!r.smartPtrType!=!!r.smartPtr&&_e("Both smartPtrType and smartPtr must be specified"),r.count={value:1},Oe(Object.create(e,{$$:{value:r}}))}function Oe(e){return"undefined"==typeof FinalizationRegistry?(Oe=e=>e,e):(Pe=new FinalizationRegistry(e=>{Te(e.$$)}),Ae=e=>Pe.unregister(e),(Oe=e=>{var r=e.$$;return r.smartPtr&&Pe.register(e,{$$:r},e),e})(e))}function M(){}function je(e,r,t){var n;void 0===e[r].overloadTable&&(n=e[r],e[r]=function(){return e[r].overloadTable.hasOwnProperty(arguments.length)||F("Function '"+t+"' called with an invalid number of arguments ("+arguments.length+") - expects one of ("+e[r].overloadTable+")!"),e[r].overloadTable[arguments.length].apply(this,arguments)},e[r].overloadTable=[],e[r].overloadTable[n.argCount]=n)}function xe(e,r,t,n,o,i,s,a){this.name=e,this.constructor=r,this.instancePrototype=t,this.rawDestructor=n,this.baseClass=o,this.getActualType=i,this.upcast=s,this.downcast=a,this.pureVirtualFunctions=[]}function Ue(e,r,t){for(;r!==t;)r.upcast||F("Expected null or instance of "+t.name+", got an instance of "+r.name),e=r.upcast(e),r=r.baseClass;return e}function Be(e,r){if(null===r)return this.isReference&&F("null is not a valid "+this.name),0;r.$$||F('Cannot pass "'+er(r)+'" as a '+this.name),r.$$.ptr||F("Cannot pass deleted object as a pointer of type "+this.name);var t=r.$$.ptrType.registeredClass;return Ue(r.$$.ptr,t,this.registeredClass)}function We(e,r){if(null===r)return this.isReference&&F("null is not a valid "+this.name),this.isSmartPointer?(n=this.rawConstructor(),null!==e&&e.push(this.rawDestructor,n),n):0;r.$$||F('Cannot pass "'+er(r)+'" as a '+this.name),r.$$.ptr||F("Cannot pass deleted object as a pointer of type "+this.name),!this.isConst&&r.$$.ptrType.isConst&&F("Cannot convert argument of type "+(r.$$.smartPtrType||r.$$.ptrType).name+" to parameter type "+this.name);var t,n,o=r.$$.ptrType.registeredClass;if(n=Ue(r.$$.ptr,o,this.registeredClass),this.isSmartPointer)switch(void 0===r.$$.smartPtr&&F("Passing raw pointer to smart pointer is illegal"),this.sharingPolicy){case 0:r.$$.smartPtrType===this?n=r.$$.smartPtr:F("Cannot convert argument of type "+(r.$$.smartPtrType||r.$$.ptrType).name+" to parameter type "+this.name);break;case 1:n=r.$$.smartPtr;break;case 2:r.$$.smartPtrType===this?n=r.$$.smartPtr:(t=r.clone(),n=this.rawShare(n,Ye.toHandle(function(){t.delete()})),null!==e&&e.push(this.rawDestructor,n));break;default:F("Unsupporting sharing policy")}return n}function ze(e,r){if(null===r)return this.isReference&&F("null is not a valid "+this.name),0;r.$$||F('Cannot pass "'+er(r)+'" as a '+this.name),r.$$.ptr||F("Cannot pass deleted object as a pointer of type "+this.name),r.$$.ptrType.isConst&&F("Cannot convert argument of type "+r.$$.ptrType.name+" to parameter type "+this.name);var t=r.$$.ptrType.registeredClass;return Ue(r.$$.ptr,t,this.registeredClass)}function Ie(e){return this.fromWireType(l[e>>2])}function $(e,r,t,n,o,i,s,a,u,l,c){this.name=e,this.registeredClass=r,this.isReference=t,this.isConst=n,this.isSmartPointer=o,this.pointeeType=i,this.sharingPolicy=s,this.rawGetPointee=a,this.rawConstructor=u,this.rawShare=l,this.rawDestructor=c,o||void 0!==r.baseClass?this.toWireType=We:(this.toWireType=n?Be:ze,this.destructorFunction=null)}var Le=[];function Ne(e){var r=Le[e];return r||(e>=Le.length&&(Le.length=e+1),Le[e]=r=Q.get(e)),r}function R(e,r){var o,i,s,t=(e=P(e)).includes("j")?(o=e,i=r,s=[],function(){return s.length=0,Object.assign(s,arguments),r=i,t=s,(e=o).includes("j")?(n=r,e=y["dynCall_"+e],t&&t.length?e.apply(null,[n].concat(t)):e.call(null,n)):Ne(r).apply(null,t);var e,r,t,n}):Ne(r);return"function"!=typeof t&&F("unknown function pointer with signature "+e+": "+r),t}var He=void 0;function qe(e){var e=wr(e),r=P(e);return j(e),r}function Ve(e,r){var t=[],n={};throw r.forEach(function e(r){n[r]||T[r]||(ge[r]?ge[r].forEach(e):(t.push(r),n[r]=!0))}),new He(e+": "+t.map(qe).join([", "]))}function Ge(e,r){for(var t=[],n=0;n<e;n++)t.push(p[r+4*n>>2]);return t}function Xe(e){for(;e.length;){var r=e.pop();e.pop()(r)}}function Je(e,r){if(!(e instanceof Function))throw new TypeError("new_ called with constructor type "+typeof e+" which is not a function");var t=ve(e.name||"unknownFunctionName",function(){}),t=(t.prototype=e.prototype,new t),e=e.apply(t,r);return e instanceof Object?e:t}function Ke(e,r,t,n,o){var i=r.length;i<2&&F("argTypes array size mismatch! Must at least get return value and 'this' types!");for(var t=null!==r[1]&&null!==t,s=!1,a=1;a<r.length;++a)if(null!==r[a]&&void 0===r[a].destructorFunction){s=!0;break}for(var u="void"!==r[0].name,l="",c="",a=0;a<i-2;++a)l+=(0!==a?", ":"")+"arg"+a,c+=(0!==a?", ":"")+"arg"+a+"Wired";var d="return function "+we(e)+"("+l+") {\nif (arguments.length !== "+(i-2)+") {\nthrowBindingError('function "+e+" called with ' + arguments.length + ' arguments, expected "+(i-2)+" args!');\n}\n",f=(s&&(d+="var destructors = [];\n"),s?"destructors":"null"),p=["throwBindingError","invoker","fn","runDestructors","retType","classParam"],h=[F,n,o,Xe,r[0],r[1]];for(t&&(d+="var thisWired = classParam.toWireType("+f+", this);\n"),a=0;a<i-2;++a)d+="var arg"+a+"Wired = argType"+a+".toWireType("+f+", arg"+a+"); // "+r[a+2].name+"\n",p.push("argType"+a),h.push(r[a+2]);if(d+=(u?"var rv = ":"")+"invoker(fn"+(0<(c=t?"thisWired"+(0<c.length?", ":"")+c:c).length?", ":"")+c+");\n",s)d+="runDestructors(destructors);\n";else for(a=t?1:2;a<r.length;++a){var m=1===a?"thisWired":"arg"+(a-2)+"Wired";null!==r[a].destructorFunction&&(d+=m+"_dtor("+m+"); // "+r[a].name+"\n",p.push(m+"_dtor"),h.push(r[a].destructorFunction))}return u&&(d+="var ret = retType.fromWireType(rv);\nreturn ret;\n"),p.push(d+="}\n"),Je(Function,p).apply(null,h)}var Ze=[],O=[{},{value:void 0},{value:null},{value:!0},{value:!1}];function Qe(e){4<e&&0==--O[e].refcount&&(O[e]=void 0,Ze.push(e))}var Ye={toValue:e=>(e||F("Cannot use deleted val. handle = "+e),O[e].value),toHandle:e=>{switch(e){case void 0:return 1;case null:return 2;case!0:return 3;case!1:return 4;default:var r=Ze.length?Ze.pop():O.length;return O[r]={refcount:1,value:e},r}}};function er(e){if(null===e)return"null";var r=typeof e;return"object"==r||"array"==r||"function"==r?e.toString():""+e}var rr="undefined"!=typeof TextDecoder?new TextDecoder("utf-16le"):void 0;function tr(e,r){for(var t,n=e>>1,o=n+r/2;!(o<=n)&&J[n];)++n;if(32<(t=n<<1)-e&&rr)return rr.decode(f.subarray(e,t));for(var i="",s=0;!(r/2<=s);++s){var a=u[e+2*s>>1];if(0==a)break;i+=String.fromCharCode(a)}return i}function nr(e,r,t){if((t=void 0===t?2147483647:t)<2)return 0;for(var n=r,o=(t-=2)<2*e.length?t/2:e.length,i=0;i<o;++i){var s=e.charCodeAt(i);u[r>>1]=s,r+=2}return u[r>>1]=0,r-n}function or(e){return 2*e.length}function ir(e,r){for(var t=0,n="";!(r/4<=t);){var o,i=l[e+4*t>>2];if(0==i)break;++t,65536<=i?(o=i-65536,n+=String.fromCharCode(55296|o>>10,56320|1023&o)):n+=String.fromCharCode(i)}return n}function sr(e,r,t){if((t=void 0===t?2147483647:t)<4)return 0;for(var n=r,o=n+t-4,i=0;i<e.length;++i){var s=e.charCodeAt(i);if(55296<=s&&s<=57343&&(s=65536+((1023&s)<<10)|1023&e.charCodeAt(++i)),l[r>>2]=s,(r+=4)+4>o)break}return l[r>>2]=0,r-n}function ar(e){for(var r=0,t=0;t<e.length;++t){var n=e.charCodeAt(t);55296<=n&&n<=57343&&++t,r+=4}return r}var ur={},lr=[],cr=[],dr={};function fr(){if(!fr.strings){var e={USER:"web_user",LOGNAME:"web_user",PATH:"/",PWD:"/",HOME:"/home/<USER>",LANG:("object"==typeof navigator&&navigator.languages&&navigator.languages[0]||"C").replace("-","_")+".UTF-8",_:L||"./this.program"};for(r in dr)void 0===dr[r]?delete e[r]:e[r]=dr[r];var r,t=[];for(r in e)t.push(r+"="+e[r]);fr.strings=t}return fr.strings}function pr(e,r,t,n){this.parent=e=e||this,this.mount=e.mount,this.mounted=null,this.id=k.nextInode++,this.name=r,this.mode=t,this.node_ops={},this.stream_ops={},this.rdev=n}Object.defineProperties(pr.prototype,{read:{get:function(){return 365==(365&this.mode)},set:function(e){e?this.mode|=365:this.mode&=-366}},write:{get:function(){return 146==(146&this.mode)},set:function(e){e?this.mode|=146:this.mode&=-147}},isFolder:{get:function(){return k.isDir(this.mode)}},isDevice:{get:function(){return k.isChrdev(this.mode)}}}),k.FSNode=pr,k.staticInit();for(var hr=new Array(256),mr=0;mr<256;++mr)hr[mr]=String.fromCharCode(mr);ye=hr,D=y.BindingError=be(Error,"BindingError"),Ee=y.InternalError=be(Error,"InternalError"),M.prototype.isAliasOf=function(e){if(!(this instanceof M))return!1;if(!(e instanceof M))return!1;for(var r=this.$$.ptrType.registeredClass,t=this.$$.ptr,n=e.$$.ptrType.registeredClass,o=e.$$.ptr;r.baseClass;)t=r.upcast(t),r=r.baseClass;for(;n.baseClass;)o=n.upcast(o),n=n.baseClass;return r===n&&t===o},M.prototype.clone=function(){if(this.$$.ptr||Se(this),this.$$.preservePointerOnDelete)return this.$$.count.value+=1,this;var e=Oe(Object.create(Object.getPrototypeOf(this),{$$:{value:{count:(e=this.$$).count,deleteScheduled:e.deleteScheduled,preservePointerOnDelete:e.preservePointerOnDelete,ptr:e.ptr,ptrType:e.ptrType,smartPtr:e.smartPtr,smartPtrType:e.smartPtrType}}}));return e.$$.count.value+=1,e.$$.deleteScheduled=!1,e},M.prototype.delete=function(){this.$$.ptr||Se(this),this.$$.deleteScheduled&&!this.$$.preservePointerOnDelete&&F("Object already scheduled for deletion"),Ae(this),Te(this.$$),this.$$.preservePointerOnDelete||(this.$$.smartPtr=void 0,this.$$.ptr=void 0)},M.prototype.isDeleted=function(){return!this.$$.ptr},M.prototype.deleteLater=function(){return this.$$.ptr||Se(this),this.$$.deleteScheduled&&!this.$$.preservePointerOnDelete&&F("Object already scheduled for deletion"),Fe.push(this),1===Fe.length&&Me&&Me(Ce),this.$$.deleteScheduled=!0,this},y.getInheritedInstanceCount=function(){return Object.keys($e).length},y.getLiveInheritedInstances=function(){var e,r=[];for(e in $e)$e.hasOwnProperty(e)&&r.push($e[e]);return r},y.flushPendingDeletes=Ce,y.setDelayFunction=function(e){Me=e,Fe.length&&Me&&Me(Ce)},$.prototype.getPointee=function(e){return e=this.rawGetPointee?this.rawGetPointee(e):e},$.prototype.destructor=function(e){this.rawDestructor&&this.rawDestructor(e)},$.prototype.argPackAdvance=8,$.prototype.readValueFromPointer=Ie,$.prototype.deleteObject=function(e){null!==e&&e.delete()},$.prototype.fromWireType=function(e){var r=this.getPointee(e);if(!r)return this.destructor(e),null;if(n=function(e,r){for(void 0===r&&F("ptr should not be undefined");e.baseClass;)r=e.upcast(r),e=e.baseClass;return r}(n=this.registeredClass,n=r),void 0!==(n=$e[n])){if(0===n.$$.count.value)return n.$$.ptr=r,n.$$.smartPtr=e,n.clone();n=n.clone();return this.destructor(e),n}function t(){return this.isSmartPointer?Re(this.registeredClass.instancePrototype,{ptrType:this.pointeeType,ptr:r,smartPtrType:this,smartPtr:e}):Re(this.registeredClass.instancePrototype,{ptrType:this,ptr:e})}if(n=this.registeredClass.getActualType(r),!(n=De[n]))return t.call(this);var n=this.isConst?n.constPointerType:n.pointerType,o=function e(r,t,n){if(t===n)return r;if(void 0===n.baseClass)return null;r=e(r,t,n.baseClass);return null===r?null:n.downcast(r)}(r,this.registeredClass,n.registeredClass);return null===o?t.call(this):this.isSmartPointer?Re(n.registeredClass.instancePrototype,{ptrType:n,ptr:o,smartPtrType:this,smartPtr:e}):Re(n.registeredClass.instancePrototype,{ptrType:n,ptr:o})},He=y.UnboundTypeError=be(Error,"UnboundTypeError"),y.count_emval_handles=function(){for(var e=0,r=5;r<O.length;++r)void 0!==O[r]&&++e;return e},y.get_first_emval=function(){for(var e=5;e<O.length;++e)if(void 0!==O[e])return O[e];return null};var yr={q:function(e){return br(e+24)+24},p:function(e,r,t){throw new fe(e).init(r,t),e},C:function(e,r,t){S.varargs=t;try{var n=S.getStreamFromFD(e);switch(r){case 0:return(o=S.get())<0?-28:k.createStream(n,o).fd;case 1:case 2:case 6:case 7:return 0;case 3:return n.flags;case 4:var o=S.get();return n.flags|=o,0;case 5:return o=S.get(),u[o+0>>1]=2,0;case 16:case 8:default:return-28;case 9:return l[gr()>>2]=28,-1}}catch(e){if(void 0!==k&&e instanceof k.ErrnoError)return-e.errno;throw e}},w:function(e,r,t,n){S.varargs=n;try{r=S.getStr(r),r=S.calculateAt(e,r);var o=n?S.get():0;return k.open(r,t,o).fd}catch(e){if(void 0!==k&&e instanceof k.ErrnoError)return-e.errno;throw e}},u:function(e,r,t,n,o){},E:function(e,t,n,o,i){var s=me(n);C(e,{name:t=P(t),fromWireType:function(e){return!!e},toWireType:function(e,r){return r?o:i},argPackAdvance:8,readValueFromPointer:function(e){var r;if(1===n)r=d;else if(2===n)r=u;else{if(4!==n)throw new TypeError("Unknown boolean type size: "+t);r=l}return this.fromWireType(r[e>>s])},destructorFunction:null})},t:function(u,e,r,l,t,c,n,d,o,f,p,i,h){p=P(p),c=R(t,c),d=d&&R(n,d),f=f&&R(o,f),h=R(i,h);var s,m=we(p);t=m,n=function(){Ve("Cannot construct "+p+" due to unbound types",[l])},y.hasOwnProperty(t)?(F("Cannot register public name '"+t+"' twice"),je(y,t,t),y.hasOwnProperty(s)&&F("Cannot register multiple overloads of a function with the same number of arguments ("+s+")!"),y[t].overloadTable[s]=n):y[t]=n,ke([u,e,r],l?[l]:[],function(e){e=e[0],e=l?(s=e.registeredClass).instancePrototype:M.prototype;var r,t,n=ve(m,function(){if(Object.getPrototypeOf(this)!==o)throw new D("Use 'new' to construct "+p);if(void 0===i.constructor_body)throw new D(p+" has no accessible constructor");var e=i.constructor_body[arguments.length];if(void 0===e)throw new D("Tried to invoke ctor of "+p+" with invalid number of parameters ("+arguments.length+") - expected ("+Object.keys(i.constructor_body).toString()+") parameters instead!");return e.apply(this,arguments)}),o=Object.create(e,{constructor:{value:n}}),i=(n.prototype=o,new xe(p,n,o,h,s,c,d,f)),e=new $(p,i,!0,!1,!1),s=new $(p+"*",i,!1,!1,!1),a=new $(p+" const*",i,!1,!0,!1);return De[u]={pointerType:s,constPointerType:a},r=m,n=n,y.hasOwnProperty(r)||_e("Replacing nonexistant public symbol"),y[r].overloadTable,y[r]=n,y[r].argCount=t,[e,s,a]})},r:function(e,n,r,t,o,i){G(0<n);var s=Ge(n,r);o=R(t,o),ke([],[e],function(r){var t="constructor "+(r=r[0]).name;if(void 0===r.registeredClass.constructor_body&&(r.registeredClass.constructor_body=[]),void 0!==r.registeredClass.constructor_body[n-1])throw new D("Cannot register multiple constructors with identical number of parameters ("+(n-1)+") for class '"+r.name+"'! Overload resolution is currently only performed using the parameter count, not actual type info!");return r.registeredClass.constructor_body[n-1]=()=>{Ve("Cannot construct "+r.name+" due to unbound types",s)},ke([],s,function(e){return e.splice(1,0,null),r.registeredClass.constructor_body[n-1]=Ke(t,e,null,o,i),[]}),[]})},f:function(e,i,s,r,t,a,u,l){var c=Ge(s,r);i=P(i),a=R(t,a),ke([],[e],function(r){var t=(r=r[0]).name+"."+i;function e(){Ve("Cannot call "+t+" due to unbound types",c)}i.startsWith("@@")&&(i=Symbol[i.substring(2)]),l&&r.registeredClass.pureVirtualFunctions.push(i);var n=r.registeredClass.instancePrototype,o=n[i];return void 0===o||void 0===o.overloadTable&&o.className!==r.name&&o.argCount===s-2?(e.argCount=s-2,e.className=r.name,n[i]=e):(je(n,i,t),n[i].overloadTable[s-2]=e),ke([],c,function(e){e=Ke(t,e,r,a,u);return void 0===n[i].overloadTable?(e.argCount=s-2,n[i]=e):n[i].overloadTable[s-2]=e,[]}),[]})},D:function(e,r){C(e,{name:r=P(r),fromWireType:function(e){var r=Ye.toValue(e);return Qe(e),r},toWireType:function(e,r){return Ye.toHandle(r)},argPackAdvance:8,readValueFromPointer:Ie,destructorFunction:null})},n:function(e,r,t){t=me(t);C(e,{name:r=P(r),fromWireType:function(e){return e},toWireType:function(e,r){return r},argPackAdvance:8,readValueFromPointer:function(e,r){switch(r){case 2:return function(e){return this.fromWireType(K[e>>2])};case 3:return function(e){return this.fromWireType(Z[e>>3])};default:throw new TypeError("Unknown float type: "+e)}}(r,t),destructorFunction:null})},c:function(e,r,t,n,o){r=P(r);var i,s=me(t),a=e=>e,t=(0===n&&(i=32-8*t,a=e=>e<<i>>>i),r.includes("unsigned"));C(e,{name:r,fromWireType:a,toWireType:t?function(e,r){return this.name,r>>>0}:function(e,r){return this.name,r},argPackAdvance:8,readValueFromPointer:function(e,r,t){switch(r){case 0:return t?function(e){return d[e]}:function(e){return f[e]};case 1:return t?function(e){return u[e>>1]}:function(e){return J[e>>1]};case 2:return t?function(e){return l[e>>2]}:function(e){return p[e>>2]};default:throw new TypeError("Unknown integer type: "+e)}}(r,s,0!==n),destructorFunction:null})},b:function(e,r,t){var n=[Int8Array,Uint8Array,Int16Array,Uint16Array,Int32Array,Uint32Array,Float32Array,Float64Array][r];function o(e){var r=p,t=r[e>>=2],r=r[e+1];return new n(X,r,t)}C(e,{name:t=P(t),fromWireType:o,argPackAdvance:8,readValueFromPointer:o},{ignoreDuplicateRegistrations:!0})},m:function(e,r){var l="std::string"===(r=P(r));C(e,{name:r,fromWireType:function(e){var r,t=p[e>>2],n=e+4;if(l)for(var o=n,i=0;i<=t;++i){var s,a=n+i;i!=t&&0!=f[a]||(s=ee(o,a-o),void 0===r?r=s:r=r+String.fromCharCode(0)+s,o=a+1)}else{for(var u=new Array(t),i=0;i<t;++i)u[i]=String.fromCharCode(f[n+i]);r=u.join("")}return j(e),r},toWireType:function(e,r){var t,n="string"==typeof(r=r instanceof ArrayBuffer?new Uint8Array(r):r),o=(n||r instanceof Uint8Array||r instanceof Uint8ClampedArray||r instanceof Int8Array||F("Cannot pass non-string to std::string"),t=l&&n?te(r):r.length,br(4+t+1)),i=o+4;if(p[o>>2]=t,l&&n)re(r,f,i,t+1);else if(n)for(var s=0;s<t;++s){var a=r.charCodeAt(s);255<a&&(j(i),F("String has UTF-16 code units that do not fit in 8 bits")),f[i+s]=a}else for(s=0;s<t;++s)f[i+s]=r[s];return null!==e&&e.push(j,o),o},argPackAdvance:8,readValueFromPointer:Ie,destructorFunction:function(e){j(e)}})},i:function(e,u,o){var l,i,c,s,d;o=P(o),2===u?(l=tr,i=nr,s=or,c=()=>J,d=1):4===u&&(l=ir,i=sr,s=ar,c=()=>p,d=2),C(e,{name:o,fromWireType:function(e){for(var r,t=p[e>>2],n=c(),o=e+4,i=0;i<=t;++i){var s,a=e+4+i*u;i!=t&&0!=n[a>>d]||(s=l(o,a-o),void 0===r?r=s:r=r+String.fromCharCode(0)+s,o=a+u)}return j(e),r},toWireType:function(e,r){"string"!=typeof r&&F("Cannot pass non-string to C++ string type "+o);var t=s(r),n=br(4+t+u);return p[n>>2]=t>>d,i(r,n+4,t+u),null!==e&&e.push(j,n),n},argPackAdvance:8,readValueFromPointer:Ie,destructorFunction:function(e){j(e)}})},o:function(e,r){C(e,{isVoid:!0,name:r=P(r),argPackAdvance:0,fromWireType:function(){},toWireType:function(e,r){}})},h:function(){return Date.now()},d:function(e,r,t,n){(e=lr[e])(r=Ye.toValue(r),t=void 0===(r=ur[e=t])?P(e):r,null,n)},j:Qe,e:function(e,r){var t=function(e,r){for(var t,n,o=new Array(e),i=0;i<e;++i)o[i]=(t=p[r+4*i>>2],void 0===(n=T[t])&&F("parameter "+i+" has unknown type "+qe(t)),n);return o}(e,r),r=t[0],n=r.name+"_$"+t.slice(1).map(function(e){return e.name}).join("_")+"$",o=cr[n];if(void 0!==o)return o;for(var i=["retType"],s=[r],a="",u=0;u<e-1;++u)a+=(0!==u?", ":"")+"arg"+u,i.push("argType"+u),s.push(t[1+u]);for(var l="return function "+we("methodCaller_"+n)+"(handle, name, destructors, args) {\n",c=0,u=0;u<e-1;++u)l+="    var arg"+u+" = argType"+u+".readValueFromPointer(args"+(c?"+"+c:"")+");\n",c+=t[u+1].argPackAdvance;for(l+="    var rv = handle[name]("+a+");\n",u=0;u<e-1;++u)t[u+1].deleteObject&&(l+="    argType"+u+".deleteObject(arg"+u+");\n");r.isVoid||(l+="    return retType.toWireType(destructors, rv);\n"),i.push(l+="};\n");var r=Je(Function,i).apply(null,s),d=lr.length;return lr.push(r),cr[n]=o=d},a:function(){v("")},A:function(e,r,t){f.copyWithin(e,r,r+t)},v:function(e){f.length,v("OOM")},y:function(s,a){var u=0;return fr().forEach(function(e,r){for(var t=a+u,n=(p[s+4*r>>2]=t,e),o=t,i=0;i<n.length;++i)d[o++>>0]=n.charCodeAt(i);d[o>>0]=0,u+=e.length+1}),0},z:function(e,r){var t=fr(),n=(p[e>>2]=t.length,0);return t.forEach(function(e){n+=e.length+1}),p[r>>2]=n,0},l:function(e){try{var r=S.getStreamFromFD(e);return k.close(r),0}catch(e){if(void 0!==k&&e instanceof k.ErrnoError)return e.errno;throw e}},x:function(e,r){try{var t=S.getStreamFromFD(e),n=t.tty?2:k.isDir(t.mode)?3:k.isLink(t.mode)?7:4;return d[r>>0]=n,0}catch(e){if(void 0!==k&&e instanceof k.ErrnoError)return e.errno;throw e}},B:function(e,r,t,n){try{var o=function(e,r,t){for(var n=0,o=0;o<t;o++){var i=p[r>>2],s=p[r+4>>2],i=(r+=8,k.read(e,d,i,s,void 0));if(i<0)return-1;if(n+=i,i<s)break}return n}(S.getStreamFromFD(e),r,t);return l[n>>2]=o,0}catch(e){if(void 0!==k&&e instanceof k.ErrnoError)return e.errno;throw e}},s:function(e,r,t,n,o){try{var i=t+2097152>>>0<4194305-!!r?(r>>>0)+4294967296*t:NaN;if(isNaN(i))return 61;var s=S.getStreamFromFD(e);return k.llseek(s,i,n),w=[s.position>>>0,(g=s.position,1<=+Math.abs(g)?0<g?(0|Math.min(+Math.floor(g/4294967296),4294967295))>>>0:~~+Math.ceil((g-(~~g>>>0))/4294967296)>>>0:0)],l[o>>2]=w[0],l[o+4>>2]=w[1],s.getdents&&0===i&&0===n&&(s.getdents=null),0}catch(e){if(void 0!==k&&e instanceof k.ErrnoError)return e.errno;throw e}},k:function(e,r,t,n){try{var o=function(e,r,t){for(var n=0,o=0;o<t;o++){var i=p[r>>2],s=p[r+4>>2],i=(r+=8,k.write(e,d,i,s,void 0));if(i<0)return-1;n+=i}return n}(S.getStreamFromFD(e),r,t);return p[n>>2]=o,0}catch(e){if(void 0!==k&&e instanceof k.ErrnoError)return e.errno;throw e}},g:function(e){}},j=(!function(){var r={a:yr};function t(e,r){var e=e.exports;y.asm=e,e=y.asm.F.buffer,X=e,y.HEAP8=d=new Int8Array(e),y.HEAP16=u=new Int16Array(e),y.HEAP32=l=new Int32Array(e),y.HEAPU8=f=new Uint8Array(e),y.HEAPU16=J=new Uint16Array(e),y.HEAPU32=p=new Uint32Array(e),y.HEAPF32=K=new Float32Array(e),y.HEAPF64=Z=new Float64Array(e),Q=y.asm.I,e=y.asm.G,oe.unshift(e),ae()}function n(e){t(e.instance)}function o(e){return function(){if(!i&&(N||c)){if("function"==typeof fetch&&!le(h))return fetch(h,{credentials:"same-origin"}).then(function(e){if(e.ok)return e.arrayBuffer();throw"failed to load wasm binary file at '"+h+"'"}).catch(function(){return ce(h)});if(m)return new Promise(function(r,e){m(h,function(e){r(new Uint8Array(e))},e)})}return Promise.resolve().then(function(){return ce(h)})}().then(function(e){return WebAssembly.instantiate(e,r)}).then(function(e){return e}).then(e,function(e){a("failed to asynchronously prepare wasm: "+e),v(e)})}if(se(),y.instantiateWasm)try{return y.instantiateWasm(r,t)}catch(r){return a("Module.instantiateWasm callback failed with error: "+r)}i||"function"!=typeof WebAssembly.instantiateStreaming||ue(h)||le(h)||H||"function"!=typeof fetch?o(n):fetch(h,{credentials:"same-origin"}).then(function(e){return WebAssembly.instantiateStreaming(e,r).then(n,function(e){return a("wasm streaming compile failed: "+e),a("falling back to ArrayBuffer instantiation"),o(n)})})}(),y.___wasm_call_ctors=function(){return(y.___wasm_call_ctors=y.asm.G).apply(null,arguments)},y._free=function(){return(j=y._free=y.asm.H).apply(null,arguments)}),gr=y.___errno_location=function(){return(gr=y.___errno_location=y.asm.J).apply(null,arguments)},wr=y.___getTypeName=function(){return(wr=y.___getTypeName=y.asm.K).apply(null,arguments)};y.___embind_register_native_and_builtin_types=function(){return(y.___embind_register_native_and_builtin_types=y.asm.L).apply(null,arguments)};var vr,br=y._malloc=function(){return(br=y._malloc=y.asm.M).apply(null,arguments)},Er=y._emscripten_builtin_memalign=function(){return(Er=y._emscripten_builtin_memalign=y.asm.N).apply(null,arguments)},_r=y.___cxa_is_pointer_type=function(){return(_r=y.___cxa_is_pointer_type=y.asm.O).apply(null,arguments)};function kr(){function e(){if(!vr&&(vr=!0,y.calledRun=!0,!V)){if(y.noFSInit||k.init.initialized||k.init(),k.ignorePermissions=!1,de(oe),y.onRuntimeInitialized&&y.onRuntimeInitialized(),y.postRun)for("function"==typeof y.postRun&&(y.postRun=[y.postRun]);y.postRun.length;)e=y.postRun.shift(),ie.unshift(e);var e;de(ie)}}if(!(0<t)){if(y.preRun)for("function"==typeof y.preRun&&(y.preRun=[y.preRun]);y.preRun.length;)r=y.preRun.shift(),ne.unshift(r);var r;de(ne),0<t||(y.setStatus?(y.setStatus("Running..."),setTimeout(function(){setTimeout(function(){y.setStatus("")},1),e()},1)):e())}}if(y.dynCall_viiijj=function(){return(y.dynCall_viiijj=y.asm.P).apply(null,arguments)},y.dynCall_jij=function(){return(y.dynCall_jij=y.asm.Q).apply(null,arguments)},y.dynCall_jii=function(){return(y.dynCall_jii=y.asm.R).apply(null,arguments)},y.dynCall_jiji=function(){return(y.dynCall_jiji=y.asm.S).apply(null,arguments)},n=function e(){vr||kr(),vr||(n=e)},y.preInit)for("function"==typeof y.preInit&&(y.preInit=[y.preInit]);0<y.preInit.length;)y.preInit.pop()();kr(),x.exports=y});const y="fetch",s="debug",a="warn",g={playType:"player",container:"",videoBuffer:1e3,videoBufferDelay:1e3,networkDelay:1e4,isResize:!0,isFullResize:!1,isFlv:!1,isHls:!1,isFmp4:!1,isWebrtc:!1,isWebrtcForZLM:!1,isNakedFlow:!1,isMpeg4:!1,debug:!1,debugLevel:a,debugUuid:"",isMulti:!1,hotKey:!1,loadingTimeout:10,heartTimeout:10,timeout:10,pageVisibilityHiddenTimeout:300,loadingTimeoutReplay:!0,heartTimeoutReplay:!0,loadingTimeoutReplayTimes:3,heartTimeoutReplayTimes:3,heartTimeoutReplayUseLastFrameShow:!1,replayUseLastFrameShow:!1,supportDblclickFullscreen:!1,showBandwidth:!1,showPerformance:!1,mseCorrectTimeDuration:20,keepScreenOn:!0,isNotMute:!1,hasAudio:!0,hasVideo:!0,operateBtns:{fullscreen:!1,screenshot:!1,play:!1,audio:!1,record:!1,ptz:!1,quality:!1,zoom:!1,close:!1,scale:!1,performance:!1,aiFace:!1,aiObject:!1,fullscreenFn:null,fullscreenExitFn:null,screenshotFn:null,playFn:null,pauseFn:null,recordFn:null,recordStopFn:null},extendOperateBtns:[],contextmenuBtns:[],watermarkConfig:{},controlAutoHide:!1,hasControl:!1,loadingIcon:!0,loadingText:"",background:"",backgroundLoadingShow:!1,loadingBackground:"",decoder:"decoder-pro.js",decoderAudio:"decoder-pro-audio.js",decoderWASM:"",isDecoderUseCDN:!1,url:"",rotate:0,mirrorRotate:"none",playbackConfig:{playList:[],fps:"",showControl:!0,showRateBtn:!1,rateConfig:[],isCacheBeforeDecodeForFpsRender:!1,uiUsePlaybackPause:!1,isPlaybackPauseClearCache:!0,isUseFpsRender:!1,isUseLocalCalculateTime:!1,localOneFrameTimestamp:40,supportWheel:!1,useWCS:!1},qualityConfig:[],defaultStreamQuality:"",scaleConfig:["拉伸","缩放","正常"],forceNoOffscreen:!0,hiddenAutoPause:!1,protocol:2,demuxType:"flv",useWasm:!1,useWCS:!1,useSIMD:!0,wcsUseVideoRender:!0,wasmUseVideoRender:!0,mseUseCanvasRender:!1,hlsUseCanvasRender:!1,useMSE:!1,useOffscreen:!1,useWebGPU:!1,mseDecodeErrorReplay:!0,wcsDecodeErrorReplay:!0,wasmDecodeErrorReplay:!0,autoWasm:!0,webglAlignmentErrorReplay:!0,webglContextLostErrorReplay:!0,openWebglAlignment:!1,syncAudioAndVideo:!1,playbackDelayTime:1e3,playbackFps:25,playbackForwardMaxRateDecodeIFrame:4,playbackCurrentTimeMove:!0,useVideoRender:!0,useCanvasRender:!1,networkDelayTimeoutReplay:!1,recordType:"mp4",checkFirstIFrame:!0,nakedFlowFps:25,audioEngine:null,isShowRecordingUI:!0,isShowZoomingUI:!0,useFaceDetector:!1,useObjectDetector:!1,ptzClickType:"click",ptzStopEmitDelay:.3,ptzZoomShow:!1,ptzApertureShow:!1,ptzFocusShow:!1,ptzMoreArrowShow:!1,weiXinInAndroidAudioBufferSize:4800,isCrypto:!1,isSm4Crypto:!1,sm4CryptoKey:"",cryptoKey:"",cryptoIV:"",cryptoKeyUrl:"",autoResize:!1,useWebFullScreen:!1,ptsMaxDiff:3600,aiFaceDetectWidth:192,aiObjectDetectWidth:192,videoRenderSupportScale:!0,mediaSourceTsIsMaxDiffReplay:!0,controlHtml:"",isH265:!1,isWebrtcH265:!1,supportLockScreenPlayAudio:!0,supportHls265:!0,isFmp4Private:!1},w="playAudio",v="workerFetch",b="streamEnd",E="streamSuccess",_="fetchError",k=10,S="AbortError",u=0;function P(e){return e[0]>>4===k&&e[1]===u}function A(){return(performance&&"function"==typeof performance.now?performance:Date).now()}o(function(e){var s,r,a,t,n;s="undefined"!=typeof window&&void 0!==window.document?window.document:{},r=e.exports,a=function(){for(var e,r=[["requestFullscreen","exitFullscreen","fullscreenElement","fullscreenEnabled","fullscreenchange","fullscreenerror"],["webkitRequestFullscreen","webkitExitFullscreen","webkitFullscreenElement","webkitFullscreenEnabled","webkitfullscreenchange","webkitfullscreenerror"],["webkitRequestFullScreen","webkitCancelFullScreen","webkitCurrentFullScreenElement","webkitCancelFullScreen","webkitfullscreenchange","webkitfullscreenerror"],["mozRequestFullScreen","mozCancelFullScreen","mozFullScreenElement","mozFullScreenEnabled","mozfullscreenchange","mozfullscreenerror"],["msRequestFullscreen","msExitFullscreen","msFullscreenElement","msFullscreenEnabled","MSFullscreenChange","MSFullscreenError"]],t=0,n=r.length,o={};t<n;t++)if((e=r[t])&&e[1]in s){for(t=0;t<e.length;t++)o[r[0][t]]=e[t];return o}return!1}(),t={change:a.fullscreenchange,error:a.fullscreenerror},n={request:function(o,i){return new Promise(function(e,r){var t=function(){this.off("change",t),e()}.bind(this),n=(this.on("change",t),(o=o||s.documentElement)[a.requestFullscreen](i));n instanceof Promise&&n.then(t).catch(r)}.bind(this))},exit:function(){return new Promise(function(e,r){var t,n;this.isFullscreen?(t=function(){this.off("change",t),e()}.bind(this),this.on("change",t),(n=s[a.exitFullscreen]())instanceof Promise&&n.then(t).catch(r)):e()}.bind(this))},toggle:function(e,r){return this.isFullscreen?this.exit():this.request(e,r)},onchange:function(e){this.on("change",e)},onerror:function(e){this.on("error",e)},on:function(e,r){e=t[e];e&&s.addEventListener(e,r,!1)},off:function(e,r){e=t[e];e&&s.removeEventListener(e,r,!1)},raw:a},a?(Object.defineProperties(n,{isFullscreen:{get:function(){return Boolean(s[a.fullscreenElement])}},element:{enumerable:!0,get:function(){return s[a.fullscreenElement]}},isEnabled:{enumerable:!0,get:function(){return Boolean(s[a.fullscreenEnabled])}}}),r?e.exports=n:window.screenfull=n):r?e.exports={isEnabled:!1}:window.screenfull={isEnabled:!1}}).isEnabled;try{if("object"==typeof WebAssembly&&"function"==typeof WebAssembly.instantiate){var l=new WebAssembly.Module(Uint8Array.of(0,97,115,109,1,0,0,0));if(l instanceof WebAssembly.Module)new WebAssembly.Instance(l),WebAssembly.Instance}}catch(e){}const c=Symbol(32),d=Symbol(16),f=Symbol(8);class T{constructor(e){this.g=e,this.consumed=0,e&&(this.need=e.next().value)}setG(e){this.g=e,this.demand(e.next().value,!0)}consume(){this.buffer&&this.consumed&&(this.buffer.copyWithin(0,this.consumed),this.buffer=this.buffer.subarray(0,this.buffer.length-this.consumed),this.consumed=0)}demand(e,r){return r&&this.consume(),this.need=e,this.flush()}read(t){return e=this,u=function*(){return this.lastReadPromise&&(yield this.lastReadPromise),this.lastReadPromise=new Promise((r,e)=>{this.reject=e,this.resolve=e=>{delete this.lastReadPromise,delete this.resolve,delete this.need,r(e)},this.demand(t,!0)||null==(e=this.pull)||e.call(this,t)})},new(a=(a=s=void 0)||Promise)(function(t,r){function n(e){try{i(u.next(e))}catch(e){r(e)}}function o(e){try{i(u.throw(e))}catch(e){r(e)}}function i(e){var r;e.done?t(e.value):((r=e.value)instanceof a?r:new a(function(e){e(r)})).then(n,o)}i((u=u.apply(e,s||[])).next())});var e,s,a,u}readU32(){return this.read(c)}readU16(){return this.read(d)}readU8(){return this.read(f)}close(){var e;this.g&&this.g.return(),this.buffer&&this.buffer.subarray(0,0),null!=(e=this.reject)&&e.call(this,new Error("EOF")),delete this.lastReadPromise}flush(){if(this.buffer&&this.need){let e=null;const n=this.buffer.subarray(this.consumed);let r=0;var t=e=>n.length<(r=e);if("number"==typeof this.need){if(t(this.need))return;e=n.subarray(0,r)}else if(this.need===c){if(t(4))return;e=n[0]<<24|n[1]<<16|n[2]<<8|n[3]}else if(this.need===d){if(t(2))return;e=n[0]<<8|n[1]}else if(this.need===f){if(t(1))return;e=n[0]}else if("buffer"in this.need){if("byteOffset"in this.need){if(t(this.need.byteLength-this.need.byteOffset))return;new Uint8Array(this.need.buffer,this.need.byteOffset).set(n.subarray(0,r)),e=this.need}else if(this.g)return void this.g.throw(new Error("Unsupported type"))}else{if(t(this.need.byteLength))return;new Uint8Array(this.need).set(n.subarray(0,r)),e=this.need}return this.consumed+=r,this.g?this.demand(this.g.next(e).value,!0):this.resolve&&this.resolve(e),e}}write(e){if(e instanceof Uint8Array?this.malloc(e.length).set(e):"buffer"in e?this.malloc(e.byteLength).set(new Uint8Array(e.buffer,e.byteOffset,e.byteLength)):this.malloc(e.byteLength).set(new Uint8Array(e)),!this.g&&!this.resolve)return new Promise(e=>this.pull=e);this.flush()}writeU32(e){this.malloc(4).set([e>>24&255,e>>16&255,e>>8&255,255&e]),this.flush()}writeU16(e){this.malloc(2).set([e>>8&255,255&e]),this.flush()}writeU8(e){this.malloc(1)[0]=e,this.flush()}malloc(e){if(this.buffer){var r=this.buffer.length,t=r+e;if(t<=this.buffer.buffer.byteLength-this.buffer.byteOffset)this.buffer=new Uint8Array(this.buffer.buffer,this.buffer.byteOffset,t);else{const e=new Uint8Array(t);e.set(this.buffer),this.buffer=e}return this.buffer.subarray(r,t)}return this.buffer=new Uint8Array(e),this.buffer}}T.U32=c,T.U16=d,T.U8=f;class D{constructor(i){this.log=function(e){if(i._opt.debug&&i._opt.debugLevel==s){for(var r=i._opt.debugUuid?`[${i._opt.debugUuid}]`:"",t=arguments.length,n=new Array(1<t?t-1:0),o=1;o<t;o++)n[o-1]=arguments[o];console.log(`JbPro${r}:[✅✅✅][${e}]`,...n)}},this.warn=function(e){if(i._opt.debug&&(i._opt.debugLevel==s||i._opt.debugLevel==a)){for(var r=i._opt.debugUuid?`[${i._opt.debugUuid}]`:"",t=arguments.length,n=new Array(1<t?t-1:0),o=1;o<t;o++)n[o-1]=arguments[o];console.log(`JbPro${r}:[❗❗❗][${e}]`,...n)}},this.error=function(e){for(var r=i._opt.debugUuid?`[${i._opt.debugUuid}]`:"",t=arguments.length,n=new Array(1<t?t-1:0),o=1;o<t;o++)n[o-1]=arguments[o];console.error(`JbPro${r}:[❌❌❌][${e}]`,...n)}}}function p(u){function n(){o&&(o.abort(),o=null)}let t=[],l=[],o=new AbortController,i=null,s=null,a=null,c=!1,d=[],f=0,p=0,r=null,e=()=>{var e=function(){{var t=g;let r="";if("object"==typeof t)try{r=JSON.stringify(t),r=JSON.parse(r)}catch(e){r=t}else r=t;return r}}();return{debug:e.debug,debugLevel:e.debugLevel,sampleRate:0,audioBufferSize:1024,videoBuffer:e.videoBuffer}},h={isDestroyed:!1,_opt:e(),init:function(){h.debug.log("audio worker","init and opt is",h._opt),h.stopId=setInterval(()=>{var e=(new Date).getTime(),e=e-(r=r||e);100<e&&h.debug.warn("audio worker","loop demux diff time is "+e);{let e=null;if(t.length)if(e=t[0],-1===h.getDelay(e.ts))t.shift(),h.doDecode(e);else for(;t.length;){if(e=t[0],!(h.getDelay(e.ts)>h._opt.videoBuffer)){h.delay<0&&h.debug.warn("audio worker",`loop() do not decode and delay is ${h.delay}, bufferList is `+t.length);break}t.shift(),h.doDecode(e)}else-1!==h.delay&&h.debug.log("audio worker","loop() bufferList is empty and reset delay"),h.resetAllDelay()}r=(new Date).getTime()},10)},doDecode:function(e){e.decoder.decode(e.payload,e.ts,e.isIFrame,e.cts)},getDelay:function(e){return e?(h.preDelayTimestamp&&h.preDelayTimestamp>e?1e3<h.preDelayTimestamp-e&&h.debug.warn("audio worker",`getDelay() and preDelayTimestamp is ${h.preDelayTimestamp} > timestamp is ${e} more than ${h.preDelayTimestamp-e}ms`):h.firstTimestamp?e&&(r=Date.now()-h.startTimestamp,t=e-h.firstTimestamp,h.delay=t<=r?r-t:t-r):(h.firstTimestamp=e,h.startTimestamp=Date.now(),h.delay=-1),h.preDelayTimestamp=e,h.delay):-1;var r,t},resetAllDelay:function(){h.firstTimestamp=null,h.startTimestamp=null,h.delay=-1,h.preDelayTimestamp=null},close:function(){h.debug.log("audio worker","close"),h.isDestroyed=!0,h.stopId&&(clearInterval(h.stopId),h.stopId=null),m&&(m.clear&&m.clear(),m=null),s=null,h._opt=e(),t=[],l=[],n(),a=null,i&&(i.close(1e3,"Client disconnecting"),i=null),h.resetAllDelay(),c=!1,d=[],f=0,p=0,postMessage({cmd:"closeEnd"})},fetchStream:function(e,r){h.debug.log("audio worker","fetchStream, url is "+e,"options:",JSON.stringify(r)),s=function(t){let n=0,o=A();return e=>{var r;"[object Number]"===Object.prototype.toString.call(e)&&(n+=e,1e3<=(r=(e=A())-o)&&(t(n/r*1e3),o=e,n=0))}}(e=>{postMessage({cmd:v,type:"streamRate",value:e})}),2===r.protocol?(a=new T(h.demuxFlv()),fetch(e,{signal:o.signal}).then(e=>{if(!(e.ok&&200<=e.status&&e.status<=299))return h.debug.warn("audio worker",`fetch response status is ${e.status} and ok is ${e.ok} and emit error and next abort()`),n(),void postMessage({cmd:v,type:_,value:`fetch response status is ${e.status} and ok is `+e.ok});if(postMessage({cmd:v,type:E}),"undefined"!=typeof WritableStream)e.body.pipeTo(new WritableStream({write:e=>{s(e.byteLength),a.write(e)},close:()=>{a=null,n(),postMessage({cmd:v,type:b,value:y})},abort:e=>{a=null,e.name!==S&&(n(),postMessage({cmd:v,type:_,value:e.toString()}))}}));else{const r=e.body.getReader(),t=()=>{r.read().then(e=>{var{done:e,value:r}=e;if(e)return a=null,n(),void postMessage({cmd:v,type:b,value:y});s(r.byteLength),a.write(r),t()}).catch(e=>{a=null,e.name!==S&&(n(),postMessage({cmd:v,type:_,value:e.toString()}))})};t()}}).catch(e=>{e.name!==S&&(n(),postMessage({cmd:v,type:_,value:e.toString()}),a=null)})):1===r.protocol&&(r.isFlv&&(a=new T(h.demuxFlv())),(i=new WebSocket(e)).binaryType="arraybuffer",i.onopen=()=>{h.debug.log("audio worker","fetchStream, WebsocketStream  socket open"),postMessage({cmd:v,type:E}),postMessage({cmd:v,type:"websocketOpen"})},i.onclose=e=>{h.debug.log("audio worker","fetchStream, WebsocketStream socket close and code is "+e.code),1006===e.code&&h.debug.warn("audio worker","fetchStream, WebsocketStream socket close abnormally and code is "+e.code),a=null,postMessage({cmd:v,type:b,value:"websocket"})},i.onerror=e=>{h.debug.error("audio worker","fetchStream, WebsocketStream socket error",e),a=null,postMessage({cmd:v,type:"websocketError",value:e.isTrusted?"websocket user aborted":"websocket error"})},i.onmessage=e=>{s(e.data.byteLength),r.isFlv?a.write(e.data):h.demuxM7s(e.data)})},demuxFlv:function*(){yield 9;const e=new ArrayBuffer(4),r=new Uint8Array(e),t=new Uint32Array(e);for(;;){r[3]=0;const e=yield 15,i=e[4];r[0]=e[7],r[1]=e[6],r[2]=e[5];var n=t[0],o=(r[0]=e[10],r[1]=e[9],r[2]=e[8],r[3]=e[11],t[0]),n=(yield n).slice();8===i&&h.decode(n,{type:1,ts:o})}},decode:function(e,r){postMessage({cmd:v,type:"streamAbps",value:e.byteLength}),h.pushBuffer(e,r.ts)},setCodecAudio:function(e){var r=e[0]>>4;if(P(e)||7==r||8==r){h.debug.log("audio worker","setCodecAudio: init audio codec, codeId is "+r);e=r===k?e.slice(2):e.slice(1);m.setCodec(r,h._opt.sampleRate,e),c=!0;{const e=Number("1")||1;setTimeout(()=>{h.close(),postMessage({cmd:"workerEnd"})},60*e*60*1e3)}}else h.debug.warn("audio worker","setCodecAudio: hasInitAudioCodec is false, codecId is ",r)},pushBuffer:function(e,r){P(e)?h.decodeAudio(e,r):t.push({ts:r,payload:e,decoder:{decode:h.decodeAudio},isIFrame:!1})},decodeAudio:function(e,r){var t=e[0]>>4;c?m.decode(t===k?e.slice(2):e.slice(1),r):h.setCodecAudio(e)},demuxM7s:function(e){const r=new DataView(e),t=r.getUint32(1,!1);1===r.getUint8(0)&&h.decode(new Uint8Array(e,5),{type:1,ts:t})},audioInfo:function(e,r,t){postMessage({cmd:"audioCode",code:e}),postMessage({cmd:"initAudio",sampleRate:r,channels:t}),p=t},pcmData:function(o,i,s){if(h.isDestroyed)h.debug.log("audio worker","pcmData, decoder is destroyed and return");else{let r=i,t=[],e=0,n=h._opt.audioBufferSize;for(let e=0;e<2;e++){var a=u.HEAPU32[(o>>2)+e]>>2;t[e]=u.HEAPF32.subarray(a,a+r)}if(f){if(!(r>=(i=n-f)))return f+=r,l[0]=Float32Array.of(...l[0],...t[0]),void(2==p&&(l[1]=Float32Array.of(...l[1],...t[1])));d[0]=Float32Array.of(...l[0],...t[0].subarray(0,i)),2==p&&(d[1]=Float32Array.of(...l[1],...t[1].subarray(0,i))),postMessage({cmd:w,buffer:d,delay:h.delay,ts:s},d.map(e=>e.buffer)),e=i,r-=i}for(f=r;f>=n;f-=n)d[0]=t[0].slice(e,e+=n),2==p&&(d[1]=t[1].slice(e-n,e)),postMessage({cmd:w,buffer:d,delay:h.delay,ts:s},d.map(e=>e.buffer));f&&(l[0]=t[0].slice(e),2==p&&(l[1]=t[1].slice(e)))}},sendWebsocketMessage:function(e){i?1===i.readyState?i.send(e):h.debug.error("audio worker","socket is not open"):h.debug.error("audio worker","socket is null")},timeEnd:function(){}},m=(h.debug=new D(h),null);u.AudioDecoder&&(m=new u.AudioDecoder(h)),postMessage({cmd:"init"}),self.onmessage=function(e){var r=e.data;switch(r.cmd){case"init":try{h._opt=Object.assign(h._opt,JSON.parse(r.opt))}catch(e){}h.init();break;case"fetchStream":h.fetchStream(r.url,JSON.parse(r.opt));break;case"close":h.close();break;case"updateConfig":h.debug.log("audio worker","updateConfig",r.key,r.value),h._opt[r.key]=r.value;break;case"sendWsMessage":h.sendWebsocketMessage(r.message)}}}Date.now||(Date.now=function(){return(new Date).getTime()}),i.postRun=function(){p(i)}});
