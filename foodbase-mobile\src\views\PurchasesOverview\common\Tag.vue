<template>
  <span class="tag" :style="style">
    <slot></slot>
  </span>
</template>

<script setup>
import { computed } from 'vue';

const props = defineProps({
  colorType: [Number, String], // 1,2,3,4,5
});
const colors = ['', '#1492FF', '#39AA02', '#FB9A00', '#FB9A00', '#FB9A00'];
const backgroundColors = ['', '#E6F7FF', '#E0F3CD', '#FFE8C3', '#FFE8C3', '#FFE8C3'];

const style = computed(() => {
  return {
    color: colors[props.colorType || 1],
    backgroundColor: backgroundColors[props.colorType || 1],
  };
});
</script>

<style scoped>
.tag {
  display: inline-block;
  line-height: 36px;
  border-radius: 4px;
  padding: 0 8px;
  font-weight: bold;
  margin-left: auto;
}
</style>
