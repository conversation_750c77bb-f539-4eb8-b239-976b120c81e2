import { getAreaReport, getStoreReport } from '@/api/supervision';

const quantityResultNames = {
  1: '达标',
  2: '不达标',
  3: '未开通',
};

const qualityResultNames = {
  1: '合格',
  2: '不合格',
  3: '未开通',
};

const securityResultNames = {
  1: '正常',
  2: '异常',
  3: '未开通',
};

export default {
  namespaced: true,
  state: {
    areaReport: {}, // 地区报告
    storeReport: {}, // 库点报告
    videoDetailMap: {}, // 按设备id索引监控摄像头详情
  },
  getters: {
    getVideoDetail(state) {
      return (id) => state.videoDetailMap[id];
    },
  },
  mutations: {
    addVideoDetail(state, record) {
      state.videoDetailMap[record.rmDeviceCameraId] = record;
    },
    setAreaReport(state, value) {
      state.areaReport = value;
    },
    setStoreReport(state, value) {
      state.storeReport = value;
    },
  },
  actions: {
    async fetchAreaReport({ commit }, areaCode) {
      const data = await getAreaReport(areaCode);
      if (!data) {
        return;
      }
      let areaName = data.area || data.city || data.province;
      if (data.cityCode && data.areaCode && +data.cityCode + 1 === +data.areaCode) {
        // 市本级
        areaName = data.city + data.area;
      } else if (data.cityCode === '330001') {
        // 省本级
        areaName = data.province + data.city;
      }
      const resultName = data.result === 1 ? '正常' : '异常';
      const normalStorePercent = data.storeNumber
        ? (data.normalStoreNumber / data.storeNumber) * 100
        : 100;
      const normalStorePercentText = normalStorePercent
        ? normalStorePercent.toFixed(normalStorePercent >= 100 ? 0 : 2)
        : '0';
      const normalStorehousePercent = data.storehouseNumber
        ? (data.normalStorehouseNumber / data.storehouseNumber) * 100
        : 100;
      const normalStorehousePercentText = normalStorehousePercent
        ? normalStorehousePercent.toFixed(normalStorehousePercent >= 100 ? 0 : 2)
        : '0';

      const report = {
        areaCode: areaCode,
        areaName,
        summary: {
          result: data.result,
          resultName: resultName,
          time: data.time,
          normalStorePercent: normalStorePercentText,
          scopeDesc: `本次对${areaName}所辖库点进行清查，清查的库点共${data.storeNumber}个。`,
          resultDesc:
            `清查库点${data.storeNumber}个，` +
            `正常库点${data.normalStoreNumber}个，` +
            `异常库点${data.abnormalStoreNumber}个，` +
            `正常库点占比${normalStorePercentText}%，` +
            `清查仓房${data.storehouseNumber}个，` +
            `正常仓房${data.normalStorehouseNumber}个，` +
            `异常仓房${data.abnormalStorehouseNumber}个，` +
            `正常仓房占比${normalStorehousePercentText}%，` +
            `清查结果${resultName}。`,
        },
        quantity: {
          result: data.quantityResult,
          resultName: quantityResultNames[data.quantityResult],
          reserveSum: data.reservePlan,
          expectStockSum: data.reserveStock,
          rollInSum: data.rollIn,
          rollOutSum: data.rollOut,
          actualStockSum: data.checkStock,
          stockReach: data.checkStock / data.reservePlan >= 0.7,
        },
        quality: {
          result: data.qualityResult,
          resultName: qualityResultNames[data.qualityResult],
          qualityRate: data.dbRate * 100, // 质量达标率
          unqualifiedSum: data.bdbStock, // 质量不达标数量
          suitableStorageRate: data.ycRate * 100, // 品质宜存率
          littleUnsuitableStorageSum: data.ydbycStock, // 轻度不宜存数量
          heavyUnsuitableStorageSum: data.zdbycStock, // 重度不宜存数量
          foodSafeRate: data.sahgRate * 100, // 食安合格率
          foodUnsafeSum: data.sabhgStock, // 食安不合格数量
        },
        security: {
          result: data.storageResult,
          resultName: securityResultNames[data.storageResult],
          storeSum: data.storageStoreNumber,
          normalStoreSum: data.storageNormalStoreNumber,
          storehouseSum: data.storageStorehouseNumber,
          normalStorehouseSum: data.storageNormalStorehouseNumber,
          normalStorePercent:
            (data.storageNormalStoreNumber / data.storageStoreNumber) * 100 || 100,
        },
        infrastructure: {
          resultName: '完备',
          storehouseSum: data.storehouseNumber,
          designedStorehouseCapacity: data.designStorehouseCapacity,
          tankSum: data.oilTankNumber,
          designedTankCapacity: data.oilTankStock,
          railwaySum: data.railwayNumber,
          dockSum: data.wharfNumber,
          dryingCenterSum: data.dryNumber,
        },
        subArea: data.subAreaDailyList.map((it) => {
          return {
            isStore: it.type === 2,
            storeId: it.storeId,
            areaCode: it.areaCode || it.cityCode || it.provinceCode,
            areaName: it.name,
            access:
              it.quantityResult !== 3 ||
              it.qualityResult !== 3 ||
              it.storageResult !== 3 ||
              it.facilitiesResult !== 3,
            quantityCheck: it.quantityResult,
            qualityCheck: it.qualityResult,
            securityCheck: it.storageResult,
            infrastructureCheck: it.facilitiesResult,
          };
        }),
      };
      commit('setAreaReport', report);
    },
    async fetchStoreReport({ commit }, storeId) {
      const data = await getStoreReport(storeId);
      if (!data) {
        return;
      }
      const storeName = data.storeName;
      const resultName = data.result === 1 ? '正常' : '异常';
      // const normalStorePercent = data.storeNumber
      //   ? (data.normalStoreNumber / data.storeNumber) * 100
      //   : 100;
      // const normalStorePercentText = normalStorePercent.toFixed(normalStorePercent >= 100 ? 0 : 2);
      // const normalStorehousePercent = data.storehouseNumber
      //   ? (data.normalStorehouseNumber / data.storehouseNumber) * 100
      //   : 100;
      // const normalStorehousePercentText = normalStorehousePercent.toFixed(
      //   normalStorehousePercent >= 100 ? 0 : 2,
      // );

      const report = {
        storeId: storeId,
        storeName,
        summary: {
          result: data.result,
          resultName: resultName,
          time: data.time,
          scopeDesc: `本次对${storeName}进行清查，清查的仓房共${data.storeHouseNumber}个。`,
          resultDesc: '如下',
        },
        quantity: {
          result: data.quantityResult,
          resultName: quantityResultNames[data.quantityResult],
          storeName: data.storeName,
          stockSum: data.reserveStock, // 库存数量
          rollInSum: data.rollIn,
          rollOutSum: data.rollOut,
          actualStockSum: data.checkStock, // 清查数量
          stockReach: data.checkStock / data.reserveStock >= 0.7, // 数量达标
          abnormalSum: Math.abs(data.reserveStock - data.checkStock) || 0, // 异常数量
          abnormalStorehouse: data.quantityAbnormalStorehouse,
          abnormalStorehouseSum: data.quantityAbnormalStorehouseNumber,
          warnSum: data.quantityWarnNumber,
        },
        quality: {
          result: data.qualityResult,
          resultName: qualityResultNames[data.qualityResult],
          qualityRate: data.dbRate * 100, // 质量达标率
          unqualifiedSum: data.bdbStock, // 质量不达标数量
          suitableStorageRate: data.ycRate * 100, // 品质宜存率
          littleUnsuitableStorageSum: data.ydbycStock, // 轻度不宜存数量
          heavyUnsuitableStorageSum: data.zdbycStock, // 重度不宜存数量
          foodSafeRate: data.sahgRate * 100, // 食安合格率
          foodUnsafeSum: data.sabhgStock, // 食安不合格数量
        },
        security: {
          result: data.storageResult,
          resultName: securityResultNames[data.storageResult],
          storeName: storeName,
          point: data.storagePoint,
          abnormalStorehouse: data.storageAbnormalStorehouse,
          abnormalStorehouseSum: data.storageAbnormalStorehouseNumber,
          warnSum: data.storageWarnNumber,
        },
        infrastructure: {
          resultName: '完备',
          storehouseSum: data.storeHouseNumber,
          designedStorehouseCapacity: data.designStorehouseCapacity,
          tankSum: data.oilTankNumber,
          designedTankCapacity: data.oilTankStock,
          railwaySum: data.railwayNumber,
          dockSum: data.wharfNumber,
          dryingCenterSum: data.dryNumber,
        },
        storehouses: data.storehouseDailyList.map((it) => {
          return {
            storehouseName: it.storeHouseName,
            access: it.quantityResult !== 3 || it.qualityResult !== 3 || it.storageResult !== 3,
            stockSum: it.stock,
            quantityCheck: it.quantityResult,
            qualityCheck: it.qualityResult,
            securityCheck: it.storageResult,
            warnDetail: it.warnDetail,
          };
        }),
      };
      commit('setStoreReport', report);
    },
  },
};
