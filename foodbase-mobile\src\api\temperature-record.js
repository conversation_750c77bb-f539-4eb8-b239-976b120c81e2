import { reserveRequest } from '@/utils/request';
//查询控温记录列表
export async function getRecordList(params) {
  return reserveRequest().get('/flow-batch-store-convert/infoAccountsBatchStore', { params });
}

//查询通风记录记录列表
export async function getVentilateRecordList(params) {
  return reserveRequest().get('/flow-batch-store-convert/infoAccountsBatchStore', { params });
}
//查询气调日志记录列表
export async function getLogsRecordList(params) {
  return reserveRequest().get('/flow-batch-store-convert/infoAccountsBatchStore', { params });
}
