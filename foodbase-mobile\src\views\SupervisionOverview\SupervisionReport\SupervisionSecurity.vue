<template>
  <div class="supervision-security">
    <div class="chart">
      <HEchart :options="options" />
    </div>
    <div class="value-items">
      <div class="item">
        <div class="name">全部库点</div>
        <div class="value">{{ props.data.storeSum }}</div>
        <div class="unit">个</div>
      </div>
      <div class="item">
        <div class="name">正常库点</div>
        <div class="value">{{ props.data.normalStoreSum }}</div>
        <div class="unit">个</div>
      </div>
      <div class="item">
        <div class="name">全部仓房</div>
        <div class="value">{{ props.data.storehouseSum }}</div>
        <div class="unit">个</div>
      </div>
      <div class="item">
        <div class="name">正常仓房</div>
        <div class="value">{{ props.data.normalStorehouseSum }}</div>
        <div class="unit">个</div>
      </div>
    </div>
  </div>
</template>

<script setup>
import HEchart from '@/components/HEchart/HEchart';
import { computed } from 'vue';

const props = defineProps({
  data: {
    type: Object,
    default: () => ({}),
  },
});

const createGaugeOptions = (percent) => {
  return {
    series: [
      {
        tooltip: {
          show: false,
        },
        type: 'gauge',
        radius: '180%',
        center: ['50%', '95%'],
        startAngle: 180,
        endAngle: 0,
        detail: {
          show: false,
        },
        axisLine: {
          lineStyle: {
            width: 5,
            color: [
              [0.25, '#91C7AD'],
              [0.75, '#FFAC38'],
              [1, '#6AA84F'],
            ],
          },
        },
        axisLabel: {
          show: false,
        },
        axisTick: {
          show: false,
        },
        splitLine: {
          show: false,
        },
      },
      {
        type: 'gauge',
        radius: '170%',
        min: 0,
        max: 100,
        center: ['50%', '95%'],
        data: [
          {
            value: percent,
            name: '正常库点占比',
          },
        ],
        splitNumber: 4,
        startAngle: 180,
        endAngle: 0,
        title: {
          offsetCenter: [0, -8],
          fontSize: 18,
          color: '#686B73',
        },
        detail: {
          formatter: function (value) {
            return value.toFixed(0) + '%';
          },
          fontSize: 24,
          color: 'auto',
          fontWeight: 'bolder',
          offsetCenter: [0, -36],
        },
        axisLine: {
          lineStyle: {
            width: 0,
            color: [
              [0.25, '#91C7AD'],
              [0.75, '#FFAC38'],
              [1, '#6AA84F'],
            ],
          },
        },
        axisLabel: {
          color: 'auto',
          fontSize: 16,
          distance: 8,
        },
        axisTick: {
          splitNumber: 50,
          show: true,
          lineStyle: {
            color: 'auto',
            width: 1,
          },
          length: 6,
        },
        splitLine: {
          show: true,
          length: 12,
          lineStyle: {
            color: 'auto',
            width: 2,
          },
        },
        itemStyle: {
          color: 'auto',
        },
        pointer: {
          width: 5,
          length: '80%',
          itemStyle: {
            color: {
              type: 'linear',
              x: 0,
              y: 1,
              x2: 0,
              y2: 0,
              colorStops: [
                {
                  offset: 0,
                  color: 'transparent', // 0% 处的颜色
                },
                {
                  offset: 0.5,
                  color: 'transparent', // 0% 处的颜色
                },
                {
                  offset: 0.8,
                  color: '#6AA84F', // 0% 处的颜色
                },
                {
                  offset: 1,
                  color: '#6AA84F', // 100% 处的颜色
                },
              ],
            },
          },
        },
      },
    ],
  };
};

const options = computed(() => {
  return createGaugeOptions(props.data.normalStorePercent);
});
</script>

<style scoped lang="scss">
.supervision-security {
  display: flex;
  align-items: center;
  flex-direction: column;
}
.chart {
  width: 300px;
  height: 160px;
  margin: 10px 0;

  .h-echarts {
    height: 100%;
    width: 100%;
  }
}
.value-items {
  .item {
    display: flex;
    line-height: 32px;
    margin: 4px 0;

    .name {
      font-size: 18px;
      font-weight: bold;
      color: var(--van-gray-7);
      width: 9em;
    }
    .value {
      font-size: 24px;
      font-weight: bold;
      color: #1492ff;
      width: 2.5em;
    }
    .unit {
      font-size: 16px;
      font-weight: 500;
      color: var(--van-gray-8);
    }
  }
}
</style>
