import request from '@/utils/request';
import { reserveRequest } from '@/utils/request';
import qs from 'qs';
export async function getPurchasesProgressByCategory() {
  return request().get('/dim-order-progress/getCategoryProgressSum');
}

export async function getPurchasesProgressTotal({ areaCode, foodCategory, year }) {
  return request().get('/dim-order-progress/getProgressSumOfCompany', {
    params: {
      code: areaCode,
      varietyType: foodCategory,
      year,
    },
  });
}

export async function getPurchasesProgressDetail(data) {
  return reserveRequest().get(
    '/api/flowPurchasePlan/infoZZD?' + qs.stringify(data, { arrayFormat: 'repeat' }),
  );
}
export async function getPurchasesProgress(params) {
  return reserveRequest().get('/api/flowPurchasePlan/infoRateZZD', {
    params,
  });
}
