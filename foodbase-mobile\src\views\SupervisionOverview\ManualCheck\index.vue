<template>
  <div class="manual-check">
    <CellGroup>
      <SelectField
        v-model="checkForm.task"
        only-value
        required
        placeholder="请选择"
        label="清查任务"
        :options="taskOptions"
      />
      <SelectField
        v-model="checkForm.type"
        only-value
        required
        placeholder="请选择"
        label="检查分类"
        :disabled="!typeOptions.length"
        :options="typeOptions"
      />
      <AreaField
        v-model="checkForm.areaCode"
        all-area-select
        :top-area-code="userAreaCode"
        required
        placeholder="请选择"
        label="库点地区"
      />
      <SelectField
        v-model="checkForm.storeId"
        only-value
        required
        placeholder="请选择"
        label="库点名称"
        :disabled="!checkForm.areaCode"
        :options="storeOptions"
      />
      <SelectField
        v-model="checkForm.storehouseId"
        only-value
        required
        placeholder="请选择"
        label="仓房"
        v-if="hasStorehousePicker"
        :disabled="!checkForm.storeId"
        :options="storehouseOptions"
      />
      <DatetimeField v-model="checkForm.time" required placeholder="请选择" label="清查日期" />
      <Cell>
        <div class="record-info">
          <div class="record-info-label">记录人：</div>
          <div class="record-info-value">{{ checkForm.recordUser }}</div>
        </div>
      </Cell>
    </CellGroup>
    <div class="filter">
      <div class="filter-title">检查项</div>
      <div class="filter-action">
        <Checkbox v-model="showAbnormal">只看异常</Checkbox>
      </div>
    </div>
    <Collapse v-model="activeCheckGroup">
      <CollapseItem
        v-for="checkGroup in checkForm.checkItems"
        :key="checkGroup.title"
        :name="checkGroup.title"
      >
        <template #title>
          <span class="check-group-title">{{ checkGroup.title }}</span>
          <span class="check-group-items-count"
            >(共{{
              checkGroup.child.filter((item) =>
                showAbnormal ? item.defaultValue !== item.flag : true,
              ).length
            }}项)</span
          >
        </template>
        <div class="check-item-list">
          <div
            class="check-item"
            v-for="(item, index) in checkGroup.child"
            :key="item.id"
            v-show="showAbnormal ? item.defaultValue !== item.flag : true"
          >
            <div class="check-item-title">{{ index + 1 }}. {{ item.detail }}</div>
            <RadioGroup v-model="item.flag" direction="horizontal">
              <Radio :name="1">是</Radio>
              <Radio :name="0">否</Radio>
            </RadioGroup>
          </div>
        </div>
      </CollapseItem>
    </Collapse>
    <div class="actions">
      <Button
        round
        block
        type="primary"
        @click="onSubmit"
        :loading="submitting"
        :disabled="!canSubmit"
      >
        提 交
      </Button>
    </div>
  </div>
</template>

<script setup>
import {
  Button,
  Collapse,
  CollapseItem,
  CellGroup,
  Cell,
  RadioGroup,
  Radio,
  Checkbox,
  Toast,
} from 'vant';
import { useToggle } from '@vant/use';
import { computed, onMounted, reactive, ref, watch } from 'vue';
import { useStore } from 'vuex';
import SelectField from '@/views/SupervisionOverview/ManualCheck/SelectField';
import AreaField from '@/views/SupervisionOverview/ManualCheck/AreaField';
import { getStoreByCityCompany } from '@/api/store';
import {
  getCheckQuestions,
  getCheckTasks,
  getCheckTypes,
  saveCheckQuestions,
} from '@/api/manual-check';
import { getStorehouses } from '@/api/storehouse';
import { useRouter } from 'vue-router';
import DatetimeField from '@/views/SupervisionOverview/ManualCheck/DatetimeField';
import dayjs from 'dayjs';

const store = useStore();
const router = useRouter();

const taskOptions = ref([]);
const typeOptions = ref([]);
const storeOptions = ref([]);
const storehouseOptions = ref([]);

const userAreaCode = store.getters['user/userAreaCode'];

const checkForm = reactive({
  task: null,
  type: null,
  areaCode: userAreaCode,
  storeId: null,
  storehouseId: null,
  time: dayjs().startOf('day').toDate(),
  recordUser: store.state.user.info?.nickName,
  checkItems: [],
});

const showAbnormal = ref(false);

const [submitting, toggleSubmitting] = useToggle(false);

const activeCheckGroup = ref([]);

const hasStorehousePicker = computed(() => {
  const isHouseTypeIds = typeOptions.value
    .filter((it) => it.text.indexOf('仓内') !== -1)
    .map((it) => it.value);
  return isHouseTypeIds.includes(checkForm.type);
});

const canSubmit = computed(() => {
  return (
    checkForm.task &&
    checkForm.type &&
    checkForm.areaCode &&
    checkForm.storeId &&
    (!hasStorehousePicker.value || checkForm.storehouseId)
  );
});

const loadCheckTasks = async () => {
  const data = await getCheckTasks();
  taskOptions.value = data.map((it) => {
    return {
      text: it.questName,
      value: it.id,
    };
  });

  checkForm.task = taskOptions.value[0]?.value;
};

const loadCheckTypes = async () => {
  const taskId = checkForm.task;
  if (!taskId) {
    return;
  }
  const data = await getCheckTypes(taskId);
  typeOptions.value = data.map((it) => {
    return {
      text: it.questName,
      value: it.id,
    };
  });
  checkForm.type = typeOptions.value[0]?.value;
};

const loadStorehouses = async () => {
  const storeId = checkForm.storeId;
  if (!storeId) {
    return;
  }
  const data = await getStorehouses(storeId);
  storehouseOptions.value = data.map((it) => {
    return {
      text: it.storehouseName,
      value: it.storehouseId,
    };
  });
};

const loadCheckQuestions = async () => {
  checkForm.checkItems = [];
  const typeId = checkForm.type;
  if (!typeId) {
    return;
  }
  const data = (await getCheckQuestions(typeId)) || [];
  data.forEach((it) => {
    it.child?.forEach((ch) => {
      ch.defaultValue = ch.flag;
    });
  });
  checkForm.checkItems = data;
};

onMounted(() => {
  loadCheckTasks();
  loadStores();
});

const onSubmit = async () => {
  if (submitting.value) {
    return;
  }
  try {
    toggleSubmitting(true);
    const checkList = [];
    checkForm.checkItems.forEach((item) => {
      item.child?.forEach((ch) => {
        checkList.push({
          detail: ch.detail,
          flag: ch.flag,
          id: ch.id,
          questId: ch.questId,
          questionId: ch.questionId,
          typeId: ch.typeId,
        });
      });
    });
    const data = {
      questId: checkForm.task,
      typeId: checkForm.type,
      storeId: checkForm.storeId,
      storeHouseId: hasStorehousePicker.value ? checkForm.storehouseId : null,
      recordTime: checkForm.time,
      recordUser: checkForm.recordUser,
      checkList: checkList,
    };
    await saveCheckQuestions(data);
    Toast.success({
      message: '提交成功',
      forbidClick: true,
      overlay: true,
      duration: 2000,
      onClose: () => {
        router.back();
      },
    });
  } catch (e) {
    console.error(e);
    Toast.fail('提交失败');
    toggleSubmitting(false);
  }
};

const loadStores = async () => {
  storeOptions.value = [];
  const areaCode = checkForm.areaCode;
  const { dept } = store.state.user.info;
  const list = (await getStoreByCityCompany(areaCode, dept.id, dept.level)) || [];
  storeOptions.value = list.map((it) => {
    return {
      value: it.id,
      text: it.name,
    };
  });
};

watch(
  () => checkForm.areaCode,
  () => {
    checkForm.storeId = null;
    loadStores();
  },
);

watch(
  () => checkForm.task,
  () => {
    loadCheckTypes();
  },
);

watch(
  () => checkForm.type,
  () => {
    loadCheckQuestions();
  },
);

watch(
  () => checkForm.storeId,
  () => {
    if (hasStorehousePicker.value) {
      loadStorehouses();
    }
  },
);

watch(
  () => hasStorehousePicker.value,
  (value) => {
    if (value) {
      loadStorehouses();
    } else {
      checkForm.storehouseId = null;
    }
  },
);
</script>

<style scoped lang="scss">
.manual-check {
  --van-cell-font-size: 20px;
  --van-cell-line-height: 40px;
  --van-cell-group-title-font-size: 20px;
  --van-button-normal-font-size: 20px;
  --van-collapse-item-content-padding: 0 var(--van-padding-md);

  ::v-deep(.van-collapse) {
    .van-collapse-item + .van-collapse-item {
      margin-top: 20px;
    }

    .van-cell__title {
      font-weight: 500;
    }
  }
}
.record-info {
  display: flex;
  font-size: 16px;
  font-weight: 500;
  color: #686b73;
  line-height: 22px;
  margin: 10px 0;
}
.filter {
  display: flex;
  margin: 16px;
  --van-checkbox-size: 22px;

  &-title {
    font-size: 18px;
    font-weight: 500;
    color: #363a44;
  }

  &-action {
    font-size: 18px;
    margin-left: auto;
  }

  ::v-deep(.van-checkbox) {
    .van-checkbox__icon--checked + .van-checkbox__label {
      color: #1989fa;
    }
  }
}
.check-group-items-count {
  margin-left: 8px;
}
.check-item-list {
  .check-item {
    position: relative;
    padding: 16px 0;

    &:not(:last-child)::after {
      position: absolute;
      box-sizing: border-box;
      content: ' ';
      pointer-events: none;
      top: -50%;
      right: -50%;
      bottom: -50%;
      left: -50%;
      border-bottom: 1px solid var(--van-border-color);
      transform: scale(0.5);
    }

    &-title {
      font-size: 18px;
      color: #363a44;
      line-height: 25px;
    }
    ::v-deep(.van-radio-group) {
      --van-radio-size: 20px;
      font-size: 20px;
      margin-top: 16px;
      .van-radio {
        flex: 1;

        &__label {
          line-height: 28px;
        }
      }
    }
  }
}
.actions {
  margin-top: 24px;
  padding: 16px 16px 48px 16px;
  background-color: #fff;
}
</style>
