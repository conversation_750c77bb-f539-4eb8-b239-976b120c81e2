<template>
  <Field
    :model-value="fieldValue"
    is-link
    readonly
    input-align="right"
    :disabled="disabled"
    v-bind="$attrs"
    @click="onClick"
  />
  <Popup v-model:show="showPicker" round position="bottom">
    <Picker :columns="columns" @confirm="onConfirm" @cancel="onCancel" />
  </Popup>
</template>

<script>
export default {
  name: 'SelectField',
  inheritAttrs: false,
};
</script>

<script setup>
import { Field, Popup, Picker } from 'vant';
import { computed, ref } from 'vue';

const props = defineProps({
  modelValue: [Object, String, Number],
  onlyValue: {
    type: Boolean,
    default: false,
  },
  disabled: {
    type: Boolean,
    default: false,
  },
  options: Array,
});

const emits = defineEmits(['update:modelValue']);

const showPicker = ref(false);

const fieldValue = computed(() => {
  const { modelValue } = props;
  if (!modelValue) {
    return null;
  }
  if (typeof modelValue === 'object') {
    return modelValue.text;
  } else if (typeof modelValue === 'string' || typeof modelValue === 'number') {
    return props.options.find((it) => it.value === modelValue)?.text;
  }
  return null;
});

const columns = computed(() => {
  return props.options;
});

const onClick = () => {
  if (props.disabled) {
    return;
  }
  showPicker.value = true;
};

const onConfirm = (item) => {
  let modelValue = item;
  if (props.onlyValue) {
    modelValue = item.value;
  }
  emits('update:modelValue', modelValue);
  showPicker.value = false;
};

const onCancel = () => {
  showPicker.value = false;
};
</script>
