<template>
  <van-picker
    :columns="originData"
    :columns-field-names="fieldNames"
    @cancel="onCancel"
    @confirm="onConfirm"
  ></van-picker>
</template>

<script>
import { Picker } from 'vant';
import { mapState } from 'vuex';
import { getStoreHouseOfUser } from '@/api/in-out-manage';

export default {
  name: 'AllHouseSelect',
  inheritAttrs: false,
  emits: ['confirm', 'cancel'],
  components: {
    'van-picker': Picker,
  },
  created() {
    this.getStoreHouseList();
  },
  data() {
    return {
      originData: [],
      fieldNames: {
        text: 'name',
        value: 'id',
      },
    };
  },
  computed: {
    ...mapState({
      deptId: (state) => state.user?.reserverIds?.deptId,
    }),
  },
  methods: {
    async getStoreHouseList() {
      this.originData = [];
      this.originData = await getStoreHouseOfUser(this.deptId);
    },
    onCancel() {
      this.$emit('cancel');
    },
    onConfirm(value) {
      this.$emit('confirm', value);
    },
  },
};
</script>
