<template>
  <van-picker
    :columns="carList"
    :columns-field-names="fieldNames"
    @cancel="onCancel"
    @confirm="onConfirm"
  ></van-picker>
</template>

<script>
import { Picker } from 'vant';
import { mapState } from 'vuex';

export default {
  name: 'CarSelect',
  inheritAttrs: false,
  emits: ['confirm', 'cancel'],
  components: {
    'van-picker': Picker,
  },
  data() {
    return {
      fieldNames: {
        text: 'transportVehicleNo',
        value: 'id',
      },
    };
  },
  computed: {
    ...mapState({
      carList: (state) => state.car.list,
    }),
  },
  methods: {
    onCancel() {
      this.$emit('cancel');
    },
    onConfirm(value) {
      this.$emit('confirm', value);
    },
  },
};
</script>
