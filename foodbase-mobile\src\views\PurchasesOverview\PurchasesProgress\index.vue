<template>
  <div class="purchases-progress">
    <Tabs v-model:active="active" swipeable @change="changeTab">
      <Tab :name="110101005000000" title="小麦"> </Tab>
      <Tab :name="110103001001000" title="早稻"> </Tab>
      <Tab :name="1" title="晚稻"> </Tab>
    </Tabs>
    <PurchasesProgressStatistics :food-category="foodCategory" />
  </div>
</template>

<script setup>
import { Tabs, Tab } from 'vant';
import { ref } from 'vue';
import PurchasesProgressStatistics from '@/views/PurchasesOverview/PurchasesProgress/PurchasesProgressStatistics';

const active = ref(110101005000000);
const foodCategory = ref([110101005000000]);
const changeTab = (val) => {
  if (val === 1) {
    foodCategory.value = [110103001002000, 110103002000000];
  } else {
    foodCategory.value = [val];
  }
};
</script>

<style scoped></style>
