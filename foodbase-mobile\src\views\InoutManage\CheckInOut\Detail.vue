<template>
  <div class="check-in in-out-manage-page">
    <van-form ref="form">
      <Search v-model="date" placeholder="选择日期区间" readonly @click="datePicker = true" />
      <Search v-model="schedulingNo" placeholder="请输入调度号" @search="onSearch"> </Search>
    </van-form>
    <div style="overflow: scroll; height: 100%">
      <List ref="listRef" v-model:loading="loading" :finished="finished" @load="onLoad">
        <EmptyHolder v-if="list.length === 0" />
        <div class="list" v-else>
          <CheckInoutDetailItem
            v-for="item in list"
            :item="item"
            :key="item.id"
            :typerName="item.typerName"
            :typer="item.typer"
            @handleRefresh="onSearch"
          ></CheckInoutDetailItem>
        </div>
      </List>
    </div>
    <Calendar
      allow-same-day
      v-model:show="datePicker"
      type="range"
      :min-date="minDate"
      :default-date="[new Date(beginDate), new Date(endDate)]"
      @confirm="onConfirm"
    />
  </div>
</template>

<script>
import { Search, List, Form, Calendar } from 'vant';
import dayjs from 'dayjs';
import CheckInoutDetailItem from './../common/CheckInoutDetailItem.vue';
import { getCheckInOutRecord } from '@/api/in-out-manage';
import EmptyHolder from '@/views/common/EmptyHolder';

export default {
  components: {
    'van-form': Form,
    CheckInoutDetailItem,
    Calendar,
    EmptyHolder,
    List,
    Search,
  },
  data() {
    return {
      date: '',
      minDate: new Date(dayjs().subtract(10, 'year').format('YYYY-MM-DD')),
      beginDate: dayjs().subtract(0, 'day').format('YYYY-MM-DD'),
      endDate: dayjs().format('YYYY-MM-DD'),
      schedulingNo: '',
      datePicker: false,
      confirmLoading: false,
      loading: false,
      finished: false,
      list: [],
      page: {
        current: 1,
        size: 9999,
      },
    };
  },
  created() {
    this.getList();
    this.date = `${this.beginDate} - ${this.endDate}`;
  },

  methods: {
    async getList() {
      this.loading = true;
      const { items, page, size } = await getCheckInOutRecord({
        page: this.page.current,
        size: 9999,
        schedulingNo: this.schedulingNo,
        createTime: [
          dayjs(this.beginDate).format('YYYY-MM-DD HH:mm:ss'),
          dayjs(this.endDate).endOf('day').format('YYYY-MM-DD HH:mm:ss'),
        ],
      });
      this.page.size = size;
      this.page.current = page;
      if (items?.length < this.page.size) {
        this.loading = false;
        this.finished = true;
        this.list.push(...items);
      } else {
        this.loading = true;
        this.finished = false;
        this.list.push(...items);
      }
    },
    onSearch() {
      this.list = [];
      this.finished = false;
      this.page.current = 1;
      this.getList();
    },
    onConfirm(values) {
      this.list = [];
      let [start, end] = values;
      this.beginDate = dayjs(start).format('YYYY-MM-DD 00:00:00');
      this.endDate = dayjs(end).format('YYYY-MM-DD 23:59:59');
      this.date = dayjs(start).format('YYYY-MM-DD') + ' - ' + dayjs(end).format('YYYY-MM-DD');
      this.datePicker = false;
      this.getList();
    },
    // onLoad() {
    //   let timer = setTimeout(() => {
    //     this.getList();
    //     this.page.current++;
    //     clearTimeout(timer);
    //   }, 100);
    // },
    onShowTicket() {},
    onGetSchedulingNo() {},
  },
};
</script>

<style lang="scss" scoped>
.scan-action {
  font-size: 16px;
  height: 16px;
  display: flex;
  align-items: center;
  padding: 0 10px;
}
</style>
