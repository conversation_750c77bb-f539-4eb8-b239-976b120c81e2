<template>
  <div class="sample-inspection-detail page-warpper">
    <Form ref="infoForm" class="info-form">
      <HCard class="info-card" title="基本信息" bgColor="#F7F8FA">
        <Cell title="扦样号" :value="form.qrCode" title-class="bold-text"></Cell>
        <Cell title="调度号" title-class="bold-text">
          <template #value>
            <ShortScheduleNo>{{ form.schedulingNo }}</ShortScheduleNo>
          </template>
        </Cell>
        <Cell title="作业车牌" :value="form.transportVehicleNo" title-class="bold-text"></Cell>
        <Cell title="粮食品种" :value="form.foodCategoryName" title-class="bold-text"></Cell>
        <Cell title="产地" :value="form.originName" title-class="bold-text"></Cell>
        <Field
          v-model="form.sampleTypeName"
          label="扦样方式"
          title-class="bold-text"
          is-link
          readonly
          required
          placeholder="请选择"
          :rules="[{ required: true, message: '请选择扦样方式' }]"
          @click="showCuttingTypePicker = true"
        />
        <ActionSheet v-model:show="showCuttingTypePicker" title="检验意见">
          <Picker
            :columns="cuttingTypeList"
            @cancel="showCuttingTypePicker = false"
            @confirm="onCuttingtypeConfirm"
          ></Picker>
        </ActionSheet>
      </HCard>
      <div class="test-config-title">
        <div class="text-no-wrap">
          检验项目：
          <span class="select-text" v-if="form.testConfigName" @click="onShowSchemeSelector">
            {{ form.testConfigName }}&gt;
          </span>
          <span class="select-text" v-else @click="onShowSchemeSelector">选择质检方案</span>
        </div>
        <Button
          round
          size="small"
          class="add-button"
          @click="onSelectInspectionItems"
          style="width: 8em"
        >
          增加检验项目
        </Button>
      </div>
      <div class="items-table">
        <div class="table-header">
          <div style="width: 30%">检验项目名称</div>
          <div style="width: 15%">检验值</div>
          <div style="width: 15%">单项判断</div>
          <div style="width: 16%">增量比例%</div>
          <div style="width: 16%">扣量比例%</div>
          <div style="width: 10%">操作</div>
        </div>
        <div
          class="table-body"
          v-for="(item, index) in form.qualityMngAcInfoTestingItems"
          :key="index"
        >
          <div style="width: 30%; overflow: hidden">
            {{ item.testItemName }}{{ item.testItemSign ? '（' + item.testItemSign + '）' : '' }}
          </div>
          <div style="width: 13%">
            <Field
              v-model="item.testItemAval"
              v-if="item.testItemName !== '色泽气味'"
              label-width="10.2em"
              required
              :name="`qualityMngAcInfoTestingItems.${index}.testItemAval`"
              placeholder="请输入"
              :rules="rowRules(item, 'testItemAval')"
              @blur="onValChange(item, `qualityMngAcInfoTestingItems.${index}.testItemAval`)"
            />
            <Field
              v-model="item.testItemAval"
              @click="colorSmellSelect(item)"
              v-else
              label-width="10.2em"
              required
              :name="`qualityMngAcInfoTestingItems.${index}.testItemAval`"
              placeholder="请选择"
              @blur="onValChange(item, `qualityMngAcInfoTestingItems.${index}.testItemAval`)"
            />
          </div>
          <div style="width: 15%" @click="itemQualityResultSelect(item)">
            {{ item.itemQualityResult == 1 ? '合格' : '不合格' }}
          </div>

          <div style="width: 17%">
            <Field
              v-model="item.incrementalProportion"
              label-width="8.2em"
              required
              placeholder="自动获取"
              :rules="rowRules(item, 'incrementalProportion')"
            />
          </div>
          <div style="width: 17%">
            <Field
              v-model="item.costComputeMultiValue"
              label-width="8.2em"
              required
              placeholder="自动获取"
              :rules="rowRules(item, 'costComputeMultiValue')"
            />
          </div>
          <div style="width: 10%">
            <van-icon
              v-if="!item.isHideDelete"
              name="delete"
              color="red"
              @click="deleteItem(index)"
            />
          </div>
        </div>
        <ActionSheet v-model:show="showColorSmellPicker" title="色泽气味">
          <Picker
            :columns="colorSmellPickerList"
            @cancel="showColorSmellPicker = false"
            @confirm="onColorSmellResultConfirm"
          ></Picker>
        </ActionSheet>
        <ActionSheet v-model:show="showitemQualityResultPicker" title="单项判断">
          <Picker
            :columns="itemQualityResultList"
            @cancel="showitemQualityResultPicker = false"
            @confirm="onItemQualityResultConfirm"
          ></Picker>
        </ActionSheet>
      </div>
      <HCard class="info-card" title="质检信息" bgColor="#F7F8FA">
        <Field
          v-model="form.storeHouseName"
          label="值仓仓房"
          is-link
          readonly
          required
          placeholder="请选择"
          :disabled="disableStorehouse"
          :rules="[{ required: true, message: '请选择值仓仓房' }]"
          @click="disableStorehouse ? (showHousePicker = false) : (showHousePicker = true)"
        />
        <Field
          v-model="form.levelName"
          label="粮油等级"
          is-link
          readonly
          required
          placeholder="请选择"
          :rules="[{ required: true, message: '请选择粮油等级' }]"
          @click="showLevelPicker = true"
        />
        <Field
          v-model="form.qualityResult"
          label="检验意见"
          is-link
          readonly
          required
          placeholder="请选择"
          :rules="[{ required: true, message: '请选择检验意见' }]"
          @click="showResultPicker = true"
        />
        <Field
          label="是否为超标粮"
          label-width="7.2em"
          required
          :rules="[{ required: true, message: '请选择是否为超标粮' }]"
          class="slot-field"
        >
          <template #input>
            <RadioGroup v-model="form.isOverFood" direction="horizontal">
              <Radio name="0">否</Radio>
              <Radio name="1">是</Radio>
            </RadioGroup>
          </template>
        </Field>
        <Field
          v-model="form.remark"
          type="textarea"
          label="意见备注"
          placeholder="请输入"
          autosize
        />

        <ActionSheet v-model:show="showHousePicker" title="值仓仓房">
          <CargoSelect
            v-if="hasCargoSpace"
            :food-category-id="form.foodCategoryId"
            @cancel="showHousePicker = false"
            @confirm="onHouseConfirm"
          />
          <HouseSelect
            v-else
            :food-category-id="form.foodCategoryId"
            :is-out-of-standard="form.isOverFood"
            :disable-storehouse="disableStorehouse"
            @cancel="showHousePicker = false"
            @confirm="onHouseConfirm"
          ></HouseSelect>
        </ActionSheet>
        <ActionSheet v-model:show="showLevelPicker">
          <BizDictPicker
            dict="GRAIN_AND_OIL_QUALITY_INSPECTION_GRADE"
            title="粮油等级"
            :hiddenValues="hiddenLevel"
            @cancel="showLevelPicker = false"
            @confirm="onLevelConfirm"
          ></BizDictPicker>
        </ActionSheet>
        <ActionSheet v-model:show="showResultPicker" title="检验意见">
          <Picker
            :columns="resultList"
            @cancel="showResultPicker = false"
            @confirm="onResultConfirm"
          ></Picker>
        </ActionSheet>
        <ActionSheet v-model:show="showOriginPicker" title="产地">
          <Cascader
            :options="originPlaces"
            :field-names="fieldNames"
            :closeable="false"
            @close="showOriginPicker = false"
            @change="onOriginConfirm"
          ></Cascader>
        </ActionSheet>
        <Dialog
          v-model:show="showSchemePicker"
          title="质检方案选择"
          show-cancel-button
          @cancel="showSchemePicker = false"
          @confirm="onSchemeConfirm"
        >
          <div class="scheme-warpper">
            <div
              v-for="(item, index) in schemeList"
              :key="index"
              v-bind:class="['item-warpper', { 'item-selected': scheme.id === item.id }]"
              @click="onSelectScheme(item)"
            >
              <div v-bind:class="['bold-text', { selected: scheme.id === item.id }]">
                {{ item.name }}
              </div>
              <div class="normal-text">适用品种：{{ item.foodCategoryNames }}</div>
            </div>
          </div>
        </Dialog>
        <Dialog
          v-model:show="showInspectionPicker"
          title="增加质检项目"
          show-cancel-button
          @cancel="showInspectionPicker = false"
          @confirm="onInspectionConfirm"
        >
          <div class="inspection-warpper">
            <div class="inspection-item">
              <div class="select-radio"></div>
              <div class="table-item">检验项目名称</div>
              <div class="table-item">检验标准</div>
            </div>
            <CheckboxGroup v-model="selectedInspection">
              <div v-for="(item, index) in inspectionItems" :key="index" class="inspection-item">
                <div class="select-radio">
                  <Checkbox :name="item.testItemId" :disabled="disabledInspection(item)"></Checkbox>
                </div>
                <div class="table-item">{{ item.testItemNameWithSign }}</div>
                <div class="table-item">{{ item.testItemSymbol }} {{ item.testItemSval }}</div>
              </div>
            </CheckboxGroup>
          </div>
        </Dialog>
      </HCard>
    </Form>
    <div class="bottom-warpper">
      <div class="buttons">
        <Button class="next-button" @click="onTempSave" :loading="saveLoading">
          保存当前结果
        </Button>
        <Button
          class="next-button"
          @click="onComplete"
          :loading="saveLoading"
          :disabled="form.qualityMngAcInfoTestingItems.length === 0"
        >
          完成质检
        </Button>
      </div>
      <div class="bar"></div>
    </div>
  </div>
</template>

<script>
import {
  Form,
  Cell,
  Button,
  Field,
  ActionSheet,
  Picker,
  RadioGroup,
  Radio,
  Cascader,
  Dialog,
  Toast,
  Checkbox,
  CheckboxGroup,
} from 'vant';
import { HCard } from '@/components';
import { ref, reactive, toRefs, onMounted, computed } from 'vue';
import {
  getQualityTestInfoForApp,
  getInspectionSchemeList,
  getInspectionItem,
  getFoodInspectionItems,
  getGradingStandardOfFoodCategory,
  saveInspectionJob,
  judgeFoodCategory,
  getCostValue,
  getIncreaseValue,
  getFoodCategoryLevel,
  checkIsOverStandardFood,
  checkTestItemIsMatchGrade,
} from '@/api/in-out-manage';
import { useRouter, useRoute } from 'vue-router';
import {
  ShortScheduleNo,
  HouseSelect,
  BizDictPicker,
  CargoSelect,
} from '@/views/InoutManage/common';
import { useStore } from 'vuex';
import { cloneDeep, keyBy, map, pick } from 'lodash-es';
import { combineWith } from '@/utils/inout-manage';
import { checkPermission } from '@/utils/permission';

export default {
  components: {
    Form,
    Cell,
    Button,
    Field,
    ActionSheet,
    Picker,
    RadioGroup,
    Radio,
    Cascader,
    Dialog: Dialog.Component,
    Checkbox,
    CheckboxGroup,
    HCard,
    ShortScheduleNo,
    HouseSelect,
    BizDictPicker,
    CargoSelect,
  },
  setup() {
    const router = useRouter();
    const route = useRoute();
    const store = useStore();
    const originPlaces = store.state['origin-place'].tree;
    const infoForm = ref(null);
    const state = reactive({
      form: {
        qualityMngAcInfoTestingItems: [],
      },
      originData: [],
      schemeList: [],
      scheme: {}, // 选中的质检方案id
      selectedInspection: [], // 选中的质检项
      schemeInspectionItems: [], // 根据品种和质检方案id获取质检项
      inspectionItems: [], // 所有质检项
      gradingStandard: [], // 等级标准
      showCuttingTypePicker: false, //显示扦样方式
      showitemQualityResultPicker: false,
      showColorSmellPicker: false, //色泽气味选择框
      showHousePicker: false, // 显示值仓仓房选择
      showLevelPicker: false, // 显示粮油等级选择
      showResultPicker: false, // 显示检验意见选择
      showOriginPicker: false, // 显示产地选择
      showSchemePicker: false, // 显示质检方案选择
      showInspectionPicker: false, // 显示质检方案选择
      saveLoading: false, // 按钮加载
      highCategory: null,
      selectedItem: null, //选中质检项
    });
    const hasCargoSpace = computed(() => checkPermission(['cargo_space']));
    const cuttingTypeList = [
      {
        text: '人工',
        value: '0',
      },
      {
        text: '自动',
        value: '1',
      },
      {
        text: '智能随机',
        value: '2',
      },
    ];
    const resultList = [
      {
        text: '合格',
        value: '1',
      },
      {
        text: '不合格',
        value: '2',
      },
      {
        text: '入库',
        value: '3',
      },
      {
        text: '退货',
        value: '4',
      },
    ];
    const colorSmellPickerList = [
      {
        text: '正常',
        value: '0',
      },
      {
        text: '不正常',
        value: '1',
      },
    ];
    const itemQualityResultList = [
      {
        text: '合格',
        value: '1',
      },
      {
        text: '不合格',
        value: '2',
      },
    ];
    const fieldNames = {
      text: 'label',
      value: 'value',
      children: 'children',
    };

    const deleteItem = (index) => {
      state.form.qualityMngAcInfoTestingItems.splice(index, 1);
    };

    const disableStorehouse = computed(() => {
      return ['3', '4', '6', '7'].includes(state.form.buzTyper);
    });

    const categoryLevels = computed(() => {
      return store.getters['dict/reserveDictOf']('GRAIN_AND_OIL_QUALITY_INSPECTION_GRADE');
    });

    const colorSmellSelect = (item) => {
      state.selectedItem = item;
      state.showColorSmellPicker = true;
    };

    const itemQualityResultSelect = (item) => {
      state.selectedItem = item;
      state.showitemQualityResultPicker = true;
    };

    const hiddenLevel = computed(() => {
      if (state.highCategory) {
        const oilLevels = categoryLevels.value.filter((it) => Number(it.value) >= 100);
        const foodLevels = categoryLevels.value.filter((it) => Number(it.value) < 100);
        if (state.highCategory == '1') {
          return oilLevels.map((it) => it.value);
        } else if (state.highCategory == '2') {
          return foodLevels.map((it) => it.value);
        } else {
          return [];
        }
      } else {
        return [];
      }
    });

    onMounted(async () => {
      initData();
    });
    const initData = async () => {
      const qrCode = route.params.qrCode;
      const data = await getQualityTestInfoForApp({ qrCode });
      console.log(data);
      if (!data) {
        state.form = {
          qualityMngAcInfoTestingItems: [],
        };
        return;
      } else if (data.completeQuality) {
        // 已质检完成数据，不再进入编辑页面
        router.back();
      }
      state.form = {
        ...data,
        id: data.qualityTestId,
        flowQualityTestingId: data.qualityTestId,
        storeHouseName:
          hasCargoSpace.value && data.cargoSpaceName
            ? `${data.storeHouseName}_${data.cargoSpaceName}`
            : data.storeHouseName,
        qualityMngAcInfoTestingItems: data.testingItemList
          ? data.testingItemList.map((item) => {
              return {
                ...item,
                costComputeMultiValue: item.buckleQuantityRatio,
                testItemNameWithSign:
                  item.testItemName + (item.testItemSign ? '/' + item.testItemSign : ''),
              };
            })
          : [],
      };
      if (state.form.sampleType == '0') {
        state.form.sampleTypeName = '人工';
      } else if (state.form.sampleType == '1') {
        state.form.sampleTypeName = '自动';
      } else if (state.form.sampleType == '2') {
        state.form.sampleTypeName = '智能随机';
      }
      await Promise.all([
        getSchemeInspectionItems(),
        getAllInspectionItems(),
        getSelectedFoodStandard(),
        judgeType(),
      ]);
      state.inspectionItems.forEach((item) => {
        // console.log(item.testItemId, 'items');
        // console.log(state.gradingStandard, 'state.gradingStandard');
        let grades = state.gradingStandard.find((it) => it.testQualityItemId == item.testItemId);
        // console.log(grades, 'grades');
        if (grades) {
          let thirdGrade = grades.original.find((row) => row.level == '3');
          if (thirdGrade) {
            item.testItemSymbol = thirdGrade.testItemSymbol;
            item.testItemSval = thirdGrade.upperLimit;
          }
        }
      });
      // console.log(state.inspectionItems);

      state.scheme = {
        id: Number(data.testConfigId),
        name: data.testConfigName,
      };
    };
    const getSchemeInspectionItems = async () => {
      const { testConfigId, foodCategoryId } = state.form;
      const items = await getInspectionItem(foodCategoryId, testConfigId);
      // console.log('1', items);
      if (items) {
        state.schemeInspectionItems = items.map((item) => {
          return {
            isHideDelete: true,
            costComputeMultiValue: '',
            incrementalProportion: '',
            foodCategoryId: foodCategoryId,
            testItemAval: item.testItemAval,
            testItemId: item.testItemId,
            testItemName: item.testItemName,
            testItemSign: item.testItemSign,
            testItemNameWithSign: combineWith('/', [item.testItemName, item.testItemSign]),
            testItemSval: item.testItemSval,
            testItemSymbol: item.testItemSymbol,
            sort: item.sort,
          };
        });
        state.schemeInspectionItems.sort((a, b) => a.sort - b.sort);
        state.selectedInspection = [];
        state.form.qualityMngAcInfoTestingItems = [];
        state.form.qualityMngAcInfoTestingItems = cloneDeep(state.schemeInspectionItems);
        // if (state.form.qualityMngAcInfoTestingItems.length === 0) {
        //   state.form.qualityMngAcInfoTestingItems = cloneDeep(state.schemeInspectionItems);
        // }
        state.form.qualityMngAcInfoTestingItems.forEach((it) => {
          it.itemQualityResult = '1';
        });
        state.schemeInspectionItems.forEach((it) => {
          state.selectedInspection.push(it.testItemId);
        });
      }
    };
    const getAllInspectionItems = async () => {
      const { foodCategoryId } = state.form;
      const items = await getFoodInspectionItems(foodCategoryId);
      // console.log('2', items);
      state.inspectionItems = items.map((item) => {
        return {
          costComputeMultiValue: '',
          incrementalProportion: '',
          foodCategoryId: foodCategoryId,
          testItemAval: item.testItemAval,
          testItemName: item.testItemName,
          testItemId: item.testQualityItemId,
          testItemSign: item.testItemSign,
          testItemNameWithSign: combineWith('/', [item.testItemName, item.testItemSign]),
          testItemSval: item.testItemSval,
          testItemSymbol: item.testItemSymbol,
          itemQualityResult: '1',
        };
      });
    };
    const getSelectedFoodStandard = async () => {
      const { foodCategoryId } = state.form;
      const data = await getGradingStandardOfFoodCategory(foodCategoryId);
      if (data) {
        // console.log('3', data);
        state.gradingStandard = map(data, (value, key) => {
          const [firstOriginalItem] = value;
          const { testItemName, testItemSign, testQualityItemId } = firstOriginalItem;
          return {
            testQualityItemId: testQualityItemId,
            testItemName: key,
            testItemNameWithSign: combineWith('/', [testItemName, testItemSign]),
            original: value,
            ...keyBy(value, (it) => it.level),
          };
        });
      }
      // console.log(state.gradingStandard);
    };
    const judgeType = async () => {
      const { foodCategoryId } = state.form;
      const data = await judgeFoodCategory({ categoryId: foodCategoryId });
      state.highCategory = data;
    };
    const onShowSchemeSelector = async () => {
      const { foodCategoryId } = state.form;
      const list = await getInspectionSchemeList(foodCategoryId);
      // console.log('4', list);
      state.schemeList = list.map((it) => {
        let foodCategoryNames = '';
        if (it.infoFoodCategoryDTOList) {
          // console.log('4', it.infoFoodCategoryDTOList);
          foodCategoryNames = it.infoFoodCategoryDTOList
            .map((it) => it.foodCategoryName)
            .join('，');
        }
        return {
          ...it,
          foodCategoryNames,
        };
      });
      state.showSchemePicker = true;
    };
    const onSelectScheme = (item) => {
      state.scheme = item;
    };
    const onSelectInspectionItems = () => {
      state.showInspectionPicker = true;
    };
    const onClose = () => {
      router.back();
    };
    const onHouseConfirm = (value) => {
      if (hasCargoSpace.value) {
        state.form.cargoSpaceId = value.id;
        state.form.storeHouseId = value.storeHouseId;
        state.form.storeHouseName = value.houseAndCargo;
      } else {
        state.form.storeHouseId = value.id;
        state.form.storeHouseName = value.name;
      }
      state.showHousePicker = false;
    };
    const onLevelConfirm = (value) => {
      state.form.level = value.value;
      state.form.levelName = value.label;
      state.showLevelPicker = false;
      onLevelChange();
    };
    const onResultConfirm = (value) => {
      state.form.qualityResult = value.text;
      state.form.qualityResultCode = value.value;
      state.showResultPicker = false;
    };
    const onCuttingtypeConfirm = (value) => {
      state.form.sampleTypeName = value.text;
      state.form.sampleType = value.value;
      state.showCuttingTypePicker = false;
    };
    const onItemQualityResultConfirm = (value) => {
      state.selectedItem.itemQualityResult = value.value;
      state.showitemQualityResultPicker = false;
    };
    const onColorSmellResultConfirm = (value) => {
      state.selectedItem.testItemAval = value.text;
      state.showColorSmellPicker = false;
    };
    const onOriginConfirm = ({ selectedOptions: list }) => {
      if (list.length === 0) {
        state.form.origin = '';
        state.form.originName = '';
        return;
      }
      const item = list[list.length - 1];
      state.form.origin = item.value;
      state.form.originName = item.label;
    };
    const onSchemeConfirm = () => {
      state.form.testConfigId = state.scheme.id;
      state.form.testConfigName = state.scheme.name;
      getSchemeInspectionItems();
      state.showSchemePicker = false;
    };
    const onInspectionConfirm = () => {
      const selected = state.inspectionItems.filter((it) =>
        state.selectedInspection.includes(it.testItemId),
      );
      const newItems = selected.filter(
        (item) => !state.schemeInspectionItems.some((it) => it.testItemId == item.testItemId),
      );
      state.form.qualityMngAcInfoTestingItems = state.schemeInspectionItems.concat(newItems);
      state.showInspectionPicker = false;
    };
    const disabledInspection = (item) => {
      // console.log(state.schemeInspectionItems, 'some1');
      return state.schemeInspectionItems?.some((it) => it.testItemId == item.testItemId);
    };
    const validateTrim = (value) => {
      if (!value || !value.trim()) {
        return false;
      } else {
        return true;
      }
    };
    const pattern = (value) => {
      const reg = /^[0-9]+(\.[0-9]{0,3})?$/;
      return reg.test(value);
    };
    const rowRules = (item, key) => {
      const { testItemSign } = item;
      const numberRule = [
        { required: true, message: '请输入' },
        { validator: validateTrim, message: '请输入' },
        { validator: pattern, message: '请输入有效的数量' },
      ];
      if (key != 'testItemAval' || (testItemSign && key === 'testItemAval')) {
        return numberRule;
      } else {
        return numberRule.slice(0, 2);
      }
    };
    const onValChange = async (item, key) => {
      state.saveLoading = true;
      await infoForm.value.validate(key);
      Promise.all([
        calcCost(item),
        calcIncrease(item),
        calcIsOverProof(item),
        calcLevel(item),
      ]).then(() => {
        state.saveLoading = false;
      });
    };
    const calcCost = async (row) => {
      if (state.form.buzTyper != '2') {
        row.costComputeMultiValue = 0;
        return;
      }
      const { foodCategoryId, level } = state.form;
      const { testItemAval, testItemId } = row;
      const value = await getCostValue({
        foodCategoryId,
        testItemId,
        testItemAval,
        grade: level,
      });
      row.costComputeMultiValue = Number(value) || 0;
    };
    const calcIncrease = async (row) => {
      if (state.form.buzTyper != '2') {
        row.incrementalProportion = 0;
        return;
      }
      const { foodCategoryId, level } = state.form;
      const { testItemAval, testItemId } = row;
      const value = await getIncreaseValue({
        foodCategoryId,
        testItemId,
        testItemAval,
        grade: level,
      });
      row.incrementalProportion = Number(value) || 0;
    };
    const calcIsOverProof = async (row) => {
      const { foodCategoryId } = state.form;
      const { testItemAval, testItemId } = row;
      const value = await checkIsOverStandardFood([
        {
          foodCategoryId,
          testItemId,
          testItemAval,
        },
      ]);
      row.isOverFood = Number(value);
      // console.log(state.form.qualityMngAcInfoTestingItems, 'some2');
      state.isOverFood = state.form.qualityMngAcInfoTestingItems.some(
        (item) => item.isOverFood == '1',
      )
        ? '1'
        : '0';
    };
    const calcLevel = async (row) => {
      const { foodCategoryId } = state.form;
      const items = state.form.qualityMngAcInfoTestingItems
        .filter((it) => {
          if (it.testItemAval) {
            if (it.testItemSign) {
              return !isNaN(Number(it.testItemAval));
            }
            return true;
          }
          return false;
        })
        .map((it) => {
          const item = pick(it, [
            'testItemId',
            'testItemName',
            'testItemSval',
            'testItemSymbol',
            'testItemAval',
          ]);
          item.foodCategoryId = foodCategoryId;
          return item;
        });
      if (items.length) {
        const data = await getFoodCategoryLevel(items);
        if (data && data != '-1' && data != -1) {
          if (state.form.level != data) {
            state.form.level = `${data}`;
            const dictDetails = store.getters['dict/reserveDictOf'](
              'GRAIN_AND_OIL_QUALITY_INSPECTION_GRADE',
            );
            const matched = dictDetails.find((it) => it.value == data);
            if (matched) {
              state.form.levelName = matched.label;
            }
            onLevelChange();
          } else {
            calcQualityResult(row);
          }
        } else {
          calcQualityResult(row);
        }
      }
    };

    // 单项判断
    const calcQualityResult = async (row) => {
      const { foodCategoryId, level } = state.form;
      const { testItemAval, testItemId } = row;
      if (!(testItemAval && testItemId && foodCategoryId && level)) {
        return;
      }
      let obj = {
        foodCategoryId,
        grade: level,
        testItemAval,
        testItemId,
      };
      const data = await checkTestItemIsMatchGrade([obj]);

      if (data && data.length > 0) {
        row.itemQualityResult = data[0].testItemIsMatchGrade === 0 ? '2' : '1';
      }
    };

    const onLevelChange = () => {
      state.form.qualityMngAcInfoTestingItems.forEach((row) => {
        if (row.testItemAval) {
          calcCost(row);
          calcIncrease(row);
          calcQualityResult(row);
        }
      });
    };
    const onTempSave = async () => {
      try {
        state.saveLoading = true;
        state.form.qualityMngAcInfoTestingItems.forEach((item) => {
          item.testItemAval = item.testItemAval
            ? item.testItemAval.toString().trim()
            : item.testItemAval;
          item.incrementalProportion = item.incrementalProportion
            ? item.incrementalProportion.toString().trim()
            : item.incrementalProportion;
          item.costComputeMultiValue = item.costComputeMultiValue
            ? item.costComputeMultiValue.toString().trim()
            : item.costComputeMultiValue;
        });
        state.form.qualityResult = state.form.qualityResultCode;
        const data = state.form;
        await saveData(true, data);
        onClose();
      } catch (e) {
        console.log(e);
      } finally {
        state.saveLoading = false;
      }
    };
    const onComplete = async () => {
      try {
        state.saveLoading = true;
        await infoForm.value.validate();
        state.form.qualityMngAcInfoTestingItems.forEach((item) => {
          item.testItemAval = item.testItemAval
            ? item.testItemAval.toString().trim()
            : item.testItemAval;
          item.incrementalProportion = item.incrementalProportion
            ? item.incrementalProportion.toString().trim()
            : item.incrementalProportion;
          item.costComputeMultiValue = item.costComputeMultiValue
            ? item.costComputeMultiValue.toString().trim()
            : item.costComputeMultiValue;
        });
        state.form.qualityResult = state.form.qualityResultCode;
        const data = state.form;
        await saveData(false, data);
        onClose();
      } catch (e) {
        //
      } finally {
        state.saveLoading = false;
      }
    };
    const saveData = async (isTemp, data) => {
      try {
        await saveInspectionJob({
          ...data,
          isTemp: isTemp ? '1' : '0',
        });
        if (isTemp) {
          Toast.success('已暂存');
        } else {
          Toast.success('保存成功');
        }
      } catch (e) {
        console.error(e);
        throw e;
      }
    };

    return {
      ...toRefs(state),
      infoForm,
      resultList,
      cuttingTypeList,
      itemQualityResultList,
      colorSmellPickerList,
      originPlaces,
      fieldNames,
      disableStorehouse,
      hiddenLevel,
      onClose,
      onShowSchemeSelector,
      onSelectScheme,
      onSchemeConfirm,
      onSelectInspectionItems,
      onInspectionConfirm,
      onItemQualityResultConfirm,
      onColorSmellResultConfirm,
      onHouseConfirm,
      onLevelConfirm,
      onResultConfirm,
      onCuttingtypeConfirm,
      onOriginConfirm,
      disabledInspection,
      rowRules,
      onValChange,
      onTempSave,
      onComplete,
      calcQualityResult,
      itemQualityResultSelect,
      colorSmellSelect,
      deleteItem,
      hasCargoSpace,
    };
  },
};
</script>

<style lang="scss" scoped>
@import '@/views/InoutManage/style/index';
.info-form {
  margin-bottom: 120px;
  .info-card {
    ::v-deep(.bold-text) {
      font-weight: bold;
    }
    margin-bottom: 10px;
  }
  .test-config-title {
    height: 50px;
    font-size: 16px;
    font-weight: 500;
    color: #111111;
    line-height: 50px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0 20px;
    .text-no-wrap {
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
    }
    .select-text {
      color: #1677ff;
    }
    .add-button {
      color: #165dff;
      border: 1px solid rgba(21, 93, 255, 0.5);
      background-color: transparent;
    }
  }
}
.scheme-warpper {
  height: 240px;
  overflow: auto;
  padding: 15px 0;
  .item-warpper {
    width: 260px;
    background: #f4f6f7;
    border-radius: 4px;
    border: 1px solid #ebedf0;
    margin: 0 auto;
    margin-bottom: 15px;
    padding-left: 15px;
    &.item-selected {
      border: 1px solid #1677ff;
    }
    .bold-text {
      font-size: 16px;
      font-weight: bold;
      color: #3a4056;
      line-height: 36px;
      &.selected {
        color: #1677ff;
      }
    }
    .normal-text {
      font-size: 14px;
      font-weight: 400;
      color: #3a4056;
      line-height: 24px;
    }
  }
}
.scheme-warpper::-webkit-scrollbar {
  width: 0;
  height: 0;
}
.inspection-warpper {
  height: 240px;
  overflow: auto;
  .inspection-item {
    display: flex;
    height: 40px;
    border: 1px solid #e5e5e5;
    line-height: 40px;
    .select-radio {
      width: 50px;
      display: flex;
      justify-content: center;
      align-items: center;
    }
    .table-item {
      width: calc((100% - 50px) / 2);
      text-align: center;
      white-space: nowrap;
      text-overflow: ellipsis;
      overflow: hidden;
    }
  }
}
.items-table {
  width: 100%;
  text-align: center;
  .table-header {
    display: flex;
    background-color: #f7f8fa;
    height: 45px;
    line-height: 45px;
    font-size: 14px;
    font-weight: bold;
    color: #111111;
  }
  .table-body {
    display: flex;
    background-color: #ffffff;
    margin-bottom: 1px;
    height: 44px;
    line-height: 44px;
    font-size: 14px;
    font-weight: 400;
    color: #323233;
  }
}
.inspection-warpper::-webkit-scrollbar {
  width: 0;
  height: 0;
}
</style>
