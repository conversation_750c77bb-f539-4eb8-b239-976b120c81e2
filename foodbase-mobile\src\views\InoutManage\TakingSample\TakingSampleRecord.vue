<template>
  <div class="taking-sample-record">
    <Row>
      <Col :span="12">
        <Search v-model="date" placeholder="选择日期区间" readonly @click="datePicker = true" />
      </Col>
      <Col :span="12">
        <Search v-model="search" placeholder="请输入扦样号" show-action @search="onSearch"></Search>
      </Col>
    </Row>
    <Calendar
      v-model:show="datePicker"
      allow-same-day
      type="range"
      :min-date="minDate"
      :default-date="[new Date(beginDate), new Date(endDate)]"
      @confirm="onConfirm"
    />
    <PullRefresh @refresh="onRefresh" v-model="refreshLoading">
      <List
        ref="listRef"
        v-model:loading="isLoading"
        :finished="finished"
        finished-text="没有更多了"
        @load="getList"
      >
        <HCard class="detail-card" v-for="item in list" :key="item">
          <div class="card-head">
            <img :src="IconHouse" />
            <div class="schedule-num">调度号：{{ item.schedulingNo?.split('_')[0] }}</div>
          </div>
          <div class="card-content">
            <div class="row">
              <span class="label">作业车牌：</span>
              <span class="carNum" v-for="(it, index) in item.transportVehicleNo" :key="index">
                {{ it }}
              </span>
            </div>
            <div class="row">
              <span class="label">承运人：</span>
              <span class="value">{{ item.transportVehicleDriver }}</span>
            </div>
            <div class="row">
              <span class="label">粮油品种：</span>
              <span class="value">{{ item.foodCategoryName }}</span>
            </div>
            <div class="row">
              <span class="label">联系电话：</span>
              <span class="value">{{ item.transportVehicleTel }}</span>
            </div>
            <div class="row">
              <span class="label">客户名称：</span>
              <span class="value">{{ item.farmerName }}</span>
            </div>
            <div class="row">
              <span class="label">扦样号：</span>
              <span class="value">{{ item.qrCode }}</span>
            </div>
            <div class="row">
              <span class="label">扦样时间：</span>
              <span class="value">{{ item.printTime }}</span>
            </div>
            <div class="botton-warpper">
              <Button @click="onShowBar(item)">查看扦样条形码</Button>
            </div>
          </div>
        </HCard>
      </List>
    </PullRefresh>
    <Dialog.Component
      v-model:show="visible"
      width="350px"
      :show-cancel-button="true"
      confirm-button-text="保存至手机"
      confirm-button-color="#1677ff"
      @confirm="saveTicket"
      @cancel="onCancel"
    >
      <template #default>
        <div class="barcode">
          <BarCode :barcode="barcode" :serialNo="serialNo" ref="barCodeRef"></BarCode>
          <div class="remark">请截图或将图片保存至手机</div>
        </div>
      </template>
    </Dialog.Component>
  </div>
</template>

<script setup>
import { Search, List, PullRefresh, Button, Dialog, Row, Col, Calendar } from 'vant';
import { ref, reactive, watch } from 'vue';
import { useRoute } from 'vue-router';
import { HCard } from '@/components';
import IconHouse from '@/assets/icon-house.png';
import { getTakingSampleHistory } from '@/api/in-out-manage';
import { BarCode } from '@/views/InoutManage/common';
import dayjs from 'dayjs';

const route = useRoute();
const search = ref('');
const listRef = ref();
const visible = ref(false);
const barcode = ref('');
const serialNo = ref('');

const list = ref([]);
const pagination = reactive({
  page: 0,
  size: 10,
  total: 0,
});

const isLoading = ref(false);
const finished = ref(false);
const refreshLoading = ref(false);

const beginDate = ref(dayjs().format('YYYY-MM-DD'));
const endDate = ref(dayjs().format('YYYY-MM-DD'));
const date = ref(`${beginDate.value} - ${endDate.value}`);
const datePicker = ref(false);
const minDate = new Date(dayjs().subtract(10, 'year').format('YYYY-MM-DD'));
const barCodeRef = ref(); //条形码ref
const onRefresh = () => {
  if (refreshLoading.value) {
    pagination.page = 0;
    pagination.total = 0;
    list.value = [];
    refreshLoading.value = false;
  }
  finished.value = false;
  isLoading.value = true;
  getList();
};
const onSearch = () => {
  finished.value = false;
  list.value = [];
  pagination.page = 0;
  pagination.total = 0;
  listRef.value?.check();
};
const getList = async () => {
  const { items, page, total } = await getTakingSampleHistory({
    samplingNo: search.value,
    page: pagination.page + 1,
    size: pagination.size,
    beginDate: beginDate.value,
    endDate: endDate.value,
  });
  list.value.push(...items);
  pagination.page = page;
  pagination.total = total;

  // 加载状态结束
  isLoading.value = false;

  // 数据全部加载完成
  if (list.value.length >= total) {
    finished.value = true;
  }
};

const onConfirm = (values) => {
  const [start, end] = values;
  beginDate.value = dayjs(start).format('YYYY-MM-DD');
  endDate.value = dayjs(end).format('YYYY-MM-DD');
  date.value = `${beginDate.value} - ${endDate.value}`;
  datePicker.value = false;
  onSearch();
};

const onShowBar = (item) => {
  barcode.value = item.qrCode;
  serialNo.value = item.serialNo;
  visible.value = true;
};
const saveTicket = () => {
  barCodeRef.value.downloadBarcode();
};
const onCancel = () => {
  barcode.value = '';
  serialNo.value = '';
  visible.value = false;
};

watch(
  () => route.name,
  () => {
    search.value = '';
    onSearch();
  },
);
</script>

<style lang="scss" scoped>
.scan-action {
  font-size: 25px;
  height: 34px;
  display: flex;
  align-items: center;
  padding: 0 10px;
}
.total-contain {
  padding: 9px 14px;
}
.detail-card {
  padding: 16px;
  margin-bottom: 10px;
}
.card-head {
  display: flex;
  align-items: center;
  justify-content: space-between;
  font-size: 18px;
  font-weight: bold;
  color: #1f3359;
  .next {
    color: rgba(109, 116, 141, 0.6);
  }
}
.schedule-num {
  flex-grow: 1;
  margin-left: 12px;
}
.card-content {
  margin-top: 8px;
  line-height: 30px;
  .row {
    display: flex;
    .label {
      font-size: 14px;
      font-weight: 400;
      color: #6d748d;
      flex-basis: 6em;
    }
    .value {
      font-size: 14px;
      font-weight: 400;
      color: #0f0f0f;
      flex-grow: 1;
    }
    .carNum {
      padding: 3px 6px;
      background: #f4f4f4;
      border-radius: 2px;
      border: 1px solid #ebedf0;
      margin-right: 5px;
    }
  }
  .botton-warpper {
    display: flex;
    justify-content: center;
  }
}
.barcode {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  .remark {
    font-size: 16px;
    font-weight: 500;
    color: #333333;
    line-height: 24px;
    text-align: center;
  }
}

.tag-cls {
  line-height: 25px;
}
.next-contain {
  width: 80px;
  text-align: right;
}
.icon-cls {
  vertical-align: text-top;
}
</style>
