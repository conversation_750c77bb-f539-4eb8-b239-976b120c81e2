<template>
  <div class="checking-weight-record-detail page-warpper">
    <HCard class="info-card">
      <div class="card-head">
        <img :src="IconHouse" />
        <div class="schedule-num">调度号：{{ form.schedulingNo?.split('_')[0] }}</div>
      </div>
      <div class="card-content">
        <div class="row">
          <span class="label">作业车牌：</span>
          <span class="carNum" v-for="(it, index) in form.transportVehicleNo" :key="index">
            {{ it }}
          </span>
        </div>
        <div class="row">
          <span class="label">粮食品种：</span>
          <span class="value">{{ form.foodCategoryName }}</span>
        </div>
        <div class="row">
          <span class="label">值仓仓房：</span>
          <span class="value">
            {{
              hasCargoSpace && form.cargoSpaceName
                ? `${form.storeHouseName}_${form.cargoSpaceName}`
                : form.storeHouseName
            }}
          </span>
        </div>
        <div class="row">
          <span class="label">客户名称：</span>
          <span class="value">{{ form.customerName }}</span>
        </div>
      </div>
    </HCard>
    <HCard title="检斤数据" bgColor="#F7F8FA">
      <Cell title="毛重（公斤）" title-class="bold-text">
        <template #value>
          <HFixedNumber :ratio="1000" :fraction-digits="0">
            {{ form?.countMao }}
          </HFixedNumber>
        </template>
      </Cell>
      <Cell title="皮重（公斤）" title-class="bold-text">
        <template #value>
          <HFixedNumber :ratio="1000" :fraction-digits="0">
            {{ form?.countSkin }}
          </HFixedNumber>
        </template>
      </Cell>
      <Cell title="包装物扣量（公斤）" title-class="bold-text">
        <HFixedNumber :ratio="1000" :fraction-digits="0">
          {{ form?.packingDeduction }}
        </HFixedNumber>
      </Cell>
      <Cell title="过磅重量（公斤）" title-class="bold-text">
        <HFixedNumber :ratio="1000" :fraction-digits="0">
          {{ form?.countNet }}
        </HFixedNumber>
      </Cell>
      <Cell title="其他扣量（公斤）" title-class="bold-text">
        <HFixedNumber :ratio="1000" :fraction-digits="0">
          {{ form?.otherDeduction }}
        </HFixedNumber>
      </Cell>

      <Cell title="销售出库增量" title-class="bold-text">
        <HFixedNumber :ratio="1000" :fraction-digits="0">
          {{ form?.salesOutIncrement }}
        </HFixedNumber>
      </Cell>
      <!-- <Cell title="扣量（公斤）" title-class="bold-text">
        <template #value>
          <HFixedNumber :ratio="1000" :fraction-digits="0">
            {{ form?.buckle }}
          </HFixedNumber>
        </template>
      </Cell> -->
      <!-- <Cell title="结算数量（公斤）" title-class="bold-text">
        <template #value>
          <HFixedNumber :ratio="1000" :fraction-digits="0">
            {{ form?.count }}
          </HFixedNumber>
        </template>
      </Cell> -->
      <!--      <Cell title="增量（%）" title-class="bold-text">-->
      <!--        <template #value>-->
      <!--          {{ form?.incrementalProportion }}-->
      <!--        </template>-->
      <!--      </Cell>-->
      <!--      <Cell title="增量（公斤）" title-class="bold-text">-->
      <!--        <template #value>-->
      <!--          <HFixedNumber :ratio="1000" :fraction-digits="0">-->
      <!--            {{ form?.increment }}-->
      <!--          </HFixedNumber>-->
      <!--        </template>-->
      <!--      </Cell>-->
    </HCard>
  </div>
</template>

<script>
import { Cell } from 'vant';
import { HCard, HFixedNumber } from '@/components';
import IconHouse from '@/assets/icon-house.png';
import { reactive, toRefs, onMounted, computed } from 'vue';
import { useRoute } from 'vue-router';
// import { saveWeightTicket } from '@/api/in-out-manage';
import { checkPermission } from '@/utils/permission';

export default {
  components: { Cell, HCard, HFixedNumber },
  setup() {
    const route = useRoute();
    const state = reactive({
      form: {},
      salesOutIncrement: '',
    });
    const hasCargoSpace = computed(() => checkPermission(['cargo_space']));

    onMounted(async () => {
      // state.salesOutIncrement = JSON.parse(route.params.schedulingNo).salesOutIncrement;
      initData();
      // savePrintTicket();
    });

    const initData = async () => {
      const record = JSON.parse(route.params.schedulingNo);
      console.log(record);
      console.log(record.otherDeduction);
      // const data = await getWeighingDetail({ schedulingNo });
      state.form = record;
    };
    // const savePrintTicket = async () => {
    //   const schedulingNo = JSON.parse(route.params.schedulingNo).schedulingNo;
    //   const data = await saveWeightTicket([{ schedulingNo }]);
    //   const row = data[0];
    //   state.form = {
    //     ...state.form,
    //     customerName: row?.customerName,
    //     countMao: row?.countMao,
    //     countSkin: row?.countSkin,
    //     otherDeduction: row?.otherDeduction,
    //     countNet: row?.countNet,
    //     buckleQuantityRatio: row?.buckleQuantityRatio,
    //     buckle: row?.buckle,
    //     count: row?.countNet - row?.buckle,
    //     incrementalProportion: row?.incrementalProportion,
    //     increment: row?.increment,
    //   };
    // };

    return {
      ...toRefs(state),
      IconHouse,
      hasCargoSpace,
    };
  },
};
</script>

<style lang="scss" scoped>
@import '@/views/InoutManage/style/index';
.noraml {
  margin-bottom: 10px;
}
.info-card {
  padding: 16px;
  margin-bottom: 10px;
}
.card-head {
  display: flex;
  align-items: center;
  justify-content: space-between;
  font-size: 18px;
  font-weight: bold;
  color: #1f3359;
  .next {
    color: rgba(109, 116, 141, 0.6);
  }
}
.schedule-num {
  flex-grow: 1;
  margin-left: 12px;
}
.card-content {
  margin-top: 8px;
  line-height: 30px;
  .row {
    display: flex;
    .label {
      font-size: 14px;
      font-weight: 400;
      color: #6d748d;
      flex-basis: 6em;
    }
    .value {
      font-size: 14px;
      font-weight: 400;
      color: #0f0f0f;
      flex-grow: 1;
    }
    .carNum {
      padding: 3px 6px;
      background: #f4f4f4;
      border-radius: 2px;
      border: 1px solid #ebedf0;
      margin-right: 5px;
    }
  }
}
</style>
