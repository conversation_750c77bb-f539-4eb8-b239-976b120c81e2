import { toNumber as _toNumber } from 'lodash-es';

export function getShortSchedulingNo(schedulingNo) {
  return schedulingNo.split('_')[0] || '-';
}

export function timeFormatter(type, val) {
  if (type === 'year') {
    return val + '年';
  }
  if (type === 'month') {
    return val + '月';
  }
  if (type === 'day') {
    return val + '日';
  }
  if (type === 'hour') {
    return val + '时';
  }
  if (type === 'minute') {
    return val + '分';
  }
  if (type === 'second') {
    return val + '秒';
  }
  return val;
}

export function combineWith(separator, items) {
  return items.filter((it) => it).join(separator);
}

export function toNumber(value) {
  return value && _toNumber(value);
}
