<template>
  <div class="order-public-list">
    <Tabs v-model:active="active" swipeable>
      <Tab :name="3" title="小麦">
        <OrderPublicList :food-category="3" />
      </Tab>
      <Tab :name="1" title="早稻">
        <OrderPublicList :food-category="1" />
      </Tab>
      <Tab :name="2" title="晚稻">
        <OrderPublicList :food-category="2" />
      </Tab>
    </Tabs>
  </div>
</template>

<script setup>
import { Tabs, Tab } from 'vant';
import { ref } from 'vue';
import OrderPublicList from '@/views/PurchasesOverview/OrderPublicList/OrderPublicList';

const active = ref(3);
</script>

<style scoped lang="scss"></style>
