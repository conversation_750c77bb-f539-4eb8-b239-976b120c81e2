<template>
  <van-picker
    :columns="originData"
    :columns-field-names="fieldNames"
    @cancel="onCancel"
    @confirm="onConfirm"
  ></van-picker>
</template>

<script>
import { Picker } from 'vant';
import { mapState } from 'vuex';
import { findAvailableStoreHouseCargoSpace } from '@/api/in-out-manage';

export default {
  name: 'CargoSelect',
  inheritAttrs: false,
  emits: ['confirm', 'cancel'],
  props: {
    foodCategoryId: {
      type: [String, Number],
      default: undefined,
    },
  },
  components: {
    'van-picker': Picker,
  },
  created() {
    this.getStoreHouseList();
  },
  data() {
    return {
      originData: [],
      fieldNames: {
        text: 'houseAndCargo',
        value: 'id',
      },
    };
  },
  computed: {
    ...mapState({
      user: (state) => state.user.info,
    }),
  },
  methods: {
    async getStoreHouseList() {
      const dept = this.user?.dept;
      this.originData = [];
      const list = await findAvailableStoreHouseCargoSpace(
        dept?.id,
        this.foodCategoryId ? this.foodCategoryId : null,
      );
      this.originData = list.map((item) => {
        return {
          ...item,
          id: item.cargoSpaceId,
          name: item.cargoSpaceName,
          houseAndCargo: `${item.storeHouseName}_${item.cargoSpaceName}`,
        };
      });
    },
    onCancel() {
      this.$emit('cancel');
    },
    onConfirm(value) {
      this.$emit('confirm', value);
    },
  },
  watch: {
    foodCategoryId(newValue, oldValue) {
      if (newValue != oldValue) {
        this.$nextTick(() => {
          this.getStoreHouseList();
        });
      }
    },
  },
};
</script>
