{"name": "com.hxid.foodreserves", "displayName": "foodbase", "version": "1.0.0", "description": "A sample Apache Cordova application that responds to the deviceready event.", "main": "index.js", "scripts": {"test": "echo \"Error: no test specified\" && exit 1"}, "keywords": ["ecosystem:cordova"], "author": "Apache Cordova Team", "license": "Apache-2.0", "devDependencies": {"cordova-android": "^10.1.2", "cordova-base64-to-gallery-castle": "^4.1.5", "cordova-plugin-android-permissions": "^1.1.3", "cordova-plugin-android-update": "^2.0.2", "cordova-plugin-appversion": "^1.0.0", "cordova-plugin-camera": "^6.0.0", "cordova-plugin-file": "^7.0.0", "cordova-plugin-statusbar": "^3.0.0", "foolnofool-cordova-plugin-file-transfer": "^2.2.0", "phonegap-plugin-barcodescanner": "^8.1.0"}, "cordova": {"platforms": ["android"], "plugins": {"cordova-plugin-statusbar": {}, "phonegap-plugin-barcodescanner": {"ANDROID_SUPPORT_V4_VERSION": "27.+"}, "cordova-plugin-file": {"ANDROIDX_WEBKIT_VERSION": "1.4.0"}, "cordova-plugin-camera": {"ANDROIDX_CORE_VERSION": "1.6.+"}, "cordova-plugin-android-permissions": {}, "foolnofool-cordova-plugin-file-transfer": {}, "cordova-plugin-android-update": {}, "cordova-plugin-file-transfer": {}, "cordova-base64-to-gallery-castle": {}}}}