<template>
  <div class="control">
    <header>
      <span>云台控制</span>
      <!-- <img id="close_button_${key}" src="@/components/Videojs/images/close.png" class="close_icon" /> -->
    </header>
    <main>
      <div class="center-btn">
        <img
          v-for="item in controlStyle"
          :key="item.imgUrl"
          :src="item.imgUrl"
          :style="item.style"
          @click="control(item.action)"
        />
      </div>
    </main>
    <footer>
      <Button type="primary" color="rgba(25, 157, 191, 0.2)" icon="plus" @click="control('zoomin')"
        >放大</Button
      >
      <Button
        type="primary"
        color="rgba(25, 157, 191, 0.2)"
        icon="minus"
        @click="control('zoomout')"
        >缩小</Button
      >
    </footer>
  </div>
</template>

<script setup>
import { Button } from 'vant';
import { reactive } from 'vue';
import { cloudControl, moveCamera } from '@/api/control.js';
const props = defineProps({
  config: Object,
});
let controlStyle = reactive([
  {
    imgUrl: require('@/components/HVideo/images/btnTop.png'),
    action: 'up',
    style: {
      width: '20vw',
      height: '10vw',
      position: 'absolute',
      top: '6vw',
      left: '10vw',
      background: 'border-box',
    },
  },
  {
    imgUrl: require('@/components/HVideo/images/btnRight.png'),
    action: 'right',
    style: {
      width: '10vw',
      height: '20vw',
      position: 'absolute',
      right: '4vw',
      top: '12vw',
      background: 'border-box',
    },
  },
  {
    imgUrl: require('@/components/HVideo/images/btnBottom.png'),
    action: 'down',
    style: {
      width: '20vw',
      height: '10vw',
      position: 'absolute',
      bottom: '2vw',
      left: '10vw',
      background: 'border-box',
    },
  },
  {
    imgUrl: require('@/components/HVideo/images/btnleft.png'),
    action: 'left',
    style: {
      width: '10vw',
      height: '20vw',
      position: 'absolute',
      left: '4vw',
      top: '12vw',
      background: 'border-box',
    },
  },
]);

console.log(controlStyle, props);

/**
 * 云台控制
 * @param { String } playFlag 根据该字段判断是否调用海康平台接口
 * @param { String } action 触发的动作
 */
const control = async (action) => {
  const isFLag = props.config.playFlag == 1;
  const api = isFLag ? moveCamera : cloudControl;
  const { cameraPassword, cameraUserName, rmDeviceCameraId, serviceIp, equipmentCode } =
    props.config;
  let params = isFLag
    ? {
        cameraPassword,
        cameraUserName,
        rmDeviceCameraId,
        serviceIp,
        code: equipmentCode,
        command: action,
        speed: '0.15',
      }
    : {
        code: equipmentCode,
        command: action,
        speed: '0.15',
      };
  await api(params);
};
</script>

<style scoped lang="scss">
.control {
  width: 100%;
  height: 100%;
  background: #093655;
  padding: 0 2vw;
  box-sizing: border-box;

  header {
    width: 100%;
    height: 10vw;
    display: flex;
    align-items: center;
    justify-content: space-between;

    span {
      color: #fff;
    }

    img {
      width: 8vw;
      height: 8vw;
    }
  }

  main {
    width: 100%;
    height: 44vw;
    display: flex;
    align-items: center;
    justify-content: center;

    .center-btn {
      width: 40vw;
      height: 40vw;
      position: relative;

      img:active {
        filter: drop-shadow(0 0 0 #20c2ff);
      }
    }
  }
}

footer {
  width: 100%;
  height: 20vw;
  display: flex;
  align-items: center;
  justify-content: space-around;
  --van-button-primary-border: 1px solid #07bbfc;
}
</style>
