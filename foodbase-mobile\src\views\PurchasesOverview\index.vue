<template>
  <div class="purchases-navigation">
    <div class="background"></div>
    <div class="links">
      <RouterLink :to="{ name: 'OrderPublicList' }" class="link">
        <div class="icon icon-order"></div>
        <span class="name">订单清册</span>
        <Icon name="arrow" />
      </RouterLink>
      <RouterLink :to="{ name: 'AppointmentList' }" class="link" v-if="false">
        <div class="icon icon-date"></div>
        <span class="name">预约查看</span>
        <Icon name="arrow" />
      </RouterLink>
      <RouterLink :to="{ name: 'PurchasesProgress' }" class="link" v-if="level != '5'">
        <div class="icon icon-calendar"></div>
        <span class="name">收购进度</span>
        <Icon name="arrow" />
      </RouterLink>
      <RouterLink :to="{ name: 'OrderSearch' }" class="link" v-if="level != '5'">
        <div class="icon icon-order-search"></div>
        <span class="name">订单查询</span>
        <Icon name="arrow" />
      </RouterLink>
    </div>
  </div>
</template>

<script setup>
import { Icon } from 'vant';
import { useStore } from 'vuex';

const store = useStore();

const level = store.state.user?.reserverIds?.level;
// const isCompanyUser = store.getters['user/isCompanyUser'];
</script>

<style scoped lang="scss">
.purchases-navigation {
  position: relative;
  .background {
    position: absolute;
    width: 100%;
    height: 165px;
    background-image: url('./assets/bg-purchases.png');
    background-size: cover;
    background-repeat: no-repeat;
  }

  .links {
    position: relative;
    padding: 120px 16px 0;

    .link {
      height: 100px;
      background-image: url('./assets/bg-link.png');
      background-repeat: no-repeat;
      background-size: 110%;
      background-position: center center;
      margin-bottom: 16px;
      box-shadow: rgba(99, 99, 99, 0.2) 0 2px 8px 0;
      border-radius: 4px;
      display: flex;
      font-size: 24px;
      font-weight: 500;
      color: #363a44;
      align-items: center;
      padding: 0 16px;
    }

    .icon {
      height: 72px;
      width: 72px;
      background-position: center;
      background-size: 100%;
      background-repeat: no-repeat;
      transform: translateY(2px);

      &.icon-order {
        background-image: url('./assets/icon-order.svg');
      }
      &.icon-date {
        background-image: url('./assets/icon-date.svg');
      }
      &.icon-calendar {
        background-image: url('./assets/icon-calendar.svg');
      }
      &.icon-order-search {
        background-image: url('./assets/icon-order-search.svg');
      }
    }

    .name {
      margin-left: 12px;
    }

    .van-icon {
      margin-left: auto;
    }
  }
}
</style>
