<template>
  <div class="checkinout-detail-item">
    <div class="title">
      <short-schedule-no class="code">{{ item.schedulingNo }}</short-schedule-no>
      <div class="type-btn" :class="[typer == '1' ? 'btn-check-in' : 'btn-check-out']">
        {{ typerName }}
      </div>
    </div>
    <div class="content">
      <div class="content-item">
        <div class="item-label">运输方式：</div>
        <div class="item-value">{{ item.typeShippingName || '-' }}</div>
      </div>
      <div class="content-item">
        <div class="item-label">车船号：</div>
        <div class="item-value">{{ item.transportVehicleNo || '-' }}</div>
      </div>
      <div class="content-item">
        <div class="item-label">承运人/联系电话：</div>
        <div class="item-value">
          {{ item.transportVehicleDriver || '-' }} / {{ item.transportVehicleTel || '-' }}
        </div>
      </div>
      <div class="content-item">
        <div class="item-label">预约号：</div>
        <div class="item-value">{{ item.flowBookingNo || '-' }}</div>
      </div>
      <div class="content-item">
        <div class="item-label">卡号：</div>
        <div class="item-value">{{ item.cardNumber || '-' }}</div>
      </div>
      <div class="content-item">
        <div class="item-label">登记时间：</div>
        <div class="item-value">{{ item.registrationTime || '-' }}</div>
      </div>
    </div>
    <div class="action">
      <div class="action-btn action-btn-block" @click="handleChangeDialog">调整登记信息</div>
      <div class="action-btn action-btn-default" @click="showQrCode">查看调度二维码</div>
    </div>
    <Dialog
      v-model:show="showChangeDialog"
      show-cancel-button
      title="调整登记信息"
      @cancel="showInspectionPicker = false"
      @confirm="handleConfirm"
    >
      <van-form ref="form">
        <van-field :border="false" readonly v-model="schedulingNoShort" label="调度号" />
        <van-field :border="false" readonly :model-value="typerName" label="登记类型" />
        <van-field :border="false" readonly v-model="typeShippingName" label="运输方式" />
        <van-field
          :border="false"
          v-model="vehicleNo"
          :rules="[{ required: true, message: '请输入车船号' }]"
          required
          label="车船号"
          placeholder="输入车船号"
          clearable
          focus
        />
      </van-form>
    </Dialog>
  </div>
</template>
<script>
import ShortScheduleNo from './ShortScheduleNo.vue';
import { Dialog, Form, Field, Toast } from 'vant';
import { UpdateCheckInOutDetail } from '@/api/in-out-manage';

export default {
  name: 'CheckInoutDetailItem',
  components: {
    ShortScheduleNo,
    Dialog: Dialog.Component,
    'van-form': Form,
    'van-field': Field,
  },
  props: {
    typer: [String],
    typerName: [String],
    item: [Object],
  },
  computed: {
    buzTypeName() {
      return this.item.buzTypeName;
    },
    typeShippingName() {
      return this.item.typeShippingName;
    },
    schedulingNoShort() {
      return this.item.schedulingNo?.split('_')[0];
    },
    transportVehicleNo() {
      return this.item.transportVehicleNo;
    },
  },
  data() {
    return {
      showChangeDialog: false,
      message: '',
      vehicleNo: '',
    };
  },
  methods: {
    showQrCode() {
      this.$router.push({
        path: '/check-inout/check-in-out-qrCode',
        query: {
          schedulingNo: this.item.schedulingNo,
        },
      });
    },
    async handleConfirm() {
      await UpdateCheckInOutDetail({
        id: this.item.id,
        transportVehicleNo: this.vehicleNo,
      });
      Toast.success('调整成功');
      this.$emit('handleRefresh');
    },
    handleChangeDialog() {
      this.showChangeDialog = true;
      this.vehicleNo = this.transportVehicleNo;
    },
    handleClose() {
      this.showChangeDialog = false;
    },
  },
};
</script>
<style lang="scss" scoped>
.checkinout-detail-item {
  background: #fff;
  margin-top: 10px;
  padding: 16px 16px 12px 16px;

  .title {
    margin-bottom: 16px;
    display: flex;
    align-items: center;
    .code {
      font-size: 16px;
      font-weight: bold;
      color: #232323;
      margin-right: 10px;
    }
    .type-btn {
      font-size: 14px;
      padding: 2px 12px;
      border-radius: 2px;
      color: #fff;
      font-family: PingFangSC-Medium, PingFang SC;
    }
    .btn-check-in {
      background: #1973f1;
    }
    .btn-check-out {
      background: #14c287;
    }
  }
  .content {
    border-bottom: 1px solid #e8e9ec;
    .content-item {
      display: flex;
      margin-bottom: 12px;
      .item-label {
        font-size: 16px;
        color: #686b73;
        min-width: 140px;
      }
      .item-value {
        color: #232323;
      }
    }
  }
  .action {
    display: flex;
    justify-content: space-between;
    align-items: center;
    font-size: 14px;
    margin-top: 12px;
    .action-btn {
      padding: 4px 12px;
      border-radius: 2px;
    }
    .action-btn-block {
      color: #1973f1;
      border: 1px solid #1973f1;
    }
    .action-btn-default {
      color: #fff;
      background: #1973f1;
    }
  }
}
</style>
