<template>
  <div>
    <HCard class="detail-card">
      <div class="type">2023第5次招标采购</div>
      <div class="card-content">
        <div class="row">
          <span class="label">竞价时间：</span>
          <span class="value">2023-08-17</span>
        </div>
        <div class="row">
          <span class="label">轮换开始时间：</span>
          <span class="value">2023-08-18</span>
        </div>
        <div class="row">
          <span class="label">轮换结束时间：</span>
          <span class="value">2023-08-18</span>
        </div>
      </div>
    </HCard>
    <div class="application">
      <van-tabs v-model:active="approval">
        <van-tab title="申请信息"></van-tab>
      </van-tabs>
    </div>
    <div v-if="approval == 0">
      <div class="fade-out">
        <img src="@/assets/icon-liangzhang.png" alt="" />
        <div class="fade-text">计划输出</div>
      </div>
      <div class="fade-detail">
        <div class="fade-head">浙江省储备粮管理有限公司</div>
        <div class="fade-tag">
          <van-tag color="#8797AF" style="margin-right: 6px">小麦</van-tag>
          <van-tag type="primary">省级储备</van-tag>
        </div>
        <div class="fade-details">
          <div class="fade-youtube">
            <div class="fade-youtubebtn">库点：</div>
            <div class="fade-youtubeout">浙江方舟粮食仓储有限公司</div>
          </div>
          <div class="fade-youtube">
            <div class="fade-youtubebtn">仓廒_货拉：</div>
            <div class="fade-youtubeout">P2</div>
          </div>
          <div class="fade-youtube">
            <div class="fade-youtubebtn">轮出数量：</div>
            <div class="fade-youtubeout">1000吨</div>
          </div>
        </div>
      </div>
      <div class="fade-detail">
        <div class="fade-head">浙江省储备粮管理有限公司</div>
        <div class="fade-tag">
          <van-tag color="#8797AF" style="margin-right: 6px">小麦</van-tag>
          <van-tag type="primary">省级储备</van-tag>
        </div>
        <div class="fade-details">
          <div class="fade-youtube">
            <div class="fade-youtubebtn">库点：</div>
            <div class="fade-youtubeout">浙江方舟粮食仓储有限公司</div>
          </div>
          <div class="fade-youtube">
            <div class="fade-youtubebtn">仓廒_货拉：</div>
            <div class="fade-youtubeout">P2</div>
          </div>
          <div class="fade-youtube">
            <div class="fade-youtubebtn">轮出数量：</div>
            <div class="fade-youtubeout">1000吨</div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref } from 'vue';

const approval = ref(0);
</script>

<style lang="scss" scoped>
.detail-card {
  padding: 16px;
  margin-bottom: 10px;

  .type {
    font-size: 16px;
    font-weight: bold;
    color: #232323;
  }
}

.total {
  padding: 10px 0 10px 16px;
  color: #232323;
  font-size: 14px;

  span {
    color: #1492ff;
  }
}

.card-head {
  display: flex;
  align-items: center;
  justify-content: space-between;
  font-size: 18px;
  font-weight: bold;
  color: #1f3359;

  .next {
    color: rgba(109, 116, 141, 0.6);
  }
}

.schedule-num {
  flex-grow: 1;
  margin-left: 12px;
}

.card-content {
  margin-top: 8px;
  line-height: 30px;

  // border-bottom: 1px solid #e8e9ec;
  .row {
    display: flex;

    .label {
      font-size: 14px;
      // font-weight: 500;
      width: 112px;
      color: #686b73;
      // flex-basis: 6em;
    }

    .value {
      font-size: 14px;
      font-weight: 400;
      color: #232323;
      flex-grow: 1;
    }

    .carNum {
      padding: 3px 6px;
      background: #f4f4f4;
      border-radius: 2px;
      border: 1px solid #ebedf0;
      margin-right: 5px;
    }
  }
}
.application {
  width: 100%;
  height: 44px;
  margin-top: 10px;
  background-color: #fff;
  font-size: 16px;
}
.fade-out {
  height: 54px;
  border-top: 1px solid #dddddd;
  border-bottom: 1px solid #dddddd;
  background-color: #fff;
  display: flex;
  flex-direction: row;
  img {
    width: 20px;
    height: 20px;
    margin: 16px 16px;
  }
}
.fade-text {
  padding-left: 1px;
  font-size: 16px;
  margin-top: 15px;
  font-weight: bold;
}
.fade-detail {
  height: 182px;
  background-color: #fff;
  font-size: 16px;
  margin-bottom: 10px;
  .fade-head {
    font-weight: bold;
    padding-left: 16px;
    padding-top: 12px;
  }
  .fade-tag {
    padding-left: 16px;
    margin-top: 12px;
  }
  .fade-details {
    padding-left: 16px;
    padding-top: 12px;
    padding-bottom: 12px;
    margin-bottom: 10px;
    .fade-youtube {
      display: flex;
      flex-direction: row;
      padding-bottom: 12px;
      .fade-youtubebtn {
        width: 89px;
        height: 20px;
        color: #686b73;
      }
      .fade-youtubeout {
        padding-left: 16px;
      }
    }
  }
}
</style>
