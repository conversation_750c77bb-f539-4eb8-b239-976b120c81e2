<template>
  <div class="check-in in-out-manage-page">
    <van-form ref="form">
      <div class="warpper">
        <van-field
          v-model="form.bookingNo"
          label="预约号:"
          placeholder="扫码或输入"
          ref="bookingField"
          @blur="onBookingNoChange"
        >
          <template #right-icon>
            <div class="scan-action" @click="handleScan">
              <svg-icon name="scan" />
            </div>
          </template>
        </van-field>
        <van-field v-model="form.cardNumber" label="卡号:" placeholder="请输入"></van-field>
      </div>
      <div class="card">
        <van-field
          v-model="shippingTypeText"
          label="运输方式:"
          is-link
          readonly
          name="signPicker"
          placeholder="选择运输方式"
          @click="showShippingTypePicker = true"
        >
        </van-field>
        <van-popup v-model:show="showShippingTypePicker" position="bottom">
          <biz-dict-picker
            dict="SHIPPING_TYPE"
            title="运输方式:"
            @cancel="showShippingTypePicker = false"
            @confirm="onShippingTypeConfirm"
          ></biz-dict-picker>
        </van-popup>
        <van-field
          v-if="form.typeShipping == '2' || form.typeShipping == '3'"
          v-model="form.entryTime"
          label="到库时间"
          is-link
          readonly
          name="timePicker"
          :rules="[
            {
              required: form.typeShipping == '2' || form.typeShipping == '3',
              message: '请选择到库时间',
            },
          ]"
          required
          placeholder="请选择"
          @click="showTimePicker = true"
        >
        </van-field>
        <van-popup v-model:show="showTimePicker" position="bottom">
          <van-datetime-picker
            type="datetime"
            v-model="selectedTime"
            :formatter="timeFormatter"
            @confirm="onTimeConfirm"
            @cancel="showTimePicker = false"
          ></van-datetime-picker>
        </van-popup>
        <van-field
          v-if="form.typeShipping === '1'"
          v-model="form.transportVehicleNo"
          label="车牌号:"
          :rules="rules.transportVehicleNo"
          required
          placeholder="请输入"
        ></van-field>
        <van-field
          v-if="form.typeShipping === '1'"
          v-model="form.transportTrailerNo"
          label="挂车号:"
          :error-message="trailerNoErr"
          placeholder="请输入"
          @blur="checkTrailerNo"
        ></van-field>
        <van-field
          v-if="form.typeShipping !== '1'"
          v-model="form.transportVehicleNo"
          :label="typeLabel"
          :rules="rules.transportVehicleNoOther"
          required
          placeholder="请输入"
        ></van-field>
        <van-field
          v-model="signText"
          label="标识符:"
          is-link
          readonly
          name="signPicker"
          placeholder="请选择"
          @click="showSignPicker = true"
        >
        </van-field>
        <van-popup v-model:show="showSignPicker" position="bottom">
          <biz-dict-picker
            dict="CAR_SYMBOL_TYPE"
            title="标识符:"
            @cancel="showSignPicker = false"
            @confirm="onSignConfirm"
          ></biz-dict-picker>
        </van-popup>
      </div>
      <div class="warpper">
        <van-field
          v-model="form.transportVehicleDriver"
          label="承运人:"
          placeholder="请输入"
          :rules="rules.transportVehicleDriver"
          required
        ></van-field>
        <van-field
          v-model="form.transportVehicleId"
          label="身份证号:"
          placeholder="请输入"
          :rules="rules.transportVehicleId"
          required
        ></van-field>
        <van-field
          v-model="form.transportVehicleTel"
          label="联系电话:"
          placeholder="请输入"
          :rules="rules.transportVehicleTel"
          required
        ></van-field>
        <van-field
          v-model="form.transportVehicleAddress"
          label="联系地址:"
          placeholder="请输入"
        ></van-field>
        <van-field name="uploader" center label="车辆照片：">
          <template #input>
            <Uploader
              :max-count="1"
              :model-value="carFileList"
              multiple
              accept=".jpg, .jpeg, .png"
              :before-read="beforeRead"
              class="upload-cls"
              @click-upload="(e) => onClickUpload(e, 'carFileList')"
              @delete="onDeleteFile(file, { index }, 'carFileList')"
            />
          </template>
        </van-field>

        <van-field name="uploader" center label="承运人照片：">
          <template #input>
            <Uploader
              :max-count="1"
              :model-value="carrierFileList"
              accept=".jpg, .jpeg, .png"
              multiple
              :before-read="beforeRead"
              class="upload-cls"
              @click-upload="(e) => onClickUpload(e, 'carrierFileList')"
              @delete="onDeleteFile(file, { index }, 'carrierFileList')"
            />
          </template>
        </van-field>
      </div>
      <div class="warpper">
        <van-field
          v-model="displaySchedulingNo"
          label="调度号:"
          :rules="rules.displaySchedulingNo"
          required
          readonly
        >
          <template #button>
            <van-button
              v-if="!displaySchedulingNo"
              size="small"
              type="primary"
              @click="onGetSchedulingNo"
            >
              获取调度号
            </van-button>
            <van-button v-else size="small" type="primary" @click="onShowTicket">
              生成二维码
            </van-button>
          </template>
        </van-field>
      </div>
      <div class="remark">注：完成到库登记，可进入调度环节</div>
      <div class="bottom-warpper">
        <van-button class="button" @click="onFinishCheckIn" :loading="confirmLoading">
          完成到库登记
        </van-button>
        <div class="bar"></div>
      </div>
      <div class="bottom-warpper">
        <van-button class="button" @click="onFinishCheckIn" :loading="confirmLoading">
          完成到库登记
        </van-button>
        <div class="bar"></div>
      </div>
    </van-form>
    <scheduling-ticket v-model:show="visible" :records="schedulingTickets"></scheduling-ticket>
    <ActionSheet
      v-model:show="showActionSheet"
      cancel-text="取消"
      description="请选择上传方式"
      :actions="actions"
      @select="onUploadActionSelect"
      close-on-click-action
    />
  </div>
</template>

<script>
import { Form, Field, Button, Toast, Popup, DatetimePicker, Uploader, ActionSheet } from 'vant';
import { getShortSchedulingNo, timeFormatter } from '@/utils/inout-manage';
import {
  getBookingInfo,
  generateSerialNo,
  getSchedulingNo,
  checkIn,
  getFileUrls,
} from '@/api/in-out-manage';
import dayjs from 'dayjs';
import { BizDictPicker, SchedulingTicket } from '@/views/InoutManage/common';
import SvgIcon from '@/components/SvgIcon/SvgIcon';
import { getReserverToken } from '@/utils/auth';

const transportVehicleNoReg =
  /^([京津晋冀蒙辽吉黑沪苏浙皖闽赣鲁豫鄂湘粤桂琼渝川贵云藏陕甘青宁新]){1}([A-Z]|[0-9]){6,7}$/;
const transportTrailerNoReg =
  /^([京津晋冀蒙辽吉黑沪苏浙皖闽赣鲁豫鄂湘粤桂琼渝川贵云藏陕甘青宁新]){1}[A-Z][A-Z0-9]{4}[挂]$/;
const telReg =
  /^$|^\d{3}-\d{8}$|^\d{4}-\d{7,8}$|^(13[0-9]|14[01456879]|15[0-35-9]|16[2567]|17[0-8]|18[0-9]|19[0-35-9])\d{8}$/;
const idCardReg =
  /^[1-9]\d{5}(18|19|20)\d{2}((0[1-9])|(1[0-2]))(([0-2][1-9])|10|20|30|31)\d{3}[0-9Xx]$/;

export default {
  name: 'CheckIn',
  components: {
    'van-form': Form,
    'van-field': Field,
    'van-button': Button,
    'van-popup': Popup,
    'van-datetime-picker': DatetimePicker,
    BizDictPicker,
    SchedulingTicket,
    SvgIcon,
    Uploader,
    ActionSheet,
  },
  data() {
    return {
      form: {
        schedulingList: [],
        bookingNo: null,
        cardNumber: null,
        typeShipping: '1',
        transportVehicleNo: null,
        transportTrailerNo: null,
        transportVehicleSign: '01', // 车牌标识符，01，黄底黑字；02，蓝底白字；03，绿底黑字；04，绿底白字；LS，临时虚拟号牌；
        transportVehicleDriver: null,
        transportVehicleId: null,
        transportVehicleTel: null,
        transportVehicleAddress: null,
        schedulingNo: '',
        entryTime: dayjs().format('YYYY-MM-DD HH:mm'),
      },
      signText: '黄底黑字',
      shippingTypeText: '汽运',
      timeout: null,
      focusVehicleNo: false,
      focusTrailerNo: false,
      confirmLoading: false,
      showTimePicker: false,
      showSignPicker: false,
      showShippingTypePicker: false,
      selectedTime: new Date(),
      visible: false,
      trailerNoErr: '',
      typeShippingOptions: [],

      rules: {
        transportVehicleNo: [
          { required: true, message: '请输入车牌号' },
          { pattern: transportVehicleNoReg, message: '请输入正确的车牌号' },
        ],
        transportVehicleNoOther: [{ required: true, message: '请输入牌号' }],
        transportVehicleDriver: [{ required: true, message: '请输入承运人' }],
        transportVehicleId: [
          { required: true, message: '请输入身份证号' },
          { pattern: idCardReg, message: '请输入正确的身份证号' },
        ],
        transportVehicleTel: [
          { required: true, message: '请输入联系电话' },
          { pattern: telReg, message: '请输入正确的联系方式' },
        ],
        displaySchedulingNo: [{ required: true, message: '请获取调度号' }],
      },
      isSearch: false,
      carFileList: [],
      carrierFileList: [],
      showActionSheet: false,
      actions: [{ name: '拍照' }, { name: '相册选择' }],
      picType: null,
      transportVehiclePhoto: '', //车辆照片id
      carrierPhoto: '', //承运人照片id
      fileItem: {},
    };
  },
  computed: {
    displaySchedulingNo() {
      const { schedulingNo } = this.form;
      if (!schedulingNo) {
        return '';
      }
      return getShortSchedulingNo(schedulingNo);
    },
    schedulingTickets() {
      const { schedulingList, transportVehicleNo } = this.form;
      const time = dayjs().format('YYYY-MM-DD HH:mm:ss');

      console.log(schedulingList, 'schedulingList-----');
      return schedulingList.map((it) => {
        return {
          ...it,
          transportVehicleNo: transportVehicleNo,
          time: time,
        };
      });
    },
    typeLabel() {
      const obj = {
        2: '船号',
        3: '火车号',
        4: '其他',
      };
      return obj[this.form.typeShipping];
    },
  },
  methods: {
    timeFormatter,
    getReserverToken,
    checkTrailerNo() {
      let data = this.form.transportTrailerNo.trim();
      if (data && !transportTrailerNoReg.test(data)) {
        this.trailerNoErr = '请输入正确的挂车号';
      } else {
        this.trailerNoErr = null;
      }
    },
    onBookingNoChange(e) {
      const value = e.target.value;
      if (!value) {
        return;
      }
      clearTimeout(this.timeout);
      this.timeout = setTimeout(async () => {
        const bookingNo = value.trim();
        try {
          const data = await getBookingInfo(bookingNo);
          const serialNo = await generateSerialNo();
          const {
            buzType,
            buzTypeName,
            customer,
            bookingId,
            schedulingNo,
            transportVehicleDriver,
            transportVehicleTel,
            idcard,
            transportVehicleNo,
          } = data;

          if (!schedulingNo) {
            Toast.fail('无效的预约号');
            return;
          }
          this.form.schedulingList = [
            {
              flowBookingId: bookingId,
              buzType,
              buzTypeName,
              customer,
              schedulingNo,
              bookingNo,
              serialNo,
            },
          ];
          this.form.transportVehicleDriver = transportVehicleDriver;
          this.form.transportVehicleTel = transportVehicleTel;
          this.form.transportVehicleId = idcard;
          this.form.schedulingNo = schedulingNo;
          if (!this.form.transportVehicleNo) {
            this.form.transportVehicleNo = transportVehicleNo;
          }
        } catch (error) {
          this.form.schedulingList = [];
        }
      }, 1000);
    },
    onSignConfirm(value) {
      this.signText = value.label;
      this.form.transportVehicleSign = value.value;
      this.showSignPicker = false;
    },
    onShippingTypeConfirm(value) {
      this.shippingTypeText = value.label;
      this.form.typeShipping = value.value;
      this.showShippingTypePicker = false;
    },
    onTimeConfirm(value) {
      this.form.entryTime = dayjs(value).format('YYYY-MM-DD HH:mm');
      this.selectedTime = new Date(value);
      this.showTimePicker = false;
    },
    async onGetSchedulingNo() {
      if (!this.isSearch) {
        this.isSearch = true;
        try {
          const schedulingNo = await getSchedulingNo();
          const serialNo = await generateSerialNo();
          this.form.schedulingList.push({
            flowBookingId: '',
            buzType: null,
            buzTypeName: '',
            customer: '',
            bookingNo: '',
            schedulingNo,
            serialNo,
          });
          this.form.schedulingNo = schedulingNo;
        } catch (e) {
          Toast.fail('获取调度号错误');
        }
        setTimeout(() => {
          this.isSearch = false;
        }, 1000);
      }
    },
    onShowTicket() {
      this.visible = true;
    },
    async onFinishCheckIn() {
      this.confirmLoading = true;
      try {
        await this.$refs.form.validate();
        const { schedulingList, entryTime, ...restData } = this.form;
        let formattedEntryTime = dayjs(entryTime).format('YYYY-MM-DD HH:mm:ss');
        const data = schedulingList.map((schedulingItem) => {
          return {
            entryTime: formattedEntryTime,
            cardNumber: this.cardNum ? this.cardNum : null,
            ...restData,
            ...schedulingItem,
            transportVehiclePhoto: this.transportVehiclePhoto,
            carrierPhoto: this.carrierPhoto,
          };
        });
        await checkIn(data);

        Toast.success('到库登记成功');
        this.resetForm();
        this.confirmLoading = false;
      } catch (error) {
        this.confirmLoading = false;
      }
    },
    resetForm() {
      this.form = {
        schedulingList: [],
        bookingNo: null,
        cardNumber: null,
        typeShipping: '1',
        transportVehicleNo: null,
        transportVehicleSign: '01',
        transportVehicleDriver: null,
        transportVehicleId: null,
        transportVehicleTel: null,
        transportVehicleAddress: null,
        schedulingNo: '',
        entryTime: dayjs().format('YYYY-MM-DD HH:mm'),
      };
      this.signText = '黄底黑字';
      this.selectedTime = new Date();
    },
    handleScan() {
      window.cordova?.plugins.barcodeScanner.scan(
        (result) => {
          if (result.text) {
            this.form.bookingNo = result.text;
            this.onBookingNoChange({ target: { value: this.form.bookingNo } });
          }
        },
        (error) => {
          alert('扫码失败 ' + error);
        },
        {
          prompt: '请将二维码放在扫码框中',
          resultDisplayDuration: 300,
        },
      );
    },
    onClickUpload(e, pic) {
      this.picType = pic;
      e.preventDefault();
      if (navigator.camera) {
        this.showActionSheet = true;
      } else {
        Toast('不支持选择照片');
      }
    },
    beforeRead(file) {
      const maxSize = 5 * 1024 * 1024; // 5 MB
      if (!(file.type.includes('jpg') || file.type.includes('jpeg') || file.type.includes('png'))) {
        Toast('请上传 jpg, png, jpeg 格式图片');
        return false;
      }
      if (file.size > maxSize) {
        Toast('文件大小不能超过5MB');
        return false; // 阻止文件上传
      }
      return true;
    },
    onDeleteFile(file, { index }, type) {
      if (type === 'carFileList') {
        this.carFileList.splice(index, 1);
      } else {
        this.carrierFileList.splice(index, 1);
      }
    },
    onUploadActionSelect({ name }) {
      const Camera = window.Camera;
      let sourceType = Camera.PictureSourceType.PHOTOLIBRARY;
      if (name === '拍照') {
        sourceType = Camera.PictureSourceType.CAMERA;
      } else if (name === '相册选择') {
        sourceType = Camera.PictureSourceType.SAVEDPHOTOALBUM;
      }
      navigator.camera.getPicture(
        (imageUri) => {
          const fileName = imageUri.substr(imageUri.lastIndexOf('/') + 1);

          const isImageFile = (fileName) => {
            const fileTypes = ['.jpg', '.jpeg', '.png'];
            const fileExtension = fileName.split('.').pop().toLowerCase();
            return fileTypes.includes('.' + fileExtension);
          };

          if (!isImageFile(fileName)) {
            Toast('请上传 jpg, png, jpeg 格式图片');
            return;
          }
          // 添加图片到图片列表
          this.fileItem = {
            url: imageUri,
            isImage: true,
            status: 'uploading',
            deletable: true,
          };
          if (this.picType === 'carFileList') {
            this.carFileList.push(this.fileItem);
          } else {
            this.carrierFileList.push(this.fileItem);
          }

          // 上传参数
          const options = new window.FileUploadOptions();
          options.fileKey = 'file';
          options.fileName = fileName;
          options.headers = {
            Authorization: this.getReserverToken(),
          };
          const params = {};
          params.coder = 'sgcb/storehouse_document';
          options.params = params;

          // 上传地址
          const reserverBaseUrl = () =>
            JSON.parse(process.env.VUE_APP_RESERVER_API_BASE_URL)[window.__isProd] ||
            location.origin;
          const uploadUri = new URL(
            '/reserver/api/fileManager/uploadFileBatch',
            reserverBaseUrl(),
          ).toString();

          console.log(imageUri, 'imageUri');
          console.log(options, 'options');
          // 上传文件
          const fileTransfer = new window.FileTransfer();
          fileTransfer.upload(
            imageUri,
            uploadUri,
            (res) => {
              console.log(res, 'res');
              // 上传成功
              const resp = res.response;
              if (resp) {
                const respJson = JSON.parse(resp);
                const { data } = respJson;
                if (this.picType === 'carFileList') {
                  this.transportVehiclePhoto = data;
                } else {
                  this.carrierPhoto = data;
                }
                this.fileItem.name = data.split(',')[0];
                this.fileItem.status = 'done';

                getFileUrls(data).then((urls) => {
                  this.fileItem.url = urls[0];
                });
              }
            },
            (error) => {
              // 上传失败
              this.fileItem.status = 'failed';
              Toast('上传失败');
              console.error(error);
            },
            options,
          );
        },
        (err) => {
          Toast('选择图片失败');
          console.error(err);
        },
        {
          quality: 85,
          destinationType: Camera.DestinationType.FILE_URI,
          sourceType: sourceType,
        },
      );
    },
  },
};
</script>

<style lang="scss" scoped>
@import '@/views/InoutManage/style/index';
.scan-action {
  font-size: 16px;
  height: 16px;
  display: flex;
  align-items: center;
  padding: 0 10px;
}
.check-in {
  .remark {
    margin-bottom: 110px;
    font-size: 14px;
    font-weight: 400;
    color: #6d748d;
    line-height: 20px;
    margin-left: 20px;
  }
  ::v-deep(.van-dialog) {
    background: transparent !important;
  }
  .carNum {
    padding: 3px 6px;
    background: #f4f4f4;
    border-radius: 2px;
    border: 1px solid #ebedf0;
    margin-right: 5px;
  }
}
::v-deep .scheduling-ticket {
  overflow: auto !important;
}
</style>
