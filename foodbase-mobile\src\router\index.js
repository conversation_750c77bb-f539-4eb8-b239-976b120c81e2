import { createRouter, createWebHashHistory } from 'vue-router';
import store from '@/store';
import Home from '@/views/Home';
import ReserveHome from '@/views/ReserveHome';
import cloneReserveHome from '@/views/cloneReserveHome';
import Login from '@/views/Auth/Login';
import Logout from '@/views/Auth/Logout';
import RouterViewContainer from '@/views/common/RouteViewContainer';
import PageHeaderLayout from '@/layouts/PageHeaderLayout';
import { CreateKeepAliveRouterView } from '@/views/common/KeepAliveRouterViewContainer';
import { h } from 'vue';
import ScanDeviceDetail from '@/views/SafetyProductionManagement/components/ScanDeviceDetail.vue';
// import { getHxdiframeworkToken } from '@/utils/auth';

const routes = [
  {
    path: '/',
    name: 'Home',
    component: Home,
  },
  {
    path: '/r',
    name: 'ReserveHome',
    component: ReserveHome,
  },
  {
    path: '/login',
    name: 'Login',
    component: Login,
  },
  {
    path: '/logout',
    name: '<PERSON>gout',
    component: Logout,
  },
  {
    path: '/datav',
    name: 'DataV',
    component: PageHeaderLayout,
    children: [
      {
        path: 'grain-quality',
        name: 'GrainQuality',
        component: () => import('../views/Datav/GrainQuality'),
        meta: {
          title: '粮食质量一张图',
        },
      },
      {
        path: 'acquisition-season',
        name: 'AcquisitionSeason',
        component: () => import('../views/Datav/AcquisitionSeason'),
        meta: {
          title: '粮食收购一张图',
        },
      },
    ],
  },
  {
    path: '/stock-overview',
    component: PageHeaderLayout,
    children: [
      {
        path: '',
        name: 'StockOverview',
        component: () => import('../views/StockOverview'),
        redirect: { name: 'StockStatistics' },
        children: [
          {
            path: 'stock-statistics',
            name: 'StockStatistics',
            component: () => import('../views/StockOverview/StockStatistics'),
            meta: {
              title: '库存查询',
            },
          },
          {
            path: 'stock-statistics',
            name: 'StockStatisticsAnhui',
            component: () => import('../views/StockOverview/StockStatistics'),
            meta: {
              title: '库存概况',
            },
          },
          {
            path: 'stock-detail',
            name: 'StockDetail',
            component: () => import('../views/StockOverview/StockDetail'),
            meta: {
              title: '库存详情',
            },
          },
        ],
      },
    ],
  },
  {
    path: '/store-point-list',
    component: PageHeaderLayout,
    children: [
      {
        path: '',
        name: 'StorePointList',
        component: () => import('../views/StorePointList'),
        meta: {
          title: '库点分布',
        },
      },
    ],
  },
  {
    path: '/house-capacity-distribution',
    component: PageHeaderLayout,
    children: [
      {
        path: '',
        name: 'HouseCapacityDistribution',
        component: () => import('../views/HouseCapacityDistribution'),
        meta: {
          title: '仓容分布',
        },
      },
    ],
  },
  {
    path: '/purchases-overview',
    component: PageHeaderLayout,
    children: [
      {
        path: '',
        name: 'PurchasesOverview',
        component: () => import('../views/PurchasesOverview'),
        meta: {
          title: '收购服务一条龙',
        },
      },
      {
        path: 'order-public-list',
        component: RouterViewContainer,
        children: [
          {
            path: '',
            name: 'OrderPublicList',
            component: () => import('../views/PurchasesOverview/OrderPublicList'),
            meta: {
              title: '订单清册',
            },
          },
          {
            path: 'detail',
            name: 'DetailOrderPublicList',
            component: () => import('../views/PurchasesOverview/OrderPublicList/OrderPublicDetail'),
            meta: {
              title: '订单清册详情',
            },
          },
        ],
      },
      {
        path: 'appointment-list',
        name: 'AppointmentList',
        component: () => import('../views/PurchasesOverview/AppointmentSearch'),
        meta: {
          title: '预约查询',
        },
      },
      {
        path: 'purchases-progress',
        name: 'PurchasesProgress',
        component: () => import('../views/PurchasesOverview/PurchasesProgress'),
        meta: {
          title: '收购进度',
        },
      },
      {
        path: 'order-search',
        component: RouterViewContainer,
        children: [
          {
            path: '',
            name: 'OrderSearch',
            component: () => import('../views/PurchasesOverview/OrderSearch'),
            meta: {
              title: '订单查询',
            },
          },
          {
            path: 'detail',
            name: 'DetailOrderList',
            component: () => import('../views/PurchasesOverview/OrderSearch/DetailOrderList'),
            meta: {
              title: '订单详情',
            },
          },
        ],
      },
    ],
  },
  {
    path: '/supervision-overview',
    component: PageHeaderLayout,
    children: [
      {
        path: '',
        name: 'SupervisionOverview',
        component: () => import('../views/SupervisionOverview'),
      },
      {
        path: 'report/store/:storeId',
        name: 'SupervisionStoreReport',
        component: () => import('../views/SupervisionOverview/SupervisionReport'),
        meta: {
          title: '清查报告',
        },
      },
      {
        path: 'report/:areaCode',
        name: 'SupervisionAreaReport',
        component: () => import('../views/SupervisionOverview/SupervisionReport'),
        meta: {
          title: '清查报告',
        },
      },
      {
        path: 'risk-list',
        component: CreateKeepAliveRouterView(['RiskList']),
        children: [
          {
            path: '',
            name: 'RiskList',
            component: () => import('../views/SupervisionOverview/RiskList'),
            meta: {
              title: '风险提示',
            },
          },
          {
            path: '',
            name: 'RiskDetail',
            component: () => import('../views/SupervisionOverview/RiskList/RiskDetail'),
            meta: {
              title: '风险详情',
            },
          },
        ],
      },
      {
        path: 'food-infomation',
        component: CreateKeepAliveRouterView(['FoodInfomation']),
        children: [
          {
            path: '',
            name: 'FoodInfomation',
            component: () => import('../views/SupervisionOverview/FoodInfomation'),
            meta: {
              title: '粮情信息',
            },
          },
          {
            path: 'new',
            name: 'FoodInfomationAnhui',
            component: () => import('../views/SupervisionOverview/FoodInfomation'),
            meta: {
              title: '储粮信息',
            },
          },
          {
            path: ':storeHouseId',
            name: 'FoodInfomationDetail',
            component: () =>
              import('../views/SupervisionOverview/FoodInfomation/FoodInfomationDetail'),
            meta: {
              title: '粮情信息详情',
            },
          },
        ],
      },
      {
        path: 'health-index',
        name: 'HealthIndex',
        component: () => import('../views/SupervisionOverview/HealthIndex'),
        meta: {
          title: '健康指数',
        },
      },
      {
        path: 'firm-enterprise',
        component: CreateKeepAliveRouterView(['firmEnterprise']),
        children: [
          {
            path: '',
            name: 'firmEnterprise',
            component: () => import('../views/SupervisionOverview/firmEnterprise'),
            meta: {
              title: '应急企业',
            },
          },
          {
            path: '/supervision-overview/firm-enterprise/firmEnterpriseDetail',
            name: 'firmEnterpriseDetail',
            component: () =>
              import('../views/SupervisionOverview/firmEnterprise/firmEnterpriseDetail'),
            meta: {
              title: '    ',
            },
          },
        ],
      },
      {
        path: 'circulation-monitor',
        component: CreateKeepAliveRouterView(['circulationMonitor']),
        children: [
          {
            path: 'circulation-monitor',
            name: 'circulationMonitor',
            component: () => import('../views/CirculationMonitor'),
            meta: {
              title: '流通监测',
            },
          },
          {
            path: 'food-price-collection',
            name: 'FoodPriceCollection',
            component: () => import('../views/CirculationMonitor/food-price-collection'),
            meta: {
              title: '粮食价格采集',
            },
          },
          {
            path: 'watch-reported-record',
            name: 'WatchReportedRecord',
            component: () => import('../views/CirculationMonitor/watch-reported-record'),
            meta: {
              title: '监测点上报记录',
            },
          },
          {
            path: 'watch-reported-record-detail',
            name: 'WatchReportedRecordDetail',
            component: () => import('../views/CirculationMonitor/watch-reported-record/detail'),
            meta: {
              title: '详情',
            },
          },
        ],
      },
      {
        path: 'video-monitor',
        component: CreateKeepAliveRouterView(['VideoMonitor']),
        children: [
          {
            path: '',
            name: 'VideoMonitor',
            component: () => import('../views/SupervisionOverview/VideoMonitor'),
            meta: {
              title: '视频监控',
            },
          },
          {
            path: ':cameraId',
            name: 'VideoDetail',
            component: () => import('../views/SupervisionOverview/VideoMonitor/VideoDetail'),
            meta: {
              title: '视频监控',
            },
          },
        ],
      },
      {
        path: 'manual-check',
        component: CreateKeepAliveRouterView(['ManualCheck']),
        children: [
          {
            path: '',
            name: 'ManualCheck',
            component: () => import('../views/SupervisionOverview/ManualCheck'),
            meta: {
              title: '视频监控',
            },
          },
        ],
      },
    ],
  },
  {
    path: '/inout-manage',
    component: PageHeaderLayout,
    children: [
      {
        path: '',
        name: 'InoutManage',
        component: () => import('../views/InoutManage/index'),
        meta: {
          title: '智能物流作业',
        },
      },
    ],
  },
  {
    path: '/purchases-manage',
    component: PageHeaderLayout,
    children: [
      {
        path: '',
        name: 'PurchasesManage',
        component: () => import('../views/PurchasesManage/index'),
        meta: {
          title: '收购管理',
        },
      },
    ],
  },
  {
    path: '/job-appointment',
    component: PageHeaderLayout,
    children: [
      {
        path: '',
        name: 'JobAppointment',
        component: () => import('../views/InoutManage/JobAppointment/JobAppointment'),
        meta: {
          title: '预约审核',
        },
      },
      {
        path: 'job-appointment-detail/:id',
        name: 'JobAppointmentDetail',
        component: () => import('../views/InoutManage/JobAppointment/JobAppointmentDetail'),
        meta: {
          title: '预约详情',
        },
      },
    ],
  },
  {
    path: '/check-inout',
    component: PageHeaderLayout,
    children: [
      {
        path: 'check-in-out',
        name: 'CheckInOut',
        component: () => import('../views/InoutManage/CheckInOut/index'),
        meta: {
          title: '登记管理',
          subTitle: { title: '登记记录', name: 'CheckInOutDetail' },
        },
      },
      {
        path: 'check-in-out-detail',
        name: 'CheckInOutDetail',
        component: () => import('../views/InoutManage/CheckInOut/Detail'),
        meta: {
          title: '登记记录',
        },
      },
      {
        path: 'check-in-out-qrCode',
        name: 'CheckInOutQrCode',
        component: () => import('../views/InoutManage/CheckInOut/QrCode'),
        meta: {
          title: '生成二维码',
        },
      },
      // {
      //   path: 'check-in',
      //   name: 'CheckIn',
      //   component: () => import('../views/InoutManage/CheckInOut/CheckIn'),
      //   meta: {
      //     title: '到库登记',
      //     subTitle: { title: '离库登记', name: 'CheckOut' },
      //   },
      // },
      // {
      //   path: 'check-out',
      //   name: 'CheckOut',
      //   component: () => import('../views/InoutManage/CheckInOut/CheckOut'),
      //   meta: {
      //     title: '离库登记',
      //     subTitle: { title: '到库登记', name: 'CheckIn' },
      //   },
      // },
    ],
  },
  {
    path: '/job-scheduling',
    component: PageHeaderLayout,
    children: [
      {
        path: '',
        name: 'JobScheduling',
        component: () => import('../views/InoutManage/JobScheduling/JobScheduling'),
        meta: {
          title: '作业调度',
          subTitle: { title: '调度记录', name: 'JobSchedulingRecord', entryType: 'replace' },
        },
      },
      {
        path: 'job-scheduling-detail/:schedulingNo',
        name: 'JobSchedulingDetail',
        component: () => import('../views/InoutManage/JobScheduling/JobSchedulingDetail'),
        meta: {
          title: '安排调度',
        },
      },
      {
        path: 'job-scheduling-record',
        name: 'JobSchedulingRecord',
        component: () => import('../views/InoutManage/JobScheduling/JobSchedulingRecord'),
        meta: {
          title: '调度记录',
          subTitle: { title: '作业调度', name: 'JobScheduling', entryType: 'replace' },
        },
      },
      {
        path: 'job-scheduling-record-detail/:schedulingNo',
        name: 'JobSchedulingRecordDetail',
        component: () => import('../views/InoutManage/JobScheduling/JobSchedulingRecordDetail'),
        meta: {
          title: '调度记录详情',
        },
      },
    ],
  },
  {
    path: '/taking-sample',
    component: PageHeaderLayout,
    children: [
      {
        path: '',
        name: 'TakingSample',
        component: () => import('../views/InoutManage/TakingSample/TakingSample'),
        meta: {
          title: '扦样管理',
          subTitle: { title: '扦样记录', name: 'TakingSampleRecord', entryType: 'replace' },
        },
      },
      {
        path: 'taking-sample-record',
        name: 'TakingSampleRecord',
        component: () => import('../views/InoutManage/TakingSample/TakingSampleRecord'),
        meta: {
          title: '扦样记录',
          subTitle: { title: '扦样管理', name: 'TakingSample', entryType: 'replace' },
        },
      },
    ],
  },

  {
    path: '/sample-inspection',
    component: PageHeaderLayout,
    children: [
      {
        path: '',
        name: 'SampleInspection',
        component: () => import('../views/InoutManage/SampleInspection/SampleInspection'),
        meta: {
          title: '化验质检',
          subTitle: { title: '检验记录', name: 'SampleInspectionRecord', entryType: 'replace' },
        },
      },
      {
        path: 'sample-inspection-detail/:qrCode',
        name: 'SampleInspectionDetail',
        component: () => import('../views/InoutManage/SampleInspection/SampleInspectionDetail'),
        meta: {
          title: '检验',
        },
      },
      {
        path: 'sample-inspection-record',
        name: 'SampleInspectionRecord',
        component: () => import('../views/InoutManage/SampleInspection/SampleInspectionRecord'),
        meta: {
          title: '检验记录',
          subTitle: { title: '化验质检', name: 'SampleInspection', entryType: 'replace' },
        },
      },
      {
        path: 'sample-inspection-record-detail/:qrCode',
        name: 'SampleInspectionRecordDetail',
        component: () =>
          import('../views/InoutManage/SampleInspection/SampleInspectionRecordDetail'),
        meta: {
          title: '质检单详情',
        },
      },
    ],
  },
  {
    path: '/checking-weight',
    component: PageHeaderLayout,
    children: [
      {
        path: '',
        name: 'CheckingWeight',
        component: () => import('../views/InoutManage/CheckingWeight/CheckingWeight'),
        meta: {
          title: '称重检斤',
          subTitle: { title: '检斤记录', name: 'CheckingWeightRecord', entryType: 'replace' },
        },
      },
      {
        path: 'checking-weight-detail/:schedulingNo',
        name: 'CheckingWeightDetail',
        component: () => import('../views/InoutManage/CheckingWeight/CheckingWeightDetail'),
        meta: {
          title: '称重',
          // subTitle: { title: '增加称重数据', name: 'CheckingWeightAddData' },
        },
      },
      {
        path: 'checking-weight-add-dafta',
        name: 'CheckingWeightAddData',
        component: () => import('../views/InoutManage/CheckingWeight/CheckingWeightAddData'),
        meta: {
          title: '增加称重数据',
        },
      },

      {
        path: 'checking-weight-record',
        name: 'CheckingWeightRecord',
        component: () => import('../views/InoutManage/CheckingWeight/CheckingWeightRecord'),
        meta: {
          title: '检斤记录',
          subTitle: { title: '称重检斤', name: 'CheckingWeight', entryType: 'replace' },
        },
      },
      {
        path: 'checking-weight-record-detail/:schedulingNo',
        name: 'CheckingWeightRecordDetail',
        component: () => import('../views/InoutManage/CheckingWeight/CheckingWeightRecordDetail'),
        meta: {
          title: '检斤记录详情',
        },
      },
      {
        path: 'checking-weight-record-other/:schedulingNo',
        name: 'CheckingWeightRecordOther',
        component: () => import('../views/InoutManage/CheckingWeight/CheckingWeightRecordOther'),
        meta: {
          title: '其他扣量',
        },
      },
      {
        path: 'checking-weight-add-history',
        name: 'CheckingWeightAddHistory',
        component: () => import('../views/InoutManage/CheckingWeight/CheckingWeightAddHistory'),
        meta: {
          title: '新增车辆称重记录',
        },
      },

      {
        path: 'checking-weight-confirm',
        name: 'CheckingWeightConfirm',
        component: () => import('../views/InoutManage/CheckingWeight/CheckingWeightConfirm'),
        meta: {
          title: '数据填报',
        },
      },
    ],
  },
  {
    path: '/house-operation',
    component: PageHeaderLayout,
    children: [
      {
        path: '',
        name: 'HouseOperation',
        component: () => import('../views/InoutManage/HouseOperation/HouseOperation'),
        meta: {
          title: '值仓管理',
          subTitle: { title: '值仓记录', name: 'HouseOperationRecord' },
        },
      },
      {
        path: 'house-detail/:schedulingNo',
        name: 'HouseDetail',
        component: () => import('../views/InoutManage/HouseOperation/HouseDetail'),
        meta: {
          title: '值仓详情',
        },
      },
      {
        path: 'house-operation-record',
        name: 'HouseOperationRecord',
        component: () => import('../views/InoutManage/HouseOperation/HouseOperation'),
        meta: {
          title: '值仓记录',
        },
      },
      {
        path: 'house-detail-record/:schedulingNo',
        name: 'HouseDetailRecord',
        component: () => import('../views/InoutManage/HouseOperation/HouseDetailRecord'),
        meta: {
          title: '值仓记录',
        },
      },
    ],
  },
  {
    path: '/settlement-manage',
    component: PageHeaderLayout,
    children: [
      {
        path: '',
        name: 'SettlementManage',
        component: () => import('../views/InoutManage/SettlementManage/SettlementManage'),
        meta: {
          title: '结算管理',
          subTitle: { title: '结算记录', name: 'SettlementManageRecord', entryType: 'replace' },
        },
      },
      {
        path: 'settlement-manage-detail/:schedulingNo',
        name: 'SettlementManageDetail',
        component: () => import('../views/InoutManage/SettlementManage/SettlementManageDetail'),
        meta: {
          title: '结算',
        },
      },
      {
        path: 'settlement-manage-record',
        name: 'SettlementManageRecord',
        component: () => import('../views/InoutManage/SettlementManage/SettlementManageRecord'),
        meta: {
          title: '结算记录',
          subTitle: { title: '结算管理', name: 'SettlementManage', entryType: 'replace' },
        },
      },
      // {
      //   path: 'settlement-manage-record-detail/:schedulingNo',
      //   name: 'SettlementManageRecordDetail',
      //   component: () =>
      //     import('../views/InoutManage/SettlementManage/SettlementManageRecordDetail'),
      //   meta: {
      //     title: '结算记录详情',
      //   },
      // },
    ],
  },
  {
    path: '/job-approve',
    component: PageHeaderLayout,
    children: [
      {
        path: '',
        name: 'JobApprove',
        component: () => import('../views/JobApprove'),
        meta: {
          title: '业务审批',
          // subTitle: { title: '审批记录', name: 'JobApproveRecord' },
        },
      },
      {
        path: 'plan',
        name: 'JobApprovePlan',
        component: () => import('../views/JobApprove'),
        meta: {
          title: '计划审批',
          // subTitle: { title: '审批记录', name: 'JobApproveRecord' },
        },
      },
      {
        path: 'ticket',
        name: 'JobApproveTicket',
        component: () => import('../views/JobApprove'),
        meta: {
          title: '单证审批',
          // subTitle: { title: '审批记录', name: 'JobApproveRecord' },
        },
      },
      {
        path: 'detail/:type/:id/:coder/:batchId',
        name: 'JobApproveDetail',
        component: () => import('../views/JobApprove/JobApproveDetail'),
        meta: {
          title: '',
        },
      },
      {
        path: 'detail/tenderDetails',
        name: 'TenderDetails',
        component: () => import('../views/JobApprove/details/TenderDetails'),
        meta: {
          title: '招标采购方案标的',
        },
      },
      {
        path: 'detail/auctionsalesDetails',
        name: 'AuctionsalesDetails',
        component: () => import('../views/JobApprove/details/AuctionsalesDetails'),
        meta: {
          title: '竞价销售方案标的',
        },
      },
    ],
  },
  {
    path: '/early-warning-approval',
    component: PageHeaderLayout,
    children: [
      {
        path: '',
        name: 'EarlyWarningApproval',
        component: () => import('../views/EarlyWarningApproval'),
        meta: {
          title: '预警处理',
          // subTitle: { title: '审批记录', name: 'JobApproveRecord' },
        },
      },
    ],
  },
  {
    path: '/law-enforcement-supervision-and-inspection',
    component: PageHeaderLayout,
    children: [
      {
        path: '',
        name: 'LawEnforcementSupervisionAndInspection',
        component: () => import('../views/LawEnforcementSupervisionAndInspection'),
        meta: {
          title: '监管检查',
          // subTitle: { title: '审批记录', name: 'JobApproveRecord' },
        },
      },
    ],
  },
  {
    path: '/law-enforcement-supervision',
    component: PageHeaderLayout,
    children: [
      {
        path: '',
        name: 'LawEnforcementSupervision',
        component: () => import('../views/LawEnforcementSupervision'),
        meta: {
          title: '执法督察',
          // subTitle: { title: '审批记录', name: 'JobApproveRecord' },
        },
      },
    ],
  },
  {
    path: '/safety-production-management',
    component: PageHeaderLayout,
    children: [
      {
        path: '',
        name: 'SafetyProductionManagement',
        component: () => import('../views/SafetyProductionManagement'),
        meta: {
          title: '安全生产管理',
          subTitle: { title: '设备详情', component: h(ScanDeviceDetail) },
        },
      },
      {
        path: 'device-detail',
        name: 'DeviceDetails',
        component: () => import('../views/SafetyProductionManagement/DeviceDetails'),
        meta: {
          title: '设备详情',
          // subTitle: { title: '设备详情', name: 'JobApproveRecord' },
        },
      },
      {
        path: 'maintain-records',
        name: 'MaintainRecords',
        component: () =>
          import('../views/SafetyProductionManagement/DeviceDetails/MaintainRecords'),
        meta: {
          title: '维护记录',
        },
      },
      {
        path: 'receipt-record',
        name: 'ReceiptRecord',
        component: () => import('../views/SafetyProductionManagement/DeviceDetails/ReceiptRecord'),
        meta: {
          title: '领用记录',
        },
      },
    ],
  },
  {
    path: '/system-notices',
    component: PageHeaderLayout,
    children: [
      {
        path: '',
        name: 'SystemNotices',
        component: () => import('../views/SystemNotices'),
        meta: {
          title: '系统公告',
        },
      },
    ],
  },
  {
    path: '/empty-store-check',
    component: PageHeaderLayout,
    children: [
      {
        path: '',
        name: 'EmptyStoreCheck',
        component: () => import('../views/EmptyStoreCheck'),
        meta: {
          title: '空仓验收',
        },
      },
    ],
  },
  {
    path: '/warning-log',
    component: PageHeaderLayout,
    children: [
      {
        path: '',
        name: 'warninglog',
        component: () => import('../views/SupervisionOverview/Warninglog'),
        meta: {
          title: '预告警日志',
        },
      },
      {
        path: '',
        name: 'WarningDetail',
        component: () => import('../views/SupervisionOverview/Warninglog/WarningDetail'),
        meta: {
          title: '预警处理-处理',
        },
      },
    ],
  },
  {
    path: '/intelligent-storage',
    component: PageHeaderLayout,
    children: [
      {
        path: '',
        name: 'IntelligentStorage',
        component: () => import('../views/SupervisionOverview/IntelligentStorage'),
        meta: {
          title: '智能仓储作业',
        },
      },
    ],
  },
  {
    path: '/temperature-record',
    component: PageHeaderLayout,
    children: [
      {
        path: '',
        name: 'TemperatureRecord',
        component: () => import('../views/SupervisionOverview/TemperatureRecord'),
        meta: {
          title: '控温记录',
        },
      },
    ],
  },
  {
    path: '/ventilation-record',
    component: PageHeaderLayout,
    children: [
      {
        path: '',
        name: 'VentilationRecord',
        component: () => import('../views/SupervisionOverview/VentilationRecord'),
        meta: {
          title: '通风记录',
        },
      },
    ],
  },
  {
    path: '/airconditioning-log',
    component: PageHeaderLayout,
    children: [
      {
        path: '',
        name: 'AirconditioningLog',
        component: () => import('../views/SupervisionOverview/AirconditioningLog'),
        meta: {
          title: '气调日志',
        },
      },
    ],
  },
  {
    path: '/home-work-apply',
    component: PageHeaderLayout,
    children: [
      {
        path: '',
        name: 'HomeWorkApply',
        component: () => import('../views/SupervisionOverview/HomeWorkApply'),
        meta: {
          title: '作业申请',
        },
      },
    ],
  },
  {
    path: '/home-work-ask',
    component: PageHeaderLayout,
    children: [
      {
        path: '',
        name: 'HomeWorkAsk',
        component: () => import('../views/SupervisionOverview/HomeWorkAsk'),
        meta: {
          title: '作业申请',
        },
      },
    ],
  },
  {
    path: '/cloneReserveHome',
    name: 'cloneReserveHome',
    component: cloneReserveHome,
  },
];

const router = createRouter({
  history: createWebHashHistory(process.env.BASE_URL),
  routes,
});

const guestAllowedPath = ['/login'];

router.beforeEach(async (to) => {
  // 检查自动登录参数
  if (to.query && (to.query.phone || to.query.employeeCode) && to.name !== 'Login') {
    // 跳到登录页面，根据手机号自动登录
    const query = { ...to.query, redirect: to.path };
    window.sensors_sw.quick('autoTrackSinglePage');
    return { name: 'Login', query, replace: true };
  }

  if (to.name === 'Home' && process.env.VUE_APP_HOME_NAME === 'ReserveHome') {
    window.sensors_sw.quick('autoTrackSinglePage');
    return { name: 'ReserveHome', replace: true };
  }

  const allowGuest = guestAllowedPath.some((it) => to.path.indexOf(it) === 0);

  if (allowGuest) {
    window.sensors_sw.quick('autoTrackSinglePage');
    return true;
  }
  if (!store.state.user.info) {
    try {
      await store.dispatch('user/getInfo');
    } catch (e) {
      console.error(e);
      return false;
    }
  }
  if (!store.state.baseDataLoaded) {
    try {
      await store.dispatch('loadBaseData');
    } catch (e) {
      console.error(e);
      return false;
    }
  }
  if (!store.state.user.reserverIds) {
    try {
      await store.dispatch('user/getReserverUserIdAndDeptId');
    } catch (e) {
      console.error(e);
      return false;
    }
  }
  window.sensors_sw.quick('autoTrackSinglePage');
  // if (!getHxdiframeworkToken()) {
  //   try {
  //     await store.dispatch('user/getHxdiframeworkToken');
  //   } catch (e) {
  //     console.error(e);
  //     return false;
  //   }
  // }
  return true;
});

router.beforeResolve((to) => {
  store.commit('setPageTitle', to.meta?.title);
});

export default router;
