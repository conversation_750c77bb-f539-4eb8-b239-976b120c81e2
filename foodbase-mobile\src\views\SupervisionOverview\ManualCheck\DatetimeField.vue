<template>
  <Field
    :model-value="currentLabel"
    is-link
    readonly
    input-align="right"
    v-bind="$attrs"
    @click="onClick"
  />
  <Popup v-model:show="showPicker" round position="bottom" teleport="body">
    <DatetimePicker
      :model-value="modelValue"
      title="请选择时间"
      :type="type"
      @cancel="showPicker = false"
      @confirm="onFinish"
    />
  </Popup>
</template>

<script>
export default {
  name: 'DatetimeField',
  inheritAttrs: false,
};
</script>

<script setup>
import { Field, Popup, DatetimePicker } from 'vant';
import { computed, ref } from 'vue';
import dayjs from 'dayjs';

const props = defineProps({
  modelValue: Date,
  displayFormat: String,
  type: {
    type: String,
    default: 'date',
  },
});

const emits = defineEmits(['update:modelValue']);

const showPicker = ref(false);

const currentLabel = computed(() => {
  const { modelValue, displayFormat = 'YYYY-MM-DD' } = props;
  return dayjs(modelValue).format(displayFormat);
});

const onClick = () => {
  showPicker.value = true;
};

const onFinish = (value) => {
  showPicker.value = false;
  emits('update:modelValue', value);
};
</script>
