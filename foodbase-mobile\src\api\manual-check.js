import request from '@/utils/request';

export async function getCheckTasks() {
  return request().get('/info-manual-quest/getCheckQuest');
}

export async function getCheckTypes(taskId) {
  return request().get('/info-manual-quest/getCheckType', { params: { id: taskId } });
}

export async function getCheckQuestions(typeId) {
  return request().get('/info-manual-quest/getCheckQuestion', { params: { id: typeId } });
}

export async function saveCheckQuestions(data) {
  return request().post('/info-check-manual/saveManualCheck', data);
}
