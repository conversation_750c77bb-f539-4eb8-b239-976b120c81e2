<template>
  <div class="module">
    <p class="title">{{ props.item.storageUnit }}</p>
    <div class="tags">
      <span class="tag category-tag">{{ props.item.category }}</span>

      <span class="tag level-tag" :style="{ 'background-color': levelColor[props.item.grade] }">
        <BizDictName
          dict="GRAIN_AND_OIL_QUALITY_INSPECTION_GRADE"
          :value="props.item.grade"
          v-if="!isSellOut && !isBidInvite"
        ></BizDictName>
        <span v-else>{{ props.item.grade }}</span>
      </span>
      <span class="tag type-tag" v-if="isOut">
        <BizDictName dict="FOOD_PERPROTIES" :value="props.item.propertiesGrainOil" />
      </span>
    </div>
    <div class="detail">
      <p v-if="isOut || isSellOut || isBidInvite">
        <span class="label">库点：</span>{{ props.item.store }}
      </p>
      <p v-if="isOut || isSellOut || isBidInvite">
        <span class="label">仓廒_货位：</span>{{ props.item.storeHouse }}
      </p>
      <p v-if="isOut || isIn">
        <span class="label">{{ rotationTxt }}数量：</span>{{ props.item.plannedNumber }}吨
      </p>
      <p v-if="isSellOut || isBidInvite">
        <span class="label">收获年份：</span>{{ props.item.yearGain }}
      </p>
      <p v-if="isSellOut"><span class="label">本批次轮出数：</span>{{ props.item.count }}吨</p>
      <p v-if="isBidInvite"><span class="label">选择分配数：</span>{{ props.item.count }}</p>
    </div>
  </div>
</template>

<script setup>
import { computed, defineProps } from 'vue';
import { BizDictName } from '@/views/InoutManage/common';

const levelColor = {
  1: '#14C287',
  2: '#1973F1',
  3: '#FFA40D',
  4: '#8658F0',
  5: '#E95952',
  6: '#A56126',
  101: '#14C287',
  102: '#1973F1',
  103: '#FFA40D',
  104: '#8658F0',
  105: '#E95952',
};

const props = defineProps({
  rotationType: String,
  item: Object,
});

const isIn = computed(() => {
  return props.rotationType == 'in';
});

const isOut = computed(() => {
  return props.rotationType == 'out';
});

const isSellOut = computed(() => {
  return props.rotationType == 'sellExpoundOut';
});

const isBidInvite = computed(() => {
  return props.rotationType == 'bidInvitingPlan';
});

const rotationTxt = computed(() => {
  return props.rotationType == 'out' ? '轮出' : '轮入';
});
</script>

<style scoped lang="scss">
.module {
  .title {
    font-size: 16px;
    font-weight: bold;
    color: #232323;
  }
  .tags {
    display: flex;
    margin-top: 12px;
    .tag {
      height: 22px;
      padding: 0 12px;
      line-height: 22px;
      border-radius: 2px;
      font-size: 14px;
      margin-right: 6px;
    }
    .category-tag {
      background: #8797af;
      color: #ffffff;
    }
    .level-tag {
      background: #14c287;
      color: #ffffff;
    }
    .type-tag {
      background: rgba(25, 115, 241, 0.15);
      color: #1973f1;
    }
  }
  .detail {
    p {
      font-size: 16px;
      color: #232323;
      margin-top: 12px;

      .label {
        width: 120px;
        display: inline-block;
      }
    }
  }
}
</style>
