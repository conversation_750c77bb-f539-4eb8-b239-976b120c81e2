<template>
  <div class="multiple-select">
    <van-popup
      v-model:show="show"
      position="bottom"
    >
      <div class="multiple-select-header">
        <div class="multiple-select-cancel" @click="handleCancel">取消</div>
        <div class="multiple-select-title">{{ title }}</div>
        <div class="multiple-select-ok" @click="handleOk">确定</div>
      </div>
      <div class="multiple-select-wrapper">
        <van-checkbox-group ref="checkboxGroupRef" v-model="selectValue">
          <van-cell
            v-for="(item, index) in options"
            :title="item[fieldName.label]"
            :key="item[fieldName.value]"
            clickable
            @click="toggle(index)"
          >
            <template #right-icon>
              <van-checkbox
                :name="item[fieldName.value]"
                :ref="(el) => (checkboxRefs[index] = el)"
                @click.stop
              >
                <template #icon="props">
                  <van-icon name="success" class="selected-icon" v-if="props.checked"  />
                </template>
              </van-checkbox>
            </template>
          </van-cell>
        </van-checkbox-group>
      </div>
    </van-popup>
  </div>
</template>

<script>
export default {
  name: 'HMultipleSelect',
}
</script>

<script setup>
import { ref, watch } from 'vue'

const props = defineProps({
  visible: {
    type: Boolean,
    default: false,
  },
  value: {
    type: Array,
    default: () => [],
  },
  title: {
    type: String,
    default: '请选择'
  },
  options: {
    type: Array,
    default: () => [],
  },
  fieldName: {
    type: Object,
    default: () => ({ label: 'label', value: 'value' }),
  },
  selectOne: {
    type: Boolean,
    default: false,
  },
})

const emits = defineEmits(['update:visible', 'update:value', 'update:nodes', 'change'])

const show = ref(props.visible)

const selectItem = ref([])

const selectValue = ref([])

const selectText = ref("");

const checkboxRefs = ref([]);

const checkboxGroupRef = ref(null);

watch(() => props.visible, (val) => {
  show.value = val
})

watch(() => [props.value, props.nodes], (val) => {
  if (props.value?.length > 0) {
    selectItem.value = props.options?.filter((item) => props.value.includes(item[props.fieldNames.value]));
    selectValue.value = props.value;
    selectText.value = selectItem.value?.map(item => item[props.fieldNames.label]);
  } else if (props.nodes?.length > 0) {
    selectItem.value = props.nodes
    selectValue.value = props.nodes?.map(item => item[props.fieldNames.value]);
    selectText.value = selectItem.value?.map(item => item[props.fieldNames.label]);
  } else {
    selectItem.value = []
    selectValue.value = []
    selectText.value = ''
  }
}, { immediate: true, deep: true })

const toggle = (index) => {
  if (props.selectOne) {
    props.options.forEach((item, i) => {
      if (i !== index) {
        checkboxRefs.value[i].toggle(false);
      }
    })
    checkboxRefs.value[index].toggle();
    if (!checkboxRefs.value[index]?.checked?.value) {
      selectValue.value = [selectValue.value[selectValue.value.length - 1]]
    } else {
      selectValue.value = []
    }
  } else {
    checkboxRefs.value[index].toggle();
  }
};

const handleCancel = () => {
  emits('update:visible', false)
}

const handleOk = () => {
  const { options } = props;
  selectItem.value = options?.filter(
    (item) => selectValue.value?.indexOf(item[props.fieldName.value]) > -1
  );
  selectText.value = selectItem.value
    ?.map((item) => item[props.fieldName.label]);
  emits('update:visible', false)
  emits('update:value', selectValue.value)
  emits('update:nodes', selectItem.value)
  emits('change', selectValue.value, selectText.value, selectItem.value)
}
</script>

<style lang="scss" scoped>
.multiple-select {
  .multiple-select-header {
    display: flex;
    justify-content: space-between;
    padding: 8px 12px;
    .multiple-select-title {
      font-weight: bold;
    }
    .multiple-select-ok {
      color: var(--van-primary-color);
    }
    .multiple-select-cancel {
      color: var(--van-gray-7);
    }
  }
  .multiple-select-wrapper {
    height: 300px;
    overflow-x: hidden;
    overflow-y: auto;
    .selected-icon {
      background-color: white;
      border: white;
      color: var(--van-primary-color);
    }
  }
}
</style>
