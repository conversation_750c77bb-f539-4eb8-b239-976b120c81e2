<template>
  <div class="job-appointment page-warpper">
    <div class="date-select">
      <div
        class="date-item"
        v-bind:class="{ active: date == item.value }"
        v-for="item in dates"
        :key="item.value"
        @click="selectDate(item)"
      >
        {{ item.label }}
      </div>
    </div>
    <PullRefresh @refresh="onRefresh" v-model="refreshLoading" style="margin-bottom: 120px">
      <List
        ref="listRef"
        v-model:loading="isLoading"
        :finished="finished"
        finished-text="没有更多了"
        @load="getList"
      >
        <HCard class="detail-card" v-for="item in list" :key="item" @click="goToDetail(item.id)">
          <div class="card-head">
            <img :src="IconHouse" />
            <div class="schedule-num">预约号：{{ item.name }}</div>
            <Checkbox
              v-if="showSelected"
              v-model="item.checked"
              shape="square"
              @click.stop
            ></Checkbox>
          </div>
          <div class="card-content">
            <div class="row">
              <span class="label">预约客户：</span>
              <span class="value">{{ item.bookinger }}</span>
            </div>
            <div class="row">
              <span class="label">预约业务：</span>
              <span class="value">{{ item.buzType }}</span>
            </div>
            <div class="row">
              <span class="label">联系电话：</span>
              <span class="value">{{ item.bookingerTel }}</span>
            </div>
            <div class="row">
              <span class="label">预约时间：</span>
              <span class="value">
                {{ item.bookingerDate ? dayjs(item.bookingerDate).format('YYYY-MM-DD') : '' }}
              </span>
            </div>
            <div class="row">
              <span class="label">粮油品种：</span>
              <span class="value">{{ item.detail?.foodCategoryName }}</span>
            </div>
            <div class="row">
              <span class="label">运输方式：</span>
              <span class="value">{{ item.detail?.typeShippingName }}</span>
            </div>
            <div class="row">
              <span class="label">数量(公斤)：</span>
              <span class="value">
                <HFixedNumber :ratio="1000" :fraction-digits="0">
                  {{ item.detail?.counter }}
                </HFixedNumber>
              </span>
            </div>
            <div class="row">
              <span class="label">预约库点：</span>
              <span class="value">{{ item.bookingered }}</span>
            </div>
          </div>
        </HCard>
      </List>
    </PullRefresh>
    <div class="bottom-warpper" v-if="list.length > 0">
      <div class="buttons">
        <Button
          v-if="showSelected"
          class="cancel-button"
          style="width: 100px"
          @click="showSelected = false"
        >
          取消
        </Button>
        <Button
          v-if="showSelected"
          class="next-button"
          style="width: 100px"
          @click="onShowDialog(2)"
        >
          同意
        </Button>
        <Button
          v-if="showSelected"
          class="reject-button"
          style="width: 100px"
          @click="onShowDialog(3)"
        >
          拒绝
        </Button>
        <Button
          v-if="!showSelected"
          type="primary"
          style="width: 200px"
          @click="showSelected = true"
        >
          批量审核
        </Button>
      </div>
      <div class="bar"></div>
    </div>
    <Dialog.Component
      v-model:show="dialogVisible"
      width="350px"
      title="预约审核"
      :show-confirm-button="false"
      :show-cancel-button="false"
    >
      <template #default>
        <div>审核结果：{{ bookingerAudit === 2 ? '同意' : '拒绝' }}</div>
        <Field
          v-model="bookingerCause"
          type="textarea"
          autosize
          label="反馈："
          maxlength="200"
          show-word-limit
          border
          class="border-field"
          label-width="3.2em"
        ></Field>
      </template>
      <template #footer>
        <div>
          <Button @click="onCancelDialog" style="width: 50%">取消</Button>
          <Button type="primary" @click="onApproval" :loading="confirmLoading" style="width: 50%">
            确定
          </Button>
        </div>
      </template>
    </Dialog.Component>
  </div>
</template>

<script setup>
import { List, PullRefresh, Checkbox, Button, Dialog, Field, Toast } from 'vant';
import { ref, reactive } from 'vue';
// import { useRouter } from 'vue-router';
import { HCard, HFixedNumber } from '@/components';
import IconHouse from '@/assets/icon-house.png';
import { getAppointmentList, approvalAppointment } from '@/api/in-out-manage';
import dayjs from 'dayjs';

// const router = useRouter();

const date = ref(dayjs().format('YYYY-MM-DD'));
const list = ref([]);
const pagination = reactive({
  page: 0,
  size: 10,
  total: 0,
});
const dates = [0, 1, 2, 3].map((d) => {
  const date = dayjs().add(d, 'day');
  return {
    value: date.format('YYYY-MM-DD'),
    label: `${date.format('MM.DD')}`,
  };
});

const isLoading = ref(false);
const finished = ref(false);
const refreshLoading = ref(false);
const showSelected = ref(false);
const confirmLoading = ref(false);
const selectedList = ref([]);
const dialogVisible = ref(false);
const bookingerAudit = ref(null);
const bookingerCause = ref('');

const onRefresh = () => {
  if (refreshLoading.value) {
    pagination.page = 0;
    pagination.total = 0;
    list.value = [];
    refreshLoading.value = false;
  }
  finished.value = false;
  isLoading.value = true;
  getList();
};
const selectDate = (item) => {
  date.value = item.value;
  refreshLoading.value = true;
  onRefresh();
};
const getList = async () => {
  const { content, page, totalElements } = await getAppointmentList({
    page: pagination.page + 1,
    size: pagination.size,
    bookingerAudit: 1, // 待审核
    beginDate: date.value,
    endDate: date.value,
  });
  list.value.push(...content);
  pagination.page = page;
  pagination.total = totalElements;
  // 加载状态结束
  isLoading.value = false;

  // 数据全部加载完成
  if (list.value.length >= totalElements) {
    finished.value = true;
  }
};
const goToDetail = (id) => {
  console.log(id);
  // router.push({
  //   name: 'JobAppointmentDetail',
  //   params: {
  //     id: id,
  //   },
  // });
};
const onShowDialog = (status) => {
  bookingerAudit.value = status;
  selectedList.value = list.value.filter((item) => item.checked);
  if (selectedList.value.length === 0) {
    Toast.fail('请选择要批量审核的数据');
    return;
  }
  dialogVisible.value = true;
};
const onCancelDialog = () => {
  dialogVisible.value = false;
};
const onApproval = async () => {
  confirmLoading.value = true;
  const idStr = selectedList.value.map((it) => it.id).join(',');
  try {
    await approvalAppointment({
      idStr,
      bookingerAudit: bookingerAudit.value,
      bookingerCause: bookingerCause.value,
    });
    Toast.success('已保存');
    confirmLoading.value = false;
    dialogVisible.value = false;
    refreshLoading.value = true;
    onRefresh();
  } catch (error) {
    Toast.fail('保存失败');
  }
};
</script>

<style lang="scss" scoped>
@import '@/views/InoutManage/style/index';
.job-appointment {
  .date-select {
    width: 100%;
    display: flex;
    margin-bottom: 10px;
    .date-item {
      width: 25%;
      height: 40px;
      line-height: 40px;
      text-align: center;
      color: #111111;
      border: 1px solid #111111;
      background: #ffffff;
      &.active {
        background: #1989fa;
        color: #ffffff;
        border: 1px solid #1989fa;
      }
    }
  }
  .detail-card {
    padding: 16px;
    margin-bottom: 10px;
  }
  .card-head {
    display: flex;
    align-items: center;
    justify-content: space-between;
    font-size: 18px;
    font-weight: bold;
    color: #1f3359;
    .next {
      color: rgba(109, 116, 141, 0.6);
    }
    .schedule-num {
      flex-grow: 1;
      margin-left: 12px;
    }
  }
  .card-content {
    margin-top: 8px;
    line-height: 30px;
    .row {
      display: flex;
      .label {
        font-size: 14px;
        font-weight: 400;
        color: #6d748d;
        flex-basis: 6em;
      }
      .value {
        font-size: 14px;
        font-weight: 400;
        color: #0f0f0f;
        flex-grow: 1;
      }
      .carNum {
        padding: 3px 6px;
        background: #f4f4f4;
        border-radius: 2px;
        border: 1px solid #ebedf0;
        margin-right: 5px;
      }
    }
  }
}
</style>
