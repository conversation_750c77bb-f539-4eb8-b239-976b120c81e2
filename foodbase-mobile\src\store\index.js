import { createStore } from 'vuex';
import user from './modules/user';
import area from './modules/area';
import dict from '@/store/modules/dict';
import stockOverview from './modules/stock-overview';
import superVisionOverview from './modules/supervision-overview';
import foodCategory from './modules/food-category';
import dryTower from './modules/dry-tower';
import car from './modules/car';
import originPlace from './modules/originPlace';

export default createStore({
  state: {
    baseDataLoaded: false,
    pageTitle: process.env.VUE_APP_TITLE,
  },
  mutations: {
    setBaseDataLoaded(state, value) {
      state.baseDataLoaded = value;
    },
    setPageTitle(state, title) {
      state.pageTitle = title || process.env.VUE_APP_TITLE;
    },
  },
  actions: {
    async loadBaseData({ dispatch, commit }) {
      await Promise.all([dispatch('area/load'), dispatch('dict/load')]);
      if (process.env.VUE_APP_HOME_NAME === 'ReserveHome') {
        await Promise.all([
          dispatch('dict/loadReserve'),
          dispatch('food-category/load'),
          dispatch('dry-tower/load'),
          dispatch('car/load'),
          dispatch('origin-place/load'),
        ]);
      }
      commit('setBaseDataLoaded', true);
    },
  },
  modules: {
    user,
    area,
    dict,
    'stock-overview': stockOverview,
    'supervision-overview': superVisionOverview,
    'food-category': foodCategory,
    'dry-tower': dryTower,
    car,
    'origin-place': originPlace,
  },
});
