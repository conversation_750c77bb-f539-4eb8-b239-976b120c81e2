<template>
  <div class="risk-detail">
    <div v-if="checktableList.length > 0">
      <div class="food-risk-title">
        <div class="food-risk-content">
          <div>仓房：</div>
          <div>{{ titleData.storeHouse }}</div>
        </div>
        <div class="food-risk-content">
          <div>检测时间：</div>
          <div>{{ titleData.jobStartTime }}</div>
        </div>
        <div class="food-risk-content">
          <div>单位：</div>
          <div>℃</div>
        </div>
      </div>
      <div class="food-risk-table">
        <table v-for="(item, tableIndex) in checktableList" :key="tableIndex">
          <tr>
            <td class="left-top-title">
              <span class="slash"></span>
              <span>层号</span>
              <span>电缆号</span>
            </td>
            <td v-for="indexNum in 5" :key="indexNum">{{ `${5 * tableIndex + indexNum}` }}</td>
          </tr>
          <tr v-for="(trItem, trIndex) in item" :key="trIndex">
            <td>
              {{ trIndex + 1 }}
            </td>
            <td v-for="(tdItem, tdIndex) in trItem" :key="tdIndex">
              {{ tdItem.temperature }}
            </td>
          </tr>
        </table>
      </div>
    </div>
    <div v-else>
      <van-empty
        image="https://fastly.jsdelivr.net/npm/@vant/assets/custom-empty-image.png"
        image-size="80"
        description="暂无内容"
      />
    </div>
  </div>
</template>
<script>
import { groupBy, max } from 'lodash-es';
import dayjs from 'dayjs';
import { temperatureDetail } from '@/api/supervision';

export default {
  name: 'StorehouseTemperatureModal',
  props: {
    record: {
      type: Object,
      default: () => ({}),
    },
  },

  data() {
    return {
      loading: false,
      tempList: [],
      circle: null,
      titleData: {
        storeHouse: '',
        jobStartTime: '',
      },
    };
  },
  computed: {
    checktableList() {
      if (this.tempList.length === 0) {
        return [];
      }
      const maxCable = max(this.tempList.map((it) => parseInt(it.tempCableNo)));
      const maxLayer = max(this.tempList.map((it) => parseInt(it.layer)));

      for (let i = 1; i <= maxLayer; i++) {
        const data = this.tempList.filter((item) => String(item.layer) === String(i));
        const max = Math.max.apply(
          Math,
          data.map((item) => {
            return item.temperature;
          }),
        );
        const min = Math.min.apply(
          Math,
          data.map((item) => {
            return item.temperature;
          }),
        );
        data.forEach((m) => {
          if (String(m.temperature) === String(max)) {
            m.temperature = `*${m.temperature}`;
          }
          if (String(m.temperature) === String(min)) {
            m.temperature = `#${m.temperature}`;
          }
        });
      }

      const tableSize = Math.ceil(maxCable / 5);
      const tableList = Array(tableSize).fill(Array(maxLayer).fill(Array(5).fill(0)));
      const groupedTempList = groupBy(this.tempList, 'tempCableNo');
      return tableList.map((tableData, tableIndex) => {
        return tableData.map((rowData, rowIndex) => {
          return rowData.map((cell, cellIndex) => {
            const cableNo = cellIndex + 5 * tableIndex + 1;
            const layer = rowIndex + 1;
            const cellItem = groupedTempList[cableNo]?.find((it) => it.layer === layer);
            return (
              cellItem || {
                isGood: 1,
                temperature: '-',
              }
            );
          });
        });
      });
    },
    Time() {
      const datetime = this.tempList[0]?.jobStartTime;
      if (datetime) {
        return dayjs(datetime).format('YYYY-MM-DD HH:mm');
      }
      return '____-__-__ __:__';
    },
    isQ() {
      return this.record.cfmc.split('', 1)[0] === 'Q';
    },
  },
  created() {
    this.getTempList();
  },
  methods: {
    async getTempList() {
      if (this.loading) {
        return;
      }
      this.tempList = [];
      try {
        this.loading = true;
        const { storeHouseId } = this.$route.params;
        this.tempList = await temperatureDetail({ storeHouseId: storeHouseId });
        if (this.tempList.length > 0) {
          const { storeHouse, jobStartTime } = this.tempList[0];
          this.titleData = { storeHouse: storeHouse, jobStartTime: jobStartTime };
        }

        if (this.tempList.length > 0) {
          this.circle = JSON.parse(this.tempList[0].totalCircle) || {
            totalCircle: null,
            smallCircle: null,
          };
        }
      } finally {
        this.loading = false;
      }
    },
    // 匹配杆数圈数
    cablesNo(no) {
      let arr;
      if (this.circle.smallCircle) {
        arr = this.circle.smallCircle.split(',').map(Number);
      } else {
        arr = [];
      }

      let flag = 0;
      let sum = 0;
      for (let i = 0; i < arr.length; i++) {
        for (let j = 1; j <= arr[i]; j++) {
          sum++;
          if (no <= sum) {
            return `${no}/${i + 1}-${no - flag}`;
          }
        }
        flag += arr[i];
      }
      return no;
    },
  },
};
</script>
<style scoped lang="scss">
table {
  width: 310px;
  border: 1px solid #000;
  border-collapse: collapse;
  margin: 0 auto 20px;
  tr {
    text-align: center;
  }
  td {
    padding: 5px;
    text-align: center;
    border: 1px solid #000;
  }
}
.risk-detail {
  background-color: #fff;
  margin-bottom: 30px;
  padding: 10px;
  .food-risk-table {
    margin-top: 20px;
    .left-top-title {
      padding: 30px 40px;
      position: relative;
      span {
        position: absolute;
      }
      span:nth-child(2) {
        bottom: 3px;
        left: 3px;
      }
      span:nth-child(3) {
        top: 3px;
        right: 3px;
      }
      .slash {
        display: block;
        top: 0;
        left: 0;
        /* 斜边边长 */
        /*  */
        /* Math.sqrt(Math.pow(150, 2) + Math.pow(80, 2)) = 170 */
        width: 102px;
        height: 1px;
        background-color: #ccc;
        /* 旋转角度计算公式 */
        /*  Math.atan(height / width) * 180 / Math.PI  */
        /*  Math.atan(80 / 150) * 180 / Math.PI  = 28.072486935852954 */
        transform: rotate(35.53767779197438deg);
        transform-origin: top left;
      }
    }
  }
  .food-risk-title {
    //display: flex;
    //justify-content: space-evenly;
    .food-risk-content:nth-child(1) {
      margin-left: 31px;
    }
    .food-risk-content:nth-child(3) {
      margin-left: 31px;
    }
    .food-risk-content:nth-child(2) {
      margin: 4px 0;
    }
    .food-risk-content {
      display: flex;
    }
  }
  .risk-image {
    width: 100vw;
    height: 56vw;
  }

  .custom-indicator {
    position: absolute;
    right: 5px;
    bottom: 5px;
    padding: 2px 5px;
    font-size: 12px;
    color: #ffffff;
    background: rgba(255, 255, 255, 0.1);
  }

  ::v-deep(.store-name) {
    flex: 4;
  }
  ::v-deep(.dispose-time) {
    flex: 2;
  }
  ::v-deep(.dispose-detail) {
    flex: 2;
  }

  --van-cell-font-size: 18px;
  --van-cell-group-title-font-size: 17px;
  --van-button-normal-font-size: 20px;
}
</style>
