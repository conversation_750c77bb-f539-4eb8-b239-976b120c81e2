<template>
  <div class="supervision-security">
    <div class="chart">
      <HEchart :options="options" />
    </div>
    <div class="detail-values" v-if="props.data.abnormalStorehouseSum">
      <div class="item">
        <div class="name">异常仓房数</div>
        <div class="value">{{ props.data.abnormalStorehouseSum }}</div>
        个
      </div>
      <div class="item">
        <div class="name">异常仓房</div>
        <div class="store-name">{{ props.data.storeName }}</div>
      </div>
      <div class="storehouse-name-item">{{ props.data.abnormalStorehouse?.join('、') }}</div>
      <div class="item">
        <div class="name">风险提示</div>
        <div class="value">{{ props.data.warnSum }}</div>
        个
      </div>
    </div>
  </div>
</template>

<script setup>
import HEchart from '@/components/HEchart/HEchart';
import { computed } from 'vue';

const props = defineProps({
  data: {
    type: Object,
    default: () => ({}),
  },
});

const createGaugeOptions = (percent) => {
  return {
    series: [
      {
        tooltip: {
          show: false,
        },
        type: 'gauge',
        radius: '180%',
        center: ['50%', '95%'],
        startAngle: 180,
        endAngle: 0,
        detail: {
          show: false,
        },
        axisLine: {
          lineStyle: {
            width: 5,
            color: [
              [0.25, '#91C7AD'],
              [0.75, '#FFAC38'],
              [1, '#6AA84F'],
            ],
          },
        },
        axisLabel: {
          show: false,
        },
        axisTick: {
          show: false,
        },
        splitLine: {
          show: false,
        },
      },
      {
        type: 'gauge',
        radius: '170%',
        min: 0,
        max: 100,
        center: ['50%', '95%'],
        data: [
          {
            value: percent,
            name: '得分',
          },
        ],
        splitNumber: 4,
        startAngle: 180,
        endAngle: 0,
        title: {
          offsetCenter: [0, -8],
          fontSize: 18,
          color: '#686B73',
        },
        detail: {
          formatter: function (value) {
            return value.toFixed(0);
          },
          fontSize: 24,
          color: 'auto',
          fontWeight: 'bolder',
          offsetCenter: [0, -36],
        },
        axisLine: {
          lineStyle: {
            width: 0,
            color: [
              [0.25, '#91C7AD'],
              [0.75, '#FFAC38'],
              [1, '#6AA84F'],
            ],
          },
        },
        axisLabel: {
          color: 'auto',
          fontSize: 16,
          distance: 8,
        },
        axisTick: {
          splitNumber: 50,
          show: true,
          lineStyle: {
            color: 'auto',
            width: 1,
          },
          length: 6,
        },
        splitLine: {
          show: true,
          length: 12,
          lineStyle: {
            color: 'auto',
            width: 2,
          },
        },
        itemStyle: {
          color: 'auto',
        },
        pointer: {
          width: 5,
          length: '80%',
          itemStyle: {
            color: {
              type: 'linear',
              x: 0,
              y: 1,
              x2: 0,
              y2: 0,
              colorStops: [
                {
                  offset: 0,
                  color: 'transparent', // 0% 处的颜色
                },
                {
                  offset: 0.5,
                  color: 'transparent', // 0% 处的颜色
                },
                {
                  offset: 0.8,
                  color: '#6AA84F', // 0% 处的颜色
                },
                {
                  offset: 1,
                  color: '#6AA84F', // 100% 处的颜色
                },
              ],
            },
          },
        },
      },
    ],
  };
};

const options = computed(() => {
  return createGaugeOptions(props.data.point);
});
</script>

<style scoped lang="scss">
.supervision-security {
  display: flex;
  align-items: normal;
  flex-direction: column;
  padding: 10px 15px;
}

.chart {
  width: 300px;
  height: 160px;
  margin: 10px auto;

  .h-echarts {
    height: 100%;
    width: 100%;
  }
}

.item {
  display: flex;
  line-height: 22px;

  + .item {
    margin-top: 10px;
  }

  .name {
    font-size: 16px;
    color: var(--van-gray-8);
    width: 9em;
  }

  .store-name {
    font-size: 20px;
    font-weight: 500;
    color: #ff5814;
    line-height: 28px;
    text-align: right;
  }

  .value {
    font-size: 20px;
    font-weight: 500;
    color: #ff5814;
    margin-left: auto;
    margin-right: 2px;
  }
}

.storehouse-name-item {
  font-weight: normal;
  text-align: right;
}
</style>
