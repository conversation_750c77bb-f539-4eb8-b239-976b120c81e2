<template>
  <div>
    <Form ref="formRef" class="pb-[100px]">
      <div class="p-[16px] bg-[#fff]">
        <h5 class="flex items-center">
          <img src="@/assets/icon-station.png" class="w-[20px] h-[20px] mr-[8px]" />
          <strong class="title">{{ form.storageUnit }}</strong>
        </h5>
        <h5 class="mt-[16px]">
          <span class="text-[#686B73]">库点:&nbsp;</span>
          <span class="text-[#232323] ml-[16px]">{{ form.store }}</span>
        </h5>
        <h5 class="mt-[12px]">
          <span class="text-[#686B73]">仓房:&nbsp;</span>
          <span class="text-[#232323] ml-[16px]">{{ form.storeHouse }}</span>
        </h5>
      </div>
      <div class="warpper">
        <van-field
          label-width="12em"
          input-align="right"
          v-model="form.storeFacility"
          label="仓储设施状态是否完好:"
          placeholder="请选择"
          is-link
          readonly
          required
          :rules="rules.storeFacility"
          @click="onShowPiker('storeFacility')"
        >
        </van-field>
        <van-field
          label-width="12em"
          input-align="right"
          v-model="form.storeEquipment"
          label="仓储设备状态是否完好:"
          placeholder="请选择"
          is-link
          readonly
          required
          :rules="rules.storeEquipment"
          @click="onShowPiker('storeEquipment')"
        ></van-field>
        <van-field
          label-width="12em"
          input-align="right"
          v-model="form.storeHygiene"
          label="仓内卫生是否清洁:"
          placeholder="请选择"
          is-link
          readonly
          required
          :rules="rules.storeHygiene"
          @click="onShowPiker('storeHygiene')"
        ></van-field>
        <Popup v-model:show="showPicker" position="bottom">
          <Picker
            @confirm="onPickerConfirm"
            @cancel="showPicker = false"
            :columns="pickerColumns"
          />
        </Popup>
      </div>
      <HCard class="p-[16px] mt-[10px]">
        <div class="text-[#232323] font-500">备注</div>
        <Field type="textarea" placeholder="请输入" v-model="form.remark" maxlength="128" />
      </HCard>
      <div class="bg-[#fff] mt-[10px] p-[16px]">
        <h5 class="mb-[10px]">
          <span class="text-[#232323] font-500">上传附件</span>
          <span class="text-[#B3B5B9]">(支持PNG、JPG、GIF格式文件)</span>
        </h5>
        <Uploader
          :model-value="fileList"
          :max-count="2"
          :before-read="beforeRead"
          @click-upload="onClickUpload"
          @delete="onDeleteFile"
        >
        </Uploader>
      </div>
      <div class="bg-[#fff] mt-[10px]">
        <Field label="验收结果" required :rules="rules.checkResult" input-align="right">
          <template #input>
            <RadioGroup v-model="form.checkResult" direction="horizontal">
              <Radio name="1">通过</Radio>
              <Radio name="0">不通过</Radio>
            </RadioGroup>
          </template>
        </Field>
      </div>

      <div class="bottom-warpper">
        <div class="buttons">
          <Button class="cancel-button" @click="onUpdateClose">取消</Button>

          <Button class="confirm-button" @click="onConfirm"> 确定 </Button>
        </div>
        <div class="bar"></div>
      </div>
    </Form>
    <ActionSheet
      v-model:show="showActionSheet"
      cancel-text="取消"
      description="请选择上传方式"
      :actions="actions"
      @select="onUploadActionSelect"
      close-on-click-action
    />
  </div>
</template>

<script setup>
import {
  Form,
  Popup,
  Picker,
  Field,
  Uploader,
  Button,
  RadioGroup,
  Radio,
  Toast,
  ActionSheet,
} from 'vant';
import { ref, defineEmits, watch, reactive } from 'vue';
import { rules, form } from './constant';
import { getEmptyStoreDetail, addEmptyStore } from '@/api/empty-store-check';
// import { getFileUrls } from '@/api/in-out-manage';
import { getReserverToken } from '@/utils/auth';
import { getFiles } from '@/api/file';
import dayjs from 'dayjs';

const formRef = ref();
const props = defineProps({
  info: { type: Object, default: () => {} },
});
//设施
const pickerColumns = ref([
  {
    value: '0',
    text: '是',
  },
  {
    value: '1',
    text: '否',
  },
]);
const showPicker = ref(false);
const labelText = ref('');
const fileList = ref([]);
const showActionSheet = ref(false);
const actions = ref([{ name: '相册选择' }]);
const formId = ref(null);
const imageUriList = ref([]);
const onShowPiker = (label) => {
  showPicker.value = true;
  labelText.value = label;
};
const onPickerConfirm = (item) => {
  if (labelText.value === 'storeFacility') {
    form.value.storeFacility = item.text;
  } else if (labelText.value === 'storeEquipment') {
    form.value.storeEquipment = item.text;
  } else {
    form.value.storeHygiene = item.text;
  }
  showPicker.value = false;
};
const beforeRead = (file) => {
  if (
    file.type.includes('jpg') ||
    file.type.includes('jpeg') ||
    file.type.includes('png') ||
    file.type.includes('gif')
  ) {
    return true;
  } else {
    Toast('请上传 jpg, png, jpeg,gif 格式图片');
    return false;
  }
};
//获取图片列表
const fechFiles = async () => {
  const res = await getFiles({ businessId: props.info.id, coder: 'empty_store_check_report' });
  console.log(res, 'res');
  fileList.value = [];
  res.forEach((item) => {
    fileList.value.push({
      url: item.fileUrl,
      name: item.fileName,
      status: 'done',
      isImage: true,
      deletable: true,
      id: item?.id,
    });
  });
};

const onUploadActionSelect = ({ name }) => {
  const Camera = window.Camera;
  let sourceType = Camera.PictureSourceType.PHOTOLIBRARY;
  if (name === '相册选择') {
    sourceType = Camera.PictureSourceType.SAVEDPHOTOALBUM;
  }
  navigator.camera.getPicture(
    (imageUri) => {
      // 添加图片到图片列表
      const fileItem = reactive({
        url: imageUri,
        isImage: true,
        status: 'done',
        deletable: true,
      });

      fileList.value.push(fileItem);
      console.log(fileList.value, 'fileList.value');
      imageUriList.value.push(imageUri);
    },
    (err) => {
      Toast('选择图片失败');
      console.error(err);
    },
    {
      quality: 85,
      destinationType: Camera.DestinationType.FILE_URI,
      sourceType: sourceType,
    },
  );
};
const onClickUpload = (e) => {
  e.preventDefault();
  if (navigator.camera) {
    showActionSheet.value = true;
  } else {
    Toast('不支持选择照片');
  }
};
const uploadImages = () => {
  // 上传参数
  for (let i = 0; i < imageUriList.value.length; i++) {
    const imageUri = imageUriList.value[i];
    const options = new window.FileUploadOptions();
    options.fileKey = 'file';
    options.fileName = imageUri.substr(imageUri.lastIndexOf('/') + 1);
    options.headers = {
      Authorization: getReserverToken(),
    };
    const params = {};
    params.coder = 'empty_store_check_report';
    params.businessId = formId.value;
    options.params = params;
    // 上传地址
    const reserverBaseUrl = () =>
      JSON.parse(process.env.VUE_APP_RESERVER_API_BASE_URL)[window.__isProd] || location.origin;
    const uploadUri = new URL(
      '/reserver/api/fileManager/uploadFileBatch',
      reserverBaseUrl(),
    ).toString();

    console.log(imageUri, 'imageUri');
    console.log(options, 'options');
    // if (!formId.value) return;
    // 上传文件
    const fileTransfer = new window.FileTransfer();
    console.log(fileTransfer, 'fileTransfer');
    fileTransfer.upload(
      imageUri,
      uploadUri,
      (res) => {
        console.log(res, 'res11111111111');
        // 上传成功
        const resp = res.response;
        if (resp) {
          console.log(resp, 'resprespresp');
        }
      },
      (error) => {
        // 上传失败
        Toast('上传失败');
        console.error(error);
      },
      options,
    );
  }
};
const onDeleteFile = (file, { index }) => {
  fileList.value.splice(index, 1);
  imageUriList.value.splice(index, 1);
};
const emit = defineEmits(['close']);
const onUpdateClose = () => {
  formRef.value.resetValidation();
  emit('close', false);
};
const onConfirm = async () => {
  await formRef.value.validate();
  const params = {
    storeId: props.info.storeId,
    storageUnitId: props.info.storageUnitId,
    storeHouseId: props.info.storeHouseId,
    checkResult: Number(form.value.checkResult),
    checkDate: dayjs().format('YYYY-MM-DD'),
    ...form.value,
  };
  const res = await addEmptyStore(params);
  console.log('save', res);
  formId.value = res;
  uploadImages();
  Toast('验收成功');
  emit('refresh');
  onUpdateClose();
};
watch(
  () => props.info,
  async (newValue) => {
    fechFiles();
    let res;
    if (newValue?.id) {
      res = await getEmptyStoreDetail({ id: newValue.id });
    }
    form.value.storageUnit = props.info.storageUnit;
    form.value.store = props.info.store;
    form.value.storeHouse = props.info.storeHouse;
    form.value.storeFacility = res?.storeFacility;
    form.value.storeEquipment = res?.storeEquipment;
    form.value.storeHygiene = res?.storeHygiene;
    form.value.remark = res?.remark;
    form.value.checkResult = res?.checkResult + '';
  },
  {
    immediate: true,
  },
);
</script>

<style lang="scss" scoped>
.bottom-warpper {
  position: fixed;
  bottom: 0;
  width: 100%;
  height: 99px;
  background: #ffffff;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  align-items: center;
  .button {
    width: calc(100% - 28px);
    height: 49px;
    background: #1f3359;
    border-radius: 4px;
    font-size: 18px;
    font-weight: 400;
    color: #ffffff;
    line-height: 49px;
    margin-top: 14px;
  }
  .buttons {
    display: flex;
    justify-content: center;
    margin-top: 14px;
    text-align: center;
    .cancel-button {
      width: 100px;
      height: 44px;
      background: #ffffff;
      border-radius: 2px;
      border: 1px solid #686b73;
      font-size: 16px;
      font-weight: 400;
      color: #3a4056;
      line-height: 44px;
      margin-right: 15px;
    }
    .confirm-button {
      width: 100px;
      height: 44px;
      background: #1973f1;
      border-radius: 2px;
      font-size: 16px;
      font-weight: 400;
      color: #ffffff;
      line-height: 44px;
    }
  }
  .bar {
    width: 134px;
    height: 5px;
    background: #000000;
    border-radius: 144px;
    margin-bottom: 9px;
  }
}
</style>
