import { atlasRequest, reserveRequest } from '@/utils/request';

export async function getReserveScale(params) {
  return reserveRequest().get('/api/reserve/scale/info', {
    params,
  });
}

export async function getReserveStock(userId) {
  return atlasRequest().get('/api/zzdAtlas/zcdinfo/getChSjFoodKcZl', {
    params: {
      zbbm: 'gxq_kc_mxzl',
      userId,
      k1: '',
    },
  });
}

export async function getStockVarietyStructure(userId) {
  return atlasRequest().get('/api/zzdAtlas/zcdinfo/subsetArea', {
    params: {
      zbbm: 'gxq_kc_mxzl',
      userId,
    },
  });
}

export async function getStockYearStructure(userId) {
  return atlasRequest().get('/api/zzdAtlas/zcdinfo/getShnfFbKcsl', {
    params: {
      userId,
    },
  });
}

export async function getStockDistribution(userId) {
  return atlasRequest().get('/api/zzdAtlas/zcdinfo/findCdbmKcxxVO', {
    params: {
      userId,
    },
  });
}

//储备库存——根据当前用户的数据权限获取总的储备库存数量
export async function getStockSum(params) {
  return reserveRequest().get('/api/storeQuantity/getMapAreaData', { params });
}

//库存品种结构——柱状图
export async function getStoreQuantityPieData(params) {
  return reserveRequest().get('/api/storeQuantity/getStoreQuantityPieDataBaseDeploy', { params });
}

//库存品种结构——柱状图
export async function getQuantityGradeDistribute(params) {
  return reserveRequest().get('/api/storeQuantity/getQuantityGradeDistribute', { params });
}

//库存产地——柱状图
export async function getStoreQuantityProducingAreaBarData(params) {
  return reserveRequest().get('/api/storeQuantity/getStoreQuantityProducingAreaBarData', {
    params,
  });
}

//收获年份——柱状图
export async function getStoreQuantityYearGainPieData(params) {
  return reserveRequest().get('/api/storeQuantity/getStoreQuantityYearGainPieData', { params });
}

export async function getStoreQuantityBarDataBaseDeploy(params) {
  return reserveRequest().get('/api/storeQuantity/getStoreQuantityBarDataBaseDeploy', { params });
}

// 上方储备库存——根据当前用户的数据权限获取总的储备库存数量
export async function getTopStockSum(params) {
  return reserveRequest().get('/api/storeQuantity/getStockSum', { params });
}

// 储备规模（来源：政府储备）
export function getGovermentReserver(params) {
  return reserveRequest().get('/api/reserve/govermentReserver/info', {
    params,
  });
}
