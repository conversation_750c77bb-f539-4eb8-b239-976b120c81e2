<template>
  <HCard v-if="outQualityWarning.length > 0" title="出库质量预警提示">
    <div class="p-4">
      <div class="flex">
        <img src="@/views/Datav/GrainQuality/images/grain-station.svg" alt="" srcset="" />
        <p class="ml-2">{{ outQualityWarning[0].storeName }}</p>
      </div>
    </div>
    <van-cell-group class="px-2 pb-3 cells">
      <van-cell title="出库质检时间：" :value="outQualityWarning[0].createTime" />
      <van-cell title="值仓仓房：" :value="outQualityWarning[0].storeHouseName" />
      <van-cell title="质检结果：">
        <span class="text-red-500">{{ outQualityWarning[0].cause }}</span>
      </van-cell>
    </van-cell-group>
  </HCard>
</template>

<script setup>
import { onMounted, ref } from 'vue';
import { HCard } from '@/components';
import { reserveRequest } from '@/utils/request';
import dayjs from 'dayjs';
import { useStore } from 'vuex';
const store = useStore();
const userAreaCode = store.getters['user/userAreaCode'];
const outQualityWarning = ref([{}]);
onMounted(async () => {
  outQualityWarning.value = await reserveRequest().get(
    '/api/outQualityAnalysis/outQualityWarning',
    {
      params: {
        year: dayjs().format('YYYY'),
        area: userAreaCode,
      },
    },
  );
});
</script>

<style lang="scss" scoped>
.cells * {
  font-size: 16px;
}
</style>
