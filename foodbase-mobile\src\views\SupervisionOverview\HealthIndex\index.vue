<template>
  <div>
    <div class="chart-container">
      <div class="chart-item">
        <HEchart class="flex-1" ref="pieChart" :options="healthOptions1" />
        <span>健康({{ proportionList[0] }})</span>
      </div>
      <div class="chart-item">
        <HEchart class="flex-1" ref="pieChart" :options="healthOptions2" />
        <span>亚健康({{ proportionList[1] }})</span>
      </div>
      <div class="chart-item">
        <HEchart class="flex-1" ref="pieChart" :options="healthOptions3" />
        <span>不健康({{ proportionList[2] }})</span>
      </div>
    </div>
    <PullRefresh @refresh="onRefresh" v-model="refreshLoading">
      <List
        ref="listRef"
        v-model:loading="isLoading"
        :finished="finished"
        finished-text="没有更多了"
        @load="getList"
      >
        <div class="health-list">
          <div class="health-item" v-for="(item, i) in list" :key="i">
            <div class="mb-[15px] flex justify-between">
              <div class="flex">
                <img :src="require(`@/assets/${helthImgList[item.healthLevel]}`)" alt="" />
                <span class="text-[#232323] font-bold ml-[5px]">{{ item.houseName }}</span>
              </div>
              <div class="flex">
                <van-rate
                  v-model="item.rateValue"
                  color="#FFD21E"
                  void-color="#eee"
                  :allow-half="true"
                  readonly
                  style="margin-right: 10px"
                />
                <div class="text-[#232323] font-bold">评分:{{ item.score }}</div>
              </div>
            </div>
            <div class="health-content">
              评分时间:<span>{{ item.scoreDate }}</span>
            </div>
            <div class="health-content">
              扣分详情:<span>{{ item.deductionDetail ? item.deductionDetail : '无' }}</span>
            </div>
            <div class="health-content">
              作业建议:<span>{{ item.jobSuggestion ? item.jobSuggestion : '无' }}</span>
            </div>
          </div>
        </div>
      </List>
    </PullRefresh>
  </div>
</template>

<script setup>
import { reactive, ref } from 'vue';
import { List, PullRefresh } from 'vant';

import { getHealthList, getHouseProportion } from '@/api/health-index';
import { HEchart } from '@/components';
import { useStore } from 'vuex';
import dayjs from 'dayjs';

// import StoreHouseChart from './StoreHouseChart.vue'
const helthImgList = ref({
  1: 'health.png',
  2: 'sub-health.png',
  3: 'ill-health.png',
});
const proportionList = ref([0, 0, 0]);
const store = useStore();
const userInfo = store.getters['user/userInfo'];
//分页
//列表
const list = ref([]);
const isLoading = ref(false);
const finished = ref(false);
const refreshLoading = ref(false);
const listRef = ref();
let healthOptions1 = reactive({
  series: [
    {
      type: 'liquidFill',
      data: [0.0], // 可以多个波浪，数大在前，数小在后，避免遮挡（不透明情况下)。
      color: ['#28AF2F'],
      center: ['50%', '50%'], // 球心到左/上边界的距离 百分数 或者 px（其他单位也按px计算）
      radius: '80%', // 调节球的大小，百分数或者px
      amplitude: '7%', // 波浪幅度，振幅大小， 0为平面
      waveLength: '80%',
      phase: 'auto',
      period: 'auto',
      direction: 'right',
      shape: 'circle',
      waveAnimation: true, // 开启或关闭波浪动画
      animationEasing: 'linear',
      animationEasingUpdate: 'linear',
      animationDuration: 1000, // 第一次波浪升起来时的动画时长； 设为0后直接切换
      animationDurationUpdate: 2000, // data数据变动时波浪升起或下降的动画时长，数据变动后需调用mychart.setOption(option)

      outline: {
        // 水球外侧默认有一个边框
        show: false, // 默认显示，false不显示
      },

      backgroundStyle: {
        color: '#E3F7E3', // 水球背景色
        borderWidth: 3, // 水球内部圆形的边框宽度， 默认是没有的
        borderColor: 'white', // 设置了边框颜色才能看见边框；
      },
      label: {
        normal: {
          textStyle: {
            color: '#293441',
            insideColor: '#fff',
            fontSize: 12,
          },
        },
      },
      itemStyle: {
        opacity: 0.95, // 波浪颜色透明度
        shadowBlur: 50, // 波浪边缘的阴影
        shadowColor: 'rgba(0, 0, 0, 0.4)',
      },
    },
  ],
  tooltip: {
    show: false, // 鼠标放上显示数据
  },
});
let healthOptions2 = reactive({
  series: [
    {
      type: 'liquidFill',
      data: [0.0], // 可以多个波浪，数大在前，数小在后，避免遮挡（不透明情况下)。
      color: ['#FEA50D'],
      center: ['50%', '50%'], // 球心到左/上边界的距离 百分数 或者 px（其他单位也按px计算）
      radius: '80%', // 调节球的大小，百分数或者px
      amplitude: '7%', // 波浪幅度，振幅大小， 0为平面
      waveLength: '80%',
      phase: 'auto',
      period: 'auto',
      direction: 'right',
      shape: 'circle',
      waveAnimation: true, // 开启或关闭波浪动画
      animationEasing: 'linear',
      animationEasingUpdate: 'linear',
      animationDuration: 1000, // 第一次波浪升起来时的动画时长； 设为0后直接切换
      animationDurationUpdate: 2000, // data数据变动时波浪升起或下降的动画时长，数据变动后需调用mychart.setOption(option)

      outline: {
        // 水球外侧默认有一个边框
        show: false, // 默认显示，false不显示
      },

      backgroundStyle: {
        color: '#FFEED0', // 水球背景色
        borderWidth: 3, // 水球内部圆形的边框宽度， 默认是没有的
        borderColor: 'white', // 设置了边框颜色才能看见边框；
      },
      label: {
        normal: {
          textStyle: {
            color: '#293441',
            insideColor: '#fff',
            fontSize: 12,
          },
        },
      },
      itemStyle: {
        opacity: 0.95, // 波浪颜色透明度
        shadowBlur: 50, // 波浪边缘的阴影
        shadowColor: 'rgba(0, 0, 0, 0.4)',
      },
    },
  ],
  tooltip: {
    show: false, // 鼠标放上显示数据
  },
});
let healthOptions3 = reactive({
  series: [
    {
      type: 'liquidFill',
      data: [0.0], // 可以多个波浪，数大在前，数小在后，避免遮挡（不透明情况下)。
      color: ['#D17B6F'],
      center: ['50%', '50%'], // 球心到左/上边界的距离 百分数 或者 px（其他单位也按px计算）
      radius: '80%', // 调节球的大小，百分数或者px
      amplitude: '7%', // 波浪幅度，振幅大小， 0为平面
      waveLength: '80%',
      phase: 'auto',
      period: 'auto',
      direction: 'right',
      shape: 'circle',
      waveAnimation: true, // 开启或关闭波浪动画
      animationEasing: 'linear',
      animationEasingUpdate: 'linear',
      animationDuration: 1000, // 第一次波浪升起来时的动画时长； 设为0后直接切换
      animationDurationUpdate: 2000, // data数据变动时波浪升起或下降的动画时长，数据变动后需调用mychart.setOption(option)

      outline: {
        // 水球外侧默认有一个边框
        show: false, // 默认显示，false不显示
      },

      backgroundStyle: {
        color: '#FAE3E2', // 水球背景色
        borderWidth: 3, // 水球内部圆形的边框宽度， 默认是没有的
        borderColor: 'white', // 设置了边框颜色才能看见边框；
      },
      label: {
        normal: {
          textStyle: {
            color: '#293441',
            insideColor: '#fff',
            fontSize: 12,
          },
        },
      },
      itemStyle: {
        opacity: 0.95, // 波浪颜色透明度
        shadowBlur: 50, // 波浪边缘的阴影
        shadowColor: 'rgba(0, 0, 0, 0.4)',
      },
    },
  ],
  tooltip: {
    show: false, // 鼠标放上显示数据
  },
});
const getList = async () => {
  let data = await getHouseProportion({
    username: userInfo.username,
    time: dayjs().format('YYYY-MM-DD'),
  });

  if (data.data) {
    healthOptions1.series[0].data[0] = data?.data?.warehouseProportionList[0].proportion / 100;
    healthOptions2.series[0].data[0] = data?.data?.warehouseProportionList[1].proportion / 100;
    healthOptions3.series[0].data[0] = data?.data?.warehouseProportionList[2].proportion / 100;
    proportionList.value[0] = data?.data?.warehouseProportionList[0].sum;
    proportionList.value[1] = data?.data?.warehouseProportionList[1].sum;
    proportionList.value[2] = data?.data?.warehouseProportionList[2].sum;
  }
  try {
    const res = await getHealthList({
      username: userInfo.username,
      time: dayjs().format('YYYY-MM-DD'),
    });
    list.value = res.data;
    list.value.forEach((item) => {
      item.rateValue = parseFloat((item.score / 20).toFixed(1));
    });

    // 加载状态结束
    isLoading.value = false;
    finished.value = true;
    // 数据全部加载完成
    // if (list.value.length >= total) {
    //   finished.value = true;

    // }
  } catch {
    isLoading.value = false;
    finished.value = true;
  }
};
const onRefresh = () => {
  if (refreshLoading.value) {
    // pagination.page = 0;
    // pagination.total = 0;
    list.value = [];
    refreshLoading.value = false;
  }
  finished.value = false;
  isLoading.value = true;
  getList();
};
</script>

<style lang="scss" scoped>
.health-item {
  background: #ffffff;
  margin-top: 10px;
  padding: 16px;
  .health-content {
    margin-top: 12px;
    font-size: 16px;
    color: #686b73;
    span {
      margin-left: 16px;
      color: #232323;
    }
  }
}
.chart-container {
  width: 100%;
  background-color: #ffffff;
  height: 150px;
  display: flex;
  justify-content: space-between;
  margin-bottom: 10px;
  padding: 20px 10px;
  .chart-item {
    width: 33%;
    display: flex;
    flex-direction: column;
    align-items: center;
  }
  .flex-1 {
    height: 100%;
    width: 100%;
  }
}
</style>
