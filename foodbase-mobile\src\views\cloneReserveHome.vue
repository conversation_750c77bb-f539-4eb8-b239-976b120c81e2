<template>
  <div class="pb-20 overflow-hidden reserve-home">
    <div class="background"></div>
    <!-- <div class="mb-10 header">
      <div class="title">{{ active ? '我的' : '首页' }}</div>
      <div class="right-action" @click="onLogout">
        退出
        <SvgIcon name="exit"></SvgIcon>
      </div>
    </div> -->
    <van-nav-bar
      class="mt-10 mb-10 header bg-none home-nav-bar"
      :title="active ? '我的' : subMenu.length ? subMenuTitle : '首页'"
      :left-text="subMenu.length > 0 ? '返回' : ''"
      :left-arrow="subMenu.length > 0"
      @click-left="clickBack"
    />

    <div
      class="mb-2 nav-items"
      v-show="active === 0"
      v-p="['app-a-picture-of-grain-quality', 'app-acquisition-season-battle-map']"
    >
      <div class="px-4">
        <van-row gutter="14">
          <van-col :span="checkPermission('app-acquisition-season-battle-map') ? 12 : 24">
            <div class="relative h-24 chart-bg" v-p="['app-a-picture-of-grain-quality']">
              <router-link
                to="/datav/grain-quality"
                class="absolute w-full h-full pr-2 font-bold text-right text-blue-600 right-1 top-3"
              >
                粮食质量一张图
                <van-icon name="upgrade" class="rotate-90" />
              </router-link>
            </div>
          </van-col>

          <van-col :span="checkPermission('app-a-picture-of-grain-quality') ? 12 : 24">
            <div
              class="relative h-24 w-full bg-white border-white content-box"
              v-p="['app-acquisition-season-battle-map']"
            >
              <div
                class="relative h-24 chart-bg bg2"
                :class="{ 'chart-bg-full': !checkPermission('app-a-picture-of-grain-quality') }"
              >
                <router-link
                  to="/datav/acquisition-season"
                  class="absolute w-full h-full pr-2 font-bold text-right text-blue-600 right-1 top-3"
                >
                  粮食收购一张图
                  <van-icon name="upgrade" class="rotate-90" />
                </router-link>
              </div>
            </div>
          </van-col>
        </van-row>
      </div>
    </div>
    <!-- <div class="mx-4 annual"   v-show="active === 0">
        <router-link to="/annual-report">
         <p>您的2023年度工作报告，请查收！</p>
        </router-link>
      </div> -->
    <div class="nav-items" v-if="active === 0">
      <van-grid
        v-if="subMenu.length === 0"
        class="mx-4 overflow-hidden bg-white rounded-lg"
        :column-num="3"
      >
        <van-grid-item
          v-for="item in navItems"
          :key="item.name"
          @click="onNavigate(item)"
          v-p="item.permission"
        >
          <div
            :class="['icon', item.icon]"
            :style="`background-position: ${item.bgPosition}px 0;`"
          ></div>
          <span class="mt-4 name">{{ item.name }}</span>
        </van-grid-item>
      </van-grid>

      <div v-else class="nav-items">
        <div
          class="item"
          v-for="item in subMenu"
          v-p="item.permission"
          :key="item.name"
          @click="onNavigate(item)"
        >
          <div class="icon" :style="`background-position: ${item.bgPosition}px 0;`"></div>
          <span class="name">{{ item.name }}</span>
          <Icon name="arrow" />
        </div>
      </div>
    </div>

    <div v-if="active === 1">
      <van-cell-group inset>
        <van-cell title="账号名称" :value="userInfo.username" />
        <van-cell title="用户名称" :value="userInfo.nickName" />
        <van-cell title="所属组织" :value="userInfo.dept.name" />
        <van-cell
          title="APP当前版本"
          :value="`${localVersion || '无法获取'} - #${window?.__appInfo?.pipeLineId || '无法获取'}`"
        />
        <van-cell title="APP最新版本" :value="remoteVersion || '无法获取'" />
      </van-cell-group>
      <div class="mt-4"></div>
      <van-cell-group inset>
        <van-cell v-if="isNewVersion">
          <van-button icon="upgrade" type="primary" @click="checkAppVersion" plain block
            >点击更新</van-button
          >
        </van-cell>
        <van-cell>
          <van-button type="danger" @click="onLogout" plain block>退出</van-button>
        </van-cell>
      </van-cell-group>
    </div>

    <van-tabbar v-model="active">
      <van-tabbar-item icon="home-o"
        ><span class="text-lg" @click="clickShouYe">首页</span></van-tabbar-item
      >
      <van-tabbar-item icon="contact"><span class="text-lg">我的</span></van-tabbar-item>
    </van-tabbar>
  </div>
</template>

<script setup>
// 收购储备APP首页
import { ref, onMounted, computed, watch } from 'vue';
import { useRouter, useRoute } from 'vue-router';
import { useStore } from 'vuex';
import { Icon, Dialog } from 'vant';
// import SvgIcon from '@/components/SvgIcon/SvgIcon';
import { navItems } from './Home/Home';
import { checkPermission } from '@/utils/permission';
// import { computed } from 'vue';

const store = useStore();
// const isCompanyUser = store.getters['user/isCompanyUser'];
const userInfo = store.getters['user/userInfo'];

const active = ref(0);
// const userNavItems = computed(() => {
//   let filteredNavItems = [];
//   if (!isCompanyUser) {
//     filteredNavItems = navItems.filter((it) => it.name !== '预约查询');
//   }
//   if (process.env.VUE_APP_IS_HOUSE_OPERATION === 'true') {
//     filteredNavItems = navItems.filter((it) => it.name !== '智能物流作业');
//   } else {
//     filteredNavItems = navItems.filter((it) => it.name !== '值仓管理');
//   }

//   return filteredNavItems;
// });

const router = useRouter();
const route = useRoute();
const subMenu = ref([]);
// const subMenuTitle = ref('');

const onNavigate = (item) => {
  const { route } = item;
  router.push(route);
};

const onLogout = () => {
  router.replace({ name: 'Logout' });
};

const localVersion = window?.AppVersion?.build || 1;
const remoteVersion = ref('');

onMounted(async () => {
  console.log(route, '哈哈哈哈哈哈');
  remoteVersion.value = await getRemoteVersion();
  subMenu.value = route.query.data ? JSON.parse(route.query.data) : '';
  // subMenuTitle.value = subMenu.value[0].name;
});

const isNewVersion = computed(() => parseInt(localVersion) < parseInt(remoteVersion.value));

const getRemoteVersion = async () => {
  const updateUrl = `http://223.4.79.9:31201/foodreserver-apps/${window.__isProd ? 'dev-' : ''}${
    process.env.VUE_APP_MODE
  }-version.xml`;

  const remoteInfo = await fetch(updateUrl)
    .then((res) => res.text())
    .then((str) => new window.DOMParser().parseFromString(str, 'text/xml'));

  return Promise.resolve(
    `${remoteInfo.querySelector('version')?.textContent || 1} - ${
      remoteInfo.querySelector('remark')?.textContent || '无法获取'
    }`,
  );
};
const clickBack = () => {
  subMenu.value = [];
  router.back();
};
const clickShouYe = () => {
  subMenu.value = [];
  // router.back();

  router.push({ name: 'Home' });
};

const checkAppVersion = async () => {
  const msg = () => {
    Dialog.alert({
      title: '提示',
      message: '当前已是最新版本',
    });
  };

  // const remoteVersion = await getRemoteVersion();

  if (isNewVersion.value) {
    window.checkAppVersion(!window.__isProd);
  } else {
    msg();
  }
};
watch(
  () => active.value,
  () => {
    if (active.value === 1) {
      console.log(active, 'active');
      subMenu.value = [];
    }
  },
);
</script>

<style scoped lang="scss">
.reserve-home {
  position: relative;
  .background {
    position: absolute;
    width: 100%;
    height: 30vh;
    background-size: 100% 100%;
    background-image: url('../assets/bg-reserve-home.png');
    background-repeat: no-repeat;
    &::after {
      content: '';
      display: block;
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      // backdrop-filter: hue-rotate(120deg);
    }
  }
  .header {
    position: relative;
    color: #ffffff;
    width: 100%;

    .title {
      font-size: 19px;
      font-weight: 500;
      padding: 16px;
      color: #ffffff;
      line-height: 26px;
      text-shadow: 0 1px 1px rgba(0, 0, 0, 0.5);
      text-align: center;
    }

    .right-action {
      font-size: 17px;
      position: absolute;
      right: 8px;
      top: 12px;
      padding: 4px;
      display: flex;
      align-items: center;

      .svg-icon {
        margin-left: 4px;
        font-size: 16px;
      }
    }
  }

  .icon {
    height: 48px;
    width: 48px;
    background-position: center;
    background-size: 100%;
    background-repeat: no-repeat;
    background-image: url('@/assets/icons.png');
    background-size: cover;
  }

  .nav-items {
    position: relative;
    margin-top: 50px;
    .item {
      height: 80px;
      margin: 16px;
      padding: 0 20px 0 25px;
      box-shadow: 0 1px 16px 0 rgba(75, 75, 75, 0.1);
      border-radius: 8px;
      background-color: #ffffff;
      font-size: 19px;
      font-weight: 500;
      color: #272727;
      display: flex;
      align-items: center;

      .icon {
        height: 48px;
        width: 48px;
        background-position: center;
        background-size: 100%;
        background-repeat: no-repeat;
        background-image: url('@/assets/icons.png');
        background-size: cover;
      }

      .name {
        margin-left: 18px;
      }

      .van-icon {
        margin-left: auto;
        color: #8a8a8a;
      }
    }
  }
}

::v-deep(.van-nav-bar) {
  background: none !important;
}
.content-box {
  border-radius: 6px;
}
.chart-bg {
  border-radius: 6px;
  overflow: hidden;
  flex-basis: 48% !important;
  background-color: #fff;
  background: url('@/assets/home/<USER>') no-repeat bottom center;
  background-size: 100% 100%;
  justify-content: flex-start;
  align-items: end;

  &.bg2 {
    background: url('@/assets/home/<USER>') no-repeat bottom center;
    background-size: 100% 100%;
  }
}
.chart-bg-full {
  background-size: contain;
  &.bg2 {
    background-position: 20px -10px;
    background-size: contain;
  }
}
.annual {
  // width: 95%;
  position: relative;
  box-sizing: border-box;
  // height: 44px;
  // margin: auto;
  // background-color: red;
  background-image: url('@/assets/annual/reportimg.png');
  background-repeat: no-repeat;
  background-size: 100% 100%;
  margin-bottom: 10px;
  p {
    height: 44px;
    line-height: 58px;
    font-size: 14px;
    font-weight: 500;
    color: #0a4bb1;
    letter-spacing: 1px;
    padding-left: 48px;
  }
}
.public {
  z-index: 99999;
  color: #fff;
  background-color: red;
  position: relative;
}
</style>
