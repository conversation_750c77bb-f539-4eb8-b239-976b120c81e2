<template>
  <div class="job-approve-detail">
    <component :is="currentComponent"></component>
  </div>
</template>

<script setup>
import { useRoute } from 'vue-router';
import { useStore } from 'vuex';
import {
  YearRotatePlan,
  AgreementSellNotice,
  AgreementSellWaybill,
  BiddingSellGoodsOrder,
  BiddingSellNotice,
  BidInvitingNotice,
  BidInvitingPlan,
  BidInvitingResult,
  RotationPlanAllocation,
  SellExpoundPlan,
  SellExpoundResult,
  Biddingprocurementplan,
  Biddingsalesplan,
} from './details';
import { computed } from 'vue';

const route = useRoute();
const store = useStore();
// eslint-disable-next-line vue/return-in-computed-property
const currentComponent = computed(() => {
  switch (route.params.type) {
    case '年度轮换计划':
      return YearRotatePlan;
    case '轮入计划配仓':
      return RotationPlanAllocation;
    case '竞价销售轮出方案':
      return SellExpoundPlan;
    case '竞价销售成交结果':
      return SellExpoundResult;
    case '竞价销售出库通知书':
      return BiddingSellNotice;
    case '竞价销售提货单':
      return BiddingSellGoodsOrder;
    case '协议销售出库通知书':
      return AgreementSellNotice;
    case '协议销售提货单':
      return AgreementSellWaybill;
    case '招标采购轮入方案':
      return BidInvitingPlan;
    case '招标采购成交结果':
      return BidInvitingResult;
    case '招标采购入库通知书':
      return BidInvitingNotice;
    case '招标采购方案标的':
      return Biddingprocurementplan;
    case '竞价销售方案标的':
      return Biddingsalesplan;
  }
});
const titleName = route.params.type + '审批';
store.commit('setPageTitle', titleName);
</script>
