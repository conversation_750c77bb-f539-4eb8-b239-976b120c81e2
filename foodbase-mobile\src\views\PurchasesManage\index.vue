<template>
  <div class="inout-manage">
    <div
      v-for="(item, index) in list"
      :key="index"
      class="item"
      @click="gotoRoute(item)"
      v-p="[item.permission]"
    >
      <div class="icon" :style="{ backgroundImage: `url(${item.icon})` }"></div>
      <div class="name">{{ item.name }}</div>
    </div>
  </div>
</template>

<script>
import IconJobAppointment from '@/assets/icon-job-appointment.png';
import IconCheckInOut from '@/assets/icon-check-in-out.png';
import IconJobScheduling from '@/assets/icon-job-scheduling.png';

const list = [
  {
    icon: IconJobAppointment,
    name: '订单清册',
    route: { name: 'OrderPublicList' },
    permission: 'app-order-public-list',
  },
  {
    icon: IconCheckInOut,
    name: '收购进度',
    permission: 'app-purchases-progress',
    route: { name: 'PurchasesProgress' },
  },
  {
    icon: IconJobScheduling,
    name: '订单查询',
    permission: 'app-order-search',
    route: { name: 'OrderSearch' },
  },
];
export default {
  name: 'InoutManage',
  data() {
    return {
      list: list,
    };
  },
  methods: {
    gotoRoute(item) {
      this.$router.push({ name: item.route.name });
    },
  },
};
</script>

<style lang="scss" scoped>
.inout-manage {
  display: flex;
  flex-wrap: wrap;
  padding: 14px;
  justify-content: space-between;
  .item {
    flex-basis: calc((100% - 15px) / 2);
    height: 80px;
    background: #ffffff;
    border-radius: 4px;
    display: flex;
    justify-content: center;
    align-items: center;
    margin-bottom: 14px;
    .icon {
      height: 35px;
      width: 35px;
      background-position: center;
      background-size: 100%;
      background-repeat: no-repeat;
      margin-right: 12px;
    }
    .name {
      font-size: 16px;
      font-weight: 500;
      color: #272727;
      line-height: 16px;
    }
  }
}
</style>
