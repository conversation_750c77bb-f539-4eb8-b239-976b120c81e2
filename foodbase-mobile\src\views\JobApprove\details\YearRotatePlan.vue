<template>
  <div>
    <div class="plan-detail">
      <p class="title">{{ title }}</p>
      <div class="detail">
        <p class="type-txt">计划轮出</p>
        <div class="type-content">
          <div>粮食：{{ summary.grainOut }}吨</div>
          <div>食用油：{{ summary.oilOut }}吨</div>
        </div>
        <p class="type-txt type-in-txt">计划轮入</p>
        <div class="type-content">
          <div>粮食：{{ summary.grainIn }}吨</div>
          <div>食用油：{{ summary.oilIn }}吨</div>
        </div>
      </div>
    </div>
    <div class="plan-info">
      <Tabs
        v-model="active"
        animated
        title-active-color="#323233"
        title-inactive-color="#686B73"
        line-height="5px"
        line-width="40px"
      >
        <Tab title="申请信息">
          <div class="type-title" style="border-top: 1px solid #dddddd">
            <img src="@/assets/icon-rotation-out.png" />
            <p>计划轮出</p>
          </div>
          <div v-for="item in outList" :key="item.id">
            <rotationItem :rotation-type="'out'" :item="item" class="rotation-item" />
          </div>
          <div class="type-title">
            <img src="@/assets/icon-rotation-out.png" />
            <p>计划轮入</p>
          </div>
          <div v-for="item in inList" :key="item.id">
            <rotationItem :rotation-type="'in'" :item="item" class="rotation-item" />
          </div>
          <approvalButton :id="params.id" coder="rotationPlan"></approvalButton>
        </Tab>
        <Tab title="审批流程">
          <approvalProcess :id="params.id" coder="rotationPlan"></approvalProcess>
        </Tab>
      </Tabs>
    </div>
  </div>
</template>

<script setup>
import { Tab, Tabs } from 'vant';
import { ref, onMounted, reactive } from 'vue';
import { useRoute } from 'vue-router';
import rotationItem from './common/rotation-item';
import approvalProcess from './common/approval-process';
import approvalButton from './common/approval-button';
import { getYearRotatePlanDetail } from '@/api/job-approve';
import { getFoodCategory } from '@/api/in-out-manage';

const route = useRoute();
const params = ref(route.params);
const title = ref(route.query.name);
const active = ref(1);

const outList = ref([]);
const inList = ref([]);
const foodCategoryList = ref([]);
const summary = reactive({});
const getFoodCategoryList = async () => {
  foodCategoryList.value = await getFoodCategory();
};
const getDetail = async () => {
  let { batchId } = route.params;
  let data = await getYearRotatePlanDetail({ batchId: batchId });
  data.rotationPlanOutDetail.forEach((i) => {
    i.categoryType = foodCategoryList.value.find((m) => m.id == i.categoryId)?.categoryType;
    return i;
  });
  data.rotationPlanInDetail.forEach((i) => {
    i.categoryType = foodCategoryList.value.find((m) => m.id == i.categoryId)?.categoryType;
    return i;
  });
  let grainOut = data.rotationPlanOutDetail?.filter((i) => i.categoryType !== 3);
  let grainIn = data.rotationPlanInDetail?.filter((i) => i.categoryType !== 3);
  let oilOut = data.rotationPlanOutDetail?.filter((i) => i.categoryType === 3);
  let oilIn = data.rotationPlanInDetail?.filter((i) => i.categoryType === 3);
  outList.value = data.rotationPlanOutDetail;
  inList.value = data.rotationPlanInDetail;

  summary.grainOut = grainOut.reduce((p, c) => {
    return (Number(p) + Number(c.plannedNumber)).toFixed(3);
  }, 0);
  summary.grainIn = grainIn.reduce((p, c) => {
    return (Number(p) + Number(c.plannedNumber)).toFixed(3);
  }, 0);
  summary.oilOut = oilOut.reduce((p, c) => {
    return (Number(p) + Number(c.plannedNumber)).toFixed(3);
  }, 0);
  summary.oilIn = oilIn.reduce((p, c) => {
    return (Number(p) + Number(c.plannedNumber)).toFixed(3);
  }, 0);
};

onMounted(async () => {
  await getFoodCategoryList();
  getDetail();
});
</script>

<style lang="scss" scoped>
.plan-detail {
  padding: 16px;
  background: #fff;
  color: #232323;
  .title {
    font-size: 16px;
    font-weight: bold;
  }
  .detail {
    border-radius: 4px;
    background: #f4f8ff;
    padding: 10px 12px;
    margin-top: 16px;
    .type-txt {
      color: #686b73;
    }
    .type-in-txt {
      padding-top: 16px;
    }
    .type-content {
      display: flex;
      justify-content: space-between;
      padding-top: 12px;
    }
  }
}
.plan-info {
  margin-top: 10px;
  .type-title {
    display: flex;
    align-items: center;
    border-bottom: 1px solid #dddddd;
    background: #fff;
    height: 54px;
    img {
      width: 20px;
      height: 20px;
      margin: 0 8px 0 16px;
    }
    p {
      font-weight: bold;
    }
  }
  .rotation-item {
    margin-bottom: 10px;
    background: #fff;
    padding: 16px;
  }
}
</style>
