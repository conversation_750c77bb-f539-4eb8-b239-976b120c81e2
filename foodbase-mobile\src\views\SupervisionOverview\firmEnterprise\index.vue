<template>
  <HCard class="firmEnterprise-card">
    <!-- <div>1</div> -->
    <PullRefresh v-model="refreshLoading" @refresh="onRefresh">
      <van-list
        v-model:loading="loading"
        :finished="finished"
        finished-text="没有更多了"
        @load="onLoad"
      >
        <!-- 下拉刷新 -->
        <van-cell v-for="item in list" :key="item.id" @click="onNavigate(item)">
          <div class="list-icon">
            <div>
              <van-icon
                name="logistics"
                :color="colorType(item)"
                size="2rem"
                style="margin: 0 25px"
              />
            </div>
            <span>{{ item.name }}</span>
          </div></van-cell
        >
      </van-list>
    </PullRefresh>
  </HCard>
</template>

<script>
export default {
  name: 'firmEnterprise',
};
</script>

<script setup>
import { ref, onMounted, reactive } from 'vue';
import { PullRefresh, Toast } from 'vant';

import { HCard } from '@/components';
import { useRouter } from 'vue-router';
import { getHouseList } from '@/api/firmEnterprise';

const router = useRouter();
const loading = ref(false);
//列表数据
const list = ref([]);
const pagination = reactive({
  page: 1,
  size: 99999,
  total: 0,
});
const refreshLoading = ref(false);

const finished = ref(false);
const onLoad = async () => {
  loading.value = true;
  try {
    const { items, page, total } = await getHouseList({
      page: 1,
      size: 99999,
    });

    list.value = items;
    pagination.page = page;
    pagination.total = total;
    finished.value = list.value.length >= total;
    loading.value = false;
    refreshLoading.value = false;
  } catch (error) {
    finished.value = true;
    loading.value = false;
    refreshLoading.value = false;
  }
};
const onRefresh = async () => {
  await onLoad();
  Toast('刷新成功');
};
// 列表点击;
const onNavigate = (row) => {
  router.push({ name: 'firmEnterpriseDetail', params: { id: row.id } });
};
// icon颜色变化
const colorType = (row) => {
  let colorValues = '';
  switch (row.typer) {
    case 1:
      colorValues = '#70b603';
      break;
    case 2:
      colorValues = '#02a7f0';
      break;
    case 3:
      colorValues = '#d9001b';
      break;
    case 4:
      colorValues = '#8080ff';
      break;
    default:
      colorValues = '#7f7f7f';
      break;
  }
  return colorValues;
};

onMounted(() => {
  onLoad();
});
// onActivated(() => {
//   onLoad();
// });
</script>

<style scoped lang="scss">
.list-icon {
  display: flex;
}
.firmEnterprise-card {
  min-height: 100%;
  // height: 95vh;
  .content {
    padding: 15px;
    height: 100%;
  }
}
</style>
