<template>
  <div class="purchases-progress-statistics">
    <div class="query-form">
      <Row gutter="8">
        <Col span="8">
          <HYearPicker v-model:value="year" />
        </Col>
        <!-- <Col span="8" v-if="!isCompanyUser">
          <AreaPicker v-model:value="areaCode" all-area-select />
        </Col> -->

        <Col span="14">
          <FoodCategorySelect
            v-model:value="categoryId"
            :foodCategory="foodCategory"
            @confirm="confirmFoodCategory"
          />
        </Col>
      </Row>
      <Row gutter="8" style="margin-top: 8px">
        <Col span="22">
          <AllUnitSelect
            v-model:value="purchasingStation"
            :isCompanyUser="isCompanyUser"
            :userId="userId"
            @confirm="onConfirm"
            placeholder="收购单位"
          />
        </Col>

        <!-- <Col span="8" :offset="isCompanyUser ? '8' : '0'">
          <UnitName />
        </Col> -->
      </Row>
    </div>
    <template v-if="isEmpty">
      <EmptyHolder></EmptyHolder>
    </template>
    <template v-else>
      <!-- <div class="total-section">
        <div class="section-header" v-if="!isCompanyUser">
          <div class="title">
            <SvgIcon name="order-date" />
            全省收购进度
          </div>
        </div>
        <div class="section-content">
          <HCard>
            <div class="total-purchases">
              <div class="total-purchases-plan">
                <HFixedNumber :fraction-digits="0" class="value">
                  {{ purchasesProgressTotal.purchasesPlan }}
                </HFixedNumber>
                <div class="name">收购计划</div>
              </div>
              <div class="total-purchases-actual">
                <HFixedNumber :fraction-digits="0" class="value">
                  {{ purchasesProgressTotal.purchaseQuantity }}
                </HFixedNumber>
                <div class="name">收购数量</div>
              </div>
            </div>
            <div class="order-purchases">
              <div class="order-purchases-item">
                <div class="name">订单计划</div>
                <HFixedNumber :fraction-digits="0" class="value">
                  {{ purchasesProgressTotal.orderPlan }}
                </HFixedNumber>
              </div>
              <div class="order-purchases-item">
                <div class="name">订单收购数量</div>
                <HFixedNumber :fraction-digits="0" class="value">
                  {{ purchasesProgressTotal.orderQuantity }}
                </HFixedNumber>
              </div>
              <div class="order-purchases-item">
                <div class="name">订单收购进度</div>
                <div class="value">
                  <HFixedNumber
                    :fraction-digits="purchasesProgressTotal.orderProgress >= 100 ? 0 : 1"
                  >
                    {{ purchasesProgressTotal.orderProgress }}
                  </HFixedNumber>
                  <span>%</span>
                </div>
              </div>
            </div>
          </HCard>
        </div>
      </div> -->
      <div class="classify-section">
        <!-- <div class="section-header">
          <div class="title">
            <SvgIcon name="order" />
            订单收购进度详情
          </div>
        </div> -->
        <div class="section-content">
          <div v-for="order in purchasesProgressDetail" :key="order.orderType">
            <div style="font-weight: bold">{{ order.purchasingStationName }}</div>

            <HCard>
              <div class="order-detail">
                <div class="order-type-wrapper">
                  <span style="font-weight: bold">{{ order.foodCategoryName }}</span>
                  <Tag :color-type="Number(order.orderType)">{{ order.orderTypeName }}</Tag>
                </div>
                <div class="order-item">
                  <div class="name">收购计划数（吨）：</div>
                  <HFixedNumber class="value" :fraction-digits="0">
                    {{ order.orderPlan }}
                  </HFixedNumber>
                </div>
                <div class="order-item">
                  <div class="name">已收购数量（吨）：</div>
                  <HFixedNumber class="value" :fraction-digits="0">
                    {{ order.orderQuantity }}
                  </HFixedNumber>
                </div>
                <div class="order-item">
                  <div class="name">收购进度：</div>
                  <Progress
                    class="progress"
                    :show-pivot="false"
                    stroke-width="16"
                    color="#1492FF"
                    :percentage="order.orderProgress"
                  />
                  <div class="value">
                    <HFixedNumber :fraction-digits="order.orderProgress >= 100 ? 0 : 1">
                      {{ order.orderProgress }}
                    </HFixedNumber>
                    <span>%</span>
                  </div>
                </div>
                <!-- <br class="divider" />

              <div class="order-item">
                <div class="name">其中五优联动订单</div>
              </div>

              <div class="order-item">
                <div class="name">订单计划：</div>
                <HFixedNumber class="value" :fraction-digits="0">
                  {{ order.orderPlanWY }}
                </HFixedNumber>
              </div>
              <div class="order-item">
                <div class="name">订单收购数量：</div>
                <HFixedNumber class="value" :fraction-digits="0">
                  {{ order.orderQuantityWY }}
                </HFixedNumber>
              </div>
              <div class="order-item">
                <div class="name">订单收购进度：</div>
                <Progress
                  class="progress"
                  :show-pivot="false"
                  stroke-width="16"
                  color="#6BDC50"
                  :percentage="order.orderProgressWY"
                />
                <div class="value">
                  <HFixedNumber :fraction-digits="order.orderProgressWY >= 100 ? 0 : 1">
                    {{ order.orderProgressWY }}
                  </HFixedNumber>
                  <span>%</span>
                </div>
              </div> -->
              </div>
            </HCard>
          </div>
        </div>
      </div>
    </template>
  </div>
</template>

<script setup>
import { computed, effect, ref } from 'vue';
import { Progress, Row, Col } from 'vant';
import { HCard, HFixedNumber } from '@/components';
// import SvgIcon from '@/components/SvgIcon/SvgIcon';
import Tag from '@/views/PurchasesOverview/common/Tag';
// import UnitName from '@/views/common/UnitName';
import EmptyHolder from '@/views/common/EmptyHolder';
import HYearPicker from '@/components/HYearPicker/HYearPicker';
import { getPurchasesProgressDetail } from '@/api/purchases-progress';
// import AreaPicker from '@/views/common/AreaPicker';
import FoodCategorySelect from '@/views/common/FoodCategorySelect';
import { useStore } from 'vuex';
import AllUnitSelect from '@/views/PurchasesOverview/common/AllUnitSelect';

const props = defineProps({
  foodCategory: Array,
});

const store = useStore();

const orderTypeMap = {
  1: '省订单',
  2: '市订单',
  3: '县订单',
  4: '订单外',
};
const categoryId = ref(null);
const isCompanyUser = computed(() => {
  const userOrgLevel = store.state.user?.reserverIds?.userOrgLevel;
  return [7, 8, 9].includes(userOrgLevel);
});
// const areaCode = ref(store.getters['user/userAreaCode']);
const year = ref(String(new Date().getFullYear()));
const deptId = computed(() => {
  return store.state.user?.reserverIds?.deptId;
});
const level = computed(() => {
  return store.state.user?.reserverIds?.level;
});
const userId = computed(() => {
  return store.state.user?.reserverIds?.userId;
});
const purchasingStation = ref('');
const onConfirm = (value) => {
  purchasingStation.value = value;
};
const confirmFoodCategory = (value) => {
  categoryId.value = value;
};

// const purchasesProgressTotal = reactive({
//   purchasesPlan: 0,
//   purchaseQuantity: 0,
//   orderPlan: 0,
//   orderQuantity: 0,
//   orderProgress: 0,
// });

const purchasesProgressDetail = ref([]);

// 收购进度统计
// effect(async () => {
//   const data = await getPurchasesProgressTotal({
//     areaCode: areaCode.value,
//     foodCategory: props.foodCategory,
//     year: year.value,
//   });
//   const { purchasePlan, purchaseQuantity, orderPlan, orderQuantity, orderProgress } = data;
//   purchasesProgressTotal.purchasesPlan = purchasePlan;
//   purchasesProgressTotal.purchaseQuantity = purchaseQuantity;
//   purchasesProgressTotal.orderPlan = orderPlan;
//   purchasesProgressTotal.orderQuantity = orderQuantity;
//   purchasesProgressTotal.orderProgress = orderProgress * 100;
// });

// 收购进度详情
effect(async () => {
  purchasesProgressDetail.value = [];
  const { items } =
    (await getPurchasesProgressDetail({
      page: 1,
      size: 9999,
      typeList: [1, 2, 3],
      level: level.value,
      deptId: deptId.value,
      categoryIds: categoryId.value || props.foodCategory,
      year: year.value,
      purchasingStation: purchasingStation.value,
    })) || [];
  purchasesProgressDetail.value = items.map((it) => {
    const {
      totalAcquisitionPlan,
      completedNumber,
      acquisitionProgress,
      orderType,
      foodCategoryName,
      purchasingStationName,
    } = it;
    return {
      orderTypeName: orderTypeMap[orderType],
      orderPlan: totalAcquisitionPlan,
      orderQuantity: completedNumber,
      orderProgress: acquisitionProgress ? acquisitionProgress.split('%')[0] : 0,
      foodCategoryName,
      purchasingStationName,
    };
  });
});

const isEmpty = computed(() => {
  return !purchasesProgressDetail.value;
});
</script>

<style scoped lang="scss">
.purchases-progress-statistics {
  font-size: 18px;
}

.query-form {
  margin: 8px 16px;
}

.section-header {
  display: flex;
  font-weight: 500;
  color: var(--van-gray-7);
  line-height: 32px;
  padding: 8px 16px;

  .svg-icon {
    color: #1492ff;
    margin-right: 8px;
  }

  .title {
    display: flex;
    align-items: center;
  }
}

.total-purchases {
  display: flex;
  padding: 20px;
  .total-purchases-plan,
  .total-purchases-actual {
    flex: 1;
    min-width: 0;
    text-align: center;
  }

  .value {
    color: #1492ff;
    font-size: 22px;
    font-weight: bold;
    line-height: 32px;
  }
  .name {
    font-size: 18px;
    line-height: 32px;
  }
}

.order-purchases {
  padding: 0 20px 10px 20px;

  &-item {
    display: flex;
    justify-content: space-between;
    line-height: 40px;

    .name {
      font-size: 18px;
    }

    .value {
      font-size: 20px;
      font-weight: bold;
    }
  }
}

.classify-section {
  .section-content {
    padding: 0 16px;
  }

  .order-detail {
    padding: 8px;
    font-size: 18px;
    line-height: 32px;
  }

  .order-type-wrapper {
    display: flex;
  }

  .order-item {
    display: flex;
    align-items: center;
  }

  .progress {
    flex-grow: 1;
    min-width: 0;
    margin-right: 8px;
    border-radius: 8px;
    overflow: hidden;
  }

  .divider {
    border-bottom: 1px solid #e8e9ec;
    display: block;
    content: '';
    margin: 8px 0;
  }

  .h-card {
    margin-bottom: 16px;
  }
}
</style>
