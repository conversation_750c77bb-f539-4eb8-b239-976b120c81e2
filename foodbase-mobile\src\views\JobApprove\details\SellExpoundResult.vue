<template>
  <div>
    <div class="plan-detail">
      <p class="title">{{ title }}</p>
      <div class="detail">
        <img :src="require('@/assets/' + totalStatus + '-deal.png')" />
        <p><span class="label">委托数量：</span>{{ detailInfo.totalPlannedSales }}吨</p>
        <p><span class="label">总成交量：</span>{{ detailInfo.totalTurnover }}吨</p>
        <p><span class="label">总成交金额：</span>{{ detailInfo.totalPrice }}元</p>
        <p><span class="label">保证金合计：</span>{{ detailInfo.bond }}元</p>
        <p><span class="label">中标日期：</span>{{ detailInfo.winBidDate }}</p>
      </div>
    </div>
    <div class="plan-info">
      <Tabs
        v-model="active"
        animated
        title-active-color="#323233"
        title-inactive-color="#686B73"
        line-height="5px"
      >
        <Tab title="申请信息">
          <div v-for="item in detailList" :key="item.level">
            <resultItem :item="item" class="rotation-item" />
          </div>
          <approvalButton :id="params?.id" :coder="params?.coder"></approvalButton>
        </Tab>
        <Tab title="审批流程">
          <approvalProcess :id="params?.id" :coder="params?.coder"></approvalProcess>
        </Tab>
      </Tabs>
    </div>
  </div>
</template>

<script setup>
import { Tab, Tabs } from 'vant';
import { ref, onMounted } from 'vue';
import { useRoute } from 'vue-router';
import resultItem from './common/result-item';
import approvalProcess from './common/approval-process';
import approvalButton from './common/approval-button';
import { getSellExpoundResultDetail } from '@/api/job-approve';

const route = useRoute();

const params = ref(route.params);
const title = ref(route.query.name);
const active = ref(1);
const detailInfo = ref({});
const detailList = ref([]);
const totalStatus = ref('all');

const getDetail = async () => {
  let { transactionResult, list } = await getSellExpoundResultDetail({
    id: params.value.id,
    typer: '1',
  });
  detailInfo.value = transactionResult;
  detailList.value = list;
  const hasMiss = list.some((i) => i.isAbandoned == 1);
  const hasDeal = list.some((i) => i.isAbandoned == 0);
  if (hasMiss && hasDeal) {
    totalStatus.value = 'part';
  }
  if (!hasMiss && hasDeal) {
    totalStatus.value = 'all';
  }
  if (hasMiss && !hasDeal) {
    totalStatus.value = 'miss';
  }
};

onMounted(async () => {
  getDetail();
});
</script>

<style lang="scss" scoped>
.plan-detail {
  padding: 16px;
  background: #fff;
  color: #232323;
  .title {
    font-size: 16px;
    font-weight: bold;
    display: flex;
    align-items: center;
    justify-content: space-between;
  }
  .detail {
    position: relative;
    img {
      position: absolute;
      height: 52px;
      width: 52px;
      right: 14px;
      top: -10px;
    }
    p {
      font-size: 16px;
      color: #232323;
      margin-top: 12px;

      .label {
        width: 120px;
        display: inline-block;
        color: #686b73;
      }
    }
  }
}
.plan-info {
  margin-top: 10px;
  .rotation-item {
    margin-bottom: 10px;
    background: #fff;
    padding: 16px;
  }
}
</style>
