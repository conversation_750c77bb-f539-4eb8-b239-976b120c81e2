import { reserveRequest } from '@/utils/request';
//查询空仓列表
export async function getEmptyStoreList(params) {
  return reserveRequest().get('/flow-batch-store-convert/infoAccountsBatchStore', { params });
}
//根据id查询空仓详情

export async function getEmptyStoreDetail(params) {
  return reserveRequest().get('/api/emptyStoreCheck/findById', { params });
}
//空仓验收新增
export async function addEmptyStore(data) {
  return reserveRequest().post('/api/emptyStoreCheck', data);
}
