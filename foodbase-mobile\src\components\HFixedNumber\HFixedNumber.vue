<template>
  <span class="h-fixed-number">{{ formattedNumber }}</span>
</template>

<script>
export default {
  name: 'HFixedNumber',
  props: {
    ratio: {
      type: Number,
      default: 1,
    },
    fractionDigits: {
      type: Number,
      default: 3,
    },
    default: {
      type: [Number, String],
      default: '-',
    },
  },
  computed: {
    formattedNumber() {
      const defaultSlot = this.$slots.default()[0];
      if (!defaultSlot) {
        return '';
      }

      let { children: num } = defaultSlot;

      if (typeof num !== 'number') {
        num = parseFloat(num);
      }

      num = num * this.ratio;

      if (isNaN(num)) {
        return this.default;
      }

      return num.toLocaleString(undefined, {
        minimumFractionDigits: this.fractionDigits,
        maximumFractionDigits: this.fractionDigits,
      });
    },
  },
};
</script>
