<template>
  <Tag v-bind="colors">
    <slot></slot>
  </Tag>
</template>

<script setup>
import { Tag } from 'vant';
import { computed } from 'vue';

const colorMap = {
  danger: '#FFE6E6',
};

const textColorMap = {
  danger: '#FF1414',
};

const props = defineProps({
  type: String,
});

const colors = computed(() => {
  return { color: colorMap[props.type], textColor: textColorMap[props.type] };
});
</script>

<style scoped>
.van-tag {
  font-size: 18px;
  line-height: 28px;
}
</style>
