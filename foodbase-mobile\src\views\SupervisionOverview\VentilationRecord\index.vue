<template>
  <div>
    <!-- 仓房 -->
    <Field
      v-model="queryForm.houseName"
      label="仓房"
      readonly
      is-link
      name="signPicker"
      input-align="left"
      placeholder="请选择"
      label-width="80px"
      @click="showStoreHousePicker = true"
      size="large"
    >
    </Field>
    <Popup v-model:show="showStoreHousePicker" position="bottom">
      <Picker
        :columns="storeHouseOptions"
        @confirm="onStoreHouseConfirm"
        @cancel="showStoreHousePicker = false"
      />
    </Popup>
    <!-- 时间 -->
    <Field
      v-model="queryForm.year"
      readonly
      name="datetimePicker"
      label="时间"
      placeholder="请选择"
      label-width="80px"
      @click="showPicker = true"
      right-icon="warning-o"
    >
      <template #right-icon>
        <img class="abarnimg mr-[10px]" src="@/assets/date.png" alt="" />
      </template>
    </Field>
    <popup v-model:show="showPicker" position="bottom">
      <van-datetime-picker
        ref="datetimePicker"
        v-model="queryForm.currentDate"
        type="year-month"
        title="选择年月"
        :max-date="maxDate"
        @cancel="onDatetimecancel"
        @confirm="onDatetimeConfirm"
      />
    </popup>
    <PullRefresh @refresh="onRefresh" v-model="refreshLoading">
      <List
        ref="listRef"
        v-model:loading="isLoading"
        :finished="finished"
        :finished-text="list?.length ? '没有更多了' : ''"
        @load="getList"
      >
        <div class="store-list">
          <div class="store-item" v-for="(item, i) in list" :key="i">
            <div class="font-bold text-[#232323] flex header-style">
              <div class="flex">
                <img class="abarnimg" src="@/assets/abarnicon.png" alt="" />{{ item.HOUSE_NAME }}
              </div>
            </div>
            <div class="mt-[12px] text-[#232323]">
              <span class="mt-style text-[#686B73]">通风作业:</span>{{ item.JOB_NAME || '-' }}
            </div>
            <div class="mt-[12px] text-[#232323]">
              <span class="mt-style text-[#686B73]">通风模式:</span>{{ item.WINDJOB_MODE || '-' }}
            </div>
            <div class="mt-[12px] text-[#232323]">
              <span class="mt-style text-[#686B73]">操作模式:</span>{{ item.WIND_PURPOSE_NAME || '-' }}
            </div>
            <div class="mt-[12px] text-[#232323]">
              <span class="mt-style text-[#686B73]">开始时间:</span>{{ item.JOB_START_TIME || '-' }}
            </div>
            <div class="mt-[12px] text-[#232323]">
              <span class="mt-style text-[#686B73]">关闭时间:</span>{{ item.JOB_STOP_TIME || '-' }}
            </div>
            <div class="mt-[12px] text-[#232323]">
              <span class="mt-style text-[#686B73]">实际时长:</span>{{ renderTimeRange(item) || '-' }}
            </div>
            <div class="mt-[12px] text-[#232323]">
              <span class="mt-style text-[#686B73]">操作人员:</span>{{ item.OPEN_OPERATOR || '-' }}
            </div>
          </div>
        </div>
      </List>
    </PullRefresh>
    <Empty
      class="empty-camera"
      v-if="!list || list.length <= 0"
      description="暂无数据"
    />
  </div>
</template>

<script setup>
import { onMounted, ref, reactive } from 'vue';
import { Field, Picker, Popup, Empty, List, PullRefresh } from 'vant';
import { getStoreHouseAndOil } from '@/api/supervision';
import { useStore } from 'vuex';
import { getVentilationRecord, getStoreInfoByUser } from '@/api/home-work.js';
import { formatDurationHour } from '@/utils/tools.js'
import dayjs from 'dayjs';

const queryForm = reactive({
  year: dayjs().format('YYYY-MM'),
  currentDate: new Date(dayjs().year(), dayjs().month()),
  houseName: '全部',
  houseCode: '',
});
// 最大时间可选
const maxDate = new Date(dayjs().year(), dayjs().month());
const showPicker = ref(false);
const showStoreHousePicker = ref(false);
const storeHouseOptions = ref([]);

const store = useStore();

const userInfo = store.getters['user/userInfo'];

//分页
const pagination = reactive({
  page: 0,
  size: 10,
  total: 0,
});
//列表
const list = ref([]);
const isLoading = ref(false);
const finished = ref(false);
const refreshLoading = ref(false);
const listRef = ref();

const renderTimeRange = (item) => {
  return formatDurationHour(dayjs(item.JOB_START_TIME)?.valueOf(), dayjs(item.JOB_STOP_TIME)?.valueOf())
}
//获取仓房下拉框数据
const loadStoreHouses = async () => {
  const res = await getStoreInfoByUser({ deptId: userInfo?.dept?.id })
  if (res?.id) {
    storeHouseOptions.value = [];
    const list = await getStoreHouseAndOil({ storeId: res?.id });
    storeHouseOptions.value = list?.map((it) => {
      return {
        text: it.name,
        value: it.id,
      };
    });

    storeHouseOptions.value.unshift({
      text: '全部',
      value: undefined,
    });
  }
};

//选择仓房
const onStoreHouseConfirm = (val) => {
  queryForm.houseName = val.text;
  queryForm.houseCode = val.value;
  showStoreHousePicker.value = false;
  onSearch();
};

// 选择时间
const onDatetimeConfirm = (val) => {
  let date = dayjs(val).format('YYYY-MM');
  queryForm.year = date;
  showPicker.value = false;
  onSearch();
};
// 取消
const onDatetimecancel = () => {
  queryForm.currentDate = new Date(dayjs(queryForm.year).year(), dayjs(queryForm.year).month());
  showPicker.value = false;
};
const onSearch = () => {
  finished.value = false;
  list.value = [];
  pagination.page = 0;
  pagination.total = 0;
  listRef.value?.check();
};
const getList = async () => {
  try {
    pagination.page += 1
    const formData = new FormData()
    formData.append('jobStartTime', dayjs(queryForm.year).startOf('month').format('YYYY-MM-DD'))
    formData.append('jobEndTime', dayjs(queryForm.year).endOf('month').format('YYYY-MM-DD'))
    formData.append('houseCode', queryForm.houseName === '全部' ? '' : queryForm.houseCode)
    formData.append('houseName', queryForm.houseName === '全部' ? '' : queryForm.houseName)
    formData.append('pageNum', pagination.page)
    formData.append('pageSize', pagination.size)
    let { data: { obj } } = await getVentilationRecord(formData);
    list.value.push(...obj.list);
    pagination.total = obj.total;
    // 加载状态结束
    isLoading.value = false;

    // 数据全部加载完成
    if (list.value.length >= pagination.total) {
      finished.value = true;
    }
  } catch {
    isLoading.value = false;
    finished.value = true;
  }
};
const onRefresh = () => {
  if (refreshLoading.value) {
    pagination.page = 0;
    pagination.total = 0;
    list.value = [];
    refreshLoading.value = false;
  }
  finished.value = false;
  isLoading.value = true;
  getList();
};
onMounted(() => {
  loadStoreHouses();
});
</script>

<style lang="scss" scoped>
.store-item {
  background: #ffffff;
  margin-top: 10px;
  padding: 16px;

  .header-style {
    justify-content: space-between;
  }
  .abarnimg {
    width: 22px;
    height: 18px;
    margin-right: 10px;
    margin-top: 4px;
  }

  .mt-style {
    width: 25%;
    display: inline-block;
  }

  .it-value {
    width: calc(100% - 100px);
    flex-wrap: wrap;

    div {
      width: 100px;
      color: #fff;
      display: flex;
      justify-content: center;
      align-items: center;
      margin: 5px;
    }
  }
}
</style>
