<template>
  <div v-if="initStatus" class="pb-6">
    <OverviewOfAcquisitionQuantities />
    <AcquisitionQualityInspectionDetails class="mt-3" />
    <OverviewOfOutboundQuantities class="mt-3" />
    <OutboundQualityWarningPrompts class="mt-3" />
    <StorageQualityAnalysis class="mt-3" />
  </div>
</template>

<script setup>
import { ref, provide, onMounted, computed } from 'vue';
import OverviewOfAcquisitionQuantities from './OverviewOfAcquisitionQuantities.vue';
import AcquisitionQualityInspectionDetails from './AcquisitionQualityInspectionDetails.vue';
import OverviewOfOutboundQuantities from './OverviewOfOutboundQuantities.vue';
import OutboundQualityWarningPrompts from './OutboundQualityWarningPrompts.vue';
import StorageQualityAnalysis from './StorageQualityAnalysis.vue';
import { getPurchaseCategory } from '@/api/common';

const initStatus = ref(false);
const purchaseCategory = ref([]);
const purchaseCategoryData = computed(() => {
  return purchaseCategory.value.map((i) => ({ text: i.foodBigCategory, value: i.foodBigCategory }));
});

onMounted(async () => {
  purchaseCategory.value = await getPurchaseCategory();
  initStatus.value = true;
});

provide('purchaseCategory', purchaseCategoryData);
</script>

<style lang="scss" scoped></style>
