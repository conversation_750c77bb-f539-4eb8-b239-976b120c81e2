.in-out-manage-page, .page-warpper{
  .warpper {
    margin-bottom: 10px;
    ::v-deep(.van-field__right-icon) {
      color: #1677ff;
    }
    ::v-deep(.van-radio) {
      margin: 5px;
    }
  }
  .bottom-warpper {
    position: fixed;
    bottom: 0;
    width: 100%;
    height: 99px;
    background: #ffffff;
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    align-items: center;
    .button {
      width: calc(100% - 28px);
      height: 49px;
      background: #1f3359;
      border-radius: 4px;
      font-size: 18px;
      font-weight: 400;
      color: #ffffff;
      line-height: 49px;
      margin-top: 14px;
    }
    .buttons {
      display: flex;
      justify-content: center;
      margin-top: 14px;
      text-align: center;
      .cancel-button {
        width: 100px;
        height: 44px;
        background: #ffffff;
        border-radius: 2px;
        border: 1px solid #686B73;
        font-size: 16px;
        font-weight: 400;
        color: #3a4056;
        line-height: 44px;
        margin-right: 15px;
      }
      .next-button {
        width: 196px;
        height: 44px;
        background: #1f3359;
        border-radius: 2px;
        font-size: 16px;
        font-weight: 400;
        color: #ffffff;
        line-height: 44px;
      }
      .reject-button {
        width: 196px;
        height: 44px;
        background: #f25e4a;
        border-radius: 2px;
        font-size: 16px;
        font-weight: 400;
        color: #ffffff;
        line-height: 44px;
        margin-left: 15px;
      }
    }
    .bar {
      width: 134px;
      height: 5px;
      background: #000000;
      border-radius: 144px;
      margin-bottom: 9px;
    }
  }
}

::v-deep(.van-field__control) {
  text-align: right;
}

.slot-field {
  ::v-deep(.van-field__control ) {
    justify-content: flex-end;
  }
}

.border-field {
  ::v-deep(.van-field__control ) {
    border: 1px solid #000000;
    text-align: left;
  }
}