import dayjs from 'dayjs'

/**
 * 获取数据中某元素出现的次数
 */
const getArrItemNum = (arr) => {
    const obj = {}
    arr.forEach((element) => {
        if (obj[element]) {
            obj[element]++
        }
        else {
            obj[element] = 1
        }
    })
    return obj
}

// 查找所有上级
const findParentIdsById = function (id, node = {}, parentIds = []) {
    if (node.code === id) {
        return [...parentIds, node.code]
    }

    if (node.children) {
        for (const child of node.children) {
            if (child.code === id) {
                return [...parentIds, node.code, child.code]
            }
            else {
                const parentIdsWithNode = [...parentIds, node.code]
                const parentIdsArray = findParentIdsById(id, child, parentIdsWithNode)
                if (parentIdsArray) {
                    return parentIdsArray
                }
            }
        }
    }

    return null
}

// 补零函数
const formatNumber = (n) => {
  n = n.toString()
  return n[1] ? n : `0${n}`
}

const formatDuration = (startTime, endTime, zero = true) => {
  if (startTime - endTime > 0) {
    const x = startTime
    startTime = endTime
    endTime = x
  }
  if (!endTime || !startTime || startTime >= endTime) {
    return ''
  }
  const diffTime = dayjs.duration(endTime - startTime)
  const day = diffTime.days() // 天
  const hours = diffTime.hours() // 小时
  const minutes = diffTime.minutes() // 分
  let text = ''
  if (day > 0) {
    text += `${zero ? formatNumber(day) : day}天`
  }
  if (hours > 0) {
    text += `${zero ? formatNumber(hours) : hours}小时`
  }
  if (minutes > 0) {
    text += `${zero ? formatNumber(minutes) : minutes}分钟`
  }
  return text
}

const formatDurationHour = (startTime, endTime, fixed = 1) => {
  if (startTime - endTime > 0) {
    const x = startTime
    startTime = endTime
    endTime = x
  }
  if (!endTime || !startTime || startTime >= endTime) {
    return ''
  }
  const hours = dayjs.duration(endTime - startTime).asHours()
  console.log(hours)
  let text = ''
  if (hours > 0) {
    text += `${hours.toFixed(fixed)}小时`
  }
  return text
}

export { getArrItemNum, findParentIdsById, formatNumber, formatDuration, formatDurationHour }
