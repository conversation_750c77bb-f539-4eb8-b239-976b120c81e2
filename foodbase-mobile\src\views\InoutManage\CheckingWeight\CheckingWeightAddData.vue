<template>
  <div class="checking-weight-other">
    <van-form ref="form" @submit="onSave" label-width="140px">
      <van-cell-group inset>
        <!--        <van-field-->
        <!--          v-model="form.count"-->
        <!--          label="计量方式"-->
        <!--          name="signPicker"-->
        <!--          placeholder="请输入"-->
        <!--          required-->
        <!--          :rules="[{ required: true, message: '请输入计量方式' }]"-->
        <!--          type="digit"-->
        <!--        >-->
        <!--        </van-field>-->
        <van-field
          v-model="form.packageCalculateMethodName"
          label="计量方式"
          title-class="bold-text"
          is-link
          readonly
          required
          placeholder="请选择"
          :rules="[{ required: true, message: '请选择计量方式' }]"
          @click="showSampleTypePicker = true"
        />
        <ActionSheet v-model:show="showSampleTypePicker" title="计量方式">
          <Picker
            :columns="packageCalculateMethodList"
            @cancel="showSampleTypePicker = false"
            @confirm="onSampleTypeConfirm"
          ></Picker>
        </ActionSheet>
        <van-field
          v-model="form.packageCount"
          label="包数"
          name="packageCount"
          placeholder="请输入"
          required
          :rules="confirmNetweightRules.packageCount"
          type="digit"
        >
        </van-field>
        <van-field
          v-model="form.packageStandardWeight"
          v-if="form.packageCalculateMethod === '2'"
          label="标准包规格(公斤)"
          name="packageStandardWeight"
          placeholder="请输入"
          required
          :rules="confirmNetweightRules.packageStandardWeight"
          type="text"
        >
        </van-field>
        <van-field
          v-model="form.packagingWeight"
          v-else
          label="单包装物重量（公斤）"
          name="packageStandardWeight"
          placeholder="请输入"
          required
          :rules="[{ required: true, message: '请输入单包重量' }]"
          type="number"
          @input="handleInput"
        >
        </van-field>
        <van-field
          v-model="form.averageWeight"
          label="平均每包重量(公斤)"
          name="averageWeight"
          placeholder="请输入"
          required
          :rules="[{ required: true, message: '请输入平均每包重量' }]"
          type="number"
          :formatter="formatter"
        >
        </van-field>
        <van-field
          v-if="form.packageCalculateMethod === '2'"
          v-model="purStandardWeight"
          label="净重(公斤)"
          name="purStandardWeight"
          placeholder=""
          required
          disabled
          type="digit"
        >
        </van-field>
        <van-field
          v-else
          v-model="purWeight"
          label="净重(公斤)"
          name="purWeight"
          placeholder=""
          required
          disabled
          type="number"
        >
        </van-field>
        <Tabbar>
          <TabbarItem class="cancelTab">
            <Button class="com-btn end" @click="onCancel">取消</Button>
          </TabbarItem>
          <TabbarItem class="saveTab">
            <Button
              class="com-btn save"
              type="primary"
              @click="onSaveconfirmNetweightDialog"
              native-type="submit"
              >确定</Button
            >
          </TabbarItem>
        </Tabbar>
      </van-cell-group>
    </van-form>
  </div>
</template>
<script>
import { Form, Field, Button, CellGroup, Tabbar, TabbarItem, Picker, ActionSheet } from 'vant';
import BigNumber from 'bignumber.js';
import { addTotalWeight, confirmNetWeighSingle } from '@/api/in-out-manage';

const validateZero = (rule, value) => {
  if (value === 0) {
    return false;
  }
  if (!value) {
    return true;
  }
  if (value <= 0) {
    return false;
  } else {
    return true;
  }
};

export default {
  components: {
    ActionSheet,
    Picker,
    'van-form': Form,
    'van-field': Field,
    'van-cell-group': CellGroup,
    Button,
    Tabbar,
    TabbarItem,
  },
  data() {
    return {
      form: {
        packageCalculateMethodName: '标准包计量',
        packageCalculateMethod: '1',
        packageCount: '',
        packageStandardWeight: '',
        packagingWeight: '',
        averageWeight: '',
      },
      // numberFixedDigit,
      confirmNetweightRules: {
        packageCalculateMethod: [
          { required: true, message: '请选择计量方式', trigger: 'onChange' },
        ],
        packageCount: [
          { required: true, message: '请输入包数', trigger: 'onBlur' },
          { validator: validateZero, trigger: 'blur', message: '请输入正数' },
        ],
        packagingWeight: [
          { required: true, message: '请输入单包装物重量', trigger: 'onBlur' },
          { validator: validateZero, trigger: 'blur', message: '请输入正数' },
        ],
        averageWeight: [
          { required: true, message: '请输入数量', trigger: 'onBlur' },
          { validator: validateZero, trigger: 'blur', message: '请输入正数' },
        ],
        packageStandardWeight: [
          { required: true, message: '请输入标准包规格', trigger: 'onBlur' },
          {
            validator: this.validatePackageStandard,
            trigger: 'onBlur',
            message: '平均每包重量与标准包规格误差超过5%',
          },
        ],
      },
      weightHistory: [],
      record: {},
      showSampleTypePicker: false,
      packageCalculateMethodList: [
        {
          text: '标准包计量',
          value: '2',
        },
        {
          text: '抽样包计量',
          value: '1',
        },
      ],
    };
  },
  mounted() {
    this.form.averageWeight = this.$route.params.averageWeight;
    console.log(this.form.averageWeight, 'average');
    this.form.totalWeight = this.$route.params.totalWeight;
    this.weightHistory = JSON.parse(this.$route.params.weightHistory);
    this.record = JSON.parse(this.$route.params.record);
    // console.log(this.record);
  },
  computed: {
    purStandardWeight() {
      return Math.round(
        Number(BigNumber(this.form.packageStandardWeight || 0).times(this.form.packageCount || 0)),
      );
    },
    purWeight() {
      return Math.round(
        Number(
          BigNumber(this.form.totalWeight)
            .div(this.weightHistory.length)
            .minus(this.form.packagingWeight || 0)
            .times(this.form.packageCount || 0),
        ),
      );
    },
  },
  methods: {
    //保存确认净重数据
    formatter(value) {
      return parseFloat(value).toFixed(3).padEnd(4, '0');
    },

    handleInput(e) {
      // 固定两位小数
      e.target.value = e.target.value.replace(/[^\d.]/g, ''); //清除“数字”和“.”以外的字符
      e.target.value = e.target.value.replace(/\.{2,}/g, '.'); //只保留第一个. 清除多余的
      e.target.value = e.target.value.replace('.', '$#$').replace(/\./g, '').replace('$#$', '.');
      e.target.value = e.target.value.replace(/^(-)*(\d+)\.(\d\d\d).*$/, '$1$2.$3'); //只能输入两个小数
      e.target.value = e.target.value.replace(/^\./g, ''); //首位不能输入“.”
      if (e.target.value.indexOf('.') < 0 && e.target.value != '') {
        //如果没有小数点，首位不能为0，如01、02...
        e.target.value = parseFloat(e.target.value);
      }
      e.target.value = e.target.value.match(/^\d*(\.?\d{0,3})/g)[0] || '';
      this.$nextTick(() => {
        this.form.packagingWeight = e.target.value;
      });
    },

    async onSaveconfirmNetweightDialog() {
      this.$refs.form
        .validate(['packageCount', 'packageStandardWeight'])
        .then(async () => {
          const params = {
            weighDetailDTOList: this.weightHistory,
            weighingDataType: 1,
            schedulingNo: this.record.schedulingNo,
            isWeight: '6',
            packagingWeight: this.form.packagingWeight / 1000,
            packageCount: this.form.packageCount,
            packageCalculateMethod: this.form.packageCalculateMethod,
            packageStandardWeight: this.form.packageStandardWeight / 1000,
          };
          if (this.record.isWeighRepeatedly == '1') {
            try {
              const data = await addTotalWeight(params);
              // console.log(data,'data1');
              if (!data) return;
              this.$router.replace({
                name: 'CheckingWeight',
              });
            } catch (error) {
              console.log(error);
            }
          } else {
            try {
              const data = await confirmNetWeighSingle(params);
              if (!data) return;
              this.$router.replace({
                name: 'CheckingWeight',
              });
            } catch (error) {
              console.log(error);
            }
          }
        })
        .catch(() => {
          console.log('shibai');
        });
    },
    validatePackageStandard(value) {
      const averageWeight = this.form.averageWeight;
      const low = BigNumber(value).times(0.95);
      const up = BigNumber(value).times(1.05);
      // console.log(rule, value, averageWeight, low, up);
      if (averageWeight >= low && averageWeight <= up) {
        return true;
      } else {
        return false;
        // canew Error('平均每包重量与标准包规格误差超过5%'));
      }
    },
    onCancel() {
      this.$router.back();
    },
    onSampleTypeConfirm(value) {
      this.form.packageCalculateMethodName = value.text;
      this.form.packageCalculateMethod = value.value;
      this.showSampleTypePicker = false;
    },
  },
};
</script>
<style lang="scss" scoped>
.checking-weight-other {
  background-color: white;
  .cancelTab,
  .saveTab {
    width: 50%;
    padding: 10px 10px;
  }
  .com-btn {
    padding: 0 60px;
    margin-bottom: 15px;
  }
  ::v-deep(.van-field__control) {
    text-align: right;
  }
}
</style>
