/**
 * 对 video 节点创建 canvas 画布
 * @param video
 * @returns {HTMLCanvasElement}
 */
export function createCanvasFromVideo(video) {
  const width = video.videoWidth;
  const height = video.videoHeight;

  const canvas = document.createElement('canvas');
  const context = canvas.getContext('2d');
  canvas.width = width;
  canvas.height = height;
  context.drawImage(video, 0, 0, width, height);

  return canvas;
}
