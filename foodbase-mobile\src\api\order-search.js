import { request, reserveRequest } from '@/utils/request';
import qs from 'qs';
export async function getOrderList(params) {
  return request().get('/flow-sign-order/getSignOrderMobileList', { params });
}

export async function getDetailOrderList(params) {
  return request().get('/flow-sign-order/getSignOrderMobileDetailList', { params });
}

export async function getCompanyOrderList(params) {
  return request().get('/flow-sign-order/getGsSignOrderMobileList', { params });
}
//订单查询
export async function getInfoZZD(data) {
  return reserveRequest().get(
    '/api/flowPurchasePlan/infoZZD?' + qs.stringify(data, { arrayFormat: 'repeat' }),
  );
}
//订单查询-查看详情
export async function getDetailZZD(params) {
  return reserveRequest().get('/api/orderInventory/detailZZD', { params });
}
