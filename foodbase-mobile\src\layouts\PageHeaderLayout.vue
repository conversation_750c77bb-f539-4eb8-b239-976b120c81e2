<template>
  <div class="page-header-layout">
    <NavBar
      :title="pageTitle"
      left-text="返回"
      left-arrow
      :right-text="rightText"
      @click-left="onClickLeft"
      @click-right="onClickRight"
      v-if="showNavBar"
    >
      <template #right v-if="rightComponent">
        <component :is="rightComponent" />
      </template>
    </NavBar>
    <main class="content">
      <router-view></router-view>
    </main>
  </div>
</template>

<script setup>
import { NavBar } from 'vant';
import { useRouter, useRoute } from 'vue-router';
import { computed } from 'vue';
import { useStore } from 'vuex';

const router = useRouter();
const route = useRoute();
const store = useStore();

const showNavBar = process.env.VUE_APP_PAGE_HEADER === 'true';

const rightText = computed(() => {
  return route.meta?.subTitle?.title;
});

const rightComponent = computed(() => {
  return route.meta?.subTitle?.component;
});

const pageTitle = computed(() => {
  return store.state.pageTitle;
});

const onClickLeft = () => {
  router.back();
};
const onClickRight = () => {
  router[route.meta?.subTitle?.entryType ?? 'push']({ name: route.meta?.subTitle?.name });
};
</script>

<style scoped lang="scss">
.page-header-layout {
  height: 100vh;
  display: flex;
  flex-direction: column;

  ::v-deep(.van-nav-bar) {
    --van-nav-bar-title-font-size: 20px;
    --van-nav-bar-title-text-color: #ffffff;
    --van-nav-bar-text-color: #ffffff;
    --van-nav-bar-icon-color: #ffffff;
    --van-nav-bar-arrow-size: 18px;

    .van-nav-bar__left,
    .van-nav-bar__right {
      font-size: 18px;
    }

    &.van-hairline--bottom::after {
      border-bottom-color: #45597e;
    }

    [class*='van-hairline']::after {
      border-bottom-color: #45597e;
    }
  }
}
.content {
  flex-grow: 1;
  min-height: 0;
  overflow-y: scroll;
}
</style>
