<template>
  <div class="video-detail">
    <div class="video-container">
      <Videojs
        :config="videoConfig"
        :configs="{
          rmDeviceEquipmentId: $route.params.rmDeviceEquipmentId,
          playFlag: $route.params.playFlag,
        }"
      />
    </div>
    <div class="control" v-if="controlFlag">
      <Control :config="$route.params" />
    </div>
  </div>
</template>

<script setup>
import { computed, onMounted, ref } from 'vue';
import { useStore } from 'vuex';
import { useRoute, useRouter } from 'vue-router';
import Videojs from '@/components/HVideo/HVideo.vue';
import Control from './Control.vue';

const store = useStore();
const route = useRoute();
const router = useRouter();
const detail = ref({});

const getDetail = () => {
  const getVideoDetail = store.getters['supervision-overview/getVideoDetail'];
  const { cameraId } = route.params;
  const videoDetail = getVideoDetail(cameraId);
  if (!videoDetail) {
    router.replace({ name: 'VideoMonitor' });
    return;
  }
  detail.value = videoDetail;
};

onMounted(() => {
  getDetail();
});

const videoConfig = computed(() => {
  const { playFlag, rmDeviceEquipmentId, videoUrl } = detail.value;
  if (!videoUrl) {
    return {};
  }
  return {
    playFlag,
    rmDeviceEquipmentId,
    videoUrl,
  };
});

// 判断云台控制是否展示
const controlFlag = computed(
  () => route.params.rmDeviceTypeId === 'BALLHEAD-CAMERA' && route.params.cloudFlag == '1',
);
</script>

<style scoped lang="scss">
.video-detail {
  height: 100%;
  background-color: #000;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  .video-container {
    width: 100%;
    height: 124vw;
  }
  .control {
    flex: 1;
  }
}
</style>
