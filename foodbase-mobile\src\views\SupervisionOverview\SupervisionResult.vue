<template>
  <HCard title="天天大清查" @header-click="toSupervision">
    <template #header-extra v-if="!allNotRegistered">
      <Icon name="arrow" />
    </template>

    <div class="supervision-result">
      <div class="content">
        <div class="results">
          <div class="result">
            <div class="result-name">数量清查</div>
            <div class="result-icon icon-dabiao" v-if="result.quantityResult === 1"></div>
            <div class="result-icon icon-budabiao" v-else-if="result.quantityResult === 2"></div>
            <div class="result-icon icon-weikaitong" v-else-if="result.quantityResult === 3"></div>
          </div>
          <div class="result">
            <div class="result-name">质量清查</div>
            <div class="result-icon icon-hege" v-if="result.qualityResult === 1"></div>
            <div class="result-icon icon-buhege" v-else-if="result.quantityResult === 2"></div>
            <div class="result-icon icon-weikaitong" v-else-if="result.quantityResult === 3"></div>
          </div>
          <div class="result">
            <div class="result-name">安全清查</div>
            <div class="result-icon icon-zhengchang" v-if="result.storageResult === 1"></div>
            <div class="result-icon icon-yichang" v-else-if="result.quantityResult === 2"></div>
            <div class="result-icon icon-weikaitong" v-else-if="result.quantityResult === 3"></div>
          </div>
          <div class="result">
            <div class="result-name">设施清查</div>
            <div class="result-icon icon-wanbei" v-if="result.facilitiesResult === 1"></div>
            <div class="result-icon icon-buwanbei" v-else-if="result.quantityResult === 2"></div>
            <div class="result-icon icon-weikaitong" v-else-if="result.quantityResult === 3"></div>
          </div>
        </div>
      </div>
      <div class="time" v-if="!allNotRegistered">清查时间：{{ result.datetime }}</div>
    </div>
  </HCard>
</template>

<script setup>
import { Icon } from 'vant';
import { computed, effect, reactive } from 'vue';
import { getSupervisionResult } from '@/api/supervision';
import { useStore } from 'vuex';
import dayjs from 'dayjs';
import { useRouter } from 'vue-router';

const router = useRouter();
const store = useStore();

const result = reactive({
  quantityResult: 0,
  qualityResult: 0,
  storageResult: 0,
  facilitiesResult: 0,
  datetime: '',
});

const allNotRegistered = computed(() => {
  return (
    result.quantityResult === 3 &&
    result.qualityResult === 3 &&
    result.storageResult === 3 &&
    result.facilitiesResult === 3
  );
});

const areaCode = store.getters['user/userAreaCode'];

effect(async () => {
  const data = await getSupervisionResult(areaCode);
  if (!data) {
    return;
  }
  result.quantityResult = data.quantityResult;
  result.qualityResult = data.qualityResult;
  result.storageResult = data.storageResult;
  result.facilitiesResult = data.facilitiesResult;
  result.datetime = data.time && dayjs(data.time).format('YYYY-MM-DD HH:mm');
});

const toSupervision = () => {
  if (allNotRegistered.value) {
    return;
  }
  const userAreaCode = store.getters['user/userAreaCode'];
  router.push({ name: 'SupervisionOverview', params: { areaCode: userAreaCode } });
};
</script>

<style scoped lang="scss">
.supervision-result {
  padding: 15px 0;

  .content {
    .results {
      display: flex;
      justify-content: space-evenly;
    }

    .result {
      display: flex;
      flex-direction: column;
      align-items: center;

      &-icon {
        width: 72px;
        height: 66px;
        margin-top: 8px;
        background-size: contain;
        background-position: center;
        background-repeat: no-repeat;

        &.icon-dabiao {
          background-image: url('assets/icon-dabiao.png');
        }
        &.icon-budabiao {
          background-image: url('assets/icon-budabiao.png');
        }
        &.icon-hege {
          background-image: url('assets/icon-hege.png');
        }
        &.icon-buhege {
          background-image: url('assets/icon-buhege.png');
        }
        &.icon-zhengchang {
          background-image: url('assets/icon-zhengchang.png');
        }
        &.icon-yichang {
          background-image: url('assets/icon-yichang.png');
        }
        &.icon-wanbei {
          background-image: url('assets/icon-wanbei.png');
        }
        &.icon-buwanbei {
          background-image: url('assets/icon-buwanbei.png');
        }
        &.icon-weikaitong {
          background-image: url('assets/icon-weikaitong.png');
        }
      }

      &-name {
        font-size: 18px;
        line-height: 25px;
        margin-top: 5px;
        color: var(--van-gray-8);
      }
    }
  }

  .time {
    color: var(--van-gray-6);
    text-align: center;
    font-size: 18px;
    margin-top: 15px;
  }
}
</style>
