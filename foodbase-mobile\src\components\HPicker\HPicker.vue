<template>
  <div class="h-picker">
    <div class="h-picker-trigger" @click="showPicker = true">
      <span class="h-picker-label" :class="{ 'h-picker-placeholder': placeholder }">
        {{ currentLabel }}
      </span>
      <span>
        <Icon name="close" v-if="showClear" @click="handleClear" />
        <Icon name="arrow-down" />
      </span>
    </div>
    <Popup v-model:show="showPicker" round position="bottom" teleport="body">
      <Search v-model="keyword" placeholder="请输入"></Search>
      <Picker
        :title="title"
        :columns="listFilter(columns)"
        @cancel="showPicker = false"
        @confirm="onConfirm"
      />
    </Popup>
  </div>
</template>

<script setup>
import { Popup, Picker, Icon, Search } from 'vant';
import { computed, ref } from 'vue';

const emits = defineEmits(['update:value']);

const props = defineProps({
  title: String,
  options: Array,
  value: [String, Number],
  valueKey: String,
  placeholder: String,
  showClear: <PERSON>ole<PERSON>,
});

const showPicker = ref(false);

const title = props.title || '请选择';

const columns = computed(() => props.options);

const listFilter = (columns) => {
  if (!columns) {
    return [];
  }
  const filterUnitList = columns.filter((item) => item.text.indexOf(keyword.value) > -1);
  return filterUnitList;
};

const keyword = ref('');

const currentLabel = computed(() => {
  const { options, valueKey = 'value', value, placeholder = '请选择' } = props;
  return options.find((it) => it[valueKey] === value)?.text || placeholder;
});

const placeholder = computed(() => {
  const { options, valueKey = 'value', value } = props;
  return !options.some((it) => it[valueKey] === value);
});
const handleClear = (e) => {
  e.stopPropagation();
  emits('update:value', null);
};

const onConfirm = (item) => {
  const { valueKey = 'value' } = props;
  showPicker.value = false;
  emits('update:value', item[valueKey]);
};
</script>

<style lang="scss">
.h-picker {
  background-color: #ffffff;
  line-height: 32px;
  color: var(--van-gray-7);

  &-trigger {
    border: 1px solid var(--van-gray-5);
    padding: 0 10px;
    border-radius: 4px;
    display: flex;
    align-items: center;
    justify-content: space-between;
  }

  &-label {
    margin-right: 4px;
    overflow: hidden;
    white-space: nowrap;
  }

  &-placeholder {
    color: var(--van-gray-5);
  }
}
</style>
