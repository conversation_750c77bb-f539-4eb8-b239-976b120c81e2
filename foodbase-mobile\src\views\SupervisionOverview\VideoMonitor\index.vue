<template>
  <div class="video-monitor">
    <div class="total-values">
      <div class="value-item all">
        <div class="name">全部</div>
        <div class="value">{{ cameraNum.total }}</div>
      </div>
      <div class="value-item online">
        <div class="name">在线</div>
        <div class="value">{{ cameraNum.online }}</div>
      </div>
      <div class="value-item offline">
        <div class="name">离线</div>
        <div class="value">{{ cameraNum.offline }}</div>
      </div>
    </div>
    <div class="query-form bg-white mt-2">
      <Row gutter="8" class="row-line">
        <Col span="8">
          <AreaPicker
            v-model:value="queryForm.areaCode"
            :top-area-code="userAreaCode"
            all-area-select
            placeholder="地市"
          />
        </Col>
        <Col span="16">
          <HPicker
            :options="storeOptions"
            v-model:value="queryForm.storeCode"
            placeholder="选择库点"
          />
        </Col>
      </Row>
    </div>
    <HCard v-if="loading && selectedStore">
      <template #header-title>
        <div class="camera-list-title">
          <SvgIcon name="location" />
          <span>{{ selectedStore.text }}</span>
        </div>
      </template>
      <Loading class="loading" type="spinner" vertical>加载中...</Loading>
    </HCard>
    <HCard v-if="!loading && selectedStore">
      <template #header-title>
        <div class="camera-list-title">
          <SvgIcon name="location" />
          <span>{{ selectedStore.text }}</span>
        </div>
      </template>
      <div class="camera-list-filter">
        <Row gutter="8">
          <Col span="12">摄像头({{ onlineCount }}/{{ totalCount }})</Col>
          <Col span="12">
            <Row gutter="8" class="row-line" style="border-bottom: none">
              <Col span="12">
                <HPicker
                  :options="cameraLocationOptions"
                  v-model:value="cameraLocation"
                  placeholder="全部"
                />
              </Col>
              <Col span="12">
                <HPicker
                  :options="cameraOnlineOptions"
                  v-model:value="cameraOnline"
                  placeholder="全部"
                />
              </Col>
            </Row>
          </Col>
        </Row>
      </div>
    </HCard>
    <div class="camera-list">
      <div
        class="camera-item"
        v-for="item in filteredCameraList"
        :key="item.equipmentCode"
        @click="openVideoDetail(item)"
      >
        <div class="camera-status" :class="{ online: item.equipmentStatus === '01' }"></div>
        <div class="camera-type" :class="{ online: item.equipmentStatus === '01' }">
          <SvgIcon v-if="item.rmDeviceTypeId === 'BALLHEAD-CAMERA'" name="ball-camera" />
          <SvgIcon v-else name="gun-camera" />
        </div>
        <span class="camera-name">{{ item.title }}</span>
        <Icon name="arrow" />
      </div>
      <van-empty
        class="empty-camera"
        v-if="filteredCameraList && filteredCameraList.length === 0"
        description="暂无数据"
      />
    </div>
  </div>
</template>

<script>
export default {
  name: 'VideoMonitor',
};
</script>

<script setup>
import { Row, Col, Icon, Loading } from 'vant';
import AreaPicker from '@/views/common/AreaPicker';
import HPicker from '@/components/HPicker/HPicker';
import { computed, onMounted, reactive, ref, watch } from 'vue';
import { getStoreByCityCompany } from '@/api/store';
import { useStore } from 'vuex';
import HCard from '@/components/HCard/HCard';
import SvgIcon from '@/components/SvgIcon/SvgIcon';
import { getCameraData } from '@/api/supervision';
import { useRouter } from 'vue-router';

const store = useStore();
const router = useRouter();

const userAreaCode = store.getters['user/userAreaCode'];

const storeOptions = ref([]);

const cameraLocationOptions = ref([
  { value: null, text: '全部' },
  // { value: '仓内', text: '仓内' },
  // { value: '仓外', text: '仓外' },
  { value: '仓内', text: '仓内' },
  { value: '业务', text: '业务' },
  { value: '安防', text: '安防' },
  { value: '其他', text: '其他' },
]);

const cameraOnlineOptions = ref([
  { value: null, text: '全部' },
  { value: '01', text: '在线' },
  { value: '02', text: '离线' },
]);

const loading = ref(false);

const queryForm = reactive({
  areaCode: store.getters['user/userAreaCode'],
  storeCode: null,
});

const cameraLocation = ref(null); // 仓内/仓外/全部
const cameraOnline = ref(null); // 在线/离线/全部

const cameraNum = reactive({
  total: 0,
  online: 0,
  offline: 0,
});
const cameraList = ref([]);

const filteredCameraList = computed(() => {
  let list = cameraList.value;
  if (cameraLocation.value) {
    list = list.filter((it) => it.location.substr(0, 2) === cameraLocation.value);
  }
  if (cameraOnline.value) {
    if (cameraOnline.value === '01') {
      list = list.filter((it) => it.equipmentStatus === '01');
    } else {
      list = list.filter((it) => it.equipmentStatus !== '01');
    }
  }
  return list;
});

const openVideoDetail = (item) => {
  if (item.equipmentStatus === '01') {
    store.commit('supervision-overview/addVideoDetail', item);
    router.push({
      name: 'VideoDetail',
      params: {
        cameraId: item.rmDeviceCameraId,
        playFlag: item.playFlag,
        rmDeviceTypeId: item.rmDeviceTypeId,
        cloudFlag: item.cloudFlag,
        cameraPassword: item.cameraPassword,
        cameraUserName: item.cameraUserName,
        rmDeviceCameraId: item.rmDeviceCameraId,
        rmDeviceEquipmentId: item.rmDeviceEquipmentId,
        serviceIp: item.serviceIp,
        equipmentCode: item.equipmentCode,
      },
    });
  }
};

const loadCameraData = async () => {
  if (loading.value) {
    return;
  }
  cameraList.value = [];

  if (!queryForm.storeCode) {
    return;
  }
  try {
    loading.value = true;
    const data = await getCameraData(queryForm);
    const { offlineNumber, onlineNumber, total } = data.date;
    cameraNum.total = total || 0;
    cameraNum.online = onlineNumber || 0;
    cameraNum.offline = offlineNumber || 0;

    const { children } = data.record;
    children.forEach((item) => {
      const { title, children: list } = item;
      list.forEach((it) => {
        cameraList.value.push({
          ...it,
          location: title,
        });
      });
    });
  } catch (e) {
    //
  } finally {
    loading.value = false;
  }
};

watch(() => queryForm.storeCode, loadCameraData);

const onlineCount = computed(() => {
  return cameraList.value.filter((it) => it.equipmentStatus === '01').length;
});

const totalCount = computed(() => {
  return cameraList.value.length;
});

const loadStores = async () => {
  storeOptions.value = [];
  const areaCode = queryForm.areaCode;
  const { dept } = store.state.user.info;
  const list = (await getStoreByCityCompany(areaCode, dept.id, dept.level)) || [];
  storeOptions.value = list.map((it) => {
    return {
      value: it.coder,
      text: it.name,
    };
  });
  const firstStoreCode = storeOptions.value[0]?.value;
  if (queryForm.storeCode !== firstStoreCode) {
    queryForm.storeCode = firstStoreCode;
  } else {
    // storeCode 相同，不会触发摄像头数据加载，手动触发
    loadCameraData();
  }
};

watch(
  () => queryForm.areaCode,
  () => {
    cameraNum.total = 0;
    cameraNum.online = 0;
    cameraNum.offline = 0;
    loadStores();
  },
);

const selectedStore = computed(() => {
  return storeOptions.value.find((it) => it.value === queryForm.storeCode);
});

onMounted(() => {
  loadStores();
});
</script>

<style scoped lang="scss">
.query-form {
  padding: 8px 16px;

  .van-col {
    margin: 8px 0;
  }
}

.total-values {
  display: flex;
  justify-content: space-between;
  background-color: #fff;

  .value-item {
    display: flex;
    flex-direction: column;
    align-items: center;
    width: 120px;
    padding: 20px 0 20px 0;

    .name {
      font-size: 16px;
      color: var(--van-gray-7);
      line-height: 22px;
    }

    .value {
      font-size: 26px;
      font-weight: 500;
      line-height: 37px;
      margin-top: 5px;
      color: var(--value-color);

      &:after {
        content: ' ';
        display: block;
        height: 3px;
        background-color: var(--value-color);
        width: 70%;
        margin: 4px auto 0;
      }
    }

    &.all {
      --value-color: #0d86ff;
    }

    &.online {
      --value-color: #43bf58;
    }

    &.offline {
      --value-color: #f08802;
    }
  }
}

.camera-list-title {
  display: flex;
  align-items: center;

  .svg-icon {
    font-size: 28px;
    margin-right: 8px;
  }
}

.camera-list-filter {
  font-size: 18px;
  color: var(--van-gray-7);
  line-height: 25px;
  padding: 20px;
}

::v-deep(.h-card-body) {
  min-height: 0;
}

.loading {
  padding: 16px;
}

.camera-list {
  padding: 16px;

  .camera-item {
    height: 68px;
    background-color: #ffffff;
    margin-bottom: 10px;
    padding-left: 35px;
    padding-right: 22px;
    display: flex;
    align-items: center;
    position: relative;
  }

  .camera-status {
    width: 12px;
    height: 12px;
    position: absolute;
    left: 12px;
    top: 8px;
    background-color: #b3b8ba;
    border-radius: 50%;

    &.online {
      background-color: #40db2b;
    }
  }

  .camera-type {
    width: 36px;
    height: 36px;
    background-color: #686b73;
    border-radius: 18px;
    color: #ffffff;
    font-size: 22px;
    display: flex;
    align-items: center;
    justify-content: center;

    &.online {
      background-color: #1492ff;
    }
  }

  .camera-name {
    font-size: 18px;
    font-weight: 600;
    color: var(--van-gray-8);
    line-height: 25px;
    margin-left: 15px;
  }

  .van-icon-arrow {
    margin-left: auto;
    font-size: 24px;
    color: #86888d;
  }
}
</style>
