<template>
  <div>
    <!-- -->
    <HCard title="车辆称重记录" bgColor="#F7F8FA" v-if="props.weighTyper !== 6">
      <table
        v-if="!props.showConfirmTable && props.refreshTable"
        class="history-table"
        style="width: 100%"
      >
        <thead>
          <tr>
            <td width="20%">称重</td>
            <td width="45%">称重时间</td>
            <td width="25%">重量（公斤）</td>
            <td style="width: 10%">操作</td>
          </tr>
        </thead>
        <tbody>
          <tr v-for="(item, index) in weightHistory" :key="index">
            <td v-if="item.weighTyper !== 6">
              {{ item.weighTyper == 2 ? '称毛' : '称皮' }}
            </td>
            <td v-if="item.weighTyper === 6">
              {{ item.weighTyper == 6 ? '称重点包' : '-' }}
            </td>
            <td>{{ item.createTime }}</td>
            <td>
              <HFixedNumber :ratio="1000" :fraction-digits="3">
                {{ item.count }}
              </HFixedNumber>
            </td>
            <td>
              <van-icon name="delete" color="red" @click="deleteItem(item)" />
            </td>
          </tr>
        </tbody>
      </table>
      <div v-else class="confirm-table">
        <div class="bold-text">
          净重（公斤）：
          <HFixedNumber>
            {{ netWeight }}
          </HFixedNumber>
        </div>
        <CheckboxGroup v-model="selected" @change="changeSelect">
          <div v-for="(item, index) in weightHistory" :key="index" class="weight-row">
            <div class="select">
              <div class="select-radio">
                <Checkbox :name="index"></Checkbox>
              </div>
              <div class="normal-text">{{ item.weighTyper == 2 ? '称毛' : '称皮' }}</div>
            </div>
            <div class="normal-text">
              <HFixedNumber :ratio="1000" :fraction-digits="3">
                {{ item.count }}
              </HFixedNumber>
            </div>
          </div>
        </CheckboxGroup>
      </div>
    </HCard>

    <HCard title="车辆称重记录" bgColor="#F7F8FA" v-else>
      <ul class="tab-row">
        <li>称重</li>
        <li>称重时间</li>
        <li>重量</li>
      </ul>
      <van-swipe-cell v-for="(item, index) in weightHistory" :key="index">
        <ul class="tab-row">
          <li width="20%">{{ item.weighTyper == 2 ? '称毛' : '称皮' }}</li>
          <li width="50%">{{ item.createTime }}</li>
          <li width="30%">
            <HFixedNumber :ratio="1000" :fraction-digits="0">
              {{ item.count }}
            </HFixedNumber>
          </li>
        </ul>
        <template #right>
          <Button
            square
            text="删除"
            @click="handleDelete(item)"
            type="danger"
            class="delete-button"
          />
        </template>
      </van-swipe-cell>
    </HCard>
  </div>
</template>

<script setup>
import { ref } from 'vue';
import { HCard, HFixedNumber } from '@/components';
import { Checkbox, CheckboxGroup, Button } from 'vant';
import { deleteWeighDetail } from '@/api/in-out-manage';
import { Dialog } from 'vant';
const props = defineProps({
  weighTyper: [String, Number],
  refreshTable: Boolean,
  showConfirmTable: Boolean,
  weightHistory: Array,
});
// const route = useRoute();
const selected = ref([]);
const emits = defineEmits(['changeSelect', 'reFresh', 'getNetWeight']);
const netWeight = ref();

const reFresh = () => {
  emits('reFresh');
};
const changeSelect = (val) => {
  let tempMao = 0;
  let tempSkin = 0;
  selected.value.forEach((item) => {
    if (props.weightHistory[item].weighTyper === 3) {
      tempSkin += props.weightHistory[item].count * 1000;
    } else if (props.weightHistory[item].weighTyper === 2) {
      tempMao += props.weightHistory[item].count * 1000;
    }
  });
  console.log(val, netWeight.value);
  netWeight.value = tempMao - tempSkin;
  emits('changeSelect', val);
  emits('getNetWeight', netWeight.value);
};
const deleteItem = async (item) => {
  Dialog.confirm({
    message: '确认要删除吗？',
  })
    .then(async () => {
      await deleteWeighDetail({ id: item.id });
      reFresh();
      // location.reload();
    })
    .catch(() => {
      // on cancel
    });
};
</script>

<style lang="scss" scoped>
.history-add {
  width: 26px;
  height: 26px;
  font-size: 20px;
  text-align: center;
  line-height: 26px;
  background-color: #165dff;
  color: #fff;
}

.history-table {
  font-size: 14px;
  text-align: center;
  tr {
    td {
      height: 36px;
      line-height: 36px;
    }
  }
}

.confirm-table {
  .bold-text {
    padding: 10px 16px;
    background: #f7f8fa;
    font-size: 16px;
    font-weight: bold;
    color: #111111;
    line-height: 22px;
  }
  .weight-row {
    padding: 0 16px;
    display: flex;
    padding: 0 10px;
    justify-content: space-between;
    align-items: center;
    .select {
      display: flex;
      justify-content: center;
      align-items: center;
      height: 36px;
      .select-radio {
        margin-right: 10px;
      }
    }
  }
}

.tab-row {
  display: flex;
  font-size: 14px;
  text-align: center;
  li {
    height: 36px;
    line-height: 36px;
  }
  li:nth-of-type(1) {
    width: 20%;
  }
  li:nth-of-type(2) {
    width: 50%;
  }
  li:nth-of-type(3) {
    width: 30%;
  }
}

.delete-button {
  height: 36px;
}
</style>
