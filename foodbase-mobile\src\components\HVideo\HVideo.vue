<template>
  <div class="h-video">
    <div class="status" v-if="loading || hasError">
      <i class="el-icon-warning" v-if="hasError"></i>
      <i class="el-icon-loading" v-else></i>
    </div>

    <img
      v-if="closeable && config.videoUrl"
      @click="closeVid"
      src="./images/close.png"
      class="close_button"
      alt=""
    />

    <h265Player
      style="width: 100%; height: 100%; position: absolute"
      v-if="config.videoUrl"
      :url="config.videoUrl"
      v-show="!loading"
    />
  </div>
</template>

<script>
// import flv from 'flv.js';
import h265Player from './h265Player.vue';
import { pushStreamByCode } from '@/api/supervision';

// const isSupported = flv.isSupported();

export default {
  name: 'HVideo',
  components: {
    h265Player,
  },
  props: {
    url: String,
    config: {
      type: Object,
      default: function () {
        return {
          videoUrl: '',
          src: '',
          serviceIp: '',
          cameraUserName: 'admin',
          cameraPassword: 'admin123',
          playFlag: 1,
        };
      },
    },
    configs: {
      type: Object,
    },
    //录像列表
    luxiangUrls: {
      type: Array,
      default: function () {
        return [];
      },
    },
    closeable: {
      type: Boolean,
      default: false,
    },
    placeholder: {
      type: String,
      default: '',
    },
  },
  async mounted() {
    this.pushStream();
    setTimeout(() => {
      this.loading = false;
    }, 1000);
  },
  data() {
    return {
      loading: true,
      hasError: false,
      // isSupported,
      flvPlayer: null,
    };
  },
  methods: {
    // 视频推流
    async pushStream() {
      if (this.configs.rmDeviceEquipmentId && this.configs.playFlag == '0') {
        await pushStreamByCode({ code: this.configs.rmDeviceEquipmentId });
      }
    },
    closeVid() {
      // this.dispose();
      this.$emit('close-vid');
    },

    destroy() {
      if (this.flvPlayer) {
        this.flvPlayer.unload();
        this.flvPlayer.detachMediaElement();
        this.flvPlayer.destroy();
        this.flvPlayer = null;
        this.loading = true;
        this.hasError = false;
      }
    },
  },
};
</script>

<style lang="scss">
.h-video {
  width: 100%;
  height: 100%;
  background-color: black;
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
  background: url('./images/placeholder.png');
  background-size: cover;
  background-position: center;

  .status {
    font-size: 50px;
  }

  &:hover .close_button {
    position: absolute;
    right: 0;
    top: 0;
    z-index: 999;
    cursor: pointer;
  }

  video {
    width: 100%;
    height: 100%;
    object-fit: fill;
  }
}
</style>
