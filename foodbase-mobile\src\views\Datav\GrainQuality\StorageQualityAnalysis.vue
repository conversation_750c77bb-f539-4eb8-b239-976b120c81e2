<template>
  <HCard title="储存质量分析">
    <template #header-extra>
      <span class="text-gray-400">单位：个仓</span>
    </template>
    <div class="p-4">
      <van-row gutter="10">
        <van-col span="10">
          <div class="flex p-3 rounded-md bg-blue-50">
            <img src="@/views/Datav/GrainQuality/images/full-warehouse.svg" alt="" srcset="" />
            <div class="ml-3">
              <p>满仓鉴定</p>
              <p class="text-blue-500 text-[20px] font-bold">
                {{ getStorageQualityAnalysis.total ?? '-' }}
              </p>
            </div>
          </div>
        </van-col>
        <van-col span="14">
          <div class="flex p-3 rounded-md bg-blue-50">
            <img src="@/views/Datav/GrainQuality/images/quality-improvement.svg" alt="" srcset="" />
            <div class="ml-3">
              <p>质量升降等</p>
              <p class="text-blue-500 text-[20px] font-bold">
                <van-icon name="play" class="text-green-500 -rotate-90" />
                <span class="text-green-500">
                  {{ getStorageQualityAnalysis.up ?? '-' }}
                </span>
                <van-icon name="play" class="ml-5 text-red-500 rotate-90" />
                <span class="text-red-500">
                  {{ getStorageQualityAnalysis.down ?? '-' }}
                </span>
              </p>
            </div>
          </div>
        </van-col>
      </van-row>
    </div>
  </HCard>
</template>

<script setup>
import { onMounted, ref } from 'vue';
import { HCard } from '@/components';
import { reserveRequest } from '@/utils/request';
const getStorageQualityAnalysis = ref({});
onMounted(async () => {
  getStorageQualityAnalysis.value = await reserveRequest().get(
    '/api/outQualityAnalysis/getStorageQualityAnalysis',
  );
});
</script>

<style lang="scss" scoped>
.cells * {
  font-size: 16px;
}
</style>
