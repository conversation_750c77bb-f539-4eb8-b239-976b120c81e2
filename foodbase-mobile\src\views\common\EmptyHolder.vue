<template>
  <div class="empty-holder">
    <div class="empty-image" />
    <span class="empty-desc">{{ desc }}</span>
  </div>
</template>

<script>
export default {
  name: 'EmptyHolder',
  props: {
    desc: {
      type: String,
      default: '暂无数据',
    },
  },
};
</script>

<style scoped lang="scss">
.empty-holder {
  width: 100%;
  height: 100%;
  max-height: 200px;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  user-select: none;

  .empty-image {
    flex-grow: 1;
    width: 100%;
    background-image: url('../../assets/bg-empty.png');
    background-size: contain;
    background-repeat: no-repeat;
    background-position: center;
  }

  .empty-desc {
    margin-top: 10px;
    color: var(--van-gray-6);
  }
}
</style>
