<template>
  <van-picker
    :columns="dryTowerList"
    :columns-field-names="fieldNames"
    @cancel="onCancel"
    @confirm="onConfirm"
  ></van-picker>
</template>

<script>
import { Picker } from 'vant';
import { mapState } from 'vuex';

export default {
  name: 'DryTowerSelect',
  inheritAttrs: false,
  emits: ['confirm', 'cancel'],
  components: {
    'van-picker': Picker,
  },
  data() {
    return {
      fieldNames: {
        text: 'name',
        value: 'id',
      },
    };
  },
  computed: {
    ...mapState({
      dryTowerList: (state) => state['dry-tower'].list,
    }),
  },
  methods: {
    onCancel() {
      this.$emit('cancel');
    },
    onConfirm(value) {
      this.$emit('confirm', value);
    },
  },
};
</script>
