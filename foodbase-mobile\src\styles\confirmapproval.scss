.auctionsales {
  height: 36px;
  background-color: #fff;
}
.contentsof {
  margin-top: 10px;
  background-color: #fff;
}
.numbering {
  display: flex;
  flex-direction: row;
  padding-left: 16px;
  padding-top: 15px;
  .numbering-B {
    color: rgba(31, 31, 31, 0.65);
  }
  .numbering-S {
    color: #121212;
    font-weight: bold;
    font-size: 14px;
  }
}
.deliveryPoint {
  margin-top: 2px;
  .deliveryhead {
    text-align: center;
    font-size: 18px;
    font-weight: bold;
  }
  .deliverytext {
    padding-top: 15px;
    padding-left: 16px;
    margin-bottom: 15px;
  }
}
.detailStart {
  padding-left: 16px;
  display: flex;
  flex-direction: row;
  margin-bottom: 8px;
  .detailStart_one {
    width: 94px;
    font-size: 14px;
    color: rgba(31, 31, 31, 0.65);
  }
  .detailStart_two {
    margin-left: 2px;
  }
}
.contact {
  padding-left: 16px;
  display: flex;
  flex-direction: row;
  margin-top: 8px;
  .contact_for {
    width: 126px;
    font-size: 14px;
    color: rgba(31, 31, 31, 0.65);
  }
  .contact_act {
  }
}
.card3 {
  border: 0;
  border-top: 2px dotted #a2a9b6;
  margin-left: 16px;
  margin-right: 15px;
}
.indicates {
  padding-left: 16px;
  padding-right: 24px;
  margin-top: 12px;
  background-color: #fff;
  font-size: 14px;
}
.border-bottom {
  height: 99px;
  margin-top: 10px;
  background-color: #fff;
  display: flex;
  flex-direction: row;
  justify-content: space-between;
}
.border-left {
  padding-left: 16px;
  padding-top: 14px;
}
.border-right {
  padding-right: 16px;
  padding-top: 14px;
}
.approvals_required{
  background-color: #fff;
}