<template>
  <div class="settlement-manage-record">
    <Row>
      <Col :span="12">
        <Search v-model="date" placeholder="选择日期区间" readonly @click="datePicker = true" />
      </Col>
      <Col :span="12">
        <Search v-model="search" placeholder="请输入调度号" show-action @search="onSearch">
          <template #action>
            <div class="scan-action" @click="onScan">
              <SvgIcon name="scan" />
            </div>
          </template>
        </Search>
      </Col>
    </Row>
    <Calendar
      v-model:show="datePicker"
      allow-same-day
      type="range"
      :min-date="minDate"
      :default-date="[new Date(startTime), new Date(endTime)]"
      @confirm="onConfirm"
    />
    <PullRefresh @refresh="onRefresh" v-model="refreshLoading">
      <List
        ref="listRef"
        v-model:loading="isLoading"
        :finished="finished"
        finished-text="没有更多了"
        @load="getList"
      >
        <!-- @click="goToDetail(item.schedulingNo)" -->
        <HCard class="detail-card" v-for="item in list" :key="item">
          <HCardHead :title="item.schedulingNo?.split('_')[0]">
            <template #icon>
              <img src="@/assets/in-out-manage/title-doc.png" alt="" />
            </template>
            <template #tail>
              <!-- 粮油品种名称 -->
              <Tag>{{ item.foodCategoryName }}</Tag>
              <!-- 结算类型 -->
              <Tag :type="item.buzTyper">{{ item.buzTyperName }}</Tag>
            </template>
          </HCardHead>
          <div class="card-content">
            <HDetail
              title="到库车船号:"
              :value="item.parentLicensePlateNumber"
              labelWidth="100px"
            />
            <HDetail title="作业车牌:" :value="item.transportVehicleNo" labelWidth="100px" />
            <HDetail title="值仓仓房:" :value="item.storeHouseName" labelWidth="100px" />
            <HDetail title="客户名称:" :value="item.customerName" labelWidth="100px" />
            <HDetail title="过磅重量:" labelWidth="100px">
              <template #value>
                <HFixedNumber :ratio="1000" :fractionDigits="0">
                  {{ item.weighingQuantity || 0 }}
                </HFixedNumber>
                公斤
              </template>
            </HDetail>
            <HDetail title="结算数量:" labelWidth="100px">
              <template #value>
                <HFixedNumber :ratio="1000" :fractionDigits="0">
                  {{ item.count || 0 }}
                </HFixedNumber>
                公斤
              </template>
            </HDetail>
            <HDetail title="扣量:" labelWidth="100px">
              <template #value>
                <HFixedNumber :ratio="1000" :fractionDigits="0">
                  {{ item.deduction || 0 }}
                </HFixedNumber>
                公斤
              </template>
            </HDetail>
            <HDetail title="增量:" labelWidth="100px">
              <template #value>
                <HFixedNumber :ratio="1000" :fractionDigits="0">
                  {{ item.incremental || 0 }}
                </HFixedNumber>
                公斤
              </template>
            </HDetail>
            <HDetail title="其他扣量:" labelWidth="100px">
              <template #value>
                <HFixedNumber :ratio="1000" :fractionDigits="0">
                  {{ item.otherDeduction || 0 }}
                </HFixedNumber>
                公斤
              </template>
            </HDetail>
            <HDetail title="销售出库增量:" labelWidth="100px">
              <template #value>
                <HFixedNumber :ratio="1000" :fractionDigits="0">
                  {{ item.salesOutIncrement || 0 }}
                </HFixedNumber>
                公斤
              </template>
            </HDetail>
            <HDetail title="结算时间:" :value="item.createTime" labelWidth="100px" />
            <!-- <Button
              :disabled="item.approvalStatus != 0"
              class="view-button"
              @click="handleApproval(item)"
            >
              结算审批
            </Button> -->
          </div>
        </HCard>
      </List>
    </PullRefresh>

    <Dialog.Component
      v-model:show="dialogVisible"
      width="350px"
      title="结算审批"
      :show-confirm-button="false"
      :show-cancel-button="false"
      confirm-button-text="确定"
      confirm-button-color="#1677ff"
      cancel-button-text="取消"
      @confirm="onApproval"
      @cancel="onCancel"
    >
      <template #default>
        <div style="height: 60px; display: flex; align-items: center">
          <Field name="radio" label="审批意见" class="slot-field">
            <template #input>
              <RadioGroup v-model="approvalStatus" direction="horizontal">
                <Radio :name="1">同意</Radio>
                <Radio :name="-1">拒绝</Radio>
              </RadioGroup>
            </template>
          </Field>
        </div>
      </template>
      <template #footer>
        <div>
          <Button @click="onCancel" style="width: 50%">取消</Button>
          <Button type="primary" @click="onApproval" :loading="confirmLoading" style="width: 50%">
            确定
          </Button>
        </div>
      </template>
    </Dialog.Component>
  </div>
</template>

<script setup>
import {
  Search,
  List,
  PullRefresh,
  Dialog,
  Field,
  RadioGroup,
  Radio,
  Button,
  Toast,
  Row,
  Col,
  Calendar,
} from 'vant';
import { ref, reactive, watch, computed } from 'vue';
import { useRouter, useRoute } from 'vue-router';
import SvgIcon from '@/components/SvgIcon/SvgIcon';
import { HCard } from '@/components';
import { getSettlementHistory, settlementApproval } from '@/api/in-out-manage';
import { HFixedNumber } from '@/components';
import dayjs from 'dayjs';
import HDetail from '@/views/InoutManage/common/HDetail';
import HCardHead from '@/views/InoutManage/common/HCardHead';
import Tag from '@/views/PurchasesOverview/common/Tag1';
import { checkPermission } from '@/utils/permission';

const router = useRouter();
const route = useRoute();
const search = ref('');
const listRef = ref();

const list = ref([]);
const pagination = reactive({
  page: 0,
  size: 10,
  total: 0,
});

const isLoading = ref(false);
const finished = ref(false);
const refreshLoading = ref(false);
const selectRecord = ref({});
const dialogVisible = ref(false);
const approvalStatus = ref(1);
const confirmLoading = ref(false);

const startTime = ref(dayjs().format('YYYY-MM-DD'));
const endTime = ref(dayjs().format('YYYY-MM-DD'));
const date = ref(`${startTime.value} - ${endTime.value}`);
const datePicker = ref(false);
const minDate = new Date(dayjs().subtract(10, 'year').format('YYYY-MM-DD'));
const hasCargoSpace = computed(() => checkPermission(['cargo_space']));

const onScan = () => {
  window.cordova?.plugins.barcodeScanner.scan(
    (result) => {
      if (result.text) {
        goToDetail(result.text);
      }
    },
    (error) => {
      alert('扫码失败 ' + error);
    },
    {
      prompt: '请将二维码放在扫码框中',
      resultDisplayDuration: 300,
    },
  );
};
const onRefresh = () => {
  if (refreshLoading.value) {
    pagination.page = 0;
    pagination.total = 0;
    list.value = [];
    refreshLoading.value = false;
  }
  finished.value = false;
  isLoading.value = true;
  getList();
};
const onSearch = () => {
  finished.value = false;
  list.value = [];
  pagination.page = 0;
  pagination.total = 0;
  listRef.value?.check();
};
const getList = async () => {
  const { items, total } = await getSettlementHistory({
    schedulingNo: search.value ? search.value : undefined,
    page: pagination.page + 1,
    size: pagination.size,
    startTime: startTime.value,
    endTime: endTime.value,
  });
  list.value.push(...items);
  list.value.forEach((item) => {
    item.storeHouseName =
      hasCargoSpace.value && item.cargoSpaceName
        ? `${item.storeHouseName}_${item.cargoSpaceName}`
        : item.storeHouseName;
  });
  pagination.total = total;
  // 加载状态结束
  isLoading.value = false;

  // 数据全部加载完成
  if (list.value.length >= pagination.total) {
    finished.value = true;
  }
};

const onConfirm = (values) => {
  const [start, end] = values;
  startTime.value = dayjs(start).format('YYYY-MM-DD');
  endTime.value = dayjs(end).format('YYYY-MM-DD');
  date.value = `${startTime.value} - ${endTime.value}`;
  datePicker.value = false;
  onSearch();
};

const goToDetail = (schedulingNo) => {
  router.push({
    name: 'SettlementManageRecordDetail',
    params: {
      schedulingNo: schedulingNo,
    },
  });
};

// const handleApproval = (item) => {
//   selectRecord.value = item;
//   dialogVisible.value = true;
// };
const onApproval = async () => {
  try {
    confirmLoading.value = true;
    await settlementApproval({
      approvalStatus: approvalStatus.value,
      accountDetailNo: selectRecord.value.accountDetailNo,
    });
    Toast.success('审批成功');
    onCancel();
    refreshLoading.value = true;
    onRefresh();
  } finally {
    confirmLoading.value = false;
  }
};
const onCancel = () => {
  dialogVisible.value = false;
  selectRecord.value = {};
  approvalStatus.value = 1;
};
watch(
  () => route.name,
  () => {
    search.value = '';
    onSearch();
  },
);
</script>

<style lang="scss" scoped>
.scan-action {
  font-size: 25px;
  height: 34px;
  display: flex;
  align-items: center;
  padding: 0 10px;
}
.total-contain {
  padding: 9px 14px;
}
.detail-card {
  padding: 16px;
  margin-bottom: 10px;
}
.card-head {
  display: flex;
  align-items: center;
  justify-content: space-between;
  font-size: 18px;
  font-weight: bold;
  color: #1f3359;
  .next {
    color: rgba(109, 116, 141, 0.6);
  }
}
.schedule-num {
  flex-grow: 1;
}
.card-content {
  margin-top: 8px;
  line-height: 30px;
  .row {
    display: flex;
    .label {
      font-size: 14px;
      font-weight: 400;
      color: #6d748d;
      flex-basis: 6em;
    }
    .value {
      font-size: 14px;
      font-weight: 400;
      color: #0f0f0f;
      flex-grow: 1;
    }
    .carNum {
      padding: 3px 6px;
      background: #f4f4f4;
      border-radius: 2px;
      border: 1px solid #ebedf0;
      margin-right: 5px;
    }
  }
  ::v-deep(.view-button) {
    width: 100%;
    height: 36px;
    color: #165dff;
    border: 1px #165dff solid;
    text-align: center;
    font-size: 15px;
    font-weight: 400;
    line-height: 36px;
  }
}
.tag-cls {
  line-height: 25px;
}
.next-contain {
  width: 80px;
  text-align: right;
}
.icon-cls {
  vertical-align: text-top;
}
</style>
