<template>
  <div class="supervision-summary">
    <div class="summary-item">清查时间：<HDateTime :value="props.data.time" /></div>
    <div class="summary-item">
      清查结果：{{ props.data.resultName }}
      <Icon class="checked" name="checked" v-if="props.data.result === 1" />
      <Icon class="warning" name="warning" v-else />
    </div>
    <div class="summary-item result-title">结论：</div>
    <div class="summary-item result-value">1、清查范围：{{ props.data.scopeDesc }}</div>
    <div class="summary-item result-value">2、清查结果：{{ props.data.resultDesc }}</div>
  </div>
</template>

<script setup>
import { Icon } from 'vant';
import HDateTime from '@/components/HDateTime/HDateTime';

const props = defineProps({
  data: {
    type: Object,
    default: () => ({}),
  },
});
</script>

<style scoped lang="scss">
.supervision-summary {
  padding: 10px 25px;
}
.summary-item {
  display: flex;
  align-items: center;
  font-size: 18px;
  font-weight: 500;
  color: var(--van-gray-7);
  line-height: 25px;
  margin: 10px 0;

  &.result-title {
    color: var(--van-gray-8);
  }
  &.result-value {
    font-weight: normal;
  }
}
.checked {
  margin: 0 10px;
  color: #2ec635;
}
.warning {
  margin: 0 10px;
  color: #ea4531;
}
</style>
