<?xml version="1.0" encoding="UTF-8"?>
<svg width="20px" height="20px" viewBox="0 0 20 20" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
    <title>已勾选</title>
    <defs>
        <rect id="path-1" x="0" y="0" width="20" height="20"></rect>
        <rect id="path-3" x="0" y="0" width="16" height="16"></rect>
    </defs>
    <g id="页面-1" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
        <g id="安排调度" transform="translate(-62.000000, -77.000000)">
            <g id="已勾选" transform="translate(62.000000, 77.000000)">
                <mask id="mask-2" fill="white">
                    <use xlink:href="#path-1"></use>
                </mask>
                <g id="已勾选-(Background/Mask)"></g>
                <g id="笔记" mask="url(#mask-2)">
                    <g transform="translate(2.000000, 2.000000)">
                        <mask id="mask-4" fill="white">
                            <use xlink:href="#path-3"></use>
                        </mask>
                        <g id="笔记-(Background/Mask)" stroke="none" fill="none"></g>
                    </g>
                </g>
                <g id="Byte-Design-教育" mask="url(#mask-2)">
                    <g transform="translate(1.000000, 1.000000)" id="笔记">
                        <path d="M18,0 L18,18 L0,18 L0,0 L18,0 Z" id="矩形"></path>
                        <path d="M15.3754883,1.87500004 L15.3751142,6.37462549 L13.8751139,6.37462549 L13.8754883,2.625 L3.37548828,2.625 L3.37548828,15.171702 L7.12548849,15.171702 L7.12548849,16.6717014 L2.62548833,16.6717014 C2.21127473,16.6717014 1.87548828,16.3359145 1.87548828,15.9217009 L1.87548828,1.87500004 C1.87548828,1.46078644 2.21127473,1.125 2.62548833,1.125 L14.6254887,1.125 C15.0397023,1.125 15.3754883,1.46078644 15.3754883,1.87500004 Z M17.1595034,9.35292377 C17.4523966,9.645817 17.3611231,10.2119653 16.9556383,10.6174501 L10.9653033,15.9871279 C10.7697459,16.1826853 10.5259156,16.3138095 10.2878795,16.3514225 L8.39831084,16.65 C7.9070414,16.7276274 7.57315392,16.3937408 7.65078132,15.9024714 L7.94935882,14.0129027 C7.98697181,13.7748666 8.11809464,13.5310355 8.31365217,13.335478 L14.3039876,7.965799 C14.7094724,7.56031415 15.275619,7.46904029 15.5685122,7.76193352 L17.1595034,9.35292377 Z M9.23780794,14.5326427 L9.08908567,15.2116974 L9.7681386,15.0629734 L15.6069343,9.92522821 L15.0766037,9.39489841 L9.23780794,14.5326427 Z M9.37548828,8.25000029 L9.37548828,9 C9.37548828,9.20710678 9.20759544,9.37500029 9.00048866,9.37500029 L5.25048854,9.37500029 C5.04338175,9.37500029 4.87548828,9.20710678 4.87548828,9 L4.87548828,8.25000029 C4.87548828,8.0428935 5.04338175,7.87500043 5.25048854,7.87500043 L9.00048866,7.87500043 C9.20759544,7.87500043 9.37548828,8.0428935 9.37548828,8.25000029 Z M12.3754883,5.25000014 L12.3754883,6.00000029 C12.3754883,6.20710707 12.207596,6.37500014 12.0004893,6.37500014 L5.25048854,6.37500014 C5.04338175,6.37500014 4.87548828,6.20710707 4.87548828,6.00000029 L4.87548828,5.25000014 C4.87548828,5.04289336 5.04338175,4.87500029 5.25048854,4.87500029 L12.0004893,4.87500029 C12.207596,4.87500029 12.3754883,5.04289336 12.3754883,5.25000014 Z" id="形状" fill="#1677FF"></path>
                    </g>
                </g>
            </g>
        </g>
    </g>
</svg>