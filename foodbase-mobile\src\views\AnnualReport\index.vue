<template>
  <div class="summary_addNode">
    <div class="box">
      <div class="summary">
        <div class="buffer_head">
          <div class="setDate">
            <img src="@/assets/annual/setDate.png" alt="" />
          </div>
          <div class="preview">年终回顾</div>
        </div>
        <div class="buffer_head">
          <div class="avatar-inline">
            <img src="@/assets/annual/avatar.png" alt="" />
          </div>
          <div class="names">{{ userInfo.nickName }}，您好！</div>
        </div>
        <div class="content">
          <span>时光荏苒</span>
          <span>在过去的2023年里</span>
          <span>我们不畏困难,砥砺前行</span>
          <span>深耕细作护粮安</span>
          <span>感谢您的一路同行</span>
        </div>
        <div class="support">
          <img src="@/assets/annual/arrow.png" alt="" />
          <p>上滑屏幕继续</p>
        </div>
        <!-- <div class="unit" v-if="isSichuan">华信咨询设计研究院有限公司</div>
        <div class="unit" v-if="isZhejiang">华信咨询设计研究院有限公司</div>
        <div class="unit" v-if="isNeimeng">内蒙古自治区物资和粮食储备局</div> -->
      </div>
      <div class="summary">
        <div class="saturday">
          <div class="gl-avatar">
            <p class="setDate">{{ query.registerDate }}</p>
            <p>是我们初次相识的日子</p>
            <p>
              一转眼
              <span class="setFont">{{ query.registerDays }}</span>
              天
            </p>
          </div>
          <div class="avatarUrl">
            <img src="@/assets/annual/Computeroffice.png" alt="" />
          </div>
        </div>
        <div class="blavatar">
          过去一年您累计访问系统
          <span>{{ query.visitTimes }}</span>
          次
        </div>
        <div class="make_jobs">
          <div class="jobsimg">
            <img src="@/assets/annual/jobs.png" alt="" />
          </div>
          <div class="jobbody">
            <p class="job_t">这一天，您工作到</p>
            <p class="job_s">{{ query.lastWorkTime }}</p>
            <p class="job_y">没人比您更清楚奋斗的底色，前行必有曙光</p>
          </div>
        </div>
        <div class="monitor">
          <div class="monitor_a">
            <p>
              透过
              <span class="setFont">{{ query.cameraCount }}</span>
              路实时监控画面
            </p>
            <p>
              <span class="setFont">{{ query.temperatureCount }}</span>
              次精准粮情监测
            </p>
          </div>
          <div class="monitor_b">
            <img src="@/assets/annual/addr.png" alt="" />
          </div>
        </div>
        <div class="monitor_ing">我看到了您作为大国粮仓守护者的责任与担当</div>
      </div>
      <div class="summary">
        <div class="storage">仓廪实，天下安，我们曾共同监督执行</div>
        <div class="theVehicle">
          <div class="housedimg">
            <img src="@/assets/annual/house.png" alt="" />
          </div>
          <div class="storagegem">
            <p>
              <span>{{ query.carInOutCount }}</span>
              辆运粮车的出入库作业
            </p>
            <p>
              为
              <span>{{ query.grainInOutCount }}</span>
              吨
            </p>
            <p>粮食保驾护航</p>
          </div>
        </div>
        <div class="border-bottom">
          <div class="border-bottom-text">
            <span v-if="isZhejiang">感谢您在“浙江粮仓” </span>
            <span v-if="isZhejiang">粮食安全数字化协同应用平台</span>
            <span v-if="isZhejiang">的主动实践，特此评选您为“浙江粮仓”</span>

            <span v-if="isSichuan">感谢您在“天府粮仓” </span>
            <span v-if="isSichuan">四川省粮食购销监管平台</span>
            <span v-if="isSichuan">的主动实践，特此评选您为“天府粮仓”</span>

            <span v-if="isNeimeng">感谢您在内蒙古粮食储备系统的主动实践</span>
            <span v-if="isNeimeng">特此评选您为“内蒙古粮食储备系统”</span>

            <span v-if="['5'].includes(userInfo.dept.level)" class="setFonttitle">劳动模范</span>
            <span v-else-if="['1', '2', '3'].includes(userInfo.dept.level)" class="setFonttitle"
              >监管标兵</span
            >
            <span v-else class="setFonttitle">达人用户</span>

            <span>脚踏实地，兢兢业业做好本职工作</span>
            <span v-if="isZhejiang"> 您，就是“浙江粮仓”的 </span>
            <span v-if="isSichuan"> 您，就是“天府粮仓”的 </span>
            <span v-if="isNeimeng"> 您，就是“北疆粮仓”的 </span>
            <span class="content-date">最美守护人！！</span>
          </div>
        </div>
        <div class="attrName" v-if="isSichuan">天府粮仓四川省粮食购销监管平台</div>
        <div class="attrName" v-if="isZhejiang">浙江粮仓粮食安全数字化协同应用平台</div>
        <div class="attrName" v-if="isNeimeng">内蒙古自治区物资和粮食储备局</div>
      </div>
    </div>
  </div>
</template>

<script setup>
import './fontRef.scss';
import { ref, onMounted, computed } from 'vue';
import { useStore } from 'vuex';
import { getstatistics } from './api';
const query = ref({});
const store = useStore();
const userInfo = store.getters['user/userInfo'];

const isSichuan = computed(() => {
  return process.env.VUE_APP_MODE === 'sichuan';
});
const isNeimeng = computed(() => {
  return process.env.VUE_APP_MODE === 'neimenggu';
});
const isZhejiang = computed(() => {
  return process.env.VUE_APP_MODE === 'zhejiang';
});
const getworkreport = async () => {
  getstatistics().then((res) => {
    query.value = res;
  });
};
onMounted(() => {
  getworkreport();
});
</script>
<style scoped lang="scss">
.summary_addNode {
  // width: 100%;
  height: 100%;
  // height: 100vh;
  scroll-snap-type: y mandatory;
  overflow-y: scroll;

  .box {
    background-image: url('@/assets/annual/bg.png');
    background-repeat: no-repeat;
    background-size: 100% 100%;
  }

  .summary {
    width: 100%;
    // height: 100vh;
    overflow: hidden;
    scroll-snap-align: start;

    .buffer_head {
      width: 100%;
      display: flex;
      flex-direction: row;

      .setDate {
        width: 111px;
        height: 260px;
        margin-left: 13px;
        margin-top: 49px;

        img {
          width: 100%;
          height: 100%;
        }
      }

      .preview {
        // width: 221px;
        height: 38px;
        font-size: 52px;
        font-weight: bold;
        color: #ffffff;
        line-height: 38px;
        letter-spacing: 3px;
        padding-top: 261px;
        padding-left: 13px;
      }

      .avatar-inline {
        margin-left: 42px;
        margin-top: 48px;

        img {
          width: 116px;
          height: 86px;
        }
      }

      .names {
        padding-top: 84px;
        padding-left: 10px;
        font-size: 22px;
        font-weight: normal;
        color: #ffffff;
        line-height: 38px;
        letter-spacing: 1px;
      }
    }

    .content {
      margin-top: 38px;
      font-size: 16px;
      display: flex;
      font-weight: normal;
      color: #ffffff;
      flex-direction: column;

      span {
        margin: auto;
        line-height: 36px;
        letter-spacing: 2px;
      }
    }

    .support {
      margin-top: 21px;

      img {
        margin: auto;
        width: 26px;
        height: 24px;
      }

      p {
        height: 30px;
        text-align: center;
        font-size: 12px;
        font-weight: normal;
        color: rgba(255, 255, 255, 0.7);
        line-height: 30px;
      }
    }

    .unit {
      padding-left: 20px;
      padding-top: 80px;
      font-size: 12px;
      font-weight: normal;
      color: rgba(0, 0, 0, 0.4);
      line-height: 16px;
    }

    .saturday {
      display: flex;
      flex-direction: row;
      margin-top: 94px;
      margin-left: 26px;

      .gl-avatar {
        .setDate {
          font-size: 30px;
          font-weight: normal;
          color: #ffffff;
          line-height: 30px;
          letter-spacing: 1px;
        }

        p {
          font-size: 16px;
          font-weight: normal;
          color: #ffffff;
          line-height: 36px;
          padding-top: 11px;

          .setFont {
            font-size: 30px;
            font-family: PadFront;
            color: #e4832a;
          }
        }
      }

      .avatarUrl {
        margin-top: 40px;
        margin-left: 2px;

        img {
          width: 108px;
          height: 90px;
        }
      }
    }

    .blavatar {
      padding-left: 26px;
      padding-top: 6px;
      font-size: 16px;
      font-weight: normal;
      color: #ffffff;
      line-height: 32px;

      span {
        font-size: 30px;
        font-family: PadFront;
        color: #e4832a;
      }
    }

    .make_jobs {
      display: flex;
      flex-direction: row;
      margin-top: 100px;
      margin-left: 26px;

      .jobsimg {
        width: 108px;
        height: 123px;
      }

      .jobbody {
        display: flex;
        flex-direction: column;
        margin-right: 26px;
        text-align: right;

        .job_t {
          font-size: 16px;
          font-weight: normal;
          color: #ffffff;
          line-height: 38px;
          // padding-left: 81px;
          height: 38px;
          letter-spacing: 1px;
        }

        .job_s {
          font-size: 30px;
          font-weight: normal;
          color: #ffffff;
          line-height: 30px;
          padding-top: 8px;
          letter-spacing: 2px;
        }

        .job_y {
          font-size: 16px;
          font-weight: normal;
          color: #ffffff;
          line-height: 34px;
          padding-top: 10px;
          letter-spacing: 1px;
        }
      }
    }

    .monitor {
      display: flex;
      flex-direction: row;
      margin-left: 26px;
      margin-top: 100px;

      p {
        font-size: 16px;
        font-weight: normal;
        color: #ffffff;
        line-height: 38px;

        // padding-top: 8px;
        span {
          font-size: 30px;
          font-family: PadFront;
          color: #e4832a;
        }
      }

      .monitor_b {
        width: 108px;
        height: 84px;
        margin-left: 20px;
        // padding-top: 8px;
      }
    }

    .monitor_ing {
      padding-left: 26px;
      padding-top: 6px;
      font-size: 16px;
      font-weight: normal;
      color: #ffffff;
      line-height: 38px;
    }

    .storage {
      margin-top: 100px;
      text-align: right;
      padding-right: 26px;
      font-size: 16px;
      font-weight: normal;
      color: #ffffff;
      line-height: 34px;
    }

    .theVehicle {
      display: flex;
      flex-direction: row;
      margin-left: 26px;
      text-align: right;

      .housedimg {
        width: 101px;
        height: 80px;

        img {
          // width: 100%;
          // height: 100%;
          padding-top: 22px;
        }
      }

      .storagegem {
        width: 100%;
        text-align: right;
        padding-right: 26px;

        p {
          font-size: 16px;
          font-weight: normal;
          color: #ffffff;
          line-height: 36px;
          padding-top: 10px;

          span {
            font-size: 30px;
            font-family: PadFront;
            color: #e4832a;
          }
        }
      }
    }

    .border-bottom {
      width: 95%;
      height: 364px;
      margin: auto;
      background-image: url('@/assets/annual/frame.png');
      background-repeat: no-repeat;
      background-size: 100% 100%;
      margin-top: 70px;

      .border-bottom-text {
        display: flex;
        flex-direction: column;
        font-size: 16px;
        padding-top: 61px;

        span {
          margin: auto;
          font-weight: 500;
          color: #000000;
          line-height: 34px;
        }

        .setFonttitle {
          font-size: 28px;
          font-weight: 700;
          color: #e4832a;
          line-height: 32px;
          letter-spacing: 4px;
          padding-top: 20px;
          padding-bottom: 22px;
        }

        .content-date {
          color: #000000;
          letter-spacing: 1px;
          font-weight: 700;
        }
      }
    }

    .attrName {
      // position: absolute;
      // bottom: 10px;
      // left: auto;
      text-align: center;
      margin-top: 20px;
      margin-bottom: 20px;
      color: rgba(0, 0, 0, 0.7);
      line-height: 16px;
      font-size: 12px;
    }
  }
}
</style>
