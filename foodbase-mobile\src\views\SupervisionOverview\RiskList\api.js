import { reRequest } from '@/utils/request';

export async function getRiskwarning(params) {
  return reRequest().get('/api/videoView/getAppAIList.do', { params });
}

export async function addHandling(params) {
  return reRequest().get('/api/videoView/disposeWarning.do', { params });
}
export async function getErrorList(params) {
  return reRequest().get('/api/rmErrorCauseSummary/getErrorCauseSummaryByType.do', { params });
}

export async function postErrorResult(data) {
  return reRequest().post('/api/rmErrorCauseSummary/discern.do', data);
}