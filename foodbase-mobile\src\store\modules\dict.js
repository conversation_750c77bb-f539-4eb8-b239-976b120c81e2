import {
  getBizDict,
  getBizDictDetail,
  getBizDictFromReserve,
  getBizDictDetailFromReserve,
} from '@/api/biz-dict';

export default {
  namespaced: true,
  state: {
    list: [],
    dictMap: {},
    reserveList: [],
    reserveDictMap: {},
  },
  getters: {
    dictOf(state) {
      return function (name) {
        return state.dictMap[name];
      };
    },
    reserveDictOf(state) {
      return function (name) {
        return state.reserveDictMap[name];
      };
    },
  },
  mutations: {
    setList(state, list) {
      state.list = list;
    },
    setDictMap(state, map) {
      state.dictMap = map;
    },
    setReserveList(state, list) {
      state.reserveList = list;
    },
    setReserveDictMap(state, map) {
      state.reserveDictMap = map;
    },
  },
  actions: {
    async load({ state, commit }, forceReload = false) {
      if (state.list.length > 0 && !forceReload) {
        return;
      }
      const list = await getBizDict();
      const names = list.map((it) => it.name);
      const details = await getBizDictDetail(names.join(','));

      commit('setList', list);
      commit('setDictMap', details);
      commit('setReserveDictMap', details);
    },
    async loadReserve({ state, commit }, forceReload = false) {
      if (state.reserveList.length > 0 && !forceReload) {
        return;
      }
      const list = await getBizDictFromReserve();
      const names = list.map((it) => it.name);
      const details = await getBizDictDetailFromReserve(names.join(','));
      commit('setReserveList', list);
      commit('setReserveDictMap', details);
    },
  },
};
