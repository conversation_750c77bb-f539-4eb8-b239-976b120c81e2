<template>
  <svg class="svg-icon" :style="iconStyle">
    <use :xlink:href="`#icon-${name}`" :fill="color" />
  </svg>
</template>

<script>
export default {
  name: 'SvgIcon',
  props: {
    name: {
      type: String,
      required: true,
      default: 'email',
    },
    size: {
      type: String,
      default: '1em',
    },
    color: {
      type: String,
      default: 'currentColor',
    },
  },
  computed: {
    iconStyle() {
      return {
        width: this.size,
        height: this.size,
      };
    },
  },
};
</script>
