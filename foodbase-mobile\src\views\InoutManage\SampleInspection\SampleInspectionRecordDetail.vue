<template>
  <div class="sample-inspection-record-detail page-warpper">
    <HCard class="info-card" title="基本信息" bgColor="#F7F8FA">
      <Cell title="扦样号" :value="form.qrCode" title-class="bold-text"></Cell>
      <Cell title="调度号" title-class="bold-text">
        <template #value>
          <ShortScheduleNo>{{ form.schedulingNo }}</ShortScheduleNo>
        </template>
      </Cell>
      <Cell title="作业车牌" :value="form.transportVehicleNo" title-class="bold-text"></Cell>
      <Cell title="粮油品种" :value="form.foodCategoryName" title-class="bold-text"></Cell>
      <Cell title="产地" :value="form.originName"></Cell>
      <Cell title="值仓仓房" :value="form.storeHouseName"></Cell>
      <Cell title="粮油等级" :value="form.levelName"></Cell>
      <Cell title="检验意见" :value="form.qualityResult"></Cell>
      <Cell title="是否为超标粮" :value="form.isOverFoodName"></Cell>
      <Cell title="意见备注" :value="form.remark"></Cell>
    </HCard>
    <div class="items-table">
      <div class="table-header">
        <div style="width: 35%">检验项目名称</div>
        <div style="width: 15%">检验值</div>
        <div style="width: 25%">增量比例%</div>
        <div style="width: 25%">扣量比例%</div>
      </div>
      <div class="table-body" v-for="(item, index) in itemsList" :key="index">
        <div style="width: 35%">
          {{ item.testItemName }}{{ item.testItemSign ? '（' + item.testItemSign + '）' : '' }}
        </div>
        <div style="width: 15%">{{ item.testItemAval }}</div>
        <div style="width: 25%">{{ item.incrementalProportion }}</div>
        <div style="width: 25%">{{ item.buckleQuantityRatio }}</div>
      </div>
    </div>
  </div>
</template>

<script>
import { Cell } from 'vant';
import { HCard } from '@/components';
import { ShortScheduleNo } from '@/views/InoutManage/common';
import { ref, onMounted, computed } from 'vue';
import { useRoute } from 'vue-router';
import { getQualityTestInfoForApp } from '@/api/in-out-manage';
import { checkPermission } from '@/utils/permission';

export default {
  components: { Cell, HCard, ShortScheduleNo },
  setup() {
    const route = useRoute();
    const form = ref({});
    const itemsList = ref([]);
    const hasCargoSpace = computed(() => checkPermission(['cargo_space']));

    onMounted(() => {
      initData();
    });

    const initData = async () => {
      const qrCode = route.params.qrCode;
      const data = await getQualityTestInfoForApp({ qrCode });
      if (!data) {
        form.value = {};
        itemsList.value = [];
        return;
      }
      const { testingItemList, ...rest } = data;
      itemsList.value = testingItemList;
      form.value = {
        ...rest,
        isOverFoodName: rest.isOverFood == '1' ? '是' : '否',
        storeHouseName:
          hasCargoSpace.value && rest.cargoSpaceName
            ? `${rest.storeHouseName}_${rest.cargoSpaceName}`
            : rest.storeHouseName,
      };
    };

    return {
      form,
      itemsList,
    };
  },
};
</script>

<style lang="scss" scoped>
.info-card {
  ::v-deep(.bold-text) {
    font-weight: bold;
  }
}
.items-table {
  width: 100%;
  text-align: center;
  .table-header {
    display: flex;
    background-color: #f7f8fa;
    height: 45px;
    line-height: 45px;
    font-size: 14px;
    font-weight: bold;
    color: #111111;
  }
  .table-body {
    display: flex;
    background-color: #ffffff;
    margin-bottom: 1px;
    // height: 44px;
    height: 100%;
    line-height: 44px;
    font-size: 14px;
    font-weight: 400;
    color: #323233;
  }
}
</style>
