import { reserverPurchase } from '@/utils/request';
import { reactive } from 'vue';
import { Toast } from 'vant';
import { getReserverToken } from '@/utils/auth';
import qs from 'qs';

const getList = async (type) => {
  const params = {
    state: 0,
    pageSize: 50,
  };
  return await reserverPurchase().get(
    `/api/hrps/${type.replace('Execute', '')}/execute/page/query`,
    { params },
  );
};

const uploadImg = async () => {
  return await reserverPurchase().post(`/api/fast_dfs/upload`, {});
};

const cameraGetPicture = (fileList) => {
  return new Promise((resolve, reject) => {
    const Camera = window.Camera;
    const sourceType = Camera.PictureSourceType.CAMERA;

    navigator.camera.getPicture(
      (imageUri) => {
        const fileItem = reactive({
          url: imageUri,
          isImage: true,
          status: 'done',
          deletable: true,
        });
        fileList.push(fileItem);

        // 上传参数
        const options = new window.FileUploadOptions();
        options.fileKey = 'file';
        options.fileName = imageUri.substr(imageUri.lastIndexOf('/') + 1);
        options.headers = {
          Authorization: getReserverToken(),
        };
        const params = {};
        params.coder = 'sgcb/storehouse_document';
        options.params = params;

        // 上传地址
        const reserverBaseUrl = () =>
          JSON.parse(process.env.VUE_APP_RESERVER_API_BASE_URL)[window.__isProd] || location.origin;
        const uploadUri = new URL(
          '/purchase_supervision/api/fast_dfs/upload',
          reserverBaseUrl(),
        ).toString();

        // 上传文件
        const fileTransfer = new window.FileTransfer();
        fileTransfer.upload(
          imageUri,
          uploadUri,
          (res) => {
            // 上传成功
            const resp = res.response;
            if (resp) {
              const respJson = JSON.parse(resp);
              const { data } = respJson;
              fileItem.url = data.filePath;
              fileItem.name = data.fileName;
              fileItem.status = 'done';

              resolve(data);
            }
          },
          (error) => {
            // 上传失败
            fileItem.status = 'failed';
            Toast('上传失败');
            console.error(error);
            reject(error);
          },
          options,
        );
      },
      (err) => {
        console.error(err);
      },
      {
        quality: 85,
        destinationType: Camera.DestinationType.FILE_URI,
        sourceType: sourceType,
      },
    );
  });
};

const update = async (data, type) => {
  await reserverPurchase().put(`/api/hrps/${type.replace('Execute', '')}/execute/update`, data);
};

const taskSubmit = async (data, type) => {
  // return await reserverPurchase().post(`/api/activiti/task/submit`, qs.stringify(data), {
  //   headers: {
  //     'Content-Type': 'application/x-www-form-urlencoded;charset=UTF-8',
  //   },
  // });
  return await reserverPurchase().put(
    `/api/hrps/${type.replace('Execute', '')}/execute/doneSubmit`,
    qs.stringify(data),
    {
      headers: {
        'Content-Type': 'application/x-www-form-urlencoded;charset=UTF-8',
      },
    },
  );
};

export { getList, uploadImg, cameraGetPicture, update, taskSubmit };
