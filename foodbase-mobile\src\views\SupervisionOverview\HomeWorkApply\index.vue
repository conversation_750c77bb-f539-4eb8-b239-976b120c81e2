<template>
  <div class="homework-apply">
    <div v-if="checkPermission('app-home-work-apply-start')" class="homework-apply-header">
      <div class="homework-apply-header-bg">
        <span>当前审核中：{{ waitApproveNo }}条</span>
        <van-button type="primary" round @click="handleApply">
          作业申请
        </van-button>
      </div>
    </div>
    <van-tabs v-model:active="tabActive" @change="onTabChange">
      <van-tab title="审核中" name="1"></van-tab>
      <van-tab title="全部" name="2"></van-tab>
    </van-tabs>
    <van-divider class="!mt-[0px] !mb-[0px]" />
    <van-dropdown-menu v-if="tabActive === '2'" active-color="var(--van-primary-color)">
      <van-dropdown-item
        title="作业类型"
        v-model="queryForm.jobTypeCode"
        :options="jobTypeOptions"
        @change="onSearch"
      />
      <van-dropdown-item title="日期" ref="dropItemRef" @open="onDropOpen">
        <van-datetime-picker
          ref="datetimePicker"
          v-model="queryForm.currentDate"
          type="year-month"
          title="请选择日期"
          :max-date="maxDate"
          @cancel="onDatetimeCancel"
          @confirm="onDatetimeConfirm"
        />
      </van-dropdown-item>
    </van-dropdown-menu>
    <van-divider v-if="tabActive === '2'" class="!mt-[0px] !mb-[0px]" />
    <HList
      ref="hListRef"
      :get-list-fn="getHomeworkRecord"
      :search-params="queryForm"
    >
      <template #list-wrapper>
        <div class="store-list">
          <div v-for="item in hListRef?.list" :key="item.id" class="store-item">
            <div class="store-item-header">
              <span class="store-item-tag">{{ item.jobTypeName?.replace('作业', '') }}</span>
              <span class="store-item-title">
                {{ item.syStartedusername }}-{{ item.storehouseNames }}-{{ item.jobTypeName }}
              </span>
              <span v-if="item.approvalStatus === 0" class="store-item-state2">未提交</span>
              <span v-else-if="item.approvalStatus === 1" class="store-item-state1">已提交</span>
              <span v-else-if="item.approvalStatus === 2" class="store-item-state1">审批通过</span>
              <span v-else-if="item.approvalStatus === 3" class="store-item-state2">审批未通过</span>
            </div>
            <div class="mt-[15px] flex">
              <span class="mt-style">仓房:</span>
              <span class="mt-value">{{ item.storehouseNames }}</span>
            </div>
            <div class="mt-[15px]">
              <span class="mt-style">计划操作时间:</span>
              <span>{{ item.plainOperateTime }}</span>
            </div>
            <div class="mt-[15px] pb-[15px]">
              <span class="mt-style">申请时间:</span>
              <span>{{ item.createTime && dayjs(item.createTime).format('YYYY-MM-DD') }}</span>
            </div>
            <div
              class="store-item-footer"
              v-if="item.approvalStatus === 1 && checkPermission('app-home-work-apply-back') && checkPermission('app-home-work-apply-ok')"
            >
              <van-button v-if="checkPermission('app-home-work-apply-back')" type="default" class="w-full" size="small" @click="handleBack(item)">
                驳回
              </van-button>
              <van-button v-if="checkPermission('app-home-work-apply-ok')" type="primary" class="w-full" size="small" @click="handleApprove(item)">
                通过
              </van-button>
            </div>
          </div>
        </div>
      </template>
    </HList>
  </div>
</template>

<script setup>
import { onMounted, ref, reactive } from 'vue';
import { getHomeworkRecord, startApprove } from '@/api/home-work.js';
import dayjs from 'dayjs';
import { useRouter } from 'vue-router';
import { Toast } from 'vant';
import { checkPermission } from '@/utils/permission';

const router = useRouter()

const tabActive = ref('1')

const waitApproveNo = ref(0)

const hListRef = ref(null)

const dropItemRef = ref(null)

const jobTypeOptions = ref([
  {
    text: '全部',
    value: undefined,
  },
  {
    text: '通风作业',
    value: 'JOB_TYPE_02',
  },
  {
    text: '控温作业',
    value: 'JOB_TYPE_03',
  },
  {
    text: '气调作业',
    value: 'JOB_TYPE_04',
  },
  {
    text: '薰蒸作业',
    value: 'JOB_TYPE_05',
  },
])

// 最大时间可选
const maxDate = new Date(dayjs().year(), dayjs().month());

const queryForm = reactive({
  plainOperateMonth: undefined,
  currentDate: undefined,
  approvalStatus: '1',
  jobTypeCode: undefined,
});

const handleApply = () => {
  router.push({ name: 'HomeWorkAsk' })
}

const onTabChange = () => {
  queryForm.plainOperateMonth = undefined
  queryForm.currentDate = undefined
  queryForm.approvalStatus = tabActive.value === '1' ? '1' : undefined
  queryForm.jobTypeCode = undefined
  onSearch()
}

const onSearch = () => {
  hListRef.value.filter()
}

const onDropOpen = () => {
  if (!queryForm.plainOperateMonth && !queryForm.currentDate) {
    queryForm.plainOperateMonth = dayjs().format('YYYY-MM');
    queryForm.currentDate = new Date(dayjs().year(), dayjs().month())
  }
}

const onDatetimeCancel = () => {
  queryForm.plainOperateMonth = undefined
  queryForm.currentDate = undefined
  dropItemRef.value.toggle()
  onSearch()
}

const onDatetimeConfirm = (val) => {
  queryForm.plainOperateMonth = dayjs(val).format('YYYY-MM');
  queryForm.currentDate = new Date(dayjs(val).year(), dayjs(val).month())
  dropItemRef.value.toggle()
  onSearch()
}

const handleBack = (item) => {
  Toast.loading()
  startApprove({ ids: item.id, approved: '-1', coder: 'mobile-jobApply' }).then(() => {
    Toast.success('审批成功！');
    Toast.clear()
    onSearch()
  })
}

const handleApprove = (item) => {
  Toast.loading()
  startApprove({ ids: item.id, approved: '1', coder: 'mobile-jobApply' }).then(() => {
    Toast.success('审批成功！');
    Toast.clear()
    onSearch()
  })
}

onMounted(() => {
  getHomeworkRecord({ approvalStatus: '1' }).then(res => {
    if (res?.data?.obj) {
      waitApproveNo.value = res?.data?.obj?.total
    }
  })
})
</script>

<style lang="scss" scoped>
.homework-apply {
  width: 100%;
  height: 100%;
  .homework-apply-header {
    width: 100%;
    height: 100px;
    display: flex;
    align-items: end;
    justify-content: center;
    background: linear-gradient(to bottom, rgb(147, 216, 246), white) ;
    .homework-apply-header-bg {
      width: 90%;
      height: 80px;
      background-color: white;
      border-top-left-radius: 4px;
      border-top-right-radius: 4px;
      display: flex;
      align-items: center;
      justify-content: space-between;
      padding: 0 20px 0 20px;
    }
  }
  .store-item {
    background: #ffffff;
    margin-bottom: 12px;
    padding: 0 12px;
    .store-item-header {
      display: flex;
      align-items: center;
      padding: 15px 0px 0px 0px;
    }
    .store-item-tag {
      color: var(--van-primary-color);
      background-color: rgba(25, 137, 250, 0.2);
      border: 1px solid var(--van-primary-color);
      padding: 0 4px;
      border-radius: 2px;
      margin-right: 8px;
      text-overflow: ellipsis;
      white-space: nowrap;
    }
    .store-item-title {
      // text-overflow: ellipsis;
      // white-space: nowrap;
      // overflow: hidden;
    }
    .store-item-state1 {
      font-size: 14px;
      color: var(--van-green);
      background-color: rgba(7, 193, 96, 0.2);
      padding: 2px 8px;
      border-radius: 12px;
      margin-left: auto;
      text-overflow: ellipsis;
      white-space: nowrap;
    }
    .store-item-state2 {
      font-size: 14px;
      color: var(--van-orange);
      background-color: rgba(255, 151, 106, 0.2);
      padding: 2px 8px;
      border-radius: 12px;
      margin-left: auto;
      text-overflow: ellipsis;
      white-space: nowrap;
    }
    .store-item-footer {
      display: flex;
      align-items: center;
      justify-content: space-around;
      padding: 0px 0px 10px 0px;
      button {
        margin: 0 10px;
      }
    }
    .mt-style {
      width: 36%;
      display: inline-block;
      padding: 0 0px;
    }
    .mt-value {
      width: 64%;
    }
  }
  .store-item:last-child {
    margin-bottom: 0px;
  }
}
</style>
