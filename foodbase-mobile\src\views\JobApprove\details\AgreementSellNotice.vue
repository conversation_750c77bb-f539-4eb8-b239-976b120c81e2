<template>
  <div class="auctionsales">
    <div>
      <van-tabs v-model:active="approval">
        <van-tab title="申请信息"></van-tab>
        <van-tab title="审批流程"></van-tab>
      </van-tabs>
    </div>
    <div class="contentsof" v-if="approval == 0">
      <div class="deliveryPoint">
        <div class="deliveryhead" style="padding-top: 17px">{{ form.name }}出库通知书</div>
        <div class="deliveryhead">{{ form.serialNumber }}</div>
        <div class="deliverytext">
          <div>{{ form.header }}</div>
          <div>&emsp;&emsp;{{ form.sketch }}</div>
        </div>
        <div v-for="item in detailList" :key="item.id">
          <div class="detailStart">
            <div class="detailStart_one">粮油品种：</div>
            <div class="detailStart_two">{{ item.foodCategoryName }}</div>
          </div>
          <div class="detailStart">
            <div class="detailStart_one">成本：</div>
            <div class="detailStart_two" v-if="isNumber(item.coster)">
              {{ Number(item.coster).toFixed(2) }}
            </div>
            <div v-else>/</div>
          </div>
          <div class="detailStart">
            <div class="detailStart_one">直管属性：</div>
            <div class="detailStart_two">
              <biz-dict-name dict="STRAIGHT_PIPE" :value="item.straightPipe" />
            </div>
          </div>
          <div class="detailStart">
            <div class="detailStart_one">收货年份：</div>
            <div class="detailStart_two">{{ item.yearHarvest }}</div>
          </div>
          <div class="detailStart">
            <div class="detailStart_one">库点及仓号：</div>
            <div class="detailStart_two">{{ item.storeName }}/{{ item.storeHouseName }}</div>
          </div>
          <div class="detailStart">
            <div class="detailStart_one">计划数：</div>
            <div class="detailStart_two">{{ item.plannedNumber }}</div>
          </div>
        </div>
      </div>
      <approvalButton :id="params?.id" coder="donoticeDeportation"></approvalButton>
    </div>
    <div class="approvals_required" v-if="approval == 1">
      <approvalProcess :id="params?.id" coder="donoticeDeportation"></approvalProcess>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue';
import approvalProcess from './common/approval-process';
import approvalButton from './common/approval-button';
import { useRoute } from 'vue-router';
import { getDonoticeDeportationById } from '@/api/job-approve';
import { BizDictName } from '@/views/InoutManage/common';

const route = useRoute();

const params = ref(route.params);

// const active = ref(1);

const form = ref({});

const detailList = ref([]);
const approval = ref(0);

const getDetail = async () => {
  const { items } = await getDonoticeDeportationById({
    type: 1,
    id: params.value.id,
  });
  detailList.value = items.detailList;
  form.value = items;
  form.value.sketch = form.value.sketch.replaceAll('%t', '');
};

const isNumber = (val) => {
  return !isNaN(parseFloat(val));
};

onMounted(() => {
  getDetail();
});
</script>

<style lang="scss" scoped>
@import '@/styles/confirmapproval.scss';

.contentsof {
  padding: 0 10px;
}
</style>
