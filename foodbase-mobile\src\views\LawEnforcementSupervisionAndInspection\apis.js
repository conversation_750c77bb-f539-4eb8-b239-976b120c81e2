import { reserverPurchase } from '@/utils/request';

const getSupervisedcheckList = async (source) => {
  return await reserverPurchase().get('/api/hrps/supervisedcheck/task/list', {
    params: {
      page: 1,
      size: 50,
      source,
      status: 1,
    },
  });
};

const postSupervisedcheck = async (data) => {
  return await reserverPurchase().post('/api/hrps/supervisedcheck/task/filling', data);
};

const submitSupervisedcheck = async (id) => {
  return await reserverPurchase().get(`/api/hrps/supervisedcheck/task/submit/${id}`);
};

export { getSupervisedcheckList, postSupervisedcheck, submitSupervisedcheck };
