<template>
  <div class="food-infomation">
    <!-- 所属单位 -->
    <Field
      v-model="unitName"
      label="所属单位"
      is-link
      readonly
      name="signPicker"
      placeholder="请选择"
      @click="showUnitPicker = true"
      size="large"
    >
    </Field>
    <Popup v-model:show="showUnitPicker" position="bottom">
      <Search v-model="unitSearch" placeholder="请输入单位名称"></Search>
      <Picker
        :columns-field-names="fieldNames"
        :columns="unitFilter(unitSearch)"
        @confirm="onUnitConfirm"
        @cancel="showUnitPicker = false"
      />
    </Popup>

    <!-- 库点名称 -->
    <Field
      v-model="storeName"
      label="库点名称"
      is-link
      readonly
      name="signPicker"
      placeholder="请选择"
      @click="showStorePicker = true"
      size="large"
    >
    </Field>
    <Popup v-model:show="showStorePicker" position="bottom">
      <Search v-model="storeSearch" placeholder="请输入库点名称"></Search>
      <!--      :columns="storeOptions"-->
      <Picker
        :columns-field-names="storeFieldNames"
        @confirm="onStoreConfirm"
        @cancel="showStorePicker = false"
        :columns="storeFilter(storeSearch)"
      />
    </Popup>

    <!-- 仓房 -->
    <Field
      v-model="storeHouseName"
      label="仓房"
      is-link
      readonly
      name="signPicker"
      placeholder="请选择"
      @click="showStoreHousePicker = true"
      size="large"
    >
    </Field>
    <Popup v-model:show="showStoreHousePicker" position="bottom">
      <Picker
        :columns="storeHouseOptions"
        @confirm="onStoreHouseConfirm"
        @cancel="showStoreHousePicker = false"
      />
    </Popup>

    <PullRefresh v-model="refreshing" @refresh="onRefresh" style="margin-top: 15px">
      <List
        ref="listRef"
        v-model:loading="loading"
        :finished="finished"
        :finished-text="templist.length === 0 ? '' : '没有更多了'"
        @load="loadTempList"
      >
        <EmptyHolder v-if="templist.length === 0 && finished" />
        <div class="list" v-else>
          <div v-if="!isAnhui">
            <HCard v-for="item in templist" :key="item.guid" @click="showDetail(item)">
              <!-- <template #header-title>
              {{ item.storeName }}
            </template>
            <template #header-extra>
              <ProcessTag type="warn" v-if="item.status === '1'">未处理</ProcessTag>
              <ProcessTag type="finished" v-else-if="item.status === '2'">已处理</ProcessTag>
            </template> -->
              <!-- <Field
              :class="{ listhdbg: item?.isheader }"
              is-link
              readonly
              name="signPicker"
              placeholder=" "
            >
              <template #label> -->
              <div class="title">
                <div style="font-size: 20px">{{ item.cfMc }}</div>
                <!-- 粮油品种名称 -->
                <Tag :plain="false" class="list-title-tag" size="medium">{{ item.lypzMc }}</Tag>
                <!-- 粮油性质名称 -->
                <Tag :plain="false" size="medium" :type="handle1TagType(item.lyxzMc)">{{
                  item.lyxzMc
                }}</Tag>
              </div>
              <!-- </template>
            </Field> -->
              <div class="detail">
                <div class="item">
                  <div class="name">最高温：</div>
                  <div :class="testFunc(item.maxTemp, item.maxTempWarn, item.maxTempAlarm, item)">
                    {{ item.maxTemp || '-' }}℃
                  </div>
                </div>
                <div class="item">
                  <div class="name">平均温：</div>
                  <div :class="testFunc(item.avgTemp, item.avgTempWarn, item.avgTempAlarm, item)">
                    {{ item.avgTemp || '-' }}℃
                  </div>
                </div>
                <div class="item">
                  <div class="name">最低温：</div>
                  <div class="value">{{ item.minTemp || '-' }}℃</div>
                </div>
                <div class="item">
                  <div class="name">收获年份：</div>
                  <div class="value">{{ item.shnf }}年</div>
                </div>
                <div class="item">
                  <div class="name">库存&nbsp;&nbsp;|&nbsp;&nbsp;仓容：</div>
                  <div class="value">
                    {{ item.kcSl }}吨&nbsp;&nbsp;|&nbsp;&nbsp;{{ item.cfRl }}吨
                  </div>
                </div>
                <div class="item" v-if="isSichuan">
                  <div class="name">害虫数量：</div>
                  <div class="value">{{ item.pestNum || '-' }}</div>
                </div>
              </div>
            </HCard>
          </div>
          <div v-else>
            <HCard v-for="item in templist" :key="item.guid" @click="showDetail(item)">
              <div class="header">
                <div class="title">
                  <div style="font-size: 20px">{{ item.cfMc }}</div>
                  <!-- 粮油品种名称 -->
                  <Tag :plain="false" class="list-title-tag" size="medium">{{ item.lypzMc }}</Tag>
                  <!-- 粮油性质名称 -->
                  <Tag :plain="false" size="medium" :type="handle1TagType(item.lyxzMc)">{{
                    item.lyxzMc
                  }}</Tag>
                </div>
                <div class="voice" @click.stop="onPlayAudio(item)">
                  <span>语音播报</span>
                  <van-icon v-if="playFlag[item.id]" color="red" name="pause-circle" />
                  <van-icon v-else color="blue" name="play-circle" />
                </div>
              </div>
              <div class="detail">
                <div class="item">
                  <div class="name">收获年份：</div>
                  <div class="value">{{ item.shnf }}年</div>
                </div>
                <div class="item">
                  <div class="name">粮食产地：</div>
                  <div class="value">{{ item.lycdMc }}</div>
                </div>
                <div class="item">
                  <div class="name">粮食等级：</div>
                  <div class="value">{{ item.lydjMc }}</div>
                </div>
                <div class="item">
                  <div class="name">库存&nbsp;&nbsp;|&nbsp;&nbsp;仓容：</div>
                  <div class="value">
                    {{ item.kcSl }}吨&nbsp;&nbsp;|&nbsp;&nbsp;{{ item.cfRl }}吨
                  </div>
                </div>
                <van-divider>三温两湿度</van-divider>
                <div class="two-items">
                  <div class="item">
                    <div class="name">最高气温：</div>
                    <div :class="testFunc(item.maxTemp, item.maxTempWarn, item.maxTempAlarm, item)">
                      {{ item.maxTemp || '-' }}℃
                    </div>
                  </div>
                  <div class="item">
                    <div class="name">仓内湿度：</div>
                    <div class="value">{{ item.houseHumidity || '-' }}%</div>
                  </div>
                </div>
                <div class="two-items">
                  <div class="item">
                    <div class="name">平均温：</div>
                    <div :class="testFunc(item.avgTemp, item.avgTempWarn, item.avgTempAlarm, item)">
                      {{ item.avgTemp || '-' }}℃
                    </div>
                  </div>
                  <div class="item">
                    <div class="name">仓外湿度：</div>
                    <div class="value">{{ item.airHumidity || '-' }}%</div>
                  </div>
                </div>
                <div class="item">
                  <div class="name">最低温：</div>
                  <div class="value">{{ item.minTemp || '-' }}℃</div>
                </div>
                <van-divider>气体虫害</van-divider>
                <div class="two-items">
                  <div class="item">
                    <div class="name">氮气浓度：</div>
                    <div class="value">{{ item.nzValue || '-' }}%</div>
                  </div>
                  <div class="item">
                    <div class="name">二氧化碳浓度：</div>
                    <div class="value">{{ item.cozValue || '-' }}ppm</div>
                  </div>
                </div>
                <div class="two-items">
                  <div class="item">
                    <div class="name">氧气浓度：</div>
                    <div class="value">{{ item.ozValue || '-' }}%</div>
                  </div>
                  <div class="item">
                    <div class="name">磷化氢浓度：</div>
                    <div class="value">{{ item.phValue || '-' }}ppm</div>
                  </div>
                </div>
                <div class="item">
                  <div class="name">虫害：</div>
                  <div class="value">{{ item.pestNum || '-' }}头</div>
                </div>
              </div>
            </HCard>
          </div>
        </div>
      </List>
    </PullRefresh>
  </div>
</template>

<script>
export default {
  name: 'FoodInfomation',
};
</script>

<script setup>
import { onActivated, onMounted, ref, watch, computed, onUnmounted } from 'vue';
import { List, PullRefresh, Field, Popup, Picker, Tag, Search } from 'vant';
import { HCard } from '@/components';
import EmptyHolder from '@/views/common/EmptyHolder';
import { useRouter } from 'vue-router';
import {
  getTempList,
  getStorageUnit,
  getStoreOfCompany,
  getStoreHouseAndOil,
} from '@/api/supervision';
import { MsEdgeTTS, OUTPUT_FORMAT, PITCH, RATE } from 'msedge-tts-browserify';
import xmlescape from 'xml-escape';

const fieldNames = {
  text: 'name',
  value: 'id',
  children: 'child',
};
const storeFieldNames = {
  text: 'storeName',
  value: 'storeId',
  children: 'child',
};
const router = useRouter();
const isSichuan = computed(() => {
  return process.env.VUE_APP_MODE === 'sichuan';
});
const isAnhui = computed(() => {
  return process.env.VUE_APP_MODE === 'anhui';
});

// 筛选框
// 公司
const unitName = ref('');
const unitId = ref(undefined);
const unitSearch = ref('');
const storeSearch = ref('');
// 库点
const storeName = ref('');
const storeId = ref(undefined);
// 仓房
const storeHouseName = ref('');
const storehouseId = ref(undefined);

// 选项
const unitOptions = ref([]);
const storeOptions = ref([]);
const storeHouseOptions = ref([]);

// 弹出框显示
const showUnitPicker = ref(false);
const showStorePicker = ref(false);
const showStoreHousePicker = ref(false);

const listRef = ref();
const templist = ref([]);
const loading = ref(false);
const finished = ref(true);
const refreshing = ref(false);
const playFlag = ref({});
const myAudio = ref({});
const mySpeech = ref({});

const onRefresh = () => {
  stopAudio();
  loadTempList();
};

const showDetail = (item) => {
  stopAudio();
  router.push({ name: 'FoodInfomationDetail', params: { storeHouseId: item.cfId } });
};
const unitFilter = (value) => {
  if (!value) {
    return unitOptions.value;
  }
  const filterUnitList = unitOptions.value.filter((item) => item.name.indexOf(value) > -1);
  return filterUnitList;
};
// 库点名称搜索
const storeFilter = (value) => {
  if (!value) {
    return storeOptions.value?.storeList;
  }
  const filterUnitList = storeOptions.value?.storeList.filter(
    (item) => item.storeName.indexOf(value) > -1,
  );
  return filterUnitList;
};
const guid = () => {
  return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, function (c) {
    var r = (Math.random() * 16) | 0,
      v = c == 'x' ? r : (r & 0x3) | 0x8;
    return v.toString(16);
  });
};
//获取粮情信息列表
const loadTempList = async () => {
  templist.value = [];
  if (storeId.value) {
    loading.value = true;
    try {
      templist.value = await getTempList({
        storeId: storeId.value,
        storehouseId: storehouseId.value,
      });
      templist.value = templist.value.map((it, index) => {
        return {
          ...it,
          guid: guid(),
          kcSl: Number(it.kcSl || 0)?.toFixed(0),
          cfRl: Number(it.cfRl || 0)?.toFixed(0),
          id: index,
        };
      });
    } finally {
      loading.value = false;
      refreshing.value = false;
    }
  }
};

//获取单位下拉框数据
const loadUnits = async () => {
  unitOptions.value = [];
  unitOptions.value = await getStorageUnit();
  // unitOptions.value = list?.map((it) => {
  //   return {
  //     text: it.name,
  //     value: it.id,
  //   };
  // });
};
//获取库点下拉框数据
const loadStores = async () => {
  storeOptions.value = [];
  storeOptions.value = await getStoreOfCompany({ handleUnitId: unitId.value });
};
//获取仓房下拉框数据
const loadStoreHouses = async () => {
  storeHouseOptions.value = [];
  const list = await getStoreHouseAndOil({ storeId: storeId.value });
  storeHouseOptions.value = list?.map((it) => {
    return {
      text: it.name,
      value: it.id,
    };
  });

  storeHouseOptions.value.unshift({
    text: '全部',
    value: undefined,
  });
};
const onUnitConfirm = (val) => {
  // 清空库点 仓房的值
  storeName.value = '';
  storeId.value = '';
  storeHouseName.value = '';
  storehouseId.value = '';
  unitName.value = val.name;
  unitId.value = val.id;
  showUnitPicker.value = false;
  loadStores();
};
const onStoreConfirm = (val) => {
  storeName.value = val.storeName;
  storeId.value = val.storeId;
  // 清空仓房
  storeHouseName.value = '';
  storehouseId.value = '';
  showStorePicker.value = false;
  loadTempList();
  loadStoreHouses();
};
const onStoreHouseConfirm = (val) => {
  storeHouseName.value = val.text;
  storehouseId.value = val.value;
  showStoreHousePicker.value = false;
  loadTempList();
};
const handle1TagType = (cityLevel) => {
  return cityLevel.indexOf('县') !== -1
    ? 'warning'
    : cityLevel.indexOf('市') !== -1
    ? 'success'
    : 'primary';
};

/**
 * @param {number} max 平均温或最高温
 * @param {number} val1 预警值
 * @param {number} val2 告警值
 * @returns {string}
 */
const testFunc = (max, val1, val2, item) => {
  item.isheader = true;
  if (val2 && max >= val2) {
    return 'valueAlarm';
  }
  if (val1 && max >= val1) {
    return 'valueearly';
  }
  item.isheader = false;
  return 'value';
};

function concatenateUint8Arrays(array1, array2) {
  const result = new Uint8Array(array1.length + array2.length);
  result.set(array1, 0);
  result.set(array2, array1.length);
  return result;
}

const onPlayAudio = (item) => {
  if (playFlag.value[item.id]) {
    pauseAudio(item);
  } else {
    playAudio(item);
  }
};

const playAudio = async (item) => {
  const content = item.content;
  for (let key in playFlag.value) {
    playFlag.value[key] = false;
  }
  playFlag.value[item.id] = true;
  if ('speechSynthesis' in window) {
    window.speechSynthesis.pause();
    window.speechSynthesis.cancel();
    mySpeech.value[item.id] = new SpeechSynthesisUtterance();
    mySpeech.value[item.id].text = content;
    window.speechSynthesis.speak(mySpeech.value[item.id]);
    mySpeech.value[item.id].onend = () => {
      playFlag.value[item.id] = false;
    };
    return;
  }
  for (let key in myAudio.value) {
    if (myAudio.value[key]) {
      myAudio.value[key].pause();
    }
  }
  const tts = new MsEdgeTTS();
  await tts.setMetadata('zh-CN-XiaoxiaoNeural', OUTPUT_FORMAT.AUDIO_24KHZ_96KBITRATE_MONO_MP3);
  const url = await new Promise((resolve) => {
    const readable = tts.toStream(xmlescape(content), {
      pitch: PITCH.DEFAULT,
      rate: RATE.DEFAULT,
      volume: 100,
    });
    let data64 = Uint8Array.from(atob(''), (c) => c.charCodeAt(0));
    readable.on('data', (data) => {
      data64 = concatenateUint8Arrays(data64, data);
    });

    readable.on('end', async () => {
      const blob = new Blob([data64], { type: 'audio/mpeg' });

      if (blob.size) {
        resolve(URL.createObjectURL(blob));
      }
    });
  });
  myAudio.value[item.id] = new Audio(url);
  myAudio.value[item.id].play();
  myAudio.value[item.id].onended = () => {
    playFlag.value[item.id] = false;
  };
};

const pauseAudio = (item) => {
  if ('speechSynthesis' in window) {
    window.speechSynthesis.pause();
    window.speechSynthesis.cancel();
  }
  if (myAudio.value[item.id]) {
    myAudio.value[item.id].pause();
  }
  playFlag.value[item.id] = false;
};

const stopAudio = () => {
  if ('speechSynthesis' in window) {
    window.speechSynthesis.pause();
    window.speechSynthesis.cancel();
  }
  for (let key in myAudio.value) {
    if (myAudio.value[key]) {
      myAudio.value[key].pause();
    }
  }
  playFlag.value = {};
};

onMounted(() => {
  loadUnits();
});

onActivated(() => {
  // refreshing.value = true;
  onRefresh();
});

onUnmounted(() => {
  stopAudio();
});

watch(
  storeId,
  () => {
    templist.value = [];
    loadTempList();
  },
  { deep: true },
);
watch(
  storehouseId,
  () => {
    templist.value = [];
    loadTempList();
  },
  { deep: true },
);
</script>

<style scoped lang="scss">
.listhdbg {
  background-color: yellow;
}
.valueearly {
  color: orange;
}
.valueAlarm {
  color: red;
}
.list-title-tag {
  margin: 0 5px;
}
.h-card {
  margin-bottom: 15px;
}
.title {
  padding: 18px;
  display: flex;
  // justify-content: space-between;
  align-items: center;
}
.header {
  display: flex;
  justify-content: space-between;
  .voice {
    padding: 18px;
    span {
      color: rgb(0, 89, 255);
      margin-right: 6px;
    }
  }
}
.detail {
  font-size: 16px;
  padding: 10px 20px;
  color: var(--van-gray-7);
  .two-items {
    display: flex;
    .item {
      width: 50%;
    }
  }

  .item {
    display: flex;
    line-height: 38px;

    .name {
      font-size: 16px;
      width: 120px;
      white-space: nowrap;
    }
  }
}
</style>
