{"name": "foodbase-mobile", "version": "0.1.0", "private": true, "scripts": {"serve": "node vue-cli-service-start.js", "build:zhejiang": "vue-cli-service build --mode zhejiang", "build:neimenggu": "vue-cli-service build --mode neimenggu", "build:hubei": "vue-cli-service build --mode hubei", "build:hubei-ehb": "vue-cli-service build --mode hubei-ehb", "build:sichuan": "vue-cli-service build --mode sichuan", "build:anhui": "vue-cli-service build --mode anhui", "lint": "vue-cli-service lint"}, "dependencies": {"@vant/use": "^1.3.4", "axios": "^0.23.0", "bignumber.js": "^9.1.0", "core-js": "^3.8.3", "dayjs": "^1.10.7", "echarts": "^5.2.1", "echarts-liquidfill": "^3.1.0", "inquirer": "8.0.0", "jsbarcode": "^3.11.5", "jsencrypt": "^3.3.2", "lodash-es": "^4.17.21", "msedge-tts-browserify": "^1.4.1", "path-browserify": "^1.0.1", "pnpm": "^7.11.0", "qrcode": "^1.5.1", "qs": "^6.10.1", "resize-detector": "^0.3.0", "svg-sprite-loader": "^6.0.10", "vant": "^3.2.5", "vconsole": "^3.15.0", "vue": "^3.2.6", "vue-router": "^4.0.3", "vuex": "^4.0.0", "xml-escape": "^1.1.0"}, "devDependencies": {"@babel/core": "^7.12.16", "@babel/eslint-parser": "^7.12.16", "@vant/auto-import-resolver": "^1.0.1", "@vue/cli-plugin-babel": "~5.0.0-beta.6", "@vue/cli-plugin-eslint": "~5.0.0-beta.6", "@vue/cli-plugin-router": "~5.0.0-beta.6", "@vue/cli-plugin-vuex": "~5.0.0-beta.6", "@vue/cli-service": "~5.0.0-beta.6", "@vue/compiler-sfc": "^3.2.6", "@vue/eslint-config-prettier": "^6.0.0", "autoprefixer": "^10.4.15", "babel-plugin-import": "^1.13.3", "cross-env": "7.0.3", "eslint": "^7.20.0", "eslint-plugin-prettier": "^3.3.1", "eslint-plugin-vue": "^7.2.0", "postcss": "^8.4.29", "prettier": "^2.2.1", "sass": "^1.32.7", "sass-loader": "^12.0.0", "tailwindcss": "^3.3.3", "unplugin-vue-components": "^0.25.2"}}