echo "Beginning Build:"
rm -rf dist
mkdir -p dist
cd ../ffmpeg_wasm
echo "emconfigure"
emconfigure ./configure --cc="emcc" --cxx="em++" --ar="emar" --ranlib="emranlib" --prefix=$(pwd)/../WasmVideoPlayer/dist --enable-cross-compile --target-os=none \
        --arch=x86_32 --cpu=generic --enable-gpl --enable-version3 --disable-avdevice --disable-swresample --disable-postproc --disable-avfilter \
        --disable-programs --disable-asm --disable-logging --disable-everything  --disable-encoders --disable-decoders --enable-decoder=hevc --enable-decoder=h264  \
        --disable-ffplay --disable-ffprobe --disable-ffserver --disable-asm --disable-doc --disable-devices --disable-network --disable-hwaccels \
        --disable-parsers --disable-bsfs --disable-demuxers --enable-demuxer=flv --disable-indevs --disable-muxers --disable-outdevs \
		--disable-pthreads --disable-os2threads --disable-w32threads --disable-doc --disable-protocols --disable-debug\
		--disable-debug --disable-iconv --disable-sdl2
if [ -f "Makefile" ]; then
  echo "make clean"
  make clean
fi
echo "make"
make -j6
echo "make install"
make install
cd ../WasmVideoPlayer
./build_decoder_wasm.sh
