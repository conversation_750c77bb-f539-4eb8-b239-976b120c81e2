<template>
  <div class="bottom-warpper bottom-btn">
    <Button native-type="submit" class="button" @click="handleReject">不通过</Button>
    <Button type="primary" native-type="submit" class="button" @click="handleConfirm">通过</Button>

    <Dialog.Component
      v-model:show="show"
      :title="`审批${status}`"
      show-cancel-button
      show-confirm-button
      teleport="body"
      @confirm="onApproval"
      @cancel="onCancel"
    >
      <template #default>
        <Field
          v-model="form.remark"
          label="备注:"
          maxlength="128"
          :label-width="40"
          show-word-limit
        />
      </template>
    </Dialog.Component>
  </div>
</template>

<script setup>
import { ref, defineProps } from 'vue';
import { Button, Dialog, Field, Toast } from 'vant';
import { activitiTaskApprove } from '@/api/job-approve';
import { useRouter } from 'vue-router';

const show = ref(false);

const router = useRouter();

const props = defineProps({
  id: [String, Number],
  coder: String,
});

const form = ref({
  remark: undefined,
  approved: undefined, //1通过，-1不通过
  ids: undefined,
  coder: undefined,
});

// 审批状态
const status = ref();

const handleConfirm = () => {
  show.value = true;
  status.value = '通过';
  form.value.approved = 1;
};

const handleReject = () => {
  show.value = true;
  status.value = '不通过';
  form.value.approved = -1;
};

const onApproval = async () => {
  form.value.ids = props.id;
  form.value.coder = props.coder?.split('-')[0];

  await activitiTaskApprove(form.value);
  show.value = false;
  Toast.success('审批成功');
  router.back();
};

const onCancel = () => {
  show.value = false;
  form.value.remark = undefined;
};
</script>
<style lang="scss" scoped>
.bottom-warpper {
  padding: 14px 16px 36px;
  background: #fff;
  display: flex;
  justify-content: space-between;
  .button {
    width: 48%;
    font-size: 18px;
  }
}
</style>
