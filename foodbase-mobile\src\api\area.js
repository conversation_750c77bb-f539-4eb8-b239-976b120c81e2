import { reserveRequest } from '@/utils/request';
import { memoize } from 'lodash-es';

export async function getAreaTree(code) {
  return reserveRequest().get('service/area/findInfoAreas', { params: { code } });
}

export const getAreaTreeMemo = memoize(async () => {
  return getAreaTree();
});


// 获取地区数据（粮食储备）
export function getAreaData(filter) {
  return reserveRequest().get(`/api/area/info?filter=${filter}`)
}
