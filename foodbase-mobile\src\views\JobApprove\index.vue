<template>
  <div class="job-approve">
    <Row>
      <Col :span="24">
        <Field
          v-model="queryForm.type"
          label="审批类型"
          is-link
          readonly
          placeholder="请选择"
          @click="showTyperPicker = true"
        />
      </Col>
    </Row>
    <div class="total">
      当前待审批任务 <span>{{ total }}</span> 条
    </div>
    <PullRefresh @refresh="onRefresh" v-model="refreshLoading">
      <List
        ref="listRef"
        v-model:loading="isLoading"
        :finished="finished"
        finished-text="没有更多了"
        @load="getList"
      >
        <HCard class="detail-card" v-for="item in list" :key="item" @click="goToDetail(item)">
          <div class="type">{{ item.eventName }}</div>
          <div class="card-content">
            <div class="row" v-if="nameLabel[item.coder]">
              <span class="label">{{ nameLabel[item.coder] }}：</span>
              <span class="value">{{ item.name }}</span>
            </div>
            <div class="row">
              <span class="label">申请单位：</span>
              <span class="value">{{ item.deptName }}</span>
            </div>
            <div class="row">
              <span class="label">申请人：</span>
              <span class="value">{{ item.username }}</span>
            </div>
            <div class="row">
              <span class="label">申请时间：</span>
              <span class="value">{{ item.createTime }}</span>
            </div>
          </div>
          <div style="display: flex; justify-content: flex-end; padding-top: 12px">
            <Button
              type="primary"
              native-type="submit"
              style="width: 68px; height: 28px; border-radius: 2px"
              >审批</Button
            >
          </div>
        </HCard>
        <HCard
          class="detail-card"
          v-for="(item, index) in detailList"
          :key="index"
          @click="goToDetailCheck(item[0])"
        >
          <div class="type">{{ item[0].eventName }}</div>
          <div style="padding-bottom: 8px">
            <div v-for="d in item" :key="d.id">
              <div class="card-content" style="border: none; padding: 0">
                <div class="row">
                  <span class="label2">{{ d.name }}：</span>
                  <span class="value">{{ d.createTime }}</span>
                </div>
              </div>
            </div>
          </div>

          <div
            style="
              display: flex;
              justify-content: flex-end;
              padding-top: 12px;
              border-top: 1px solid #e8e9ec;
            "
          >
            <Button
              type="primary"
              native-type="submit"
              style="width: 68px; height: 28px; border-radius: 2px"
              >查看</Button
            >
          </div>
        </HCard>
      </List>
    </PullRefresh>
    <ActionSheet v-model:show="showTyperPicker">
      <BizDictPicker
        :dict="isAnhui ? approveType : 'ACTIVITI__EVENT_MOBILE'"
        title="审批类型"
        @cancel="showTyperPicker = false"
        @confirm="onTyperConfirm"
      ></BizDictPicker>
    </ActionSheet>
  </div>
</template>

<script setup>
import { List, Field, Button, PullRefresh, ActionSheet, Row, Col } from 'vant';
import { ref, reactive, watch, computed } from 'vue';
import { useRouter, useRoute } from 'vue-router';
import { BizDictPicker } from '@/views/InoutManage/common';
import { getApproveList } from '@/api/job-approve';
const router = useRouter();
const route = useRoute();

const listRef = ref();
const queryForm = reactive({
  type: null,
  coder: null,
});

const showTyperPicker = ref(false);

const list = ref([]);
const detailList = ref([]);
const total = ref();
const pagination = reactive({
  page: 0,
  size: 10,
  total: 0,
});

const isLoading = ref(false);
const finished = ref(false);
const refreshLoading = ref(false);

const nameLabel = ref({
  rotationPlan: '计划名称',
  rotationPlanAllocation: '',
  'rotationScheme-1': '方案名称',
  'rotationScheme-0': '方案名称',
  'rotationSchemeDeal-1': '成交结果名称',
  'rotationSchemeDeal-0': '成交结果名称',
  'donoticeDeportation-10': '通知书编号',
  'donoticeDeportation-11': '通知书编号',
  'donoticeDeportation-2': '通知书编号',
  'billLading-0': '提货单编号',
  'billLading-1': '提货单编号',
});

const isAnhui = computed(() => {
  return process.env.VUE_APP_MODE === 'anhui';
});

const approveType = computed(() => {
  if (route.name === 'JobApprovePlan') {
    return 'PLAN_APPROVE__EVENT_MOBILE';
  } else if (route.name === 'JobApproveTicket') {
    return 'TICKET_APPROVE__EVENT_MOBILE';
  } else {
    return 'ACTIVITI__EVENT_MOBILE';
  }
});

const onTyperConfirm = (val) => {
  queryForm.coder = val.value === 'all' ? null : val.value;
  queryForm.type = val.label === '全部' ? null : val.label;
  showTyperPicker.value = false;
  onSearch();
};
const onRefresh = () => {
  if (refreshLoading.value) {
    pagination.page = 0;
    pagination.total = 0;
    list.value = [];
    detailList.value = [];
    refreshLoading.value = false;
  }
  finished.value = false;
  isLoading.value = true;
  getList();
};
const getList = async () => {
  try {
    // 之后替换接口
    let bizType;
    if (route.name === 'JobApprovePlan') {
      bizType = 1;
    } else if (route.name === 'JobApproveTicket') {
      bizType = 2;
    }
    let { items, detailList0, detailList1 } = await getApproveList({
      coder: queryForm.coder || '',
      bizType: queryForm.coder ? '' : bizType,
    });
    list.value = items || [];
    if (detailList0 && detailList0.length !== 0) {
      detailList.value.push(detailList0);
    }
    if (detailList1 && detailList1.length !== 0) {
      detailList.value.push(detailList1);
    }
    total.value = list.value.length + detailList.value.length;
    pagination.page = 1;
    pagination.total = list.value.length;
    // 加载状态结束
    isLoading.value = false;

    // 数据全部加载完成
    if (list.value.length >= pagination.total) {
      finished.value = true;
    }
  } catch {
    isLoading.value = false;
    finished.value = true;
  }
};
const onSearch = () => {
  pagination.page = 0;
  list.value = [];
  detailList.value = [];
  finished.value = false;
};
const goToDetail = (item) => {
  router.push({
    name: 'JobApproveDetail',
    params: {
      type: item.eventName,
      id: item.id,
      coder: item.coder,
      batchId: item.batchId || '0',
    },
    query: {
      name: item?.name,
    },
  });
};

const goToDetailCheck = (item) => {
  router.push({
    name: 'JobApproveDetail',
    params: {
      type: item.eventName,
      id: item.id,
      coder: item.coder,
      batchId: item.batchId || '0',
    },
  });
};

// watch(
//   () => route.name,
//   () => {
//     queryForm.type = '';
//     onSearch();
//   },
// );
watch(queryForm.type, () => {
  onSearch();
});
</script>

<style lang="scss" scoped>
.detail-card {
  padding: 16px;
  margin-bottom: 10px;

  .type {
    font-size: 16px;
    font-weight: bold;
    color: #232323;
  }
}

.total {
  padding: 10px 0 10px 16px;
  color: #232323;
  font-size: 14px;
  span {
    color: #1492ff;
  }
}
.card-head {
  display: flex;
  align-items: center;
  justify-content: space-between;
  font-size: 18px;
  font-weight: bold;
  color: #1f3359;
  .next {
    color: rgba(109, 116, 141, 0.6);
  }
}
.schedule-num {
  flex-grow: 1;
  margin-left: 12px;
}
.card-content {
  margin-top: 8px;
  padding-bottom: 8px;
  line-height: 30px;
  border-bottom: 1px solid #e8e9ec;
  .row {
    display: flex;
    .label {
      font-size: 14px;
      font-weight: 400;
      color: #686b73;
      flex-basis: 6em;
    }

    .label2 {
      width: 150px;
      font-size: 14px;
      font-weight: 400;
      color: #686b73;
    }

    .value {
      font-size: 14px;
      font-weight: 400;
      color: #232323;
      flex-grow: 1;
    }

    .carNum {
      padding: 3px 6px;
      background: #f4f4f4;
      border-radius: 2px;
      border: 1px solid #ebedf0;
      margin-right: 5px;
    }
  }
}
.tag-cls {
  line-height: 25px;
}
.next-contain {
  width: 80px;
  text-align: right;
}
.icon-cls {
  vertical-align: text-top;
}
</style>
