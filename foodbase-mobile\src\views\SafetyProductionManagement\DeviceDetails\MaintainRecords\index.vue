<template>
  <HPicker
    class="w-1/3 mt-3 mb-3 ml-4"
    :options="[
      { text: '全部', value: '' },
      ...Object.keys(status).map((i) => ({ text: status[i], value: i })),
    ]"
    v-model:value="type"
    placeholder="维护项目"
  ></HPicker>

  <div class="mt-4" v-for="item in list.items" :key="item.id">
    <van-cell-group inset>
      <van-cell title="维护项目：" :value="status[item.type]" />
      <van-cell
        title="维护时间："
        :value="String(item.time).length > 3 ? dayjs(item.time).format('YYYY-MM-DD') : '-'"
      />
      <van-cell title="维护人（机构）：" :value="item.maintainer" />
      <van-cell title="维护结果：" :value="result[item.result]" />
    </van-cell-group>
  </div>

  <van-empty v-if="!(list.items && list.items.length)" description="暂无数据" />
</template>

<script setup>
import { ref, onMounted, watch } from 'vue';
import { reserverPurchase } from '@/utils/request';
import { useRoute } from 'vue-router';
import dayjs from 'dayjs';

const route = useRoute();
const list = ref([]);
const type = ref('');

const status = {
  1: '日常维护',
  2: '一级保养',
  3: '二级保养',
  4: '其他',
  5: '第三方机构鉴定',
};

const result = {
  0: '完好',
  1: '待检修',
  2: '报废',
  3: '其他',
  4: '合格',
  5: '不合格',
};

onMounted(async () => {
  getList();
});

watch(
  () => type.value,
  async (val) => {
    console.log(val);
    getList(val);
  },
);

const getList = async (val) => {
  list.value = await reserverPurchase().get(
    `/api/hrps/instrument/maintenance/list/${route.query.id}?page=1&size=999&instrumentId=${
      route.query.id
    }&type=${val ?? ''}`,
  );
};
</script>

<style lang="scss" scoped></style>
