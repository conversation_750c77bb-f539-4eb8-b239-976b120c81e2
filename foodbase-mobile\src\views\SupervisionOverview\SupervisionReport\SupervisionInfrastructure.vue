<template>
  <div class="supervision-infrastructure">
    <table class="table">
      <tbody>
        <tr>
          <td class="name" rowspan="2">仓房设施</td>
          <td>仓房数量</td>
          <td>{{ props.data.storehouseSum }}</td>
        </tr>
        <tr>
          <td>设计仓容（吨）</td>
          <td>
            <HFixedNumber :fraction-digits="0">
              {{ props.data.designedStorehouseCapacity }}
            </HFixedNumber>
          </td>
        </tr>
        <tr>
          <td rowspan="2">油罐设施</td>
          <td>油罐数量</td>
          <td>{{ props.data.tankSum }}</td>
        </tr>
        <tr>
          <td>储油数量（吨）</td>
          <td>
            <HFixedNumber :fraction-digits="0">
              {{ props.data.designedTankCapacity }}
            </HFixedNumber>
          </td>
        </tr>
        <tr>
          <td rowspan="3">其他设施</td>
          <td>铁路专用线</td>
          <td>{{ props.data.railwaySum }}</td>
        </tr>
        <tr>
          <td>码头泊位</td>
          <td>{{ props.data.dockSum }}</td>
        </tr>
        <tr>
          <td>烘干中心</td>
          <td>{{ props.data.dryingCenterSum }}</td>
        </tr>
      </tbody>
    </table>
  </div>
</template>

<script setup>
import HFixedNumber from '@/components/HFixedNumber/HFixedNumber';

const props = defineProps({
  data: {
    type: Object,
    default: () => ({}),
  },
});
</script>

<style scoped lang="scss">
.supervision-infrastructure {
  padding: 16px;
  font-size: 18px;
  color: var(--van-gray-7);

  table {
    width: 100%;
    text-align: center;
    border-collapse: collapse;

    .name {
      width: 2em;
    }

    td {
      border: 1px solid var(--van-gray-5);
      line-height: 22px;
      padding: 10px 5px;
    }
  }
}
</style>
