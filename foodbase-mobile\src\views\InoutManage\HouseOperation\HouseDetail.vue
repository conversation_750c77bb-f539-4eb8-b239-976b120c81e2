<template>
  <div>
    <HouseDetailCard :detailInfo="detailInfo" @onChangeHouse="onChangeHouse" />
    <HCard class="opinion-card">
      <div>值仓意见</div>
      <Field type="textarea" placeholder="请输入" v-model="descriper" maxlength="255" />
    </HCard>
    <HCard class="opinion-card" style="margin-bottom: 70px">
      <div>值仓照片<span class="min-size">支持多张</span></div>
      <Uploader
        :model-value="fileList"
        multiple
        :before-read="beforeRead"
        class="upload-cls"
        @click-upload="onClickUpload"
        @delete="onDeleteFile"
      />
    </HCard>

    <Tabbar>
      <TabbarItem class="endTab">
        <Button class="com-btn end" @click="end">终止值仓</Button>
      </TabbarItem>
      <TabbarItem class="saveTab">
        <Button class="com-btn save" @click="save">保 存</Button>
      </TabbarItem>

      <TabbarItem class="finishTab">
        <Button class="com-btn finish" @click="finish">完成值仓</Button>
      </TabbarItem>
    </Tabbar>

    <ActionSheet
      v-model:show="showActionSheet"
      cancel-text="取消"
      description="请选择上传方式"
      :actions="actions"
      @select="onUploadActionSelect"
      close-on-click-action
    />
  </div>
</template>

<script setup>
import { onMounted, reactive, ref, computed } from 'vue';
import { ActionSheet, Field, Uploader, Tabbar, TabbarItem, Button, Dialog, Toast } from 'vant';
import { useRouter, useRoute } from 'vue-router';
import { HCard } from '@/components';
import HouseDetailCard from './HouseDetailCard.vue';
import {
  getFileUrls,
  getHouseOperationDetail,
  saveHouseOperation,
  completeHouseOperation,
} from '@/api/in-out-manage';
import { getReserverToken } from '@/utils/auth';
import { checkPermission } from '@/utils/permission';

const route = useRoute();
const router = useRouter();
const fileList = reactive([]);
const descriper = ref('');
const showActionSheet = ref(false);

const hasCargoSpace = computed(() => checkPermission(['cargo_space']));

const save = () => {
  Dialog.confirm({
    title: '确认保存？',
  })
    .then(() => {
      // on confirm
      const { schedulingNo } = route.params;
      const {
        buzTyper,
        counterOrg,
        customerId,
        foodCategoryId,
        storeHouseId,
        transportVehicleNo,
        cargoSpaceId,
      } = detailInfo.value;
      saveHouseOperation({
        schedulingNo: schedulingNo,
        buzTyper,
        counterOrg,
        customerId,
        foodCategoryId,
        originCode: 2, //originCode  1是web端 2是app端
        status: 1,
        fileUrl: fileList.filter((it) => it.status === 'done').map((it) => it.name),
        descriper: descriper.value,
        storeHouseId,
        transportVehicleNo,
        cargoSpaceId,
      }).then(() => {
        Toast.success('保存成功');
        router.back();
      });
    })
    .catch(() => {
      // on cancel
      Toast.fail('保存失败');
    });
};

const finish = () => {
  if (!detailInfo.value.foodCategoryId || detailInfo.value.foodCategoryId == 'null') {
    Toast.fail('值仓信息不完整，请确认');
    return;
  }
  Dialog.confirm({
    title: '确认完成值仓？',
  })
    .then(() => {
      // on confirm
      const { schedulingNo } = route.params;
      const {
        buzTyper,
        counterOrg,
        customerId,
        foodCategoryId,
        storeHouseId,
        transportVehicleNo,
        cargoSpaceId,
      } = detailInfo.value;
      completeHouseOperation({
        schedulingNo: schedulingNo,
        buzTyper,
        counterOrg,
        customerId,
        foodCategoryId,
        originCode: 2,
        status: 4,
        fileUrl: fileList.filter((it) => it.status === 'done').map((it) => it.name),
        descriper: descriper.value,
        storeHouseId,
        transportVehicleNo,
        cargoSpaceId,
      }).then(() => {
        Toast.success('值仓成功');
        router.back();
      });
    })
    .catch(() => {
      // on cancel
      Toast.fail('提交失败');
    });
};
const end = () => {
  Dialog.confirm({
    title: '终止值仓后无法继续确认,请确认是否继续?',
  })
    .then(() => {
      // on confirm
      const { schedulingNo } = route.params;
      const {
        buzTyper,
        counterOrg,
        customerId,
        foodCategoryId,
        storeHouseId,
        transportVehicleNo,
        cargoSpaceId,
      } = detailInfo.value;
      completeHouseOperation({
        schedulingNo: schedulingNo,
        buzTyper,
        counterOrg,
        customerId,
        foodCategoryId,
        status: 5,
        originCode: 2,
        fileUrl: fileList.filter((it) => it.status === 'done').map((it) => it.name),
        descriper: descriper.value,
        storeHouseId,
        transportVehicleNo,
        cargoSpaceId,
      }).then(() => {
        Toast.success('终止值仓');
        router.back();
      });
    })
    .catch(() => {
      // on cancel
      Toast.fail('提交失败');
    });
};
const detailInfo = ref({});

const getDetail = async () => {
  const { schedulingNo } = route.params;

  try {
    detailInfo.value = await getHouseOperationDetail({
      schedulingNo: schedulingNo,
      houseStatusType: 2, // 1是值仓值仓记录获取详情 2扫码获取详情
    });
  } catch (err) {
    console.log(err, '________________');
    //加一个提示
    Toast.fail('已完成值仓,请确认!');
    router.back();
  }
  descriper.value = detailInfo.value.descriper;
  let fileId = detailInfo.value.fileList?.split(',');
  fileList.push(
    ...(detailInfo.value.fileUrl?.map((item, index) => {
      let name = fileId[index];
      // let name = item
      //   .match(/[^/]+(?!.*\/)+(.jpg%.*\?|.jpg)/g)
      //   .filter((item) => item)
      //   .pop()
      //   .replace('?', '')
      //   .replace('%3F', '?');
      return {
        url: item,
        name: name,
        status: 'done',
        isImage: true,
        deletable: true,
      };
    }) || []),
  );
};

const actions = [{ name: '拍照' }, { name: '相册选择' }];

const onClickUpload = (e) => {
  e.preventDefault();
  if (navigator.camera) {
    showActionSheet.value = true;
  } else {
    Toast('不支持选择照片');
  }
};

const onUploadActionSelect = ({ name }) => {
  const Camera = window.Camera;
  let sourceType = Camera.PictureSourceType.PHOTOLIBRARY;
  if (name === '拍照') {
    sourceType = Camera.PictureSourceType.CAMERA;
  } else if (name === '相册选择') {
    sourceType = Camera.PictureSourceType.SAVEDPHOTOALBUM;
  }
  navigator.camera.getPicture(
    (imageUri) => {
      // 添加图片到图片列表
      const fileItem = reactive({
        url: imageUri,
        isImage: true,
        status: 'uploading',
        deletable: true,
      });
      fileList.push(fileItem);

      // 上传参数
      const options = new window.FileUploadOptions();
      options.fileKey = 'file';
      options.fileName = imageUri.substr(imageUri.lastIndexOf('/') + 1);
      options.headers = {
        Authorization: getReserverToken(),
      };
      const params = {};
      params.coder = 'sgcb/storehouse_document';
      options.params = params;

      // 上传地址
      const reserverBaseUrl = () =>
        JSON.parse(process.env.VUE_APP_RESERVER_API_BASE_URL)[window.__isProd] || location.origin;
      const uploadUri = new URL(
        '/reserver/api/fileManager/uploadFileBatch',
        reserverBaseUrl(),
      ).toString();

      console.log(imageUri, 'imageUri');
      console.log(options, 'options');
      // 上传文件
      const fileTransfer = new window.FileTransfer();
      fileTransfer.upload(
        imageUri,
        uploadUri,
        (res) => {
          console.log(res, 'res');
          // 上传成功
          const resp = res.response;
          if (resp) {
            const respJson = JSON.parse(resp);
            const { data } = respJson;
            fileItem.name = data.split(',')[0];
            fileItem.status = 'done';

            getFileUrls(data).then((urls) => {
              fileItem.url = urls[0];
            });
          }
        },
        (error) => {
          // 上传失败
          fileItem.status = 'failed';
          Toast('上传失败');
          console.error(error);
        },
        options,
      );
    },
    (err) => {
      Toast('选择图片失败');
      console.error(err);
    },
    {
      quality: 85,
      destinationType: Camera.DestinationType.FILE_URI,
      sourceType: sourceType,
    },
  );
};

const onDeleteFile = (file, { index }) => {
  fileList.splice(index, 1);
};

const beforeRead = (file) => {
  if (
    file.type.includes('jpg') ||
    file.type.includes('jpeg') ||
    file.type.includes('png') ||
    file.type.includes('gif')
  ) {
    return true;
  } else {
    Toast('请上传 jpg, png, gif 格式图片');
    return false;
  }
};

const onChangeHouse = (value) => {
  if (hasCargoSpace.value) {
    detailInfo.value.cargoSpaceId = value.cargoSpaceId;
    detailInfo.value.cargoSpaceName = value.cargoSpaceName;
    detailInfo.value.storeHouseId = value.storeHouseId;
    detailInfo.value.storeHouseName = value.storeHouseName;
  } else {
    detailInfo.value.storeHouseId = value.id;
    detailInfo.value.storeHouseName = value.name;
  }
};

onMounted(() => {
  getDetail();
});
</script>

<style scoped lang="scss">
.opinion-card {
  padding: 16px;
  margin-bottom: 10px;
}
.com-btn {
  // padding: 10px 27px;
  color: #ffffff;
}
.save {
  padding: 10px 30px;
  background: #409eff;
}
.finish {
  padding: 10px 25px;
  margin-right: 16px;
  background: #1f3359;
}
.end {
  padding: 10px 10px;
  margin-left: 16px;
  background: #ed7a49;
}
.endTab {
  align-items: flex-start;
  flex: 8;
}
.saveTab {
  align-items: flex-end;
  flex: 11;
}
.finishTab {
  align-items: flex-end;
  flex: 11;
}

.pop-contain {
  width: 240px;
  background: #ffffff;
  padding: 16px;
}
.min-size {
  font-size: 12px;
  color: #6d748d;
  vertical-align: text-top;
  margin-left: 12px;
}
.upload-cls {
  margin-top: 10px;
}
</style>
