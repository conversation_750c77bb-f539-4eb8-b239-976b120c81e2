<template>
  <div class="qrcode-result in-out-manage-page">
    <p class="tips">请截图或将图片保存至手机</p>
    <div class="qrcode-container">
      <h-qr-code class="qrcode" :value="schedulingNo" ref="qrCode"></h-qr-code>
    </div>
    <div class="content">
      <p>{{ detail.serialNo }}</p>
      <div class="content-item">
        <div class="item-label">值仓仓房：</div>
        <div class="item-value">{{ detail.storeHouseName }}</div>
      </div>
      <div class="content-item">
        <div class="item-label">调度号：</div>
        <short-schedule-no class="item-value">{{ detail.schedulingNo }}</short-schedule-no>
      </div>
      <div class="content-item">
        <div class="item-label">客户名称：</div>
        <div class="item-value">{{ detail.customer }}</div>
      </div>
      <div class="content-item">
        <div class="item-label">车船号：</div>
        <div class="item-value">{{ detail.transportVehicleNo }}</div>
      </div>
      <div class="content-item">
        <div class="item-label">业务类型：</div>
        <div class="item-value">{{ detail.buzTypeName }}</div>
      </div>
      <div class="content-item">
        <div class="item-label">时间：</div>
        <div class="item-value">{{ time }}</div>
      </div>
    </div>
    <div class="bottom-warpper bottom-btn">
      <van-button class="button" @click="onFinishCheckOut"> 保存到手机 </van-button>
    </div>
  </div>
</template>
<script>
import { Button } from 'vant';
import dayjs from 'dayjs';
import ShortScheduleNo from './../common/ShortScheduleNo.vue';
import { getCheckInOutPrintingInfo } from '@/api/in-out-manage';

export default {
  name: 'CheckIn',
  components: {
    'van-button': Button,
    ShortScheduleNo,
  },
  data() {
    return {
      detail: {},
      schedulingNo: '',
      time: dayjs().format('YYYY-MM-DD HH:mm:ss'),
    };
  },
  created() {
    this.schedulingNo = this.$route.query.schedulingNo;
    this.getDetail();
  },
  methods: {
    async getDetail() {
      this.detail = await getCheckInOutPrintingInfo({
        schedulingNo: this.schedulingNo,
      });
    },
    onFinishCheckOut() {
      this.$refs.qrCode.saveImage();
    },
  },
};
</script>
<style lang="scss" scoped>
@import '@/views/InoutManage/style/index';

.qrcode-result {
  width: 100%;

  .tips {
    text-align: center;
    color: #333;
    padding: 20px 0;
  }
  .qrcode-container {
    width: 300px;
    height: 300px;
    border-radius: 12px;
    background: #fff;
    margin: 0 auto;
    display: flex;
    align-items: center;
    justify-content: center;

    .qrcode {
      width: 200px;
      height: 200px;
    }
  }
  .bottom-btn {
    display: flex;
  }
  .content {
    background: #fff;
    margin-top: 28px;
    padding: 16px;
    p {
      font-size: 16px;
      font-family: PingFangSC-Medium, PingFang SC;
      font-weight: bold;
      color: #232323;
      margin-bottom: 16px;
    }

    .content-item {
      display: flex;
      margin-bottom: 12px;
      .item-label {
        font-size: 16px;
        color: #686b73;
        min-width: 110px;
      }
      .item-value {
        color: #232323;
      }
    }
  }
}
</style>
