<template>
  <div class="job-scheduling-record">
    <Row>
      <Col :span="12">
        <Search v-model="date" placeholder="选择日期区间" readonly @click="datePicker = true" />
      </Col>
      <Col :span="12">
        <Search v-model="search" placeholder="请输入调度号" show-action @search="onSearch">
          <template #action>
            <div class="scan-action" @click="onScan">
              <SvgIcon name="scan" />
            </div>
          </template>
        </Search>
      </Col>
    </Row>
    <Calendar
      v-model:show="datePicker"
      allow-same-day
      type="range"
      :min-date="minDate"
      :default-date="[new Date(beginDate), new Date(endDate)]"
      @confirm="onConfirm"
    />
    <PullRefresh @refresh="onRefresh" v-model="refreshLoading">
      <List
        ref="listRef"
        v-model:loading="isLoading"
        :finished="finished"
        finished-text="没有更多了"
        @load="getList"
      >
        <HCard
          class="detail-card"
          v-for="item in list"
          :key="item"
          @click="goToDetail(item.schedulingNo)"
        >
          <div class="card-content">
            <div class="row">
              <span class="label">到库车船号：</span>
              <span class="carNum" v-for="(it, index) in item.transportVehicleNo" :key="index">
                {{ it }}
              </span>
            </div>
            <div class="row">
              <span class="label">调度单据号：</span>
              <span class="value">{{ item.schedulingOrderNo }}</span>
            </div>
            <div class="row">
              <span class="label">粮油品种：</span>
              <span class="value">{{ item.foodCategoryName }}</span>
            </div>
            <div class="row">
              <span class="label">业务类型：</span>
              <Tag class="tag-cls" :color-type="Number(item.buzTyper)">
                {{ item.buzTyperName }}
              </Tag>
            </div>
            <div class="row">
              <span class="label">客户名称：</span>
              <span class="value">{{ item.customerName }}</span>
            </div>
            <div class="row">
              <span class="label">出入库凭证：</span>
              <span class="value">{{ item.voucherId }}</span>
            </div>
          </div>
        </HCard>
      </List>
    </PullRefresh>
  </div>
</template>

<script setup>
import { Search, List, PullRefresh, Row, Col, Calendar } from 'vant';
import { ref, reactive, watch } from 'vue';
import { useRouter, useRoute } from 'vue-router';
import SvgIcon from '@/components/SvgIcon/SvgIcon';
import { HCard } from '@/components';
import Tag from '@/views/InoutManage/common/InOutTypeTag.vue';
import { getJobSchedulingHistory } from '@/api/in-out-manage';
import dayjs from 'dayjs';

const router = useRouter();
const route = useRoute();
const search = ref('');
const listRef = ref();

const list = ref([]);
const pagination = reactive({
  page: 0,
  size: 10,
  total: 0,
});

const isLoading = ref(false);
const finished = ref(false);
const refreshLoading = ref(false);

const beginDate = ref(dayjs().format('YYYY-MM-DD'));
const endDate = ref(dayjs().format('YYYY-MM-DD'));
const date = ref(`${beginDate.value} - ${endDate.value}`);
const datePicker = ref(false);
const minDate = new Date(dayjs().subtract(10, 'year').format('YYYY-MM-DD'));

const onScan = () => {
  window.cordova?.plugins.barcodeScanner.scan(
    (result) => {
      if (result.text) {
        goToDetail(result.text);
      }
    },
    (error) => {
      alert('扫码失败 ' + error);
    },
    {
      prompt: '请将二维码放在扫码框中',
      resultDisplayDuration: 300,
    },
  );
};
const onRefresh = () => {
  if (refreshLoading.value) {
    pagination.page = 0;
    pagination.total = 0;
    list.value = [];
    refreshLoading.value = false;
  }
  finished.value = false;
  isLoading.value = true;
  getList();
};
const onSearch = () => {
  finished.value = false;
  list.value = [];
  pagination.page = 0;
  pagination.total = 0;
  listRef.value?.check();
};
const getList = async () => {
  const { items, page, total } = await getJobSchedulingHistory({
    schedulingNo: search.value,
    page: pagination.page + 1,
    size: pagination.size,
    beginDate: beginDate.value,
    endDate: endDate.value,
  });
  list.value.push(...items);
  pagination.page = page;
  pagination.total = total;

  // 加载状态结束
  isLoading.value = false;

  // 数据全部加载完成
  if (list.value.length >= total) {
    finished.value = true;
  }
};

const onConfirm = (values) => {
  const [start, end] = values;
  beginDate.value = dayjs(start).format('YYYY-MM-DD');
  endDate.value = dayjs(end).format('YYYY-MM-DD');
  date.value = `${beginDate.value} - ${endDate.value}`;
  datePicker.value = false;
  onSearch();
};

const goToDetail = (schedulingNo) => {
  router.push({
    name: 'JobSchedulingRecordDetail',
    params: {
      schedulingNo: schedulingNo,
    },
  });
};
watch(
  () => route.name,
  () => {
    search.value = '';
    onSearch();
  },
);
</script>

<style lang="scss" scoped>
.scan-action {
  font-size: 25px;
  height: 34px;
  display: flex;
  align-items: center;
  padding: 0 10px;
}
.total-contain {
  padding: 9px 14px;
}
.detail-card {
  padding: 16px;
  margin-bottom: 10px;
}
.card-head {
  display: flex;
  align-items: center;
  justify-content: space-between;
  font-size: 18px;
  font-weight: bold;
  color: #1f3359;
  .next {
    color: rgba(109, 116, 141, 0.6);
  }
}
.schedule-num {
  flex-grow: 1;
  margin-left: 12px;
}
.card-content {
  margin-top: 8px;
  line-height: 30px;
  .row {
    display: flex;
    .label {
      font-size: 14px;
      font-weight: 400;
      color: #6d748d;
      flex-basis: 6em;
    }
    .value {
      font-size: 14px;
      font-weight: 400;
      color: #0f0f0f;
      flex-grow: 1;
    }
    .carNum {
      padding: 3px 6px;
      background: #f4f4f4;
      border-radius: 2px;
      border: 1px solid #ebedf0;
      margin-right: 5px;
    }
  }
}
.tag-cls {
  line-height: 25px;
  &.tag {
    margin-left: 0;
  }
}
.next-contain {
  width: 80px;
  text-align: right;
}
.icon-cls {
  vertical-align: text-top;
}
</style>
