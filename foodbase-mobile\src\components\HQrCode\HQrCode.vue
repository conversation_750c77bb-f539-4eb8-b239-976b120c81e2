<template>
  <img class="h-qr-code" :src="dataURL" :alt="value" />
</template>

<script>
import { toDataURL } from 'qrcode';
import { Toast } from 'vant';

export default {
  name: 'HQrCode',
  props: {
    value: {
      type: String,
      default: () => '',
    },
  },
  mounted() {
    this.refreshDataUrl();
  },
  data() {
    return {
      dataURL: '',
    };
  },
  methods: {
    refreshDataUrl() {
      toDataURL(this.value, { margin: 1 }).then((url) => {
        this.dataURL = url;
      });
    },
    saveImage() {
      window.cordova?.base64ToGallery(
        this.dataURL,
        {
          prefix: 'img_',
          mediaScanner: true,
        },

        function (path) {
          Toast.success('保存成功');
          console.log(path, 'true');
        },

        function (err) {
          Toast.fail('保存失败');
          console.error(err, '2222222false2222222');
        },
      );
      console.log(this.dataURL, 'this.dataURL--------Hqrcode');
      // return this.dataURL;
    },
  },

  watch: {
    value() {
      this.refreshDataUrl();
    },
  },
  // defineExpose({ saveImage })
};
</script>

<style scoped></style>
