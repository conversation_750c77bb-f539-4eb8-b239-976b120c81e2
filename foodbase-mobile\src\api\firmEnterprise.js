import { reserverPurchase } from '@/utils/request';
// export async function getHouseList(params) {
//   return reserveRequest().get('/api/appv1/info4App', { params });
// }
// export async function getHouseListId(params) {
//   return reserveRequest().get('/api/appv1/info4App', { params });
// }
export async function getHouseList(params) {
  return reserverPurchase().get('/api/emergencyUnit/page', { params });
}
export async function getHouseListId(id) {
  return reserverPurchase().get(`/api/emergencyUnit/get/id?id=${id}`);
}
