import { createApp } from 'vue';
import App from './App.vue';
import router from './router';
import store from './store';
import dayjs from 'dayjs';
import duration from 'dayjs/plugin/duration'
import components from './components';
import SvgIconPlugin from '@/components/SvgIcon';
// import VConsole from 'vconsole';
import PermissionDirective from '@/utils/permission';
import 'tailwindcss/tailwind.css';
import 'vant/lib/index.css';

// if (process.env.NODE_ENV === 'development') {
// 或者使用配置参数进行初始化
// eslint-disable-next-line no-unused-vars
// const vConsole = new VConsole({ theme: 'dark' });
// console.log(vConsole);
// 调用 console 方法输出日志
//  console.log('Hello world');
// }

dayjs.extend(duration)

const app = createApp(App);

app.use(SvgIconPlugin);
app.use(components);
app.use(store);
app.use(router);
app.use(PermissionDirective);

app.mount('#app');
